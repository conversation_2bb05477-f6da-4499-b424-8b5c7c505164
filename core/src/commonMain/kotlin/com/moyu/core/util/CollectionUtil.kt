package com.moyu.core.util

import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import kotlin.math.roundToInt

fun List<Property>.reduce() = reduceOrNull { acc, property -> acc + property } ?: EMPTY_PROPERTY

fun List<Int>.perPlusI(peer: List<Int>): List<Int> {
    val resultList = mutableListOf<Int>()
    this.forEachIndexed { index, t ->
        resultList.add(t + peer.getOrElse(index) { 0 })
    }
    return resultList
}

fun List<Double>.perPlusD(peer: List<Double>): List<Double> {
    val resultList = mutableListOf<Double>()
    this.forEachIndexed { index, t ->
        resultList.add(t + peer.getOrElse(index) { 0.0 })
    }
    return resultList
}


fun List<Int>.perMinusI(peer: List<Int>): List<Int> {
    val resultList = mutableListOf<Int>()
    this.forEachIndexed { index, t ->
        resultList.add(t - peer.getOrElse(index) { 0 })
    }
    return resultList
}

fun List<Double>.perMinusD(peer: List<Double>): List<Double> {
    val resultList = mutableListOf<Double>()
    this.forEachIndexed { index, t ->
        resultList.add(t - peer.getOrElse(index) { 0.0 })
    }
    return resultList
}

fun List<Int>.perMultiI(times: Int): List<Int> {
    val resultList = mutableListOf<Int>()
    this.forEach {
        resultList.add(it * times)
    }
    return resultList
}

fun List<Int>.perMultiD(times: Double): List<Int> {
    val resultList = mutableListOf<Int>()
    this.forEach {
        resultList.add((it * times).roundToInt())
    }
    return resultList
}

fun List<Double>.perDMultiD(times: Double): List<Double> {
    val resultList = mutableListOf<Double>()
    this.forEach {
        resultList.add(it * times)
    }
    return resultList
}

fun List<Int>.perBiggerI(peer: List<Int>): Boolean {
    this.forEachIndexed { index, i ->
        if (i < peer[index]) {
            return false
        }
    }
    return true
}

inline fun <T> List<T>.forEachReversedWithIndex(f: (Int, T) -> Unit) {
    var i = size - 1
    while (i >= 0) {
        f(i, get(i))
        i--
    }
}

fun <E> List<E>.subListOrEmpty(from: Int, to: Int): List<E> {
    return if (size >= to) {
        subList(from, to)
    } else if (size < from) {
        emptyList()
    } else {
        subList(from, size)
    }
}

fun <T> List<T>.second(): T {
    if (size < 2)
        throw NoSuchElementException("List doesn't have 2 items.")
    return this[1]
}

fun <T, E, K> List<T>.zip(list2: List<E>, list3: List<K>): List<Triple<T, E, K >> {
    return zip(list2).zip(list3) { a, b -> Triple(a.first, a.second, b) }
}