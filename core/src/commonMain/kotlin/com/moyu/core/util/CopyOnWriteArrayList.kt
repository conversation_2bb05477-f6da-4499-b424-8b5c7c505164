package com.moyu.core.util

import kotlinx.atomicfu.atomic

/**
 * 纯 Kotlin 的 CopyOnWriteArrayList。
 * 每次修改都会创建并替换底层列表的副本，从而保证对外部读取来说是线程安全的、无需额外同步。
 */
class CopyOnWriteArrayList<T> : MutableList<T> {

    // 使用 atomic 存储底层不可变 List，每次修改都新建一个 List 并更新此原子引用
    private val data = atomic<List<T>>(emptyList())

    override val size: Int
        get() = data.value.size

    override fun isEmpty(): Boolean =
        data.value.isEmpty()

    override fun contains(element: T): Boolean =
        data.value.contains(element)

    override fun containsAll(elements: Collection<T>): Boolean =
        data.value.containsAll(elements)

    override fun get(index: Int): T =
        data.value[index]

    override fun indexOf(element: T): Int =
        data.value.indexOf(element)

    override fun lastIndexOf(element: T): Int =
        data.value.lastIndexOf(element)

    /**
     * 迭代器直接基于当前列表快照构造，不会反映之后的修改，也无需额外的同步。
     */
    override fun iterator(): MutableIterator<T> =
        data.value.toMutableList().iterator()

    override fun listIterator(): MutableListIterator<T> =
        data.value.toMutableList().listIterator()

    override fun listIterator(index: Int): MutableListIterator<T> =
        data.value.toMutableList().listIterator(index)

    override fun add(element: T): Boolean {
        while (true) {
            val current = data.value
            val newList = current + element
            if (data.compareAndSet(current, newList)) {
                return true
            }
        }
    }

    override fun add(index: Int, element: T) {
        while (true) {
            val current = data.value
            val newList = current.toMutableList().apply {
                add(index, element)
            }
            if (data.compareAndSet(current, newList)) {
                return
            }
        }
    }

    override fun addAll(elements: Collection<T>): Boolean {
        if (elements.isEmpty()) return false
        while (true) {
            val current = data.value
            val newList = current + elements
            if (data.compareAndSet(current, newList)) {
                return true
            }
        }
    }

    override fun addAll(index: Int, elements: Collection<T>): Boolean {
        if (elements.isEmpty()) return false
        while (true) {
            val current = data.value
            val newList = current.toMutableList().apply {
                addAll(index, elements)
            }
            if (data.compareAndSet(current, newList)) {
                return true
            }
        }
    }

    override fun remove(element: T): Boolean {
        while (true) {
            val current = data.value
            if (!current.contains(element)) {
                return false
            }
            val newList = current.toMutableList().apply {
                remove(element)
            }
            if (data.compareAndSet(current, newList)) {
                return true
            }
        }
    }

    override fun removeAt(index: Int): T {
        while (true) {
            val current = data.value
            val old = current.getOrNull(index)
                ?: throw IndexOutOfBoundsException("Index: $index, Size: ${current.size}")
            val newList = current.toMutableList().apply {
                removeAt(index)
            }
            if (data.compareAndSet(current, newList)) {
                return old
            }
        }
    }

    fun removeAll(predicate: (T) -> Boolean): Boolean {
        var removed = false
        while (true) {
            val current = data.value
            // 根据谓词过滤掉需要删除的元素
            val newList = current.filterNot(predicate)

            // 如果经过过滤后大小没变，说明没有任何元素被删除
            if (newList.size == current.size) {
                return removed // 没有变化就直接返回，这里的 removed 肯定是 false
            }

            // 如果 CAS 成功，就说明实际更新了 data
            if (data.compareAndSet(current, newList)) {
                removed = true
                return removed
            }
            // 如果 CAS 失败了，说明在此期间有人修改了 data，需要重试
        }
    }

    override fun removeAll(elements: Collection<T>): Boolean {
        var modified = false
        while (true) {
            val current = data.value
            val filtered = current.filterNot { it in elements }
            if (filtered.size == current.size) {
                return modified
            }
            if (data.compareAndSet(current, filtered)) {
                modified = true
                return true
            }
        }
    }

    override fun retainAll(elements: Collection<T>): Boolean {
        var modified = false
        while (true) {
            val current = data.value
            val filtered = current.filter { it in elements }
            if (filtered.size == current.size) {
                return modified
            }
            if (data.compareAndSet(current, filtered)) {
                modified = true
                return true
            }
        }
    }

    override fun set(index: Int, element: T): T {
        while (true) {
            val current = data.value
            val old = current.getOrNull(index)
                ?: throw IndexOutOfBoundsException("Index: $index, Size: ${current.size}")
            val newList = current.toMutableList().apply {
                this[index] = element
            }
            if (data.compareAndSet(current, newList)) {
                return old
            }
        }
    }

    override fun clear() {
        while (true) {
            val current = data.value
            if (current.isEmpty()) return
            if (data.compareAndSet(current, emptyList())) {
                return
            }
        }
    }

    /**
     * 返回一个“快照”子列表，不会随原列表后续修改而改变。
     * 如需类似 Java `CopyOnWriteArrayList` 那样的行为，通常直接对整个列表操作即可。
     */
    override fun subList(fromIndex: Int, toIndex: Int): MutableList<T> {
        val snapshot = data.value
        return snapshot.subList(fromIndex, toIndex).toMutableList()
    }
}