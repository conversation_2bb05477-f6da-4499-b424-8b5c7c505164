package com.moyu.core

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.runBlocking
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.stringResource

object AppWrapper {
    val globalScope: CoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

    var isForeground = mutableStateOf(true)
    var lastNetWorkTime = mutableLongStateOf(0L)
    var elapsedDiffTime: Long = 0L

    fun getStringKmp(resId: StringResource): String {
        var result = ""
        runBlocking {
            result = org.jetbrains.compose.resources.getString(resId)
        }

        return result
    }

    @Composable
    fun kmpStringResource(resId: StringResource): String {
        return stringResource(resId)
    }
}