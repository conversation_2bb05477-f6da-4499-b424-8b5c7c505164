package com.moyu.core.model

data class Unlock(
    override val id: Int,
    val name: String,
    val initialLock: Int,
    val conditionType: List<Int>,
    val conditionNum: List<Int>,
    val show: List<Int>,
    val desc: String,
    val url: String,
    val icon: String = "0",
    val dialog: String = "",
): ConfigData {
    fun isGift(): <PERSON>olean {
        return id >= 9500001
    }

    fun isUnlockPass(): Boolean {
        return id in 10000..19999
    }

    fun canShowDialog(): Boolean {
        return !isGift() && conditionType.first() != 0 && !isUnlockPass()
    }
}
