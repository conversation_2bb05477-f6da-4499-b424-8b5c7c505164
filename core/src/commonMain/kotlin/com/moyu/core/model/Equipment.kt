package com.moyu.core.model

import com.moyu.core.GameCore
import com.moyu.core.model.property.Property
import com.moyu.core.model.skill.Skill
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID

fun Int.equipIdToMainId(): Int {
    return this / 100
}
@Serializable
data class Equipment(
    @SerialName("i") override val id: Int = 0,
    @Transient val mainId: Int = 0,
    @Transient val name: String = "",
    @Transient val requestId: Int = 0,
    @Transient val requestNum: Int = 0,
    @Transient val type: Int = 0,
    @Transient val attribute1: Int = 0,
    @Transient val attribute2: Int = 0,
    @Transient val attribute3: Int = 0,
    @Transient val attribute4: Double = 0.0,
    @Transient val attribute5: Double = 0.0,
    @Transient val attribute6:Double = 0.0,
    @Transient val attribute7: Int = 0,
    @Transient val star: Int = 0,
    @Transient val starLimit: Int = 0,
    @Transient val quality: Int = 0,
    @Transient val skillEffect: Int = 0,
    @Transient val starUpNum: Int = 0,
    @Transient val starUpResourceNum: Int = 0,
    @Transient val dropLimit: Int = 0,
    @Transient val story: String = "",
    @Transient val belong: Int = 0,
    @Transient val pic: String = "",
    @Transient val power: Int = 0,
    @Transient val vipValue: Int = 0,
    @Transient val peek: Boolean = false,
    @SerialName("z") val num: Int = 1,
    @SerialName("r") val equipped: Boolean = false,
    @Transient override val new: Boolean = true,
    @Transient override val uuid: String = ""
): GameItem, ConfigData {
    override fun create(): Equipment {
        return GameCore.instance.getEquipById(id).copy(
            uuid = this.uuid.takeIf { it.isNotEmpty() } ?: UUID.generateUUID().toString(),
            new = false,
            num = num,
            equipped = equipped
        )
    }

    override fun setUnNew(): Equipment {
        return copy(new = false)
    }

    fun getProperty(): Property {
        return Property(
            attack = attribute1,
            defenses = attribute2,
            hp = attribute3,
            fatalRate = attribute4,
            fatalDamage = attribute5,
            dodgeRate = attribute6,
            speed = attribute7
        )
    }

    fun starUp(): Equipment {
        return GameCore.instance.getEquipPool(mainId)
            .firstOrNull { it.star == star + 1 }
            ?.copy(
                num = num,
                uuid = uuid,
                equipped = equipped
            ) ?: this
    }

    fun getSkill(): Skill? {
        return GameCore.instance.getSkillByIdNullable(skillEffect)
    }

    fun getNextStarEquip(): Equipment {
        return GameCore.instance.getEquipPool(mainId)
            .firstOrNull { it.star == star + 1 }?: this
    }

    fun getStarUpPowerDiff(): Int {
        return getNextStarEquip().power - power
    }

    fun starTo(star: Int): Equipment {
        val starUp = GameCore.instance.getEquipPool(mainId)
            .first { it.star == star }
        return starUp.copy(
            num = num,
            uuid = uuid,
        )
    }
}