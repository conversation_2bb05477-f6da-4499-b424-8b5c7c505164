package com.moyu.core.model.action

import com.moyu.core.model.Buff
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.skill.Skill

data class ActionState(
    val type: ActionStateType = ActionStateType.Normal,
    val skill: Skill? = null,
    val damage: DamageResult? = null,
    val value: Int = 0,
    val healResult: HealResult? = null,
    val buff: Buff? = null,
    val targets: List<String> = emptyList(),
    val effectNum: Int = 0,
    val effect: String = "",
) {
    fun isState(stateType: ActionStateType): Boolean {
        return stateType == type
    }
}