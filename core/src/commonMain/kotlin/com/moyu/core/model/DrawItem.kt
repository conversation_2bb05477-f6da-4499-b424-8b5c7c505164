package com.moyu.core.model

data class DrawItem(
    override val id: Int = 0,
    val type: Int = 0,
    val quality: Int = 0,
    val rate: Double = 0.0,
    val pool: Int = 0,
): ConfigData {
    fun isDrawAlly(): Boolean {
        return type == 3
    }

    fun isDrawEquip(): <PERSON><PERSON>an {
        return type == 4
    }

    fun isDrawActivity(): <PERSON><PERSON><PERSON> {
        return type == 5
    }
}