package com.moyu.core.model

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.identifier.NoneRoleIdentifier
import com.moyu.core.model.property.Property
import core.generated.resources.Res
import core.generated.resources.group1
import core.generated.resources.group10
import core.generated.resources.group11
import core.generated.resources.group2
import core.generated.resources.group3
import core.generated.resources.group4
import core.generated.resources.group5
import core.generated.resources.group6
import core.generated.resources.group7
import core.generated.resources.group8
import core.generated.resources.group9
import core.generated.resources.race1
import core.generated.resources.race2
import core.generated.resources.race3
import core.generated.resources.race4
import core.generated.resources.race5
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import kotlin.math.max
import kotlin.math.min

const val RACE_SIZE = 11
const val MAX_STAR = 50
const val MAX_LEVEL = 500
const val MASTER_MAIN_ID = 101

fun Int.allyIdToMainId(): Int {
    return this / 100000
}

fun Int.getRaceTypeName(): String {
    return when (this) {
        1 -> AppWrapper.getStringKmp(Res.string.race1)
        2 -> AppWrapper.getStringKmp(Res.string.race2)
        3 -> AppWrapper.getStringKmp(Res.string.race3)
        4 -> AppWrapper.getStringKmp(Res.string.race4)
        else -> AppWrapper.getStringKmp(Res.string.race5)
    }
}

fun Int.getRaceGroupName(): String {
    return when (this) {
        1 -> AppWrapper.getStringKmp(Res.string.group1)
        2 -> AppWrapper.getStringKmp(Res.string.group2)
        3 -> AppWrapper.getStringKmp(Res.string.group3)
        4 -> AppWrapper.getStringKmp(Res.string.group4)
        5 -> AppWrapper.getStringKmp(Res.string.group5)
        6 -> AppWrapper.getStringKmp(Res.string.group6)
        7 -> AppWrapper.getStringKmp(Res.string.group7)
        8 -> AppWrapper.getStringKmp(Res.string.group8)
        9 -> AppWrapper.getStringKmp(Res.string.group9)
        10 -> AppWrapper.getStringKmp(Res.string.group10)
        else -> AppWrapper.getStringKmp(Res.string.group11)
    }
}


@Serializable
data class Ally(
    @SerialName("i") val id: Int = 1,
    @Transient val mainId: Int = 0,
    @Transient val name: String = "",
    @Transient val raceType: Int = 0,
    @Transient val raceType2: Int = 0,
    @Transient val level: Int = 0,
    @Transient val star: Int = 0,
    @Transient val quality: Int = 0,
    @Transient val attribute1: Int = 0,
    @Transient val attribute2: Int = 0,
    @Transient val attribute3: Int = 0,
    @Transient val attribute4: Double = 0.0,
    @Transient val attribute5: Double = 0.0,
    @Transient val attribute6: Double = 0.0,
    @Transient val attribute7: Int = 0,
    @Transient val fixedSkills: List<Int> = emptyList(),
    @Transient val extraSkills1: List<Int> = emptyList(),
    @Transient val extraSkills2: List<Int> = emptyList(),
    @Transient val extraSkills3: List<Int> = emptyList(),
    @Transient val randomSkillId: List<Int> = emptyList(),
    @Transient val banSkillId: List<Int> = emptyList(),
    @Transient val randomSkillNum: List<Int> = emptyList(),
    @Transient val pic: String = "",
    @Transient val story: String = "",
    @Transient val dropLimit: Int = 0,
    @Transient val levelPower: Int = 0,
    @Transient val vipValue: Int = 0,

    @SerialName("z") val num: Int = 1,
    @Transient override val new: Boolean = true,
    @Transient val selected: Boolean = false,
    // 商店信息
    @Transient val peek: Boolean = false,
    // 局内信息
    @SerialName("r") val gameHp: Int = 100, // 0是死亡，改为百分比，100%是满血
    @SerialName("p") val exerciseProperty: Property? = null,
    @SerialName("a") val battlePosition: Int = -1, // 是否参战
    @Transient val roleIdentifier: Identifier = NoneRoleIdentifier,
    @Transient override val uuid: String = ""
) : GameItem {

    fun getProperty(): Property {
        return Property(
            attack = attribute1,
            defenses = attribute2,
            hp = attribute3,
            fatalRate = attribute4,
            fatalDamage = attribute5,
            dodgeRate = attribute6,
            speed = attribute7
        )
    }

    fun getHeadIcon(): String {
        return pic// + "_headicon"
    }

    fun isMelee(): Boolean {
        return raceType !in listOf(2, 4, 5)
    }

    /**
     * 选中去游戏中，是一个新的实例，带uuid，和局外军团卡无关
     */
    fun copyToGame(): Ally {
        return copy(
            uuid = UUID.generateUUID().toString(),
            roleIdentifier = Identifier.player(name),
        )
    }

    fun switchSelect(): Ally {
        return copy(
            selected = !selected, uuid = uuid.ifEmpty { UUID.generateUUID().toString() })
    }

    fun relive(): Ally {
        return copy(gameHp = 100)
    }

    fun hurt(percent: Int): Ally {
        return copy(gameHp = max(gameHp - percent, 1))
    }

    fun heal(percent: Int): Ally {
        return copy(gameHp = min(gameHp + percent, 100))
    }

    fun isDead(): Boolean {
        return gameHp <= 0
    }

    fun isHurt(): Boolean {
        return gameHp > 0 && gameHp != 100
    }

    fun starUp(): Ally {
        val starUp = GameCore.instance.getAllyNextStar(id)
        return starUp.copy(
            num = num,
            selected = selected,
            gameHp = 100,
            roleIdentifier = roleIdentifier,
            battlePosition = battlePosition,
            uuid = uuid,
        )
    }

    fun starTo(star: Int): Ally {
        val starUp = GameCore.instance.getAlly(mainId, level, star)
        return starUp.copy(
            num = num,
            selected = selected,
            gameHp = 100,
            roleIdentifier = roleIdentifier,
            battlePosition = battlePosition,
            uuid = uuid,
        )
    }

    fun levelUp(): Ally {
        val levelUp = GameCore.instance.getAllyNextLevel(id)
        return levelUp.copy(
            num = num,
            selected = selected,
            gameHp = 100,
            roleIdentifier = roleIdentifier,
            battlePosition = battlePosition,
            uuid = uuid,
        )
    }

    fun levelTo(level: Int): Ally {
        val levelUp = GameCore.instance.getAlly(mainId, level, star)
        return levelUp.copy(
            num = num,
            selected = selected,
            gameHp = 100,
            roleIdentifier = roleIdentifier,
            battlePosition = battlePosition,
            uuid = uuid,
        )
    }

    fun switchSelectToBattle(position: Int): Ally {
        return copy(
            battlePosition = position,
        )
    }

    override fun create(): Ally {
        return GameCore.instance.getAllyById(id).copy(
            num = this.num,
            selected = this.selected,
            uuid = this.uuid.takeIf { it.isNotEmpty() } ?: UUID.generateUUID().toString(),
            roleIdentifier = Identifier.player(name),
            new = false,
            gameHp = gameHp,
            exerciseProperty = exerciseProperty,
            battlePosition = battlePosition,
        )
    }

    override fun setUnNew(): GameItem {
        return copy(new = false)
    }

    fun isHero(): Boolean {
        return this.mainId == MASTER_MAIN_ID
    }

    fun getStarUpNum(): Int {
        val allyStars = GameCore.instance.getAllyStarPool().filter { it.quality == quality }
        return allyStars[star - 1].conditionNum2.first()
    }

    fun getStarUpRes(): Int {
        val allyStars = GameCore.instance.getAllyStarPool().filter { it.quality == quality }
        return allyStars[star - 1].conditionNum1.first()
    }

    fun getLevelUpNum(): Int {
        val allyLevel = GameCore.instance.getAllyLevelPool()
        return allyLevel[level - 1].conditionNum.first()
    }

    fun match(propertyAward: PropertyAward, isMyTeam: Boolean): Boolean {
        if ((isMyTeam && propertyAward.teamType == 2 || !isMyTeam && propertyAward.teamType == 1)) {
            return false
        }
        if (propertyAward.race == 0 && isHero()) {
            return false
        }
        if (propertyAward.race != 0 && propertyAward.race != raceType) {
            return false
        }
        if (propertyAward.group != 0 && propertyAward.group != raceType2) {
            return false
        }
        return true
    }

    fun getAllPower(): Int {
        return levelPower + GameCore.instance.getAllyStarPool()
            .first { it.quality == quality && it.star == star }.power
    }

    fun getStarPower(): Int {
        return GameCore.instance.getAllyStarPool()
            .first { it.quality == quality && it.star == star }.power
    }

    fun getLevelUpPowerDiff(): Int {
        return GameCore.instance.getAllyNextLevel(id).levelPower - levelPower
    }

    fun getStarUpPowerDiff(): Int {
        return GameCore.instance.getAllyNextStar(id).getStarPower() - getStarPower()
    }

    fun getGroupPic(): String {
        if (raceType2 == 19) {
            return ""
        }
        return GameCore.instance.getStoryPool()[raceType2 - 1].pic
    }

    fun getScaleByQuality(): Float {
        return if (isHero()) 1.02f
        else when (quality) {
            1 -> 0.94f
            2 -> 0.97f
            3 -> 0.99f
            else -> 1.02f
        }
    }

    fun getScaleByQualityBattle(): Float {
        return if (isHero()) 1f
        else when (quality) {
            1 -> 0.9f
            2 -> 0.95f
            3 -> 1f
            else -> 1.05f
        }
    }
}