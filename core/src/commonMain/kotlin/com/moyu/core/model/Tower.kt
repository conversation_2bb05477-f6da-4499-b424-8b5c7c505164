package com.moyu.core.model

import com.moyu.core.model.property.Property

data class Tower(
    val id: Int,
    val layer: Int,
    val type: List<Int>,
    val playPara1: List<Int>,
    val playPara2: Int,
    val eventAttribute1: Int,
    val eventAttribute2: Int,
    val eventAttribute3: Int,
    val eventAttribute4: Double,
    val eventAttribute5: Double,
    val eventAttribute6: Double,
    val eventAttribute7: Int,
    val reward: Int,
) {
    fun getDiffProperty(): Property {
        return Property(
            attack = eventAttribute1, // todo
            defenses = eventAttribute2,
            hp = eventAttribute3,
            fatalRate = eventAttribute4,
            fatalDamage = eventAttribute5,
            dodgeRate = eventAttribute6,
            speed = eventAttribute7,
        )
    }
}