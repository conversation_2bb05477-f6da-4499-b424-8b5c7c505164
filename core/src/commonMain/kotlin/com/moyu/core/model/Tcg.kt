package com.moyu.core.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class Tcg(
    @SerialName("i")
    val id: Int,
    @Transient
    val type: Int = 1,
    @Transient
    val name: String = "",
    @Transient
    val star: Int = 1,
    @Transient
    val content: List<Int> = emptyList(),
    @Transient
    val reward: Int = 0,
) {
    fun isAlly(): <PERSON><PERSON><PERSON> {
        return type < 39
    }
}