package com.moyu.core.model

import com.moyu.core.GameCore
import com.moyu.core.model.property.Property
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID

@Serializable
data class Event(
    @SerialName("z") val id: Int,
    @Transient val name: String = "",
    @Transient val age: List<Int> = emptyList(),
    @Transient val appear: List<Int> = emptyList(),
    @Transient val same: List<Int> = emptyList(),
    @Transient val front: List<Int> = emptyList(),
    @Transient val disappear: List<Int> = emptyList(),
    @Transient val weight: Int = 0,
    @Transient val condition: Int = 0,
    @Transient val play: Int = 0,
    @Transient val playPara1: List<Int> = emptyList(),
    @Transient val playPara2: List<Double> = emptyList(),
    @Transient val winReward: List<Int> = emptyList(),
    @Transient val loseReward: List<Int> = emptyList(),
    @Transient val dialogId: Int = 0,
    @Transient val dialogType: Int = 0,
    @Transient val showText: String = "",
    @Transient val startText: String = "",
    @Transient val winText: String = "",
    @Transient val loseText: String = "",
    @Transient val isEnd: Boolean = true,
    @Transient val endType: Int = 0,
    @Transient val storyDesc1: String = "",
    @Transient val storyDesc2: String = "",
    @Transient val storyBag: Int = 0,
    @Transient val eventAttribute1: Int = 0,
    @Transient val eventAttribute2: Int = 0,
    @Transient val eventAttribute3: Int = 0,
    @Transient val eventAttribute4: Double = 0.0,
    @Transient val eventAttribute5: Double = 0.0,
    @Transient val eventAttribute6: Double = 0.0,
    @Transient val eventAttribute7: Int = 0,
    @Transient val pic: String = "",
    @Transient val bgPic: String = "",
    @Transient val isMainLine: Int = 0,
    @Transient val isRepeat: Int = 0,
    @Transient val front2: List<Int> = emptyList(),
    @Transient val levelId: Int = 0,
    @Transient val option1: String = "",
    @Transient val option2: String = "",
    val uuid: String = UUID.generateUUID().toString(),
    val selectAge: Int = 0 // 哪个时间被选择
) {
    fun getDiffProperty(): Property {
        return Property(
            attack = eventAttribute1, // todo
            defenses = eventAttribute2,
            hp = eventAttribute3,
            fatalRate = eventAttribute4,
            fatalDamage = eventAttribute5,
            dodgeRate = eventAttribute6,
            speed = eventAttribute7,
        )
    }

    fun create(): Event {
        return GameCore.instance.getEventPool().first { it.id == this.id }
            .copy(uuid = this.uuid, selectAge = this.selectAge)
    }

    fun createOrNull(): Event? {
        return GameCore.instance.getEventPool().firstOrNull { it.id == this.id }
            ?.copy(uuid = this.uuid, selectAge = this.selectAge)
    }

    fun createUUID(): Event {
        return copy(uuid = UUID.generateUUID().toString())
    }

    // 性能优化
    fun simple(): Event {
        return copy(appear = emptyList(), front = emptyList(), disappear = emptyList(), front2 = emptyList())
    }
}