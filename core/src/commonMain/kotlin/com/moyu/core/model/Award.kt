package com.moyu.core.model

import com.moyu.core.GameCore
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS
import com.moyu.core.model.property.Property
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.perPlusI
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

// todo 新增国家，要改下面两个定义
val EMPTY_REPUTATION = listOf(
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
)

// todo 新增资源，要改下面两个定义
val EMPTY_RESOURCES = listOf(
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0
)

@Serializable
data class PropertyAward(
    val teamType: Int, // 1: 己方  2： 地方  3：双方
    val race: Int,
    val group: Int,
    val property: Property,
    val age: Int = 0
) {
    fun isBad(): Boolean {
        return teamType != 2 && property.isBad()
    }
}

// todo Award不会作为局内的数据序列化，所以可以屏蔽一些非关键数据
@Serializable
data class Award(
    @Transient
    val message: String = "",
    @Transient
    val allies: List<Ally> = emptyList(),
    @SerialName("oa")
    val outAllies: List<Ally> = emptyList(),
    @Transient
    val skills: List<Skill> = emptyList(),
    @SerialName("eq")
    val outEquips: List<Equipment> = emptyList(),
    @Transient
    val advProperty: AdventureProps = EMPTY_ADV_PROPS,
    @Transient
    val battleProperty: List<PropertyAward> = emptyList(),
    @SerialName("ru")
    val reputations: List<Int> = EMPTY_REPUTATION,
    @SerialName("s")
    val resources: List<Int> = EMPTY_RESOURCES,
    @SerialName("t")
    val diamond: Int = 0,
    @SerialName("p")
    val pvpDiamond: Int = 0,
    @SerialName("v")
    val pvpScore: Int = 0,
    @SerialName("l")
    val pvp2Score: Int = 0,
    @SerialName("k")
    val key: Int = 0,
    @Transient
    val extraKeyRate: Int = 0,
    @SerialName("re")
    val realMoney: Int = 0,
    @SerialName("n")
    val lotteryMoney: Int = 0,
    @SerialName("m")
    val couponAlly: Int = 0,
    @SerialName("z")
    val couponEquip: Int = 0,
    @SerialName("p1")
    val warPass1: Int = 0,
    @SerialName("p2")
    val warPass2: Int = 0,
    @SerialName("p3")
    val warPass3: Int = 0,
    @SerialName("p4")
    val warPass4: Int = 0,
    @SerialName("e")
    val electric: Int = 0,
    @SerialName("o")
    val reputationMoney: Int = 0,
    @SerialName("j")
    val holidayMoney: Int = 0,
    @SerialName("b")
    val accountExp: Int = 0,
    @SerialName("c")
    val allyExp: Int = 0,
    @SerialName("d")
    val power: Int = 0,
    @SerialName("x")
    val adMoney: Int = 0,
    @SerialName("y")
    val talentPoint: Int = 0,
    @Transient
    val allHeal: Int = 0,
    @SerialName("r")
    val roadExp: Int = 0,
    @Transient
    val unlockList: List<Int> = emptyList(),
    @Transient
    val sellId: Int = 0,
    @Transient
    val showQuestion: Boolean = false,
    @Transient
    val resultCode: Int = 0
) {
    operator fun plus(singleAward: Award): Award {
        return copy(
            outEquips = mutableListOf<Equipment>().apply {
                addAll(outEquips)
                addAll(singleAward.outEquips)
            },
            skills = mutableListOf<Skill>().apply {
                addAll(skills)
                addAll(singleAward.skills)
            },
            allies = mutableListOf<Ally>().apply {
                addAll(allies)
                addAll(singleAward.allies)
            },
            outAllies = mutableListOf<Ally>().apply {
                addAll(outAllies)
                addAll(singleAward.outAllies)
            },
            unlockList = mutableListOf<Int>().apply {
                addAll(unlockList)
                addAll(singleAward.unlockList)
            },
            battleProperty = mutableListOf<PropertyAward>().apply {
                addAll(battleProperty)
                addAll(singleAward.battleProperty)
            },
            reputations = reputations.perPlusI(singleAward.reputations),
            resources = resources.perPlusI(singleAward.resources),
            adMoney = adMoney + singleAward.adMoney,
            key = key + singleAward.key,
            extraKeyRate = extraKeyRate + singleAward.extraKeyRate,
            lotteryMoney = lotteryMoney + singleAward.lotteryMoney,
            realMoney = realMoney + singleAward.realMoney,
            couponAlly = couponAlly + singleAward.couponAlly,
            couponEquip = couponEquip + singleAward.couponEquip,
            diamond = diamond + singleAward.diamond,
            allyExp = allyExp + singleAward.allyExp,
            reputationMoney = reputationMoney + singleAward.reputationMoney,
            pvpDiamond = pvpDiamond + singleAward.pvpDiamond,
            holidayMoney = holidayMoney + singleAward.holidayMoney,
            talentPoint = talentPoint + singleAward.talentPoint,
            pvpScore = pvpScore + singleAward.pvpScore,
            pvp2Score = pvp2Score + singleAward.pvp2Score,
            allHeal = allHeal + singleAward.allHeal,
            electric = electric + singleAward.electric,
            warPass1 = warPass1 + singleAward.warPass1,
            warPass2 = warPass2 + singleAward.warPass2,
            warPass3 = warPass3 + singleAward.warPass3,
            warPass4 = warPass4 + singleAward.warPass4,
            accountExp = accountExp + singleAward.accountExp,
            roadExp = roadExp + singleAward.roadExp,
            power = power + singleAward.power,
            advProperty = advProperty + singleAward.advProperty,
            showQuestion = showQuestion || singleAward.showQuestion,
            sellId = if (sellId == 0) singleAward.sellId else sellId
        )
    }

    fun isEmpty(): Boolean {
        return this == Award() || this.copy(message = "") == Award()
    }

    operator fun unaryMinus(): Award {
        return this.copy(
            allies = emptyList(),
            skills = emptyList(),
            key = -key,
            power = -power,
            lotteryMoney = -lotteryMoney,
            reputationMoney = -reputationMoney,
            couponAlly = -couponAlly,
            couponEquip = -couponEquip,
            talentPoint = -talentPoint,
            diamond = -diamond,
            realMoney = -realMoney,
            pvpDiamond = -pvpDiamond,
            pvpScore = -pvpScore,
            pvp2Score = -pvp2Score,
            accountExp = -accountExp,
            allHeal = -allHeal,
            reputations = reputations.map { -it },
            resources = resources.map { -it },
            advProperty = EMPTY_ADV_PROPS - advProperty,
        )
    }

    fun recreate(): Award {
        return copy(outAllies = this.outAllies.map {
            GameCore.instance.getAllyById(it.id).copy(num = it.num)
        }, outEquips = this.outEquips.map {
            GameCore.instance.getEquipById(it.id).copy(num = it.num)
        })
    }

    fun isGift(): Boolean {
        return sellId != 0 && sellId in GameCore.instance.getGiftPool().map { it.id }
    }

    fun toMultipleDisplayList(): List<Award> {
        return if (this.diamond >= 10 || this.key >= 10 || this.reputations.any { it >= 10 } || this.resources.any { it >= 10 } || allyExp >= 10 || accountExp >= 10) {
            listOf(
                this,
                this,
                this,
                this,
                this,
                this,
                this,
                this,
                this,
                this
            )
        } else if (this.diamond >= 5 || this.key >= 5 || this.reputations.any { it >= 5 } || this.resources.any { it >= 5 } || allyExp >= 5 || accountExp >= 5) {
            listOf(
                this,
                this,
                this,
                this,
                this
            )
        } else listOf(this)
    }
}
