package com.moyu.core.model.level

import com.moyu.core.model.ConfigData

data class ReputationLevel(
    override val id: Int,
    val name: String = "",
    val level: Int = 0,
    val exp: Int = 0,
    val expTotal: Int = 0,
    val reward: Int = 0,
): ConfigData {
    fun storeId(typeIndex: Int): Int {
        return id * 100 + typeIndex
    }

    fun awardId(typeIndex: Int): Int {
        return reward
    }
}