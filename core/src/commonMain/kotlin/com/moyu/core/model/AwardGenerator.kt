package com.moyu.core.model

import com.moyu.core.GameCore
import com.moyu.core.logic.skill.quality
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.property.Property
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextInteger
import kotlin.random.Random


fun ReputationLevel.toAward(typeIndex: Int): Award {
    return if (awardId(typeIndex) == 0) Award() else GameCore.instance.getPoolById(awardId(typeIndex))
        .toAward()
}

fun Sign.toAward(): Award {
    return GameCore.instance.getPoolById(this.reward).toAward()
}

fun Tcg.toAward(): Award {
    return GameCore.instance.getPoolById(reward).toAward()
}

fun Sell.toAward(): Award {
    val pool = GameCore.instance.getPoolById(itemId)
    return pool.toAward() + if (isAifadian()) {
        // 爱发电商品，需要默认加入一个电量
        Award(electric = price)
    } else Award()
}

fun Sell.toAwards(): List<Award> {
    val pool = GameCore.instance.getPoolById(itemId)
    return pool.toAwards()
}

fun List<Award>.toAward(): Award {
    return this.reduceOrNull { acc, award -> acc + award } ?: Award()
}

fun Pool.toAward(forceNum: Int? = null, forceTotalNum: Int? = null, random: Random = RANDOM): Award {
    return toAwards(forceNum, forceTotalNum, random).toAward()
}


fun Vip.toAward(): Award {
    return if (effectType == 1) {
        // num要从vip的effectNum拿数据
        GameCore.instance.getPoolById(effectId).copy(num = listOf(effectNum.toDouble())).toAward()
    } else {
        Award()
    }
}

fun BattlePass.toAward(): Award {
    return GameCore.instance.getPoolById(reward).toAward()
}

fun Pool.toAwards(
    forceNum: Int? = null,
    forceTotalNum: Int? = null,
    random: Random = RANDOM,
    onLoseAlly: (() -> List<Ally>)? = null,
    onLoseSkill: (() -> List<Skill>)? = null
): List<Award> {
    val result = List(type.size) { index ->
        val realNumDouble = forceNum?.toDouble() ?: num[index]
        val realNum = realNumDouble.toInt()
        when (type[index]) {
            1 -> {
                if (pool[index].isEnum()) {
                    Award(
                        allies = (onLoseAlly?.invoke() ?: GameCore.instance.getAllyPool())
                            .filter { it.star == 1 && !it.isHero() }
                            .filter { it.raceType == pool[index] || pool[index] == 0 }
                            .filter { (quality.size == 1 && quality.first() == 0) || quality[index] == 0 || it.quality == quality[index] }
                            .filter { it.dropLimit != 1 }
                            .shuffled(random).take(enumNum.first()).map {
                                it.copy(num = num[index].toInt())
                            }
                    )
                } else {
                    Award(
                        allies = listOf(
                            GameCore.instance.getAllyById(pool[index])
                                .copy(num = num[index].toInt())
                        )
                    )
                }
            }

            2 -> {
                Award()
            }

            3 -> {
                Award()
            }

            4 -> {
                Award()
            }

            5 -> {
                Award()
            }

            6 -> {
                Award()
            }

            7 -> {
                /**
                 * 枚举7：【属性】三个子枚举合并，第一个子枚举1-3友方敌人随机;第二个子枚举01-05近战远程飞行施法英雄，00为随机种族（不包括英雄），11-21城堡壁垒塔楼地狱墓园地下据点要塞元素港口工厂，10为随机阵营,99为全部兵种;第三个子枚举1-7攻击、防御、生命、暴击、暴伤、闪避、速度
                 */
                val teamType = if (pool[index] / 1000 == 1) {
                    1
                } else if (pool[index] / 1000 == 2) {
                    2
                } else if (pool[index] / 1000 == 4) {
                    3
                } else {
                    if (random.nextBoolean()) 1 else 2
                }

                // type 是类型，1-5，近战远程飞行施法英雄，0表示所有类型(不包括英雄)
                // race 是种族，1-11，城堡壁垒塔楼地狱墓园地下据点要塞元素港口工厂，0表示所有种族
                val (race, group) = if (pool[index] % 1000 / 10 == 99) {
                    Pair(0, 0)
                } else {
                    val raceGroupValue = pool[index] % 1000 / 10
                    if (raceGroupValue == 0) {
                        Pair(random.nextInteger(1, 4), 0)
                    } else if (raceGroupValue == 10) {
                        Pair(0, random.nextInteger(1, 11))
                    } else if (raceGroupValue <= 5) {
                        Pair(raceGroupValue, 0)
                    } else if (raceGroupValue in 11..21) { // 0 表示没有限制
                        Pair(0, raceGroupValue - 10)
                    } else {
                        Pair(0, 0)
                    }
                }
                Award(
                    battleProperty = listOf(
                        PropertyAward(
                            teamType = teamType,
                            race = race,
                            group = group,
                            property = Property.getPropertyByEnum(pool[index] % 10, num[index]),
                        )
                    )
                )
            }

            45 -> {
                /**
                 * 枚举7：【属性】三个子枚举合并，第一个子枚举1-3友方敌人随机;第二个子枚举01-05近战远程飞行施法英雄，00为随机种族（不包括英雄），11-21城堡壁垒塔楼地狱墓园地下据点要塞元素港口工厂，10为随机阵营,99为全部兵种;第三个子枚举1-7攻击、防御、生命、暴击、暴伤、闪避、速度
                 */
                val teamType = 1

                // type 是类型，1-5，近战远程飞行施法英雄，0表示所有类型(不包括英雄)
                // race 是种族，1-11，城堡壁垒塔楼地狱墓园地下据点要塞元素港口工厂，0表示所有种族
                val (race, group) = if (pool[index] % 1000 / 10 == 99) {
                    Pair(0, 0)
                } else {
                    val raceGroupValue = pool[index] % 1000 / 10
                    if (raceGroupValue == 0) {
                        Pair(random.nextInteger(1, 4), 0)
                    } else if (raceGroupValue == 10) {
                        Pair(0, random.nextInteger(1, 11))
                    } else if (raceGroupValue <= 5) {
                        Pair(raceGroupValue, 0)
                    } else if (raceGroupValue in 11..21) { // 0 表示没有限制
                        Pair(0, raceGroupValue - 10)
                    } else {
                        Pair(0, 0)
                    }
                }
                Award(
                    battleProperty = listOf(
                        PropertyAward(
                            teamType = teamType,
                            race = race,
                            group = group,
                            property = Property.getPropertyByEnum(pool[index] % 10, num[index]),
                        )
                    )
                )
            }

            8 -> {
                if (pool[index].isEnum()) {
                    Award(
                        skills = (onLoseSkill?.invoke() ?: GameCore.instance.getSkillPool())
                            .filter { it.isAdventure() }
                            .filter { (quality.size == 1 && quality.first() == 0) || quality[index] == 0 || it.quality() == quality[index] }
                            .filter { it.elementType == pool[index] || pool[index] == 0 }
                            .filter { GameCore.instance.canLearnSkill(it) }
                            .shuffled(random).take(num[index].toInt())
                    )
                } else {
                    val skill = GameCore.instance.getSkillById(pool[index])
                    if (GameCore.instance.canLearnSkill(skill)) {
                        Award(
                            skills = listOf(
                                skill.copy(num = num[index].toInt())
                            )
                        )
                    } else {
                        Award()
                    }
                }
            }

            9 -> {
                Award()
            }

            10 -> {
                Award(allHeal = realNum)
            }

            11 -> {
                Award(accountExp = realNum)
            }

            12 -> {
                Award()
            }

            14 -> {
                Award()
            }

            15 -> {
                Award()
            }

            21 -> {
                Award(resources = EMPTY_RESOURCES.toMutableList().apply {
                    set(pool[index] - 1, realNum)
                })
            }

            22 -> {
                Award(key = realNum)
            }

            23 -> {
                Award(couponAlly = realNum)
            }

            24 -> {
                Award(couponEquip = realNum)
            }

            25 -> {
                // 局外兵种卡
                if (pool[index].isEnum()) {
                    Award(
                        outAllies = GameCore.instance.getAllyPool().asSequence()
                            .filter { it.star == 1 && !it.isHero() }
                            .filter { pool[index] == 0 || it.raceType == pool[index] }
                            .filter { (quality.size == 1 && quality.first() == 0) || quality[index] == 0 || it.quality == quality[index] }
                            .filter { it.dropLimit != 1 }.toList()
                            .shuffled(random).take(num[index].toInt())
                    )
                } else {
                    Award(
                        outAllies = listOf(
                            GameCore.instance.getAllyById(pool[index])
                                .copy(num = num[index].toInt())
                        )
                    )
                }
            }

            26 -> {
                // 局外宝物，也就是装备
                if (pool[index].isEnum()) {
                    var result = Award()
                    while (result.outEquips.size < realNum) {
                        result += Award(
                            outEquips = GameCore.instance.getEquipPool()
                                .filter { it.star == 1 }
                                .filter { (quality.size == 1 && quality.first() == 0) || quality[index] == 0 || it.quality == quality[index] }
                                .filter { pool[index] == 0 || it.type == pool[index] }
                                .filter { it.dropLimit != 1 }
                                .shuffled(random).take(num[index].toInt() - result.outEquips.size)
                        )
                    }
                    result
                } else {
                    val ally = GameCore.instance.getEquipById(pool[index]).copy(num = num[index].toInt())
                    val allies = listOf(ally)
                    Award(outEquips = allies)
                }
            }

            27 -> {
                Award(electric = realNum)
            }

            28 -> {
                Award(warPass1 = realNum)
            }

            29 -> {
                Award(roadExp = realNum)
            }

            30 -> {
                Award(unlockList = listOf(pool[index]))
            }

            31 -> {
                Award(warPass2 = realNum)
            }

            32 -> {
                Award(warPass3 = realNum)
            }

            33 -> {
                Award(pvpDiamond = realNum)
            }

            34 -> {
                Award(pvpScore = realNum)
            }

            35 -> {
                Award(lotteryMoney = realNum)
            }

            36 -> {
                Award(holidayMoney = realNum)
            }

            37 -> {
                Award(warPass4 = realNum)
            }

            38 -> {
                Award(talentPoint = realNum)
            }

            39 -> {
                if (pool[index] == 0) {
                    val randomIndex = random.nextInt(0, EMPTY_REPUTATION.size)
                    Award(reputations = EMPTY_REPUTATION.toMutableList().apply {
                        set(randomIndex, realNum)
                    })
                } else {
                    Award(reputations = EMPTY_REPUTATION.toMutableList().apply {
                        set(pool[index] - 1, realNum)
                    })
                }
            }

            40 -> {
                Award(reputationMoney = realNum)
            }

            41 -> {
                Award(accountExp = realNum)
            }

            42 -> {
                Award(allyExp = realNum)
            }

            43 -> {
                Award(power = realNum)
            }

            44 -> {
                Award(diamond = realNum)
            }

            else -> {
                Award()
            }
        }
    }
    val realResult = result.filter { !it.isEmpty() }.let {
        // 如果总数太多，要随机，否则固定
        if (it.size <= (forceTotalNum ?: totalNum)) it else it.shuffled(random).take(forceTotalNum ?: totalNum)
    }
    return realResult
}

// todo pool表的第二个字段有双重含义，如果是100以内，是枚举，否则是固定id
fun Int.isEnum(): Boolean {
    return this < 100
}

fun Quest.toAward(): Award {
    return GameCore.instance.getPoolById(reward).toAward()
}