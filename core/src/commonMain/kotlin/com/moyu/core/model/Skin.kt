package com.moyu.core.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class Skin(
    @SerialName("x")
   override val id: Int = 0,
    @Transient
    val name: String = "",
    @Transient
    val type: Int = 0,
    @Transient
    val quality: Int = 0,
    @Transient
    val effectType: List<Int> = emptyList(),
    @Transient
    val effectNum: List<Double> = emptyList(),
    @Transient
    val pic: String = "",
    @Transient
    val tips: String = "",
    @Transient
    val new: Boolean = false,
): ConfigData {
    fun create(): Skin {
        return this
    }

    fun getSmallIcon(): String {
        return pic + "_headicon"
    }
}