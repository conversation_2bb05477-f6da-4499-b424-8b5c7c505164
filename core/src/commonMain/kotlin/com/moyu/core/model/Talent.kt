package com.moyu.core.model

fun Int.mainIdToTalentType(): Int {
    return when (this) {
        in 8300..8499 -> 2
        in 8000..8099 -> 3
        else  -> 1
    }
}

data class Talent(
    override val id: Int = 0,
    val mainId: Int = 0,
    val type: Int = 0,
    val name: String = "",
    val level: Int = 0,
    val levelLimit: Int = 0,
    val position: List<Int> = emptyList(),
    val talentSkill: Int = 0,
    val cost: Int = 0,
    val conditionType: Int = 0,
    val conditionNum: Int = 0,
    val icon: String = "",
    val costPool: Int = 0,
    val mainName: String = "",
    val power: Int = 0,
    val conditionHideType: Int = 0,
    val conditionHideNum: Int = 0,
    val cost2: Int = 0,// 金币开销
) : ConfigData {
    fun isType2(): Boolean {
        return type in 200..299
    }

    fun isType3(): Boolean {
        return type == 3
    }

    fun isType1(): <PERSON><PERSON>an {
        return type == 1
    }
}