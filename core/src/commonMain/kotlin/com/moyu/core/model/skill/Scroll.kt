package com.moyu.core.model.skill


fun Int.scrollIdToMainId(): Int {
    return this / 100
}
data class Scroll(
    val id: Int,
    val mainId: Int,
    val name: String,
    val star: Int,
    val starLimit: Int,
    val quality: Int,
    val starUpNum: Int,
    val starUpResourceNum: Int,
    val dropLimit: Int,
    val story: String = "",
    val belong: Int = 0,
    val position: List<Int> = emptyList(),
    val cost: Int = 0,
    val conditionType: Int = 0,
    val conditionNum: Int = 0,
    val skillTreeId: Int = 0,
    val mainName: String = "",
    val life: Int = 0,
)