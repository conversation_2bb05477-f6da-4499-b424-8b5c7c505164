package com.moyu.core.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class Gift(
    @SerialName("i")
    val id: Int = 0,
    @Transient
    val name: String = "",
    @Transient
    val icon: String = "",
    @Transient
    val content: String = "",
    @Transient
    val content2: String = "",
    @Transient
    val pic: String = "",
    @Transient
    val label_pic: String = "",
    @Transient
    val poolId: Int = 0,
    @Transient
    val price: Int = 0,
    @Transient
    val priceDollar: Double = 0.0,
    @Transient
    val unlockId: Int = 0,
    @Transient
    val limitTime: Int = 0,
    @Transient
    val triggerType: Int = 0,
    @Transient
    val limitBuy: Int = 0,
    @Transient
    val priority: Int = 0,
    @SerialName("d")
    val displayTime: Long = 0,
    @SerialName("s")
    val dialogShowed: Boolean = false,
) {
    fun isTriggerEveryDay(): <PERSON>olean {
        return triggerType == 2
    }
}