package com.moyu.core.model

import com.moyu.core.model.property.Property
import kotlinx.serialization.Serializable

@Serializable
data class Dungeon(
    override val id: Int = 0,
    val name: String = "",
    val limit: Int = 0,
    val condition: Int = 0,
    val reward: Int = 0,
    val eventAttribute1: Int = 0,
    val eventAttribute2: Int = 0,
    val eventAttribute3: Int = 0,
    val eventAttribute4: Double = 0.0,
    val eventAttribute5: Double = 0.0,
    val eventAttribute6: Double = 0.0,
    val eventAttribute7: Int = 0,
    val targetText: String = "",
    val eventType: List<Int> = emptyList(),
    val eventTypeImg: List<String> = emptyList(),
    val enemyType: List<Int> = emptyList(),
    val levelImg: String = "",
    val levelImg2: String = "",
    val levelImg3: String = "",
    val levelImg4: String = "",
    val levelImg5: String = "",
    val levelImg6: String = "",
    val dungeonType: Int = 0,
): ConfigData {
    fun isShip(): Boolean {
        return dungeonType == 2
    }

    fun toProperty(): Property {
        return Property(
            attack = eventAttribute1,
            defenses = eventAttribute2,
            hp = eventAttribute3,
            fatalRate = eventAttribute4,
            fatalDamage = eventAttribute5,
            dodgeRate = eventAttribute6,
            speed = eventAttribute7,
        )
    }
}