package com.moyu.core.model

import kotlinx.serialization.Serializable

@Serializable
data class Story(
    val id: Int,
    val name: String,
    val unlockId: Int,
    val desc: String,
    val pic: String,
    val showCard: List<Int> = emptyList(),
    val sellId: Int = 0,
    val order: Int = 0,
    val selected: Boolean = false,
) {
    fun switchSelection(): Story {
        return copy(selected = !selected)
    }
}
