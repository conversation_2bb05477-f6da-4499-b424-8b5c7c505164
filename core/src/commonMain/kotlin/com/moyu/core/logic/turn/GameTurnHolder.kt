package com.moyu.core.logic.turn

import com.moyu.core.logic.identifier.RoleIdentifier

/**
 * 回合管理接口，管理回合数据
 * 比如 当前回合数，当前谁的回合，当前是否有额外回合等等
 */
interface GameTurnHolder {
    fun getTurn(): Int
    fun nextTurn()
    fun setTurn(turn: Int)
    fun isOnesTurn(roleIdentifier: RoleIdentifier): Boolean
    fun setOnesTurn(roleIdentifier: RoleIdentifier)
    fun getAttacker(): RoleIdentifier
    fun isPassiveTurn(): Boolean
    fun setPassiveTurn(value: Boolean)
    fun isCheckingDeath(): Boolean
    fun setCheckingDeath(value: Boolean)
    fun whosTurn(): RoleIdentifier
}