package com.moyu.core.logic.role

import com.moyu.core.model.Ally
import com.moyu.core.model.Buff
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.skill.Skill

interface BattlePower {
    fun isFatal(skill: Skill): Boolean
    fun isImmuneAvoid(skill: Skill): Boolean
    fun skillTimes(skill: Skill): Int
    fun isDodgeImmune(skill: Skill): Boolean
    fun skillDamageInc(skill: Skill, damageType: DamageType): Double
    fun isShieldIgnore(skill: Skill): Boolean
    fun getPierceByType(skill: Skill, damageType: DamageType): Double
    fun suckBloodRate(skill: Skill): Double
    fun getChance(skill: Skill, triggerType: Int, rawRate: Double): Double
    fun getRealCoolDown(skill: Skill): Int
    fun getHealRate(): Double
    fun isDodge(): Boolean
    fun getBuffContinueIncrease(buff: Buff): Int
    fun cantBeDispelBuff(skill: Skill): Boolean
    fun cantBeDispelDebuff(skill: Skill): Boolean
    fun isTauntImmune(skill: Skill): Boolean
    fun isDivideAttack(skill: Skill): Boolean
    fun raceDamageInc(skill: Skill, race: Ally): Double
}