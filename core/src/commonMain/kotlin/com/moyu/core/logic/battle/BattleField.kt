package com.moyu.core.logic.battle

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.logic.gameover.DefaultGameOver
import com.moyu.core.logic.gameover.GameJudge
import com.moyu.core.logic.heal.NormalHealConsumer
import com.moyu.core.logic.heal.NormalHealProcess
import com.moyu.core.logic.info.addInfo
import com.moyu.core.logic.recorder.DefaultBattleDataRecord
import com.moyu.core.logic.recorder.GameDataRecorder
import com.moyu.core.logic.role.GameRoleHolder
import com.moyu.core.logic.skill.Death
import com.moyu.core.logic.skill.Healing
import com.moyu.core.logic.skill.SuckBlock
import com.moyu.core.logic.turn.DefaultTurnProcessor
import com.moyu.core.logic.turn.GameTurnHolder
import com.moyu.core.model.Timing
import com.moyu.core.model.environment.DefaultEnvironment
import com.moyu.core.model.environment.Environment
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.heal.HealStatus
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.music.SoundEffect
import com.moyu.core.model.environment.EnvironmentHolder
import core.generated.resources.Res
import core.generated.resources.defeated_the_enemy
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.getString
import org.jetbrains.compose.resources.stringResource

class BattleField(
    private val roleHolder: GameRoleHolder,
    private val turnHolder: GameTurnHolder = DefaultTurnProcessor(),
    private val battleRecord: GameDataRecorder = DefaultBattleDataRecord(),
    private val gameOver: GameJudge = DefaultGameOver(),
    private val environmentHolder: EnvironmentHolder = DefaultEnvironment(),
) : EnvironmentHolder by environmentHolder,
    GameTurnHolder by turnHolder,
    GameRoleHolder by roleHolder,
    GameDataRecorder by battleRecord,
    GameJudge by gameOver {

    fun attacker(): Role? { // 进攻方可能突然无了
        return getRole(getAttacker())
    }

    fun gameFinished(): Boolean {
        return (getWinFlag()
                || getPlayers().all { it.isDeath() }
                || getEnemies().all { it.isDeath() }
                || GameCore.instance.callback.extraIsGameFailed()
                || GameCore.instance.callback.extraIsGameWin())
    }

    suspend fun effectRoleBuffs(role: Role) {
        role.effectAll(this, role, Timing.TurnBegin)
    }

    suspend fun clearAllAndUpdate() {
        clearBuff()
        clearSkillTurn()
        clearAnimationState()
        clearHeal()
        GameCore.instance.onBattleUIUpdate(this)
        delay(GameCore.instance.callback.gameSpeed().shortDuration())
    }

    private fun clearBuff() {
        getAllRoles().forEach {
            it.clear(this, it)
        }
    }

    fun clearSkillGrave(timing: Timing) {
        getAllRoles().forEach {
            it.clearGrave(timing)
        }
    }

    fun clearSkillTurn() {
        getAllRoles().forEach {
            it.clearCoolDown()
        }
    }

    private fun clearAnimationState() {
        getAllRoles().forEach {
            it.clearAnimationState()
        }
    }

    fun clearHeal() {
        getAllRoles().forEach {
            saveHealValue(it, 0)
        }
    }

    suspend fun doUpdateSequence() {
        if (terminated()) return
        while (GameCore.instance.callback.gameSpeed().stop()) {
            delay(100)
        }
        // 说明下更新动画的逻辑：
        // 其实就是设置动画状态，delay一个动画的时长，这样UI就会在这个时间段内，根据状态执行动画
        // 然后一个动画周期后，清理状态回到静止，再给一个时长执行返回动画即可
        GameCore.instance.onBattleUIUpdate(this)
        delay(GameCore.instance.callback.gameSpeed().animDuration())
        clearAnimationState()
        GameCore.instance.onBattleUIUpdate(this)
        delay(GameCore.instance.callback.gameSpeed().shortDuration())
    }

    private suspend fun deathProcess(role: Role) {
        getAllRoles().forEach {
            it.triggerSkillByType(
                field = this,
                triggerType = listOf(1, 2),
                skillOwner = it,
                triggerSkill = Death.copy(ownerIdentifier = role),
            )
        }
    }

    suspend fun checkDeathProcess() {
        // todo 死亡检测流程中，可能导致其他角色死亡，不做交叉check，所以必须等到一次检测完成，再进行下一次
        if (isCheckingDeath()) return
        setCheckingDeath(true)
        getAllRoles().filter { it.isDeath() && !it.isDeathChecked() }.forEach {
            it.setDeathChecked(true)
            deathProcess(it)
        }
        val tobeRemove = getAllRoles().filter { it.isDeath() }.onEach {
            it.setDeathState()
            it.setOver(true)
            GameCore.instance.onBattleEffect(SoundEffect.RealDie)
        }
        if (tobeRemove.isNotEmpty()) {
            GameCore.instance.onBattleEffect(SoundEffect.OthersDie)
            doUpdateSequence()
        }
        setCheckingDeath(false)
        getAllRoles().onEach {
            it.setDeathChecked(false)
        }
    }

    suspend fun healTarget(
        doer: Role,
        target: Role,
        heal: Int,
        skill: Skill,
        healStatus: HealStatus = HealStatus(),
        index: Int
    ): HealResult {
        val healResult = NormalHealProcess(
            attacker = doer,
            victim = target,
            initHeal = heal,
            skill = skill,
            healStatus = healStatus
        ).process()
        return NormalHealConsumer().consumeHeal(
            this,
            healResult,
            doer,
            target,
            index
        )
    }

    suspend fun doSuckBlood(
        suckBlood: Int,
        doer: Role,
        target: Role,
        field: BattleField,
        index: Int
    ) {
        val suckBloodSkill = SuckBlock.copy(
            ownerIdentifier = doer
        )
        val healResult = healTarget(
            doer,
            target,
            suckBlood,
            suckBloodSkill,
            healStatus = HealStatus(isSuckBlood = true),
            index = index
        )
        field.doUpdateSequence()
        skillChainHandler.invoke(
            Healing.copy(
                ownerIdentifier = healResult.victim,
                healResult = healResult
            ), field
        )
    }

    fun checkBuffCondition() {
        attacker()?.let {
            it.checkBuffCondition(this, it)
        }
    }

    fun updateAllBuffTurn() {
        if (turnHolder.getTurn() == 1) return // 第一回合不需要,否则被动技能buff就1回合没了
        getAllRoles().forEach {
            it.checkBuffContinue(this, it)
            if (isOnesTurn(it)) {
                it.markSkillNewTurn(it)
            }
        }
    }

    fun dealAward() {
        // 敌人死亡，更新战利品x
        GameCore.instance.addInfo(
            AppWrapper.getStringKmp(Res.string.defeated_the_enemy),
            BattleInfoType.Battle
        )
    }

    override fun changeEnvironment(environment: Environment) {
        getAllRoles().forEach {
            it.checkBuffCondition(this, it)
        }
        this.environmentHolder.changeEnvironment(environment)
    }
}