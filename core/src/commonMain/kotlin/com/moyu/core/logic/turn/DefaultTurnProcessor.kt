package com.moyu.core.logic.turn

import com.moyu.core.logic.identifier.NoneRoleIdentifier
import com.moyu.core.logic.identifier.RoleIdentifier

/**
 * 回合管理接口实现
 */
class DefaultTurnProcessor: GameTurnHolder {
    private var turn: Int = 1
    private var whosTurn: RoleIdentifier = NoneRoleIdentifier
    private var isPassiveTurn: Boolean = true
    private var checkDeath: <PERSON>olean = false

    override fun getTurn(): Int {
        return turn
    }

    override fun nextTurn() {
        setTurn(getTurn() + 1)
    }

    override fun isPassiveTurn(): Boolean {
        return isPassiveTurn
    }

    override fun setPassiveTurn(value: <PERSON><PERSON>an) {
        isPassiveTurn = value
    }

    override fun isCheckingDeath(): <PERSON><PERSON><PERSON> {
        return checkDeath
    }

    override fun setCheckingDeath(value: <PERSON><PERSON>an) {
        checkDeath = value
    }

    override fun setTurn(turn: Int) {
        this.turn = turn
    }

    override fun isOnesTurn(roleIdentifier: RoleIdentifier): <PERSON><PERSON><PERSON> {
        return roleIdentifier.playerId() == whosTurn.playerId()
    }

    override fun setOnesTurn(roleIdentifier: RoleIdentifier) {
        whosTurn = roleIdentifier
    }

    override fun getAttacker(): RoleIdentifier {
        return whosTurn
    }

    override fun whosTurn(): RoleIdentifier {
        return whosTurn
    }
}