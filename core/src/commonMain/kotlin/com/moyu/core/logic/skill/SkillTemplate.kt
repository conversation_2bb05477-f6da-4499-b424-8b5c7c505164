package com.moyu.core.logic.skill

import com.moyu.core.AppWrapper
import core.generated.resources.Res
import core.generated.resources.*
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.directDamage

fun Skill.isDeath() = id == -2
fun Skill.isDot() = id == -3
fun Skill.isHot() = id == -4
fun Skill.isSuckBlock() = id == -5
fun Skill.isHealing() = id == -130
fun Skill.isGettingBuff() = id == -100
fun Skill.isGettingDebuff() = id == -101
fun Skill.isDispelEnemy() = id == -110
fun Skill.isDispelMyself() = id == -111
fun Skill.isSelfHarm() = id == -120
fun Skill.isReAlive() = id == -140
fun Skill.isCastSkill() = id == -160
fun Skill.isWounded() = id == -170
fun Skill.isCastDamage() = id == -180
fun Skill.isTurnBegin() = id == -200
fun Skill.isTurnEnd() = id == -210
fun Skill.isSummonMinion() = id == -220

val SpecialSkillTemplate = Skill(
    0,
    1,
    0,
    0,
    0,
    "",
    "",
    0,
    0.0,
    listOf(0),
    listOf(0),
    1,
    listOf(directDamage),
    listOf(2),
    listOf(2),
    listOf(10.0),
    listOf("10"),
    0,
    0,
    0,
    "",
    0,
    0,
    0
)
val Death = SpecialSkillTemplate.copy(id = -2, name = AppWrapper.getStringKmp(Res.string.death), mainId = -2)
val Dot = SpecialSkillTemplate.copy(id = -3, name = AppWrapper.getStringKmp(Res.string.continuous_damage), mainId = -3)
val Hot = SpecialSkillTemplate.copy(id = -4, name = AppWrapper.getStringKmp(Res.string.continued_treatment), mainId = -4)
val SuckBlock = SpecialSkillTemplate.copy(id = -5, name = AppWrapper.getStringKmp(Res.string.drink), mainId = -5)
val GetBuff = SpecialSkillTemplate.copy(id = -100, name = AppWrapper.getStringKmp(Res.string.i_achieved_positive_results), mainId = -100)
val GetDebuff = SpecialSkillTemplate.copy(id = -101, name = AppWrapper.getStringKmp(Res.string.i_achieved_negative_effects), mainId = -101)
val DispelEnemy = SpecialSkillTemplate.copy(id = -110, name = AppWrapper.getStringKmp(Res.string.dispel_the_opponent_buff), mainId = -110)
val DispelMyself = SpecialSkillTemplate.copy(id = -111, name = AppWrapper.getStringKmp(Res.string.dispel_your_own_buff), mainId = -111)
val SelfHarm = SpecialSkillTemplate.copy(id = -120, name = AppWrapper.getStringKmp(Res.string.damage_self), mainId = -120)
val Healing = SpecialSkillTemplate.copy(id = -130, name = AppWrapper.getStringKmp(Res.string.cure_yourself), mainId = -130)
val ReAlive = SpecialSkillTemplate.copy(id = -140, name = AppWrapper.getStringKmp(Res.string.free_sb_from_death), mainId = -140)
val CastSkill = SpecialSkillTemplate.copy(id = -160, name = AppWrapper.getStringKmp(Res.string.release_a_skill), mainId = -160)
val Wounded = SpecialSkillTemplate.copy(id = -170, name = AppWrapper.getStringKmp(Res.string.got_damage), mainId = -170)
val CastDamage = SpecialSkillTemplate.copy(id = -180, name = AppWrapper.getStringKmp(Res.string.got_hurt), mainId = -180)
val Dodge = SpecialSkillTemplate.copy(id = -190, name = AppWrapper.getStringKmp(Res.string.successful_block), mainId = -190)
val TurnBegin = SpecialSkillTemplate.copy(id = -200, name = AppWrapper.getStringKmp(Res.string.start_of_the_round), mainId = -200)
val TurnEnd = SpecialSkillTemplate.copy(id = -210, name = AppWrapper.getStringKmp(Res.string.end_of_the_round), mainId = -210)
val Summon = SpecialSkillTemplate.copy(id = -220, name = AppWrapper.getStringKmp(Res.string.call_upon_a_servant), mainId = -220)