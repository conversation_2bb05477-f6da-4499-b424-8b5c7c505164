package com.moyu.core.logic.skill

import com.moyu.core.AppWrapper
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.realValueToDotWithNoDigits
import com.moyu.core.util.realValueToDotWithOneDigits
import core.generated.resources.Res
import core.generated.resources.level
import core.generated.resources.level1

fun Skill.getNameInfo() = "【$name】"

fun Skill.getDisplayName() = if (level > 0) name + " " + level + AppWrapper.getStringKmp(Res.string.level) else name

fun Skill.getLevelName() = if (level > 0) level.toString() + AppWrapper.getStringKmp(Res.string.level1) else ""

fun Skill.getRealDesc(): String {
    return desc.replace("\\n", "\n").replace("%r", rate.toInt().toString())
        .replace("%t", buffContinue.toString()).let {
            var result = it
            (15 downTo 0).forEach { index ->
                result = replaceEffectValue(result, index)
            }
            result
        }
}

private fun Skill.replaceEffectValue(desc: String, index: Int): String {
    return if (desc.contains("%d${index + 1}") && effectNum.getOrNull(index) != null) {
        desc.replace("%d${index + 1}", effectNum[index].realValueToDotWithNoDigits())
    } else if (desc.contains("%p${index + 1}") && effectNum.getOrNull(index) != null) {
        desc.replace("%p${index + 1}", effectNum[index].toInt().toString())
    } else if (desc.contains("%f${index + 1}") && effectNum.getOrNull(index) != null) {
        desc.replace("%f${index + 1}", effectNum[index].realValueToDotWithOneDigits())
    } else desc
}