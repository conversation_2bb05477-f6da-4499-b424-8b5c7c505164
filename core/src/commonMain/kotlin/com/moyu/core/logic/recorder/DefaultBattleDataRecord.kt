package com.moyu.core.logic.recorder

import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.skill.Skill

class DefaultBattleDataRecord : GameDataRecorder {
    private val healValueMap = hashMapOf<String, Int>()
    private val castSkillCountMap = hashMapOf<String, MutableList<Skill>>()

    private val damageResults = hashMapOf<String, MutableList<DamageResult>>()
    private val woundedResults = hashMapOf<String, MutableList<DamageResult>>()
    private val healResults = hashMapOf<String, MutableList<HealResult>>()

    override fun saveDamageResult(roleIdentifier: RoleIdentifier, damageResult: DamageResult) {
        val value = damageResults[roleIdentifier.playerId()]
        if (value == null) {
            damageResults[roleIdentifier.playerId()] = mutableListOf(damageResult)
        } else {
            damageResults[roleIdentifier.playerId()] = value.apply { add(damageResult) }
        }
    }

    override fun getDamageResults(roleIdentifier: RoleIdentifier): List<DamageResult> {
        return damageResults[roleIdentifier.playerId()]?: emptyList()
    }

    override fun saveWoundedResult(roleIdentifier: RoleIdentifier, damageResult: DamageResult) {
        val value = woundedResults[roleIdentifier.playerId()]
        if (value == null) {
            woundedResults[roleIdentifier.playerId()] = mutableListOf(damageResult)
        } else {
            woundedResults[roleIdentifier.playerId()] = value.apply { add(damageResult) }
        }
    }

    override fun getWoundedResults(roleIdentifier: RoleIdentifier): List<DamageResult> {
        return woundedResults[roleIdentifier.playerId()]?: emptyList()
    }

    override fun saveHealResult(roleIdentifier: RoleIdentifier, healResult: HealResult) {
        if (healResults[roleIdentifier.playerId()] == null) {
            healResults[roleIdentifier.playerId()] = mutableListOf(healResult)
        } else healResults[roleIdentifier.playerId()]!!.add(healResult)
    }

    override fun getHealResults(roleIdentifier: RoleIdentifier): List<HealResult> {
        return healResults[roleIdentifier.playerId()]?: emptyList()
    }

    override fun saveCastSkills(roleIdentifier: RoleIdentifier, skill: Skill) {
        val value = castSkillCountMap[roleIdentifier.playerId()]
        if (value == null) {
            castSkillCountMap[roleIdentifier.playerId()] = mutableListOf(skill)
        } else {
            castSkillCountMap[roleIdentifier.playerId()] = value.apply { add(skill) }
        }
    }

    override fun getCastSkills(roleIdentifier: RoleIdentifier): List<Skill> {
        return castSkillCountMap[roleIdentifier.playerId()] ?: emptyList()
    }

    override fun saveHealValue(roleIdentifier: RoleIdentifier, healValue: Int) {
        healValueMap[roleIdentifier.playerId()] = healValue
    }

    override fun getHealValue(roleIdentifier: RoleIdentifier): Int {
        return healValueMap[roleIdentifier.playerId()] ?: 0
    }

    override fun getDamageResultsBySide(roleIdentifier: RoleIdentifier): List<DamageResult> {
        val results = mutableListOf<DamageResult>()
        damageResults.entries.filter { roleIdentifier.isPlayerSide() == RoleIdentifier.isPlayerSide(it.key) }.forEach {
            results.addAll(it.value)
        }
        return results
    }

    override fun getHealResultsBySide(roleIdentifier: RoleIdentifier): List<HealResult> {
        val results = mutableListOf<HealResult>()
        healResults.entries.filter { roleIdentifier.isPlayerSide() == RoleIdentifier.isPlayerSide(it.key) }.forEach {
            results.addAll(it.value)
        }
        return results
    }

    override fun getCastSkillsBySide(roleIdentifier: RoleIdentifier): List<Skill> {
        val results = mutableListOf<Skill>()
        castSkillCountMap.entries.filter { roleIdentifier.isPlayerSide() == RoleIdentifier.isPlayerSide(it.key) }.forEach {
            results.addAll(it.value)
        }
        return results
    }

}