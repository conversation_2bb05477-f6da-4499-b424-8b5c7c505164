package com.moyu.core.logic.role

import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.model.role.Role

interface GameRoleHolder {
    fun getRole(roleIdentifier: RoleIdentifier): Role?
    fun getRoleMap(): Map<Int, Role?>
    fun getAllRoles(): List<Role>
    fun randomRole(): Role
    fun getEnemies():List<Role>
    fun getPlayers():List<Role>
    fun getAllPlayers():List<Role>
    fun getAllEnemies():List<Role>
    fun getPlayersRow1():List<Role?>
    fun getPlayersRow2():List<Role?>
    fun getEnemiesRow1():List<Role?>
    fun getEnemiesRow2():List<Role?>
    fun getMyTeamRoles(attacker: RoleIdentifier): List<Role>
    fun getPeerTeamRoles(attacker: RoleIdentifier): List<Role>
    fun getMyTeamRolesNotMe(attacker: RoleIdentifier): List<Role>
    fun getMyTeamHero(attacker: RoleIdentifier): Role?
    fun getMinion(role: Role): Role?
    fun addMinion(role: Role)

    fun getDirectPeer(role: Role): Role?
    fun getMyMinion(role: Role): Role?
    fun getMyMaster(role: Role): Role?

    fun getMyRowRoles(role: Role): List<Role>
    fun getMyColumnRoles(role: Role): List<Role>
    fun getPeerColumnRoles(role: Role): List<Role>
    fun getRow1ByRole(role: Role): List<Role>
    fun getRow2ByRole(role: Role): List<Role>
    fun getPeerRow1ByRole(role: Role): List<Role>
    fun getPeerRow2ByRole(role: Role): List<Role>
    fun getPeerColumnBackRole(role: Role): List<Role>
    fun getMyColumnBackRole(role: Role): List<Role>
}