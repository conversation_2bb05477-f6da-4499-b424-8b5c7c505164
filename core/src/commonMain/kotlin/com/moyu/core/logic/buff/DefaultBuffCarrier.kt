package com.moyu.core.logic.buff

import com.moyu.core.GameCore
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.damage.processor.buffEffectTypeToDamageType
import com.moyu.core.logic.info.addControlImmuneInfo
import com.moyu.core.logic.skill.DefaultSkillTrigger
import com.moyu.core.logic.skill.Dot
import com.moyu.core.logic.skill.Hot
import com.moyu.core.model.Buff
import com.moyu.core.model.ETERNAL_BUFF_TURN
import com.moyu.core.model.Timing
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.util.CopyOnWriteArrayList
import kotlin.math.roundToInt

val changeDamageTypeList = (5501..5510).toList()

class DefaultBuffCarrier : BuffCarrier {
    private val buffList: CopyOnWriteArrayList<Buff> = CopyOnWriteArrayList<Buff>()

    private fun controlImmune(buff: Buff): Bo<PERSON><PERSON> {
        return (buff.isControl() && isControlImmune())
                || (buff.isSilent() && isFrozenImmune())
                || (buff.isPalsy() && isPalsyImmune())
                || (buff.isHealForbidden() && isHealForbiddenImmune())
                || (buff.isFrenzy() && isFrenzyImmune())
                || (buff.isDisarm() && isDisarmImmune())
    }

    override suspend fun addBuff(battleField: BattleField, role: Role, newBuff: Buff) {
        if (controlImmune(newBuff)) {
            GameCore.instance.addControlImmuneInfo(newBuff)
        } else {
            var tobeEffect: Buff = newBuff
            buffList.find { oldBuff ->
                isSameBuff(oldBuff, newBuff)
            }?.let { oldBuff ->
                // 已有相同的，更新buff堆叠层数，持续回合数重新计算
                val layeredBuff = layeredBuff(oldBuff, newBuff)
                tobeEffect = layeredBuff
                buffList[buffList.indexOf(oldBuff)] = layeredBuff
                role.unEffect(
                    battleField,
                    role,
                    oldBuff,
                )
            } ?: buffList.add(newBuff) // 不存在相同的，直接添加新buff
            // 新buff生效
            role.effect(
                battleField, role, Timing.Immediate, tobeEffect
            )
        }
    }

    override fun removeBuff(battleField: BattleField, buff: Buff) {
        buffList.removeAll { it == buff }
    }

    private fun layeredBuff(oldBuff: Buff, newBuff: Buff): Buff {
        return when {
            newBuff.isControl() -> {
                // 控制buff不能叠加，仅更新持续回合数或者最大回合数
                if (oldBuff.isEternal()) {
                    oldBuff
                } else {
                    newBuff.copy(
                        continueCurrentTurn = controlNewCurrentTurn(oldBuff, newBuff),
                        continueMaxTurn = controlNewLayer(oldBuff, newBuff),
                    )
                }
            }
            else -> {
                val oldLayer = oldBuff.buffCurrentLayer
                val newLayer = minOf(oldBuff.buffCurrentLayer + 1, newBuff.buffMaxLayer)

                newBuff.copy(
                    buffCurrentLayer = newLayer,
                    // 这里是解决多层buff，value不同，导致叠加后数值有偏差的问题
                    buffValue = if (newLayer > oldLayer) {
                        // 说明叠层了，数值直接加上去
                        oldBuff.buffValue + newBuff.buffValue
                    } else {
                        // 叠层没有改变 这里如果只取旧的，会导致buff数值不更新，只取新的，会导致叠出上去后，超过后会取1层
                        if (newBuff.buffMaxLayer == 1) {
                            newBuff.buffValue
                        } else {
                            oldBuff.buffValue
                        }
                    }
                )
            }
        }
    }

    private fun controlNewCurrentTurn(oldBuff: Buff, newBuff: Buff): Int {
        // 看起来的意思是：如果叠加之前的控制，最大回合数更大，则用之前的当前回合数，否则当前回合数清零
        return if (oldBuff.continueMaxTurn >= newBuff.continueMaxTurn) {
            return oldBuff.continueCurrentTurn
        } else {
            0
        }
    }

    private fun controlNewLayer(oldBuff: Buff, newBuff: Buff): Int {
        if (oldBuff.isEternal() || newBuff.isEternal()) {
            return 0
        }
        return maxOf(oldBuff.continueMaxTurn, newBuff.continueMaxTurn)
    }

    private fun isSameBuff(oldBuff: Buff, newBuff: Buff): Boolean {
        // 如果是控制效果，不关心触发的技能类型
        return if (newBuff.isControl() || newBuff.isStamp()) oldBuff.id == newBuff.id
        else oldBuff.id == newBuff.id && oldBuff.skill?.id == newBuff.skill?.id
    }

    override fun unEffect(
        field: BattleField, buffOwner: Role, buff: Buff
    ) {
        when (buff.id) {
            in 2001..5610 -> {
                // todo 如果血量相关属性枚举边了，这里要改变
                if (buff.id == 2003 || buff.id == 2103) {
                    // 血量改变是改变上限
                    buffOwner.recoverDefaultHp(buff.buffStoreProperty.hp)
                } else {
                    buffOwner.recoverCurrentProperty(buff.buffStoreProperty)
                }
            }
            else -> { }
        }
    }

    override suspend fun effectAll(field: BattleField, buffOwner: Role, timing: Timing) {
        buffList.forEach { buff ->
            effect(field, buffOwner, timing, buff)
        }
    }

    override suspend fun effect(
        field: BattleField, buffOwner: Role, timing: Timing, buff: Buff
    ) {
        if (timing == Timing.TurnBegin) {
            when (buff.id) {
                in DOT -> {
                    // 持续伤害，回合开始时候结算
                    val dot = Dot.copy(
                        ownerIdentifier = buff.skill!!.ownerIdentifier,
                        name = buff.skill.name,
                        mainId = buff.skill.mainId,
                        buffInfo = buff
                    )
                    field.getRole(buff.skill.ownerIdentifier)?.let {
                        dot.doDamage(
                            it,
                            listOf(buffOwner),
                            field,
                            buff.buffValue.roundToInt(),
                            buff.id.buffEffectTypeToDamageType(),
                            index = buff.buffIndex
                        )
                    }

                }
                HOT -> {
                    // 持续治疗，回合开始时候结算
                    val hot = Hot.copy(
                        ownerIdentifier = buff.skill!!.ownerIdentifier,
                        name = buff.skill.name,
                        buffInfo = buff
                    )
                    hot.doHeal(
                        buffOwner,
                        listOf(buffOwner),
                        field,
                        buff.buffValue.roundToInt(),
                        buff.buffIndex
                    )
                }
                else -> {
                }
            }
        } else {
            when (buff.id) {
                in 2001..5610 -> {
                    buff.buffStoreProperty = Property.getDiffPropertyByBuffId(
                        buff.id,
                        buff.buffValue,
                    )
                    // todo 如果血量相关属性枚举边了，这里要改变
                    if (buff.id == 2003 || buff.id == 2103) {
                        // 血量改变是改变上限
                        buffOwner.changeDefaultHp(buff.buffStoreProperty.hp)
                    } else {
                        buffOwner.changeCurrentProperty(buff.buffStoreProperty)
                    }
                }
                else -> {
                }
            }
        }
    }

    override fun checkBuffCondition(
        result: BattleField,
        buffOwner: Role,
    ) {
        buffList.filterNot {
            if (it.continueMaxTurn == 0) it.skill?.let { skill -> // 这里筛选的是需要移除的buff，所以把trigger返回false的
                if (skill.triggerType == 2) { // 仅回合数为0的需要check trigger条件是否变化
                    result.getRole(skill.ownerIdentifier)?.let { role ->
                        DefaultSkillTrigger.trigger(
                            skill,
                            field = result, role, skill
                        ) // 当trigger返回true时候，返回true，筛掉
                    }
                } else true // skill类型不是2、4，不需要移除，返回true，筛掉
            } ?: true // 没有skill的buff，不需要移除，返回true，筛掉
            else true
        }.map {
            buffOwner.removeBuff(result, it)
            unEffect(result, buffOwner, it)
        }
    }

    override fun checkBuffContinue(battleField: BattleField, buffOwner: Role) {
        buffList.toList().filter {
            // 这里需要过滤出来需要移除的buff
            // 如果continueMaxTurn == 0，不移除，continueCurrentTurn==99，也不移除
            it.continueMaxTurn != 0 && it.continueCurrentTurn != ETERNAL_BUFF_TURN && it.reachedMaxTurn(
                battleField, buffOwner.getBuffContinueIncrease(it)
            )
        }.map {
            removeBuff(battleField, it)
            unEffect(battleField, buffOwner, it)
        }
        // 如果当前是buff拥有者的回合开始，则+1
        // todo 特殊情况，如果释放buff的人已经死亡，则修改为自己回合+1
        buffOwner.turnPlusOne { addOnWhosTurn ->
            val isBuffCasterStillAlive =
                battleField.getAllRoles().any { it.playerId() == addOnWhosTurn }
            if (isBuffCasterStillAlive) {
                addOnWhosTurn == battleField.whosTurn().playerId()
            } else {
                battleField.whosTurn().isPlayer()
            }
        }
    }

    override fun setBuffList(newList: List<Buff>) {
        buffList.clear()
        buffList.addAll(newList)
    }

    override fun getBuffList(): List<Buff> {
        return buffList
    }

    override fun turnPlusOne(checkIfAddOne: (String) -> Boolean) {
        val newBuffList = buffList.map {
            it.copy(
                continueCurrentTurn = if (checkIfAddOne(it.addOnWhosTurn.playerId())) it.continueCurrentTurn + 1
                else it.continueCurrentTurn
            )
        }
        setBuffList(newBuffList)
    }

    override fun shield(damageType: DamageType): Int {
        val targetBuffId = getShieldBuffIdByDamageType(damageType)
        val shield = getSumValueById({ it.id == targetBuffId })
        val shieldIncrease =
            getSumValuePercentById({ it.id == SHIELD_INC }) - getSumValuePercentById({ it.id == SHIELD_DEC })
        return ((1 + shieldIncrease) * shield).roundToInt()
    }

    override fun allShield(): Int {
        val shield = getSumValueById({ it.id == ALL_SHIELD })
        val shieldIncrease =
            getSumValuePercentById({ it.id == SHIELD_INC }) - getSumValuePercentById({ it.id == SHIELD_DEC })
        return ((1 + shieldIncrease) * shield).roundToInt()
    }

    override fun allTypeShield(): Int {
        return allShield() + DamageType.entries.toTypedArray().sumOf { shield(it) }
    }

    fun getSumValueById(condition: (Buff) -> Boolean, default: Int = 0): Int {
        return buffList.filter {
            condition(it)
        }.sumOf { it.buffValue }.roundToInt().takeIf {
            it > 0
        } ?: default
    }

    override fun isPalsy(): Boolean {
        return buffList.find {
            it.id == PALSY // 眩晕
        }?.let {
            !isPalsyImmune()
        } ?: false
    }

    override fun isHealForbidden(): Boolean {
        return buffList.find {
            it.id == HEAL_FORBID // 禁疗
        }?.let {
            !isHealForbiddenImmune()
        } ?: false
    }

    override fun isBanSkill(elementType: Int): Boolean {
        return buffList.find {
            it.id - BAN_SKILL_BASE == elementType
        }?.let {
            !isControlImmune() && !isBanSkillImmune(elementType)
        }?: false
    }

    override fun isBanSkillImmune(elementType: Int): Boolean {
        return buffList.find {
            it.id - IMMUNE_SKILL_BASE == elementType
        } != null
    }

    override fun isFrenzy(): Boolean {
        return buffList.find {
            it.id == FRENZY
        }?.let {
            !isFrenzyImmune()
        } ?: false
    }

    override fun isTaunt(): Boolean {
        return buffList.find {
            it.id == TAUNT
        } != null
    }

    override fun isPoisonHeal(): Boolean {
        return buffList.find {
            it.id == POISON_HEAL
        } != null
    }

    override fun isAvoidDeathImmune(): Boolean {
        return buffList.find {
            it.id == AVOID_DEATH_IMMUNE
        } != null
    }

    override fun isSuckBloodImmune(): Boolean {
        return buffList.find {
            it.id == SUCK_BLOOD_IMMUNE
        } != null
    }

    override fun isDivideAttack(): Boolean {
        return buffList.find {
            it.id == DIVIDE_ATTACK
        } != null
    }

    override fun isFrozen(): Boolean {
        return buffList.find {
            it.id == FROZEN
        }?.let {
            !isFrozenImmune()
        } ?: false
    }

    override fun isPalsyImmune(): Boolean {
        return buffList.find {
            it.id == PALSY_IMMUNE
        }?.let { true } ?: isControlImmune()
    }

    override fun isHealForbiddenImmune(): Boolean {
        return buffList.find {
            it.id == HEAL_FORBID_IMMUNE
        }?.let { true } ?: isControlImmune()
    }

    override fun isFrenzyImmune(): Boolean {
        return buffList.find {
            it.id == FRENZY_IMMUNE
        }?.let { true } ?: isControlImmune()
    }

    override fun isDisarm(): Boolean {
        return buffList.find {
            it.id == DISARM
        }?.let {
            !isDisarmImmune()
        } ?: false
    }

    override fun isDisarmImmune(): Boolean {
        return buffList.find {
            it.id == DISARM_IMMUNE
        }?.let { true } ?: isControlImmune()
    }

    override fun isTauntImmune(): Boolean {
        return buffList.find {
            it.id == TAUNT_IMMUNE
        } != null
    }

    override fun isFrozenImmune(): Boolean {
        return buffList.find {
            it.id == FROZEN_IMMUNE
        }?.let { true } ?: isControlImmune()
    }

    override fun isDodgeImmune(): Boolean {
        return buffList.find {
            it.id == DODGE_IMMUNE
        } != null
    }

    override fun hasHot(): Boolean {
        return buffList.find {
            it.id == HOT
        } != null
    }

    override fun hasDot(): Boolean {
        return buffList.find {
            it.id in DOT
        } != null
    }

    override fun isThisTurnImmune(): Boolean {
        return buffList.find {
            it.id == IMMUNE
        } != null
    }

    override fun isShieldIgnore(): Boolean {
        return buffList.find {
            it.id == SHIELD_IGNORE
        } != null
    }

    override fun isDamageImmune(damageType: DamageType): Boolean {
        val buffId = getDamageImmuneBuffIdByDamageType(damageType)
        return buffList.find {
            it.id == buffId
        } != null
    }

    override fun isDeathAvoid(): Boolean {
        return buffList.find {
            it.id == AVOID_DEATH
        } != null
    }

    private fun getDamageImmuneBuffIdByDamageType(damageType: DamageType): Int {
        return damageType.value + DAMAGE_IMMUNE_BASE
    }

    override fun isControlImmune(): Boolean {
        return buffList.find {
            it.id == CONTROL_IMMUNE
        } != null
    }

    override fun hasHolyShield(): Boolean {
        return getSumValueById( { it.id == HOLY_SHIELD }) > 0
    }

    override fun hasShield(damageType: DamageType): Boolean {
        val shieldBuffId = getShieldBuffIdByDamageType(damageType)
        return buffList.find {
            it.id == shieldBuffId && it.buffValue > 0
        } != null
    }

    override fun hasAllShield(): Boolean {
        return buffList.find {
            it.id == ALL_SHIELD && it.buffValue > 0
        } != null
    }

    override fun removeShield(damageType: DamageType) {
        buffList.removeAll { it.id == getShieldBuffIdByDamageType(damageType) }
    }

    override fun removeAllShield() {
        buffList.removeAll { it.id == ALL_SHIELD }
    }

    override fun reduceHolyShield() {
        val layer = getSumValueById({ it.id == HOLY_SHIELD })
        if (layer > 0) {
            updateShieldById(layer - 1, HOLY_SHIELD, true)
        }
        if (!hasHolyShield()) {
            buffList.removeAll { it.id == HOLY_SHIELD }
        }
    }

    override fun updateShield(shield: Int, damageType: DamageType) {
        if (shield(damageType) > 0) {
            updateShieldById(shield, getShieldBuffIdByDamageType(damageType))
        }
    }

    override fun updateAllShield(shield: Int) {
        if (allShield() > 0) updateShieldById(shield, ALL_SHIELD)
    }

    override fun clear(field: BattleField, buffOwner: Role) {
        buffList.forEach { buff ->
            unEffect(field, buffOwner, buff)
        }
        buffList.clear()
    }

    override fun clearDebuff(field: BattleField, buffOwner: Role): Int {
        return buffList.filter { it.disposableBadBuff() }.map { buff ->
            removeBuff(field, buff)
            unEffect(field, buffOwner, buff)
        }.size
    }

    override fun clearBuff(field: BattleField, buffOwner: Role): Int {
        return buffList.filter { it.disposableGoodBuff() }.map {
            removeBuff(field, it)
            unEffect(field, buffOwner, it)
        }.size
    }

    /**
     * 这个是会返回combinedbuff的
     */
    override fun getShowBothBuff(): List<Buff> {
        return getBuffList().filterNot { buff ->
            buff.isThisControlImmune(this)
        }.filterNot { it.isStamp() }.groupBy { if (it.combinedId == 0) it.id else it.combinedId }
            .map {
                // 这里需要把buff转下，如果是combined，合并成一个buff对外显示和计数
                if (it.value.first().combinedId == 0) it.value.first()
                else BuffFactory.getCombinedBuff(it.key).copy(
                    skill = it.value.first().skill,
                    buffCurrentLayer = it.value.first().buffCurrentLayer
                )
            }
    }

    override fun getShowGoodBuff(): List<Buff> {
        return getShowBothBuff().filter {
            it.buffType == 1
        }
    }

    override fun getShowBadBuff(): List<Buff> {
        return getShowBothBuff().filter {
            it.buffType == 2
        }
    }

    override fun getDispelableGoodBuff(): List<Buff> {
        return getShowBothBuff().filter {
            it.disposableGoodBuff()
        }
    }

    override fun getDispelableBadBuff(): List<Buff> {
        return getShowBothBuff().filter {
            it.disposableBadBuff()
        }
    }

    override fun getDispelableAllBuff(): List<Buff> {
        return getShowBothBuff().filter {
            it.isDisposable()
        }
    }

    override fun cantBeDispelBuff(): Boolean {
        return buffList.find {
            it.id == BUFF_DISPEL_IMMUNE
        } != null
    }

    override fun cantBeDispelDebuff(): Boolean {
        return buffList.find {
            it.id == DEBUFF_DISPEL_IMMUNE
        } != null
    }

    override fun singleDamageLimit(): Int {
        return buffList.filter { it.id == SINGLE_DAMAGE_LIMIT }.takeIf { it.isNotEmpty() }
            ?.minOf { it.buffValue }
            ?.roundToInt() ?: -1
    }

    override fun getBuffLayer(id: Int): Int {
        return buffList.find { it.id == id }?.buffCurrentLayer ?: 0
    }

    override fun clearAnyBuff(field: BattleField, buffOwner: Role, buff: Buff) {
        buffList.find { it == buff }?.takeIf { it.isDisposable() }?.let {
            removeBuff(field, it)
            unEffect(field, buffOwner, it)
        }
        // 组合buff的清理逻辑
        buffList.filter { it.combinedId == buff.id }.filter { it.isDisposable() }.forEach {
            removeBuff(field, it)
            unEffect(field, buffOwner, it)
        }
    }

    override fun getDamageTypeChange(): DamageType? {
        return try {
            buffList.findLast { it.id in changeDamageTypeList }?.let {
                DamageType.fromTypeValue(it.id - DAMAGE_CHANGE_BASE)
            }
        } catch (e: Exception) {
            null
        }
    }


    fun getSumValuePercentById(
        condition: (Buff) -> Boolean,
        base: Int = 0,
        increaseOrDecrease: Int = 1,
        default: Double = 0.0
    ): Double {
        return buffList.filter { buff ->
            condition(buff)
        }.takeIf {
            it.isNotEmpty()
        }?.let { buffList ->
            (base + buffList.sumOf {
                (increaseOrDecrease * it.buffValue)
            }) / 100.0
        } ?: default
    }
}

