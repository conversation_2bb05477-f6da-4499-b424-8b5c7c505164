package com.moyu.core.logic.info

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.logic.skill.getNameInfo
import com.moyu.core.logic.skill.isDot
import com.moyu.core.logic.skill.isHot
import com.moyu.core.model.Buff
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.info.BattleInfo
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import core.generated.resources.Res
import core.generated.resources.ban_skill_tip1
import core.generated.resources.ban_skill_tip2
import core.generated.resources.control_immune
import core.generated.resources.disarm_tips
import core.generated.resources.forbid_heal_tip
import core.generated.resources.frenzy_tips
import core.generated.resources.gain
import core.generated.resources.heal
import core.generated.resources.hp
import core.generated.resources.let
import core.generated.resources.lost
import core.generated.resources.ones_turn
import core.generated.resources.over_turn
import core.generated.resources.palsy_tips
import core.generated.resources.release
import core.generated.resources.silent_tips
import core.generated.resources.skill_type1
import core.generated.resources.skill_type2
import core.generated.resources.skill_type3
import core.generated.resources.skill_type4
import core.generated.resources.trigger_multi_cast
import core.generated.resources.you_release


fun Int.getElementTypeName(): String {
    return when (this) {
        1 -> AppWrapper.getStringKmp(Res.string.skill_type1)
        2 -> AppWrapper.getStringKmp(Res.string.skill_type2)
        3 -> AppWrapper.getStringKmp(Res.string.skill_type3)
        else -> AppWrapper.getStringKmp(Res.string.skill_type4)
    }
}

fun GameCore.addInfo(info: String, type: BattleInfoType) {
    onBattleInfo(BattleInfo(info, type = type))
}

fun GameCore.addTurnBeginInfo(role: Role, turn: Int) {
    if (role.isPlayer()) {
        addInfo(
            (if (turn == 1) "" else "\n") + "> " + role.getSideName() + AppWrapper.getStringKmp(Res.string.ones_turn) + turn,
            type = BattleInfoType.Battle
        )
    } else {
        addInfo(
            "\n> " + role.getSideName() + AppWrapper.getStringKmp(Res.string.ones_turn) + turn,
            type = BattleInfoType.Battle
        )
    }
}

fun GameCore.addBanInfo(role: Role, elementType: Int) {
    addInfo(
        role.getSideName() + AppWrapper.getStringKmp(Res.string.ban_skill_tip1) + elementType.getElementTypeName() + AppWrapper.getStringKmp(
            Res.string.ban_skill_tip2
        ),
        BattleInfoType.Battle
    )
}


fun GameCore.addPalsyInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getStringKmp(Res.string.palsy_tips),
        BattleInfoType.Battle
    )
}

fun GameCore.addFrozenInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getStringKmp(Res.string.silent_tips),
        BattleInfoType.Battle
    )
}

fun GameCore.addDisarmInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getStringKmp(Res.string.disarm_tips),
        BattleInfoType.Battle
    )
}

fun GameCore.addFrenzyInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getStringKmp(Res.string.frenzy_tips), BattleInfoType.Battle
    )
}

fun GameCore.addForbidHealInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getStringKmp(Res.string.forbid_heal_tip), BattleInfoType.Battle
    )
}

fun GameCore.addDoubleSkillInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getStringKmp(Res.string.trigger_multi_cast),
        BattleInfoType.Battle
    )
}

fun GameCore.addControlImmuneInfo(newBuff: Buff) {
    onBattleInfo(
        BattleInfo(
            AppWrapper.getStringKmp(Res.string.control_immune) + newBuff.name, type = BattleInfoType.Buff
        )
    )
}

fun GameCore.addHealInfo(skill: Skill, doer: Role, target: Role, heal: Int) {
    val content =
        if (skill.isHot()) skill.getNameInfo() + AppWrapper.getStringKmp(Res.string.let) + target.getSideName() + AppWrapper.getStringKmp(
            Res.string.heal
        ) + heal
        else doer.getSideName() + AppWrapper.getStringKmp(Res.string.release) + skill.getNameInfo() + "," + AppWrapper.getStringKmp(
            Res.string.let
        ) + target.getSideName() + AppWrapper.getStringKmp(Res.string.heal) + heal + AppWrapper.getStringKmp(
            Res.string.hp
        )
    onBattleInfo(
        BattleInfo(
            skill = skill,
            content = content,
            type = BattleInfoType.Heal,
            doer = doer,
            target = target
        )
    )
}

fun GameCore.addDamageInfo(damage: DamageResult, doer: Role, target: Role) {
    val skill = damage.damageSkill
    val damageValue = damage.damageValue.finalDamage
    val content = when {
        skill.isDot() -> skill.buffInfo!!.getNameInfo() + AppWrapper.getStringKmp(Res.string.let) + target.getSideName() + AppWrapper.getStringKmp(
            Res.string.lost
        ) + damageValue + AppWrapper.getStringKmp(Res.string.hp)

        else -> doer.getSideName() + AppWrapper.getStringKmp(Res.string.release) + skill.getNameInfo() + "," + AppWrapper.getStringKmp(
            Res.string.let
        ) + target.getSideName() + AppWrapper.getStringKmp(Res.string.lost) + damageValue + AppWrapper.getStringKmp(
            Res.string.hp
        )
    }
    onBattleInfo(
        BattleInfo(
            skill = skill,
            content = content,
            type = BattleInfoType.Damage,
            doer = doer,
            target = target,
            damageData = damage
        )
    )
}

fun GameCore.addExtraSkillInfo(content: String, skill: Skill) {
    onBattleInfo(
        BattleInfo(
            content = AppWrapper.getStringKmp(Res.string.you_release) + skill.getNameInfo() + if (content.isNotEmpty()) ",$content" else "",
            type = BattleInfoType.ExtraSkill,
            skill = skill,
        )
    )
}

fun GameCore.addSkillCastInfo(
    skillOwner: Role, content: String, skill: Skill
) {
    onBattleInfo(
        BattleInfo(
            content = skillOwner.getSideName() + AppWrapper.getStringKmp(Res.string.release) + skill.getNameInfo() + content,
            type = BattleInfoType.TriggerSkill,
            skill = skill,
            doer = skillOwner
        )
    )
}

fun GameCore.addBuffInfo(skillOwner: Role, skill: Skill, innerTarget: Role, buff: Buff) {
    onBattleInfo(
        BattleInfo(
            content = skillOwner.getSideName() + AppWrapper.getStringKmp(Res.string.release) + skill.getNameInfo() + innerTarget.getSideName() + AppWrapper.getStringKmp(
                Res.string.gain
            ) + buff.getNameInfo(),
            type = BattleInfoType.TriggerSkill,
            skill = skill,
            doer = skillOwner
        )
    )
}


fun GameCore.addFailMaxTurn() {
    addInfo(
        AppWrapper.getStringKmp(Res.string.over_turn), type = BattleInfoType.Battle
    )
}