package com.moyu.core.logic.property

import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import kotlin.math.roundToInt

class DefaultPropertyHolder : PropertyHolder {
    private var initProperty: Property = EMPTY_PROPERTY

    private var originProperty: Property = EMPTY_PROPERTY
    private var defaultProperty: Property = EMPTY_PROPERTY
    private var currentProperty: Property = EMPTY_PROPERTY

    override fun getOriginProperty(): Property {
        return originProperty
    }

    override fun getDefaultProperty(): Property {
        return defaultProperty
    }

    override fun getCurrentProperty(): Property {
        return currentProperty
    }

    override fun setAllProperty(property: Property) {
        setOriginProperty(property)
        setDefaultProperty(property)
        setCurrentProperty(property)
    }

    override fun getInitProperty(): Property {
        return initProperty
    }

    override fun setOriginProperty(property: Property) {
        originProperty = property.copy()
    }

    override fun setDefaultProperty(property: Property) {
        defaultProperty = property.copy()
    }

    override fun setCurrentProperty(property: Property) {
        currentProperty = property.copy()
    }

    override fun setCurrentHp(hp: Int) {
        currentProperty = currentProperty.copy(hp = hp)
    }

    override fun setCurrentAttack(attack: Int) {
        currentProperty = currentProperty.copy(attack = attack)
    }

    override fun setCurrentDefense(defense: Int) {
        currentProperty = currentProperty.copy(defenses = defense)
    }

    override fun setDefaultHp(hp: Int) {
        defaultProperty = defaultProperty.copy(hp = hp)
    }

    override fun setDefaultAttack(attack: Int) {
        defaultProperty = defaultProperty.copy(attack = attack)
    }

    override fun setDefaultDefense(defense: Int) {
        defaultProperty = defaultProperty.copy(defenses = defense)
    }

    override fun changeDefaultHp(diff: Int) {
        val currentPercentage = currentProperty.hp.toDouble() / if (defaultProperty.hp == 0) 1 else defaultProperty.hp
        defaultProperty += Property(hp = diff)
        currentProperty += Property(hp = (diff * currentPercentage).roundToInt())
    }

    override fun changeAllProperty(diff: Property) {
        defaultProperty += diff
        currentProperty += diff
        originProperty += diff
        ensureCurrentPropertyNotNegative()
    }

    override fun changeCurrentProperty(diff: Property) {
        currentProperty += diff
        ensureCurrentPropertyNotNegative()
    }

    private fun ensureCurrentPropertyNotNegative() {
    }

    override fun recoverDefaultHp(diff: Int) {
        val currentPercentage =
            if (defaultProperty.hp == 0) 0.0 else currentProperty.hp.toDouble() / defaultProperty.hp
        defaultProperty -= Property(hp = diff)
        currentProperty -= Property(hp = (diff * currentPercentage).roundToInt())
    }

    override fun recoverOriginProperty(diff: Property) {
        defaultProperty -= diff
        currentProperty -= diff
        originProperty -= diff
    }

    override fun recoverCurrentProperty(diff: Property) {
        currentProperty -= diff
    }

    override fun recoverToDefault() {
        defaultProperty = originProperty.copy()
        currentProperty = originProperty.copy()
    }

    override fun recoverToDefaultExceptHp() {
        defaultProperty = originProperty.copy()
        currentProperty = originProperty.copy(hp = currentProperty.hp)
    }

    override fun isDeath(): Boolean {
        return currentProperty.hp <= 0
    }

    override fun isHurt(): Boolean {
        return currentProperty.hp < defaultProperty.hp
    }

    override fun setInitProperty(property: Property) {
        initProperty = property
    }
}