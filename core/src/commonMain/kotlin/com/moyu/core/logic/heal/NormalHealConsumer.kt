package com.moyu.core.logic.heal

import com.moyu.core.GameCore
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.info.addHealInfo
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.role.Role

class NormalHealConsumer : HealConsumer {
    override suspend fun consumeHeal(
        field: <PERSON>Field,
        heal: Heal<PERSON><PERSON><PERSON>,
        doer: Role,
        target: Role,
        index: Int
    ): HealResult {
        val realHealPoint =
            if (target.getCurrentProperty().hp + heal.healValue > target.getDefaultProperty().hp) target.getDefaultProperty().hp - target.getCurrentProperty().hp
            else heal.healValue
        val result = heal.copy(healValue = realHealPoint)
        target.setCurrentHp(realHealPoint + target.getCurrentProperty().hp)
        target.setBeingHeal(heal.healSkill, result, index)
        GameCore.instance.addHealInfo(heal.healSkill, doer, target, realHealPoint)

        field.saveHealValue(target, realHealPoint)
        field.saveHealResult(target, result)
        return result
    }
}