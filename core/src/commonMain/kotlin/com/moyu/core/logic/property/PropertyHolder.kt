package com.moyu.core.logic.property

import com.moyu.core.model.property.Property

interface PropertyHolder {
    fun getOriginProperty(): Property
    fun getDefaultProperty(): Property
    fun getCurrentProperty(): Property
    fun setAllProperty(property: Property)
    fun setOriginProperty(property: Property)
    fun setDefaultProperty(property: Property)
    fun setCurrentProperty(property: Property)
    fun setCurrentHp(hp: Int)
    fun setCurrentAttack(attack: Int)
    fun setCurrentDefense(defense: Int)
    fun setDefaultHp(hp: Int)
    fun setDefaultAttack(attack: Int)
    fun setDefaultDefense(defense: Int)
    fun changeDefaultHp(diff: Int)
    fun changeAllProperty(diff: Property)
    fun changeCurrentProperty(diff: Property)
    fun recoverDefaultHp(diff: Int)
    fun recoverOriginProperty(diff: Property)
    fun recoverCurrentProperty(diff: Property)
    fun recoverToDefault()
    fun recoverToDefaultExceptHp()
    fun isDeath(): Boolean
    fun isHurt(): Boolean
    fun setInitProperty(property: Property)
    fun getInitProperty(): Property
}