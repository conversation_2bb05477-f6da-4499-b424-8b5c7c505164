package com.moyu.core.logic.enemy

import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.Ally
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role

object DefaultAllyCreator : RoleCreator {
    override fun create(race: Ally, diffProperty: Property, extraSkills: List<Int>, identifier: Identifier, needRandomSkills: Boolean): Role {
        return createDetailed(race, diffProperty, identifier, extraSkills, needRandomSkills)
    }
}