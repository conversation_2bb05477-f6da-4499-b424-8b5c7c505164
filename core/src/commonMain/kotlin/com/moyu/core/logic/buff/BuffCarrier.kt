package com.moyu.core.logic.buff

import com.moyu.core.logic.battle.BattleField
import com.moyu.core.model.Buff
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.role.Role
import com.moyu.core.model.Timing

interface BuffCarrier {
    fun setBuffList(newList: List<Buff>)
    fun getBuffList(): List<Buff>
    fun getShowBothBuff(): List<Buff>
    fun getShowGoodBuff(): List<Buff>
    fun getShowBadBuff(): List<Buff>
    suspend fun addBuff(battleField: BattleField, role: Role, newBuff: Buff)
    fun removeBuff(battleField: BattleField, buff: Buff)
    suspend fun effectAll(
        field: <PERSON><PERSON>ield,
        buffOwner: Role,
        timing: Timing
    )

    fun unEffect(
        field: BattleField,
        buffOwner: Role,
        buff: Buff,
    )

    suspend fun effect(
        field: BattleField,
        buffOwner: Role,
        timing: Timing,
        buff: Buff
    )

    fun checkBuffCondition(
        result: BattleField,
        buffOwner: Role,
    )

    fun checkBuffContinue(
        battleField: BattleField,
        buffOwner: Role
    )

    fun clear(field: BattleField, buffOwner: Role)
    fun clearDebuff(field: <PERSON>Field, buffOwner: Role): Int
    fun clearBuff(field: BattleField, buffOwner: Role): Int
    fun reduceHolyShield()
    fun removeAllShield()
    fun updateShield(shield: Int, damageType: DamageType)
    fun updateAllShield(shield: Int)
    fun removeShield(damageType: DamageType)
    fun turnPlusOne(checkIfAddOne: (String) -> Boolean)
    fun hasHolyShield(): Boolean
    fun isThisTurnImmune(): Boolean
    fun isShieldIgnore(): Boolean
    fun isDodgeImmune(): Boolean
    fun isControlImmune(): Boolean
    fun allShield(): Int
    fun allTypeShield(): Int
    fun shield(damageType: DamageType): Int
    fun isBanSkill(elementType: Int): Boolean
    fun isFrenzy(): Boolean
    fun isTaunt(): Boolean
    fun isFrozen(): Boolean
    fun isHealForbidden(): Boolean
    fun isPalsy(): Boolean
    fun isFrenzyImmune(): Boolean
    fun isDisarm(): Boolean
    fun isDisarmImmune(): Boolean
    fun isFrozenImmune(): Boolean
    fun isHealForbiddenImmune(): Boolean
    fun isPalsyImmune(): Boolean
    fun isTauntImmune(): Boolean
    fun isPoisonHeal(): Boolean
    fun isAvoidDeathImmune(): Boolean
    fun isSuckBloodImmune(): Boolean
    fun isDivideAttack(): Boolean
    fun hasShield(damageType: DamageType): Boolean
    fun hasAllShield(): Boolean
    fun hasHot(): Boolean
    fun hasDot(): Boolean
    fun getDispelableGoodBuff(): List<Buff>
    fun getDispelableBadBuff(): List<Buff>
    fun getDispelableAllBuff(): List<Buff>
    fun isDamageImmune(damageType: DamageType): Boolean
    fun cantBeDispelDebuff(): Boolean
    fun singleDamageLimit(): Int
    fun cantBeDispelBuff(): Boolean
    fun getBuffLayer(id: Int): Int
    fun clearAnyBuff(field: BattleField, buffOwner: Role, buff: Buff)
    fun isDeathAvoid(): Boolean
    fun getDamageTypeChange(): DamageType?
    fun isBanSkillImmune(elementType: Int): Boolean
}