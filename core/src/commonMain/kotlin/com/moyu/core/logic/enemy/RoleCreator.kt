package com.moyu.core.logic.enemy

import com.moyu.core.GameCore
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.level.DefaultLevelController
import com.moyu.core.model.Ally
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.util.RANDOM

interface RoleCreator {
    fun create(race: Ally, diffProperty: Property, extraSkills: List<Int> = emptyList(), identifier: Identifier, needRandomSkills: Boolean = true): Role
    fun createDetailed(
        race: Ally,
        diffProperty: Property,
        roleIdentifier: Identifier,
        extraSkills: List<Int>,
        needRandomSkills: Boolean = true
    ): Role {
        return Role(
            roleIdentifier = roleIdentifier,
            updateId = RANDOM.nextLong(),
            roleLevel = DefaultLevelController().apply {
                setLevel(1)
            }).apply {
            this.setRace(race)
            this.createSolidSkills(race)
            if (!roleIdentifier.isPlayer() && needRandomSkills) {
                this.createRandomSkills(race)
            }
            this.createExtraSkills(extraSkills)
            this.setProperty(race, diffProperty)
        }
    }
}

fun Role.setProperty(race: Ally, diffProperty: Property) {
    val targetProperty = (race.getProperty() + diffProperty).ensureNotNegative()
    setInitProperty(targetProperty)
    setPropertyToDefault()
}

fun Role.createSolidSkills(race: Ally) {
    race.fixedSkills.filter { it != 0 }.forEach { id ->
        val skill = GameCore.instance.getSkillById(id)
        this.learnSkill(skill, this.roleIdentifier)
    }
    if (race.star >= 10) {
        race.extraSkills3.filter { it != 0 }.forEach { id ->
            val skill = GameCore.instance.getSkillById(id * 100 + race.star)
            this.learnSkill(skill, this.roleIdentifier)
        }
    } else if (race.star >= 5) {
        race.extraSkills2.filter { it != 0 }.forEach { id ->
            val skill = GameCore.instance.getSkillById(id * 100 + race.star)
            this.learnSkill(skill, this.roleIdentifier)
        }
    } else {
        race.extraSkills1.filter { it != 0 }.forEach { id ->
            val skill = GameCore.instance.getSkillById(id * 100 + race.star)
            this.learnSkill(skill, this.roleIdentifier)
        }
    }
}

fun Role.createRandomSkills(race: Ally) {
    race.randomSkillId.shuffled(RANDOM).take(race.randomSkillNum.first()).forEach { skillId ->
        val skill = GameCore.instance.getSkillById(skillId)
        this.learnSkill(skill, this.roleIdentifier)
    }
}

fun Role.createExtraSkills(extraSkills: List<Int>) {
    extraSkills.forEach { skillId ->
        val skill = GameCore.instance.getSkillById(skillId)
        this.learnSkill(skill, this.roleIdentifier)
    }
}