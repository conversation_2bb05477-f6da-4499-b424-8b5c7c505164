package com.moyu.core.music

enum class SoundEffect(val isBattle: Boolean = false) {
    <PERSON><PERSON>,
    <PERSON>Good,
    StartGame,
    SelectEvent,
    GameOver,
    UserMoney,
    EquipItem,
    StarUpItem,
    UpgradeTalent1,
    UpgradeTalent2,
    UpgradeTalent3,
    TriggerSkill,
    ReliveAlly,
    CardLevelUp,
    HorseWalk,
    ShipSail,
    LotteryRing,
    HealAlly,
    EventWin,
    GainAward,
    Draw,
    OpenChest,
    PropertyAward,
    ReputationAward,
    ResourcesAward,
    DiamondAward,
    MoneyAward,
    HealAward,
    AllyExpAward,
    EquipAward,
    AllyAward,
    Resources8Award,
    DrawOrange,
    DrawRed,
    DoubleReward,
    DoublePunish,

    Damage1(true),
    Damage2(true),
    Damage3(true),
    <PERSON>age4(true),
    Damage5(true),
    He<PERSON>(true),
    GetBuff(true),
    GetDebuff(true),
    <PERSON><PERSON>on(true),
    <PERSON><PERSON>ie(true),
    Dispel(true),
    AvoidDeath(true),
    Control(true),
    SkillWinOrDirectDeath(true),
    Battle<PERSON>in(false),
    <PERSON>(true),
    <PERSON>Failed(false),
    <PERSON><PERSON><PERSON>in(true),
    <PERSON><PERSON><PERSON>(true),;
}