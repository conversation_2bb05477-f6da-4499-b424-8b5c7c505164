package com.moyu.core.json

import com.moyu.core.logic.level.DefaultLevelController
import com.moyu.core.logic.level.GameLevelData
import com.moyu.core.logic.level.LevelController
import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

object LevelSerializer : KSerializer<LevelController> {
    override val descriptor: SerialDescriptor = GameLevelData.serializer().descriptor

    override fun serialize(encoder: Encoder, value: LevelController) {
        val dataClass = value.toDataClass()
        encoder.encodeSerializableValue(GameLevelData.serializer(), dataClass)
    }

    override fun deserialize(decoder: Decoder): LevelController {
        val surrogate = decoder.decodeSerializableValue(GameLevelData.serializer())
        return DefaultLevelController().apply {
            fromDataClass(surrogate)
        }
    }
}