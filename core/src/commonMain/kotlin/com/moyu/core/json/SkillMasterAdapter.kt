package com.moyu.core.json

import com.moyu.core.logic.skill.DefaultSkillMaster
import com.moyu.core.logic.skill.SkillData
import com.moyu.core.logic.skill.SkillMaster
import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

object SkillMasterSerializer : KSerializer<SkillMaster> {
    override val descriptor: SerialDescriptor = SkillData.serializer().descriptor

    override fun serialize(encoder: Encoder, value: SkillMaster) {
        val dataClass = value.toPersistData()
        encoder.encodeSerializableValue(SkillData.serializer(), dataClass)
    }

    override fun deserialize(decoder: Decoder): SkillMaster {
        val surrogate = decoder.decodeSerializableValue(SkillData.serializer())
        return DefaultSkillMaster().apply {
            setSkills(surrogate.skills.mapNotNull {
                it.createOrNull()
            })
            setGraveSkills(surrogate.graveSkills.mapNotNull {
                it.createOrNull()
            })
        }
    }
}