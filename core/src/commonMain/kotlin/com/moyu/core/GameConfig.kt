package com.moyu.core

// taptap
const val tapClientId = "udim4cdw8pbw5ca6kk"
const val tapClientToken = "1ALt9eJT7wzoyPDJC0BU3Nx83kqej0VrFN9shoBV"
const val tapServerUrl = "https://ngtimzzz.cloud.tds1.tapapis.cn"
const val tapShareCodeUrl = "https://www.taptap.cn/app/647984"

// 好游快爆
const val gameId = "31775"

// bugly
const val buglyId = "55a6916283"


// 存档
const val DS_NAME = "_error_fatal2"
const val DS_NAME2 = "_crash_fatal2"
const val DS_NAME3 = "_crash_fatal3" // 专门用来记录当前服务器id

// 加密密钥
const val INT_ENCRYPT = "y=IP*?%&1o"

// todo 正式上线还要注意修改云存档/排行榜的api

// 隐私
const val privacyLink = "https://note.youdao.com/s/Q2c7bfEj"

// 许可
const val licenseLink = "https://note.youdao.com/s/2nuY7boI"

const val discordLink = "https://discord.gg/9VQZe5ugBy"


const val AD_UNIT_ID_T1 = "ca-app-pub-5058022002121914/**********"


const val merchantId = "**********"
const val APPID = "wx7726f9b3708bf447"
const val wcMusic = "battleeffect_16.mp3"

const val appFlyerDevKey = "BerpMdszVXZZsKMmBgkLYm"

const val taptapForum = "https://www.taptap.cn/moment/575024195264578721?share_id=f7ea2e9c2274&utm_medium=share&utm_source=copylink"

const val configPath = "impossible.mp3"

const val aiFaDian = "https://afdian.com/a/wujindeyuansushi"
