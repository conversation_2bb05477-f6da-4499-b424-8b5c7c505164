package com.moyu.core.config

import com.moyu.core.model.DayReward

class DayRewardConfigParser : ConfigParser<DayReward> {
    override fun parse(line: String): DayReward {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val valueType = words[i++].trim().toInt()
        val value = words[i++].trim().toInt()
        val unlock = words[i++].trim().toInt()
        val disappear = words[i++].trim().toInt()
        val priority = words[i].trim().toInt()
        return DayReward(
            id,
            type,
            valueType,
            value,
            unlock,
            disappear,
            priority,
        )
    }
}