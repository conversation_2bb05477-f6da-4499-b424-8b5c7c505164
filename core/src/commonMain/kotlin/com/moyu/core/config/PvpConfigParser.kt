package com.moyu.core.config

import com.moyu.core.model.Pvp

class PvpConfigParser : ConfigParser<Pvp> {
    override fun parse(line: String): Pvp {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val rank = words[i++].trim().toInt()
        val winPoint = words[i++].trim().toInt()
        val losePoint = words[i++].trim().toInt()
        val winToken = words[i++].trim().toInt()
        val loseToken = words[i++].trim().toInt()
        val type = words[i].trim().toInt()
        return Pvp(
            id,
            rank,
            winPoint,
            losePoint,
            winToken,
            loseToken,
            type
        )
    }
}