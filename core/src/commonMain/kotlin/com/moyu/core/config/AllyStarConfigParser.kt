package com.moyu.core.config

import com.moyu.core.model.AllyStar

class AllyStarConfigParser : ConfigParser<AllyStar> {
    override fun parse(line: String): AllyStar {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val quality = words[i++].trim().toInt()
        val star = words[i++].trim().toInt()
        val conditionNum1 = words[i++].trim().split(",").map { it.toInt() }
        val conditionNum2 = words[i++].trim().split(",").map { it.toInt() }
        val power = words[i].trim().toInt()
        return AllyStar(
            id,
            name,
            quality,
            star,
            conditionNum1,
            conditionNum2,
            power
        )
    }
}