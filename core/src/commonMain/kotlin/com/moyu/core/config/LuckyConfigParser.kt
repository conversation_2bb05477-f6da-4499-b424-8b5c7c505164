package com.moyu.core.config

import com.moyu.core.model.Lucky

class LuckyConfigParser : ConfigParser<Lucky> {
    override fun parse(line: String): Lucky {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val reward = words[i++].trim().toInt()
        val price = words[i].trim().toInt()
        return Lucky(
            id,
            name,
            reward,
            price,
        )
    }
}