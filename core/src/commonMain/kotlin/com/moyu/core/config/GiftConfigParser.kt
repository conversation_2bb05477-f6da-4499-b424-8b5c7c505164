package com.moyu.core.config

import com.moyu.core.model.Gift

class GiftConfigParser : ConfigParser<Gift> {
    override fun parse(line: String): Gift {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val icon = words[i++].trim()
        val content = words[i++].trim()
        val content2 = words[i++].trim()
        val pic = words[i++].trim()
        val label_pic = words[i++].trim()
        val poolId = words[i++].trim().toInt()
        val price = words[i++].trim().toInt()
        val priceDollar = words[i++].trim().toDouble()
        val unlockId = words[i++].trim().toInt()
        val limitTime = words[i++].trim().toInt()
        val triggerType = words[i++].trim().toInt()
        val limitBuy = words[i++].trim().toInt()
        val priority = words[i].trim().toInt()
        return Gift(
            id,
            name,
            icon,
            content,
            content2,
            pic,
            label_pic,
            poolId,
            price,
            priceDollar,
            unlockId,
            limitTime,
            triggerType,
            limitBuy,
            priority,
        )
    }
}