package com.moyu.core.config

import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.SkillEffectType

class SkillConfigParser : ConfigParser<Skill> {
    override fun parse(line: String): Skill {
        var i = 0
        val words = line.split("\t")
        val id = words[i++].toInt()
        val skillType = words[i++].toInt()
        val mainId = words[i++].toInt()
        val level = words[i++].toInt()
        val elementType = words[i++].toInt()
        val name = words[i++]
        val desc = words[i++]
        val triggerType = words[i++].toInt()
        val rate = words[i++].toDouble()
        val activeCondition = words[i++].split(",").map { it.toInt() }
        val activeConditionNum = words[i++].split(",").map { it.toDouble().toInt() } // todo
        val conditionLogic = words[i++].toInt()
        val effectType = words[i++].split(",").map { SkillEffectType(it.toInt()) }
        val subType = words[i++].split(",").map { it.toInt() }
        val target = words[i++].split(",").map { it.toInt() }
        val effectNum = words[i++].split(",").map {
            it.toDouble()
        }
        val effectReference = words[i++].split(",").map { it }
        val buffContinue = words[i++].toInt()
        val buffLayer = words[i++].toInt()
        val priority = words[i++].toInt()
        val icon = words[i++]
        val special = words[i++].toInt()
        val grave = words[i++].toInt()
        val coolDown = words[i++].toInt()
        val skillTagIds = words[i++].split(",").map { it.toInt() }
        val isDispel = words[i++].toInt()
        val combinedBuffId = words[i++].split(",").map { it.toInt() }
        val skillEffect = words.getOrNull(i++)?.takeIf { it != "" }?.split(",")?: emptyList()
        val skillEffectNum = words.getOrNull(i)?.takeIf { it != "" }?.split(",")?.map { it.toInt() }?: emptyList()

//        if (effectType.size != effectNum.size || effectType.size != effectReference.size || effectType.size != subType.size || effectType.size != target.size) {
//            println("SkillConfigParser 技能子效果配置错误：$name")
//        }
//        (1..10).toList().forEach { index->
//            if (desc.contains("%d${index}") && effectNum.size < index) {
//                println("SkillConfigParser 技能描述d${index}错误：$name")
//            }
//        }
//        if (activeCondition.size != activeConditionNum.size) {
//            println("SkillConfigParser 技能条件配置错误：$name")
//        }
        return Skill(
            id = id,
            skillType = skillType,
            mainId = mainId,
            level = level,
            elementType = elementType,
            name = name,
            desc = desc,
            triggerType = triggerType,
            rate = rate,
            activeCondition = activeCondition,
            activeConditionNum = activeConditionNum,
            conditionLogic = conditionLogic,
            effectType = effectType,
            subType = subType,
            target = target,
            effectNum = effectNum,
            effectReference = effectReference,
            buffContinue = buffContinue,
            buffLayer = buffLayer,
            priority = priority,
            icon = icon,
            special = special,
            grave = grave,
            coolDown = coolDown,
            skillTagIds = skillTagIds,
            isDispel = isDispel == 0,
            combinedBuffId = combinedBuffId,
            skillEffect = skillEffect,
            skillEffectNum = skillEffectNum
        )
    }
}