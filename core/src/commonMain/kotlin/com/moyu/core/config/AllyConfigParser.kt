package com.moyu.core.config

import com.moyu.core.model.Ally

class AllyConfigParser : ConfigParser<Ally> {
    override fun parse(line: String): Ally {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].toInt()
        val roleId = words[i++].toInt()
        val name = words[i++]
        val raceType = words[i++].toInt()
        val raceType2 = words[i++].toInt()
        val level = words[i++].toInt()
        val star = words[i++].toInt()
        val quality = words[i++].toInt()
        val attribute1 = words[i++].toInt()
        val attribute2 = words[i++].toInt()
        val attribute3 = words[i++].toInt()
        val attribute4 = words[i++].toDouble()
        val attribute5 = words[i++].toDouble()
        val attribute6 = words[i++].toDouble()
        val attribute7 = words[i++].toInt()
        val fixedSkills = words[i++].split(",").map { it.toInt() }
        val extraSkills1 = words[i++].split(",").map { it.toInt() }
        val extraSkills2 = words[i++].split(",").map { it.toInt() }
        val extraSkills3 = words[i++].split(",").map { it.toInt() }
        val randomSkillId = words[i++].split(",").map { it.toInt() }
        val banSkillId = words[i++].split(",").map { it.toInt() }
        val randomSkillNum = words[i++].split(",").map { it.toInt() }
        val pic = words[i++]
        val story = words[i++]
        val dropLimit = words[i++]
        val levelPower = words[i++].toInt()
        val vipValue = words[i].toInt()
        return Ally(
            id,
            roleId,
            name,
            raceType,
            raceType2,
            level,
            star,
            quality,
            attribute1,
            attribute2,
            attribute3,
            attribute4,
            attribute5,
            attribute6,
            attribute7,
            fixedSkills,
            extraSkills1,
            extraSkills2,
            extraSkills3,
            randomSkillId,
            banSkillId,
            randomSkillNum,
            pic,
            story,
            dropLimit.toInt(),
            levelPower,
            vipValue
        )
    }
}