package com.moyu.core.config

import com.moyu.core.model.Tcg

class TcgConfigParser : ConfigParser<Tcg> {
    override fun parse(line: String): Tcg {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val name = words[i++].trim()
        val star = words[i++].trim().toInt()
        val content = words[i++].trim().split(",").map { it.toInt() }
        val reward = words[i].trim().toInt()
        return Tcg(
            id,
            type,
            name,
            star,
            content,
            reward,
        )
    }
}