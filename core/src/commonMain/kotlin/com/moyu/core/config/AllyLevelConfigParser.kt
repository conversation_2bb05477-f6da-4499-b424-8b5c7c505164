package com.moyu.core.config

import com.moyu.core.model.AllyLevel

class AllyLevelConfigParser : ConfigParser<AllyLevel> {
    override fun parse(line: String): AllyLevel {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val level = words[i++].trim().toInt()
        val conditionNum = words[i].trim().split(",").map { it.toInt() }
        return AllyLevel(
            id,
            name,
            level,
            conditionNum,
        )
    }
}