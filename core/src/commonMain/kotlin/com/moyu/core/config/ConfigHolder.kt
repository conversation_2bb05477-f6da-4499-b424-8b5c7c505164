package com.moyu.core.config

import com.moyu.core.model.Ally
import com.moyu.core.model.AllyLevel
import com.moyu.core.model.AllyStar
import com.moyu.core.model.Arena
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Buff
import com.moyu.core.model.DayReward
import com.moyu.core.model.DrawItem
import com.moyu.core.model.Dungeon
import com.moyu.core.model.Equipment
import com.moyu.core.model.Event
import com.moyu.core.model.Gift
import com.moyu.core.model.Lucky
import com.moyu.core.model.Pool
import com.moyu.core.model.Pvp
import com.moyu.core.model.PvpRank
import com.moyu.core.model.Quest
import com.moyu.core.model.Sell
import com.moyu.core.model.Sign
import com.moyu.core.model.Story
import com.moyu.core.model.Talent
import com.moyu.core.model.Tcg
import com.moyu.core.model.Title
import com.moyu.core.model.Tower
import com.moyu.core.model.TurnTable
import com.moyu.core.model.Unlock
import com.moyu.core.model.Vip
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.skill.Scroll
import com.moyu.core.model.skill.Skill

const val BUFF_FILE_NAME = "buff.txt"
const val REPUTATION_LEVEL_FILE_NAME = "prestige.txt"
const val ALLY_FILE_NAME = "ally.txt"
const val DRAW_FILE_NAME = "draw.txt"
const val GIFT_FILE_NAME = "gift.txt"
const val COMMON_FILE_NAME = "common.txt"
const val SELL_FILE_NAME = "sell.txt"
const val SKILL_FILE_NAME = "skill.txt"
const val EQUIPMENT_FILE_NAME = "equipment.txt"
const val POSITION_FILE_NAME = "position.txt"
const val TASK_FILE_NAME = "task.txt"
const val UNLOCK_FILE_NAME = "unlock.txt"
const val TALENT1_FILE_NAME = "talent1.txt"
const val TALENT2_FILE_NAME = "talent2.txt"
const val TALENT3_FILE_NAME = "talent3.txt"
const val EVENT_FILE_NAME = "event.txt"
const val SCROLL_FILE_NAME = "scroll.txt"
const val POOL_FILE_NAME = "pool.txt"
const val VIP_FILE_NAME = "vip.txt"
const val COMBINEDBUFF_FILE_NAME = "combinedbuff.txt"
const val WAR_PASS1_FILE_NAME = "battlepass.txt"
const val WAR_PASS2_FILE_NAME = "battlepass2.txt"
const val WAR_PASS3_FILE_NAME = "battlepass3.txt"
const val WAR_PASS4_FILE_NAME = "battlepass3.txt" // todo
const val SIGN_FILE_NAME = "sign.txt"
const val PVP_FILE_NAME = "pvp.txt"
const val DAY_REWARD_FILE_NAME = "dayreward.txt"
const val LUCKY_FILE_NAME = "lucky.txt"
const val TURNTABLE_FILE_NAME = "turntable.txt"
const val ARENA_FILE_NAME = "arena2.txt"
const val TOWER_FILE_NAME = "tower.txt"
const val DUNGEON_FILE_NAME = "dungeon.txt"
const val STORY_FILE_NAME = "story.txt"
const val ALLY_LEVEL_FILE_NAME = "allylevel.txt"
const val ALLY_STAR_FILE_NAME = "allystar.txt"
const val PVP_RANK_FILE_NAME = "pvprank.txt"
const val TCG_FILE_NAME = "tcg.txt"


interface ConfigHolder {
    suspend fun setGameConfig(key: String, pool: List<Any>)

    // basic
    fun getSkillPool(): List<Skill>
    fun getSkillPool(level: Int): List<Skill>
    fun getBuffPool(): List<Buff>
    fun getTitlePool(): List<Title>
    fun getEquipPool(): List<Equipment>
    fun getEquipPool(mainId: Int): List<Equipment>
    fun getEventPool(): List<Event>
    fun getAllyPool(): List<Ally>
    fun getAllyPool(mainId: Int): List<Ally>
    fun getTalentPool(type: Int): List<Talent>
    fun getTalent1Pool(): List<Talent>
    fun getTalent2Pool(): List<Talent>
    fun getTalent3Pool(): List<Talent>
    fun getSellPool(): List<Sell>
    fun getGameTaskPool(): List<Quest>
    fun getUnlockPool(): List<Unlock>
    fun getScrollPool(mainId: Int): List<Scroll>
    fun getVipPool(): List<Vip>
    fun getCombinedBuffPool(): List<Buff>
    fun getBattlePass1Pool(): List<BattlePass>
    fun getBattlePass2Pool(): List<BattlePass>
    fun getBattlePass3Pool(): List<BattlePass>
    fun getBattlePass4Pool(): List<BattlePass>
    fun getDungeonPool(): List<Dungeon>
    fun getSignPool(): List<Sign>
    fun getPvpPool(type: Int): List<Pvp>
    fun getDrawPool(): List<DrawItem>
    fun getGiftPool(): List<Gift>
    fun getDayRewardPool(): List<DayReward>
    fun getLuckyPool(): List<Lucky>
    fun getStoryPool(): List<Story>
    fun getTurnTablePool(): List<TurnTable>
    fun getArenaPool(): List<Arena>
    fun getTowerPool(): List<Tower>
    fun getReputationLevelPool(): List<ReputationLevel>
    fun getAllyLevelPool(): List<AllyLevel>
    fun getAllyStarPool(): List<AllyStar>
    fun getPvpRankPool(): List<PvpRank>
    fun getTcgPool(): List<Tcg>


    // common
    fun getConstA(): Double
    fun getConstB(): Double
    fun getInitGold(): Int
    fun getInitWood(): Int
    fun getInitStone(): Int
    fun getEndingAwardLevel(age: Int): Int
    fun getEndingDiamondLevel(age: Int): Int
    fun getEndingAwardDiamond(difficult: Int, level: Int): Int
    fun getRefreshShopCost(): Int
    fun getKeyToResource1Rate(): Int
    fun getDailyShopRefreshCount(): Int

    // extra
    fun getSkillById(skillId: Int): Skill
    fun getSkillByIdNullable(skillId: Int): Skill?
    fun getEquipById(equipId: Int): Equipment
    fun getGameTaskById(id: Int): Quest
    fun getAllyById(id: Int): Ally
    fun getAlly(mainId: Int, level: Int, star: Int): Ally
    fun getAllyNextLevel(id: Int): Ally
    fun getAllyNextStar(id: Int): Ally
    fun getScrollById(id: Int): Scroll
    fun getBuffById(buffId: Int): Buff
    fun getUnlockById(id: Int): Unlock
    fun getFirstAwardPoolId(): Int
    fun getFirstEndingAwardPoolId(): Int
    fun getPoolById(id: Int): Pool
    fun getWarPassQuestCount(): Int
    fun getNewQuestCount(): Int
    fun getDailyQuestCount(): Int
    fun getReputationQuestCount(): Int
    fun getShopDataByIndex(index: Int): List<Int>
    fun getUnlockTalentPageLevel(): Int

    fun getMaxOtherUseYourCount(): Int
    fun getMaxUseOtherCount(): Int
    fun getShareCodeAwardKeyNum(): Int
    fun getMaxOneDayDiamondLimit(): Int
    fun getTextShareAwardNum(): Int
    fun getImageShareAwardNum(): Int
    fun getQuality1AllyNum(difficult: Int, level: Int): Int
    fun getQuality2AllyNum(difficult: Int, level: Int): Int
    fun getQuality3AllyNum(difficult: Int, level: Int): Int
    fun getQuality4AllyNum(difficult: Int, level: Int): Int
    fun getQuality5AllyNum(difficult: Int, level: Int): Int
    fun getQuality1AllyPool(): List<Int>
    fun getQuality2AllyPool(): List<Int>
    fun getQuality3AllyPool(): List<Int>
    fun getQuality4AllyPool(): List<Int>
    fun getQuality5AllyPool(): List<Int>
    fun getFamousDiamond(): Int
    fun getAllyCouponRate(): Int
    fun getHeroCouponRate(): Int
    fun getActivityCouponRate(): Int
    fun getInitCouponPoolId(): Int
    fun getGiftById(value: Int): Gift?
    fun getCheapLotteryCosts(): List<Int>
    fun getExpensiveLotteryCosts(): List<Int>
    fun getHolidayLotteryCosts(): List<Int>
    fun getTowerAwardKey(towerLevel: Int): Int
    fun getMaxPower(): Int
    fun getEachStageConsumePower(): Int
    fun getEachEndlessConsumePower(): Int
    fun getEachRecoverPower(): Int
    fun getLuckyWeights(): List<Int>
    fun getLuckyOutputs(): List<Int>
}