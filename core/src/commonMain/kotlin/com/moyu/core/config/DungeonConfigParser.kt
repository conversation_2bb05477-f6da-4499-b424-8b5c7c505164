package com.moyu.core.config

import com.moyu.core.model.Dungeon

class DungeonConfigParser : ConfigParser<Dungeon> {
    override fun parse(line: String): Dungeon {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val limit = words[i++].trim().toInt()
        val condition = words[i++].trim().toInt()
        val reward = words[i++].trim().toInt()
        val eventAttribute1 = words[i++].trim().toInt()
        val eventAttribute2 = words[i++].trim().toInt()
        val eventAttribute3 = words[i++].trim().toInt()
        val eventAttribute4 = words[i++].trim().toDouble()
        val eventAttribute5 = words[i++].trim().toDouble()
        val eventAttribute6 = words[i++].trim().toDouble()
        val eventAttribute7 = words[i++].trim().toInt()
        val targetText = words[i++].trim()
        val eventType = words[i++].trim().split(",").map { it.toInt() }
        val eventTypeImg = words[i++].trim().split(",")
        val enemyType = words[i++].trim().split(",").map { it.toInt() }
        val levelImg = words[i++].trim()
        val levelImg2 = words[i++].trim()
        val levelImg3 = words[i++].trim()
        val levelImg4 = words[i++].trim()
        val levelImg5 = words[i++].trim()
        val levelImg6 = words[i++].trim()
        val dungeonType = words[i].trim().toInt()
        return Dungeon(
            id,
            name,
            limit,
            condition,
            reward,
            eventAttribute1,
            eventAttribute2,
            eventAttribute3,
            eventAttribute4,
            eventAttribute5,
            eventAttribute6,
            eventAttribute7,
            targetText,
            eventType,
            eventTypeImg,
            enemyType,
            levelImg,
            levelImg2,
            levelImg3,
            levelImg4,
            levelImg5,
            levelImg6,
            dungeonType
        )
    }
}