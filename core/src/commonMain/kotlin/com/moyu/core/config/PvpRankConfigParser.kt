package com.moyu.core.config

import com.moyu.core.model.PvpRank

class PvpRankConfigParser : ConfigParser<PvpRank> {
    override fun parse(line: String): PvpRank {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val name = words[i++].trim()
        val winPoint = words[i++].trim().toInt()
        val pic = words[i].trim()
        return PvpRank(
            id,
            type,
            name,
            winPoint,
            pic,
        )
    }
}