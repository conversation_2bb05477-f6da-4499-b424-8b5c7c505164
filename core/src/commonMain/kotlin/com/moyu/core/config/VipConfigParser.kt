package com.moyu.core.config

import com.moyu.core.model.Vip

class VipConfigParser : ConfigParser<Vip> {
    override fun parse(line: String): Vip {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val level = words[i++].trim().toInt()
        val name = words[i++].trim()
        val desc = words[i++].trim()
        val num = words[i++].trim().toInt()
        val currentNum = words[i++].trim().toInt()
        val effectType = words[i++].trim().toInt()
        val effectId = words[i++].trim().toInt()
        val effectNum = words[i++].trim().toInt()
        val pic = words[i].trim()
        return Vip(
            id,
            level,
            name,
            desc,
            num,
            currentNum,
            effectType,
            effectId,
            effectNum,
            pic
        )
    }
}