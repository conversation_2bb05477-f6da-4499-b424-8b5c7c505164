import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    alias(libs.plugins.androidApplication)

    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.jetbrainsCompose)
    alias(libs.plugins.kotlinComposeCompiler)
    alias(libs.plugins.kotlinCocoapods)
    alias(libs.plugins.kotlinKapt)
    alias(libs.plugins.kotlinSerialization)

    // Google 插件先声明但不立刻应用
    id("com.google.gms.google-services") apply false
    id("com.google.firebase.crashlytics") apply false

    id("io.sentry.kotlin.multiplatform.gradle") version "0.12.0"
}

/**
 * 判断本次 gradle 命令是否在构建 “Oversea” 相关变体。
 * 例：./gradlew :app:assembleOverseaProductRelease
 */
val isOverseaBuild: Boolean =
    gradle.startParameter.taskNames.any { it.contains("Oversea", ignoreCase = true) }

if (isOverseaBuild) {
    // 仅在需要时再真正应用插件
    apply(plugin = "com.google.gms.google-services")
    apply(plugin = "com.google.firebase.crashlytics")
}

kotlin {
    jvmToolchain(17)
    androidTarget {
        compilations.all {
            compileTaskProvider.configure {
                compilerOptions {
                    jvmTarget.set(JvmTarget.JVM_17)
                }
            }
        }
    }
    jvm("desktop")
    iosArm64()
    iosSimulatorArm64()

    cocoapods {
        summary = "Some description for the Shared Module"
        homepage = "Link to the Shared Module homepage"
        version = "1.0"
        ios.deploymentTarget = "14.0"
        podfile = project.file("../iosApp/Podfile")
        framework {
            baseName = "shared"
            isStatic = true
        }

        // 文档 https://kotlinlang.org/docs/native-cocoapods-libraries.html
//        pod("SSZipArchive") {
//            version = "2.4.2"
//            extraOpts += listOf("-compiler-option", "-fmodules")
//        }
    }

    sourceSets {
        commonMain.dependencies {
            implementation(projects.core)

            // multiplatform dependencies
            implementation(compose.runtime)
            implementation(compose.foundation)
            implementation(compose.material)
            implementation(compose.material3)
            implementation(compose.ui)
            implementation(compose.animation)
            implementation(compose.animationGraphics)
            implementation(compose.components.resources)
            implementation(compose.components.uiToolingPreview)
            implementation(libs.jetbrains.androidx.lifecycle.viewmodel)
            implementation(libs.jetbrains.androidx.lifecycle.runtime.compose)
            implementation(libs.kotlinx.coroutines.core)
            implementation("org.jetbrains.androidx.navigation:navigation-compose:2.9.0-beta02")
            implementation("org.jetbrains.kotlinx:kotlinx-datetime:0.6.1")
            implementation("org.jetbrains.androidx.lifecycle:lifecycle-runtime-compose:2.9.0")

            implementation(libs.ktor.client.core)
            implementation(libs.ktor.client.negotiation)
            implementation(libs.ktor.client.serialization)

            implementation("com.eygraber:uri-kmp:0.0.19")

            // json
            implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3")
            // uuid
            implementation("app.softwork:kotlinx-uuid-core:0.0.12")

            // data store
            implementation("androidx.datastore:datastore-preferences:1.1.0")

            // async image
            implementation("io.coil-kt.coil3:coil-compose:3.0.4")
            implementation("io.coil-kt.coil3:coil-network-ktor3:3.0.4")

            implementation("org.jetbrains.kotlinx:kotlinx-collections-immutable:0.3.8")
            implementation("dev.whyoleg.cryptography:cryptography-core:0.4.0") // 加密

            api("io.github.kevinnzou:compose-webview-multiplatform:1.9.40")         // webview
        }

        androidMain.dependencies {
            implementation(projects.core)
            // androidx
            implementation("androidx.core:core-ktx:1.13.1")
            implementation("androidx.appcompat:appcompat:1.6.1")
            implementation("com.google.android.material:material:1.10.0")
            implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.8.3")
            implementation("androidx.activity:activity-compose:1.8.2")

            // zip4j
            implementation("net.lingala.zip4j:zip4j:2.11.5")

            implementation(libs.ktor.client.okhttp)

            // flip
            implementation("com.wajahatkarim:flippable:1.5.4")
            //exoplayer播放器
            implementation("androidx.media3:media3-exoplayer:1.4.1")
            implementation("androidx.media3:media3-ui:1.4.1")

            implementation("dev.whyoleg.cryptography:cryptography-provider-jdk:0.4.0") {
                exclude(group = "org.jooq", module = "joor-java-8")
            }
        }
        iosMain.dependencies {
            implementation(libs.ktor.client.darwin)
            implementation("dev.whyoleg.cryptography:cryptography-provider-apple:0.4.0") // 加密
        }
        val desktopMain by getting {
            dependencies {
                implementation(compose.desktop.common)
                implementation("net.lingala.zip4j:zip4j:2.11.5")
                implementation("com.code-disaster.steamworks4j:steamworks4j:1.9.0")
            }
        }
    }
}

afterEvaluate {
    dependencies {
        // ——— china ———
        add("chinaImplementation", "com.tencent.bugly:crashreport:*******")
        add("chinaImplementation", "com.scottyab:rootbeer-lib:0.1.0")
        add("chinaImplementation", "io.reactivex.rxjava2:rxandroid:2.1.1")
        add("chinaImplementation", "com.tencent.mm.opensdk:wechat-sdk-android:+")

        // ——— taptap ———
        add("taptapImplementation", "com.pangle.cn:ads-sdk-pro:*******")
        add("taptapImplementation", "com.taptap.sdk:tap-core:4.7.0")
        add("taptapImplementation", "com.taptap.sdk:tap-login:4.7.0")
        add("taptapImplementation", "com.taptap.sdk:tap-compliance:4.7.0")


        // —— 仅 oversea 口味需要的 Google / Firebase 依赖 ——
        add("overseaImplementation", "com.google.android.play:review:2.0.1")
        add("overseaImplementation", "com.google.android.play:review-ktx:2.0.1")
        add("overseaImplementation", "com.google.firebase:firebase-crashlytics-ktx:19.4.2")
        add("overseaImplementation", "com.google.firebase:firebase-analytics-ktx:22.4.0")
        add("overseaImplementation", "com.android.billingclient:billing-ktx:7.0.0")
        add("overseaImplementation", "com.google.android.gms:play-services-auth:21.3.0")
        add("overseaImplementation", "com.google.android.gms:play-services-ads:24.2.0")
        add("overseaImplementation", "com.android.installreferrer:installreferrer:2.2")
        add("overseaImplementation", "com.adjust.sdk:adjust-android:5.4.1")
        add("overseaImplementation", "com.google.android.gms:play-services-ads-identifier:18.0.1")
    }
}

compose.resources {
    publicResClass = true
    packageOfResClass = "shared.generated.resources"
    generateResClass = auto
}

android {
    ndkVersion = "28.1.13356709"   // ← 用 sdkmanager 安装后填真实号
    namespace = "com.moyu.chuanqirensheng"
    compileSdk = libs.versions.android.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "com.moyu.xiuxianxxx"
        minSdk = libs.versions.android.minSdk.get().toInt()
        targetSdk = libs.versions.android.targetSdk.get().toInt()
        versionCode = 10203
        versionName = "1.2.3"

        vectorDrawables {
            useSupportLibrary = true
        }
        // ② variant-level 旗標：只要留「cmake {...}」本身
        externalNativeBuild {
            cmake {
                cppFlags += listOf("-std=c++17", "-DRELEASE_BUILD=ON")
                // 开启自动对齐（r27+ 默认 ON，这行可省）
                arguments += "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
            }
        }
        ndk {             // 如果只支援特定 ABI
            abiFilters += listOf("armeabi-v7a", "arm64-v8a", "x86_64")
        }
    }

    signingConfigs {
        create("release") {
            storeFile = file("../wujindeyuansushi.keystore")
            storePassword = "tujiulong123"
            keyAlias = "key0"
            keyPassword = "tujiulong123"
        }
    }

    productFlavors {
        create("taiwan") {
            dimension = "platform"
            resValue("string", "main_page_url", "https://www.taptap.cn/app/384236")
            resValue("string", "platform_channel", "googleplay")
            resValue("string", "csjCodeId", "953443412")
            resValue("string", "csjAppId", "5428167")
            resValue("bool", "has_billing", "true")

            applicationId = "com.moyu.xiuxianxxx"
        }

        create("taptap") {
            dimension = "platform"
            resValue("string", "main_page_url", "https://www.taptap.cn/app/384236")
            resValue("string", "platform_channel", "taptap")
            resValue("string", "csjCodeId", "967616561")
            resValue("string", "csjAppId", "5713837")
            resValue("bool", "has_billing", "true")

            applicationId = "com.moyu.xiuxianxxx_tp"
        }

        create("oversea") {
            dimension = "region"
            resValue("bool", "has_google_service", "true")
            resValue("bool", "need_privacy_check", "false")
            resValue("bool", "need_anti_addict_check", "false")
            resValue("string", "serverUrl", "http://43.134.118.86:9795/yuansuqiu/api/v2/")
        }

        create("china") {
            dimension = "region"
            resValue("bool", "has_google_service", "false")
            resValue("bool", "need_privacy_check", "true")
            resValue("bool", "need_anti_addict_check", "true")
            resValue("string", "serverUrl", "http://81.69.218.207:9795/yuansuqiu/api/v2/")
        }

        create("lite") {
            dimension = "debugable"
        }
        create("product") {
            dimension = "debugable"
        }
    }
    flavorDimensions += listOf("platform", "debugable", "region")
    buildTypes {
        getByName("release") {
            isMinifyEnabled = true
            isShrinkResources = false
            signingConfig = signingConfigs.getByName("release")
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
        getByName("debug") {
            isMinifyEnabled = false
            signingConfig = signingConfigs.getByName("release")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    buildFeatures {
        compose = true
        buildConfig = true
    }
    kotlin {
        jvmToolchain(17)
    }
    packagingOptions.resources.excludes.add("/META-INF/{AL2.0,LGPL2.1}")
    bundle {
        language {
            enableSplit = false
        }
        abi { enableSplit = false }
    }

    sourceSets.getByName("main") {
        java.setSrcDirs(listOf("src/androidMain/main/java"))
        res.setSrcDirs(listOf("src/androidMain/main/res"))
        assets.setSrcDirs(listOf("src/androidMain/main/assets"))
        manifest.srcFile("src/androidMain/main/AndroidManifest.xml")
    }
    sourceSets.getByName("lite") {
        java.setSrcDirs(listOf("src/androidMain/lite/java"))
        assets.setSrcDirs(listOf("src/androidMain/lite/assets"))
    }
    sourceSets.getByName("product") {
        java.setSrcDirs(listOf("src/androidMain/product/java"))
    }
    sourceSets.getByName("oversea") {
        java.setSrcDirs(listOf("src/androidMain/oversea/java"))
        res.setSrcDirs(listOf("src/androidMain/oversea/res"))
        manifest.srcFile("src/androidMain/oversea/AndroidManifest.xml")
    }
    sourceSets.getByName("china") {
        java.setSrcDirs(listOf("src/androidMain/china/java"))
        manifest.srcFile("src/androidMain/china/AndroidManifest.xml")
        res.setSrcDirs(listOf("src/androidMain/china/res"))
    }
    sourceSets.getByName("taptap") {
        java.setSrcDirs(listOf("src/androidMain/taptap/java"))
        res.setSrcDirs(listOf("src/androidMain/taptap/res"))
        assets.setSrcDirs(listOf("src/androidMain/taptap/assets"))
        manifest.srcFile("src/androidMain/taptap/AndroidManifest.xml")
    }
//    externalNativeBuild {
//        cmake {
//            path = file("src/main/cpp/CMakeLists.txt")
//            version = "3.22.1"
//        }
//    }
}

allprojects {
    tasks.withType<KotlinCompile> {
        kotlinOptions {
            allWarningsAsErrors = false
            freeCompilerArgs += listOf("-opt-in=androidx.compose.animation.ExperimentalAnimationApi",
                "-opt-in=androidx.compose.ui.ExperimentalComposeUiApi",
                "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi",
                "-opt-in=androidx.media3.common.util.UnstableApi",
                "-opt-in=androidx.compose.foundation.layout.ExperimentalLayoutApi")
        }
    }
}

// 定义删除任务
val cleanTxtInDist by tasks.registering(Delete::class) {
    group = "cleanup"
    description = "Deletes all .txt files in the dist directory"
    doFirst {
        println("🔥 Executing cleanTxtInDist...")
    }
    delete(fileTree("src/commonMain/composeResources/files") {
        include("**/*.txt")
    })
}

// 配置任务依赖：构建 product 包前执行清理
tasks.configureEach {
    if (name.contains("Product", ignoreCase = false) &&
        (name.contains("assemble", ignoreCase = false) || name.contains("bundle", ignoreCase = false)
                )
        ) {
        dependsOn(cleanTxtInDist)
    }
}