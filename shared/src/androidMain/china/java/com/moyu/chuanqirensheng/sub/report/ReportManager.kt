package com.moyu.chuanqirensheng.sub.report

object ReportManager: ReportInterface {

    fun init() {
    }

    override fun setup() {

    }

    override fun onLogin() {
    }


    override fun onAdCompletedAF(adId: String) {
    }

    override fun onPurchaseCompletedAF(
        purchaseId: String,
        amount: Double,
        number: Int
    ) {
    }

    override fun onShopPurchase(sellId: Int, price: Int, priceType: Int) {
    }

    override fun onNewGame(mode: Int) {
    }

    override fun onContinueGame(mode: Int) {
    }

    override fun pk(win: Int, score: Int) {
    }

    override fun battle(win: Int, mode: Int, stage: Int) {
    }

    override fun onLoadAd() {
    }

    override fun onPage(route: String) {
    }

    override fun onPurchaseCompletedAdjust(dollarPrice: Double, orderId: String) {}

    override fun guide(index: Int) {
    }

    override fun giftShow(id: Int) {
    }

    override fun itemMoneyBought(id: Int) {
    }

    override fun itemKeyBought(id: Int) {
    }

    override fun unlockItem(id: Int) {

    }
    override fun unlockMonthCard() {}
}