package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.chuanqirensheng.util.killSelf
import com.moyu.core.AppWrapper
import com.scottyab.rootbeer.RootBeer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.root_tips


class RootCheckerTask : JobContent<Context> {
    override fun execute(context: Context) {
        if (BuildConfig.FLAVOR.contains("Lite")) { // Lite包不进行root检测
            return
        }
        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {
            AppWrapper.globalScope.launch {
                async(Dispatchers.IO) {
                    while (true) {
                        delay(1 * 1000)
                        val rootBeer = RootBeer(GameApp.instance)
                        if (rootBeer.checkForBinary("su")
                            || rootBeer.checkForDangerousProps()
                            || rootBeer.checkForRWPaths()
                            || rootBeer.detectTestKeys()
                            || rootBeer.checkSuExists()
                            || rootBeer.checkForRootNative()
                            || rootBeer.checkForMagiskBinary()
                        ) {
                            AppWrapper.getStringKmp(Res.string.root_tips).toast()
                            delay(2000)
                            killSelf()
                        }
                        delay(5 * 1000)
                    }
                }
            }
        }
    }
}