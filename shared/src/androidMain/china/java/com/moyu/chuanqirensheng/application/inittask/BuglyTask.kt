package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.core.buglyId
import com.tencent.bugly.crashreport.CrashReport

/**
 * Bugly初始化，用来上报crash
 */
class BuglyTask : JobContent<Context> {
    override fun execute(context: Context) {
        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {
            CrashReport.initCrashReport(GameApp.instance, buglyId, DebugManager.debug)
        }
    }
}