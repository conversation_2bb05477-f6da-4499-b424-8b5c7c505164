package com.moyu.chuanqirensheng.application

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedDispatcher
import androidx.activity.addCallback
import androidx.activity.compose.setContent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.bill.GoogleActivityResultHandler
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.util.BackUtil
import java.util.Locale

class GameActivity : ComponentActivity() {

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(updateBaseContext(newBase))
    }

    private fun updateBaseContext(context: Context?): Context {
        val lang = LanguageManager.selectedLanguage.value
        val locale = Locale(lang)
        Locale.setDefault(locale)

        return updateResourcesLocale(context, locale)
    }

    private fun updateResourcesLocale(context: Context?, locale: Locale): Context {
        val configuration = context?.resources?.configuration
        configuration?.setLocale(locale)
        return context!!.createConfigurationContext(configuration!!)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                // 如果需要布局延伸，可以加:
                // or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                // or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                )

        // 一行代码：保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        GameApp.instance.activity = this

//        val hash = getSignature()
//        heartbeat(hash)

        setContent {
            GameAppCompose {
                RegisterBackCallback(onBackPressedDispatcher)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        BillingManager.queryAsync()
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        GoogleActivityResultHandler.onActivityResult(requestCode, resultCode, data)
    }
}

@Composable
fun RegisterBackCallback(onBackPressedDispatcher: OnBackPressedDispatcher) {
    LaunchedEffect(Unit) {
        onBackPressedDispatcher.addCallback(
            GameApp.instance.activity,
        ) {
            BackUtil.actionBack()
        }
    }
}
