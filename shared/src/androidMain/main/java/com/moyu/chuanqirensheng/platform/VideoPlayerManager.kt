package com.moyu.chuanqirensheng.platform

import androidx.annotation.OptIn
import androidx.compose.runtime.mutableStateOf
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import com.moyu.chuanqirensheng.application.GameApp

object VideoPlayerManager {
    val mExoPlayer = mutableStateOf<ExoPlayer?>(null)

    @OptIn(UnstableApi::class)
    fun init() {
        try {
            if (mExoPlayer.value == null) {
                val mContext = GameApp.instance
                mExoPlayer.value = ExoPlayer.Builder(mContext).build()

//                val uri = RawResourceDataSource.buildRawResourceUri(R.raw.game_bg)
                val url = getLocalFilePath("game_bg.mp4")
                val dataSourceFactory = DefaultDataSource.Factory(mContext)
                val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
                    .createMediaSource(MediaItem.fromUri(url))
                mExoPlayer.value?.setMediaSource(mediaSource)
                mExoPlayer.value?.prepare()
                mExoPlayer.value?.repeatMode = ExoPlayer.REPEAT_MODE_ONE
                mExoPlayer.value?.playWhenReady = true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}