package com.moyu.chuanqirensheng.platform

//import com.moyu.chuanqirensheng.config.loadLocalFile
import android.app.Activity
import android.app.Application
import android.content.ClipData
import android.content.ClipboardManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.core.content.pm.PackageInfoCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView
import com.eygraber.uri.Uri
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.inittask.BillingTask
import com.moyu.chuanqirensheng.application.inittask.BuglyTask
import com.moyu.chuanqirensheng.application.inittask.ChannelTask
import com.moyu.chuanqirensheng.application.inittask.RootCheckerTask
import com.moyu.chuanqirensheng.application.inittask.TTRewardAdTask
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.media.MusicPlayerInterface
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.sub.ad.AdInterface
import com.moyu.chuanqirensheng.sub.ad.TTAdPlayer
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.bill.PayClientData
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.sub.datastore.KEY_VERIFIED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.sub.report.ReportInterface
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.sub.review.requestInAppReview
import com.moyu.chuanqirensheng.util.AESUtil.sha256
import com.moyu.chuanqirensheng.util.Platform
import com.moyu.chuanqirensheng.util.pixelToDp
import com.moyu.core.AppWrapper
import com.moyu.core.model.Sell
import io.ktor.client.HttpClient
import io.ktor.client.engine.okhttp.OkHttp
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.kotlinx.json.json
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import org.jetbrains.compose.resources.ExperimentalResourceApi
import shared.generated.resources.Res
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

actual fun getPlatform(): Platform {
    return Platform.Android
}

// 默认为 files 目录
@OptIn(ExperimentalResourceApi::class)
actual fun getLocalFilePath(item: String): String {
    return Res.getUri("files/${item}")
}

@OptIn(ExperimentalResourceApi::class)
actual fun getLocalFilePath(path: String, item: String): String {
    return Res.getUri("${path}/${item}")
}

actual fun screenWidthInDp(): Float {
    return GameApp.instance.resources.displayMetrics.widthPixels.pixelToDp().value
}

actual fun screenHeightInDp(): Float {
    return GameApp.instance.resources.displayMetrics.heightPixels.pixelToDp().value
}

actual fun screenDensity(): Float {
    return GameApp.instance.resources.displayMetrics.density
}

actual fun statusBarHeightInDp(): Dp {
    val insets = ViewCompat.getRootWindowInsets(GameApp.instance.activity.window.decorView)
    return (insets?.getInsets(WindowInsetsCompat.Type.statusBars())?.top ?: 0).pixelToDp()
}

actual fun bottomHeightInDp(): Dp {
    return 0f.dp
}

actual fun topHeightInDp(): Dp {
    return 0f.dp
}

actual fun hideKeyboard() {
    val activity = GameApp.instance.activity
    val imm: InputMethodManager =
        activity.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
    var view = activity.currentFocus
    if (view == null) {
        view = View(activity)
    }
    imm.hideSoftInputFromWindow(view.windowToken, 0)
}

actual fun triggerRebirth() {
    val packageManager: PackageManager = GameApp.instance.packageManager
    val intent: Intent? = packageManager.getLaunchIntentForPackage(GameApp.instance.packageName)
    val componentName: ComponentName? = intent?.component
    val mainIntent: Intent = Intent.makeRestartActivityTask(componentName)
    GameApp.instance.startActivity(mainIntent)
    Runtime.getRuntime().exit(0)
}

actual fun killSelf() {
    Runtime.getRuntime().exit(0)
}

private var activityAccount = 0
actual fun lifecycleExecute(isForeground: MutableState<Boolean>) {
    GameApp.instance.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        }

        override fun onActivityStarted(activity: Activity) {
            if (activityAccount == 0) {
                MusicManager.muteByBackGround(false)
            }
            isForeground.value = true
            activityAccount++
        }

        override fun onActivityResumed(activity: Activity) {
            playerMusicByScreen()
        }

        override fun onActivityPaused(activity: Activity) {
        }

        override fun onActivityStopped(activity: Activity) {
            activityAccount--
            if (activityAccount == 0) {
                isForeground.value = false
                AppWrapper.globalScope.launch {
                    MusicManager.muteByBackGround(true)
                    MusicManager.stopAll()
                }
            }
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        }

        override fun onActivityDestroyed(activity: Activity) {
        }
    })
}
/**
 * 日期格式字符串转换成时间戳
 * @param format 如：yyyy-MM-dd HH:mm:ss
 * @return
 */
// yyyy-MM-dd HH:mm:ss
actual fun date2TimeStamp(timestampMill: Long, format: String?): String {
    var tmpFormat = format
    if (tmpFormat == null) {
        tmpFormat = "yyyy-MM-dd HH:mm:ss"
    }

    try {
        val sdf = SimpleDateFormat(tmpFormat)
        return sdf.format(Date(timestampMill))
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return ""
}

actual fun Long.millisToHoursMinutesSeconds(): String {
    val hours = this / 1000 / 60 / 60
    val minutes = this / 1000 / 60 % 60
    val seconds = this / 1000 % 60
    return "%02d:%02d:%02d".format(hours, minutes, seconds)
}

actual fun getElapsedTimeMillis(): Long {
    return SystemClock.elapsedRealtime()
}

actual fun getVersion(): String {
    return try {
        GameApp.instance.packageManager.getPackageInfo(GameApp.instance.packageName, 0).versionName!!
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
        ""
    }
}

actual fun getVersionCode(): Int {
    return try {
        val packageInfo = GameApp.instance.packageManager.getPackageInfo(GameApp.instance.packageName, 0)
        PackageInfoCompat.getLongVersionCode(packageInfo).toInt()
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
        0
    }
}

actual fun isLite(): Boolean {
    return  BuildConfig.FLAVOR.contains("Lite")
}

actual fun isReleasePackage(): Boolean {
    return false
}

actual fun serverUrl(): String {
    return GameApp.instance.resources.getString(R.string.serverUrl)
}

actual fun needPrivacyCheck(): Boolean {
    return GameApp.instance.resources.getBoolean(
        R.bool.need_privacy_check
    )
}

actual fun hasGoogleService(): Boolean {
    return GameApp.instance.resources.getBoolean(
        R.bool.has_google_service
    )
}

actual fun hasBilling(): Boolean {
    return GameApp.instance.resources.getBoolean(
        R.bool.has_billing
    )
}

actual fun gameSdkDefaultProcessor(): GameSdkProcessor {
    return GameApp.instance
}

private var httpClient: HttpClient? = null
actual fun createHttpClient(): HttpClient{
    if (httpClient == null) {
        httpClient = HttpClient(OkHttp) {
            install(ContentNegotiation) {
                json(Json {
                    ignoreUnknownKeys = true
                    useAlternativeNames = false
                })
            }
        }
    }
    return httpClient!!
}

private var musicPlayer: MusicPlayerInterface? = null
actual fun createMusicPlayer(musicVolumeCallback: () -> Float): MusicPlayerInterface {
    if (musicPlayer == null) {
        musicPlayer = MusicPlayer(
            musicVolumeCallback
        )
    }

    return musicPlayer!!
}

actual fun reportManager(): ReportInterface {
    return ReportManager
}

actual fun openGamePage(uri: Uri) {
    val intent = Intent(Intent.ACTION_VIEW, android.net.Uri.parse(uri.toString()))
    ContextCompat.startActivity(GameApp.instance.activity, intent, Bundle())
}

actual fun platformChannel(): String {
    return GameApp.instance.resources.getString(R.string.platform_channel)
}

actual fun antiAddictVerified(): Boolean {
    return getBooleanFlowByKey(
        KEY_VERIFIED, !GameApp.instance.resources.getBoolean(
        R.bool.need_anti_addict_check))
}

actual fun privacyNeedShow(): Boolean {
    return getBooleanFlowByKey(
        KEY_NEED_SHOW_PRIVACY, GameApp.instance.resources.getBoolean(
        R.bool.need_privacy_check))
}

actual suspend fun billPrepay(sell: Sell, function: () -> Unit) {
    BillingManager.prepay(sell, function)
}

actual fun billPayClientDataList(): List<PayClientData> {
    return BillingManager.payClientDataList
}

actual fun billRemovePayClientData(it: PayClientData) {
     BillingManager.removePayClientData(it)
}

actual fun getSystemFilesPath(): String {
    return GameApp.instance.filesDir.absolutePath
}

actual fun getSystemCacheDirPath(): String {
    return GameApp.instance.cacheDir.absolutePath
}

actual fun getLocalLanguage(): String {
    return Locale.getDefault().language
}

actual fun setLocaleLanguage(languageCode: String) {
    val context = GameApp.instance
    val locale = Locale(languageCode)
    Locale.setDefault(locale)
    val config = Configuration(context.resources.configuration)
    config.setLocale(locale)
    context.resources.updateConfiguration(config, context.resources.displayMetrics)
}

actual fun getLocalCountry(): String {
    return Locale.getDefault().country.uppercase()
}

//actual suspend fun p_loadLocalFile(
//    fileName: String,
//    processor: (Int, String)->Unit) {
//    loadLocalFile(fileName, processor)
//}

actual fun platformTaskExecute() {
    BuglyTask().execute(GameApp.instance.gameContext)
    TTRewardAdTask().execute(GameApp.instance.gameContext)
    ChannelTask().execute(GameApp.instance.gameContext)
    RootCheckerTask().execute(GameApp.instance.gameContext)
    BillingTask().execute(GameApp.instance.gameContext)
}

actual fun getBuildFlavor(): String {
    val  buildFlavor = BuildConfig.FLAVOR
    return buildFlavor
}

actual fun p_getfootPrint(): String {
    val model: String = Build.MODEL
    val product: String = Build.PRODUCT
    val device: String = Build.DEVICE
    val brand: String = Build.BRAND

    val combinedString = model + product + device + brand
    return sha256(combinedString)
}

actual fun p_fileRename(oldFileName: String, newFileName: String) {
    File(oldFileName).renameTo(File(newFileName))
}

actual fun resetClipboard(text: String) {
    val myClipboard =
        GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val myClip =
        ClipData.newPlainText("text", text)
    myClipboard.setPrimaryClip(myClip)
}

actual fun p_requestInAppReview() {
    requestInAppReview()
}

actual fun p_getAdImp(): AdInterface {
    return TTAdPlayer
}

actual fun getHeadBitmap(): ImageBitmap? {
    return null
}

actual fun refreshRankList(type: Int, callback: ((list:List<RankData>) -> Unit)?) {}
actual fun setAchievement(name: String) {}
actual fun setLeaderBoardScore(score: Int, type: Int) {}

actual fun getTextFromFile(fileName: String): String {

    var inputStream: InputStream? = null
    var result = ""
    val targetFilePath = GameApp.instance.filesDir.absolutePath + File.separator + "uncompress" + File.separator + fileName

    try {
        val file = File(targetFilePath)
        inputStream = FileInputStream(file)
        val length: Int = inputStream.available()
        val buffer = ByteArray(length)
        inputStream.read(buffer)
        result = String(buffer, Charsets.UTF_8)
    } catch (e: IOException) {
        e.printStackTrace()
    } finally {
        inputStream?.close()
    }
    return result
}

@androidx.annotation.OptIn(UnstableApi::class)
@Composable
actual fun CMPVideoPlayer(modifier: Modifier) {
    if (VideoPlayerManager.mExoPlayer.value != null) {
        AndroidView(modifier = modifier, factory = { context ->
            PlayerView(context).apply {
                try {
                    useController = false
                    player = VideoPlayerManager.mExoPlayer.value
                    player = player
                    resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIXED_HEIGHT
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        })
    }
}

actual fun heartbeat() {
//    val hash = getSignature()
//    AndroidUtils.heartbeat(hash)
}