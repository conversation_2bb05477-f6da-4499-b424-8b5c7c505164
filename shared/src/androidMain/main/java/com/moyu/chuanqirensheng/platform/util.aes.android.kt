package com.moyu.chuanqirensheng.platform

import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.os.Build
import com.moyu.chuanqirensheng.application.GameApp
import java.io.UnsupportedEncodingException
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi
import kotlin.text.Charsets.UTF_8

/*   算法/模式/填充 */
private const val CipherMode = "AES/ECB/PKCS5Padding"

// ── 1. 线程级 Cipher 缓存 ──────────────────────────────
private val cipherLocal = object : ThreadLocal<Cipher>() {
    override fun initialValue() = Cipher.getInstance(CipherMode)
}

private val keyCache = HashMap<String, SecretKeySpec>()
private fun getKey(password: String): SecretKeySpec =
    keyCache.computeIfAbsent(password) { pass ->
        // 填 0 / 截断到 32 bytes（256 bit）
        val keyBytes = ByteArray(32)
        val src = pass.toByteArray(UTF_8)
        System.arraycopy(src, 0, keyBytes, 0, src.size.coerceAtMost(32))
        SecretKeySpec(keyBytes, "AES")
    }

/*加密(结果为16进制字符串)  */
@OptIn(ExperimentalEncodingApi::class)
actual fun aes_encrypt(content: String, password: String): String? {
    val data = content.toByteArray(UTF_8)

    val cipher = cipherLocal.get()          // 取线程私有实例
    cipher.init(Cipher.ENCRYPT_MODE, getKey(password))

    val encrypted = cipher.doFinal(data)
    return Base64.UrlSafe.encode(encrypted)
}

/*解密16进制的字符串为字符串  */
@OptIn(ExperimentalEncodingApi::class)
actual fun aes_decrypt(content: String, password: String): String? {
    var data: ByteArray? = null
    try {
        data = Base64.UrlSafe.decode(content)
    } catch (e: Exception) {
        e.printStackTrace()
    }
    data = decrypt(data, password)
    if (data == null) return null
    var result: String? = null
    try {
        result = String(data, UTF_8)
    } catch (e: UnsupportedEncodingException) {
        e.printStackTrace()
    }
    return result
}

@OptIn(ExperimentalEncodingApi::class)
actual fun getSignature(): String {
    try {
        // 获取PackageManager的实例
        val pm: PackageManager = GameApp.instance.packageManager
        // 通过调用getPackageInfo()方法获得当前应用的PackageInfo对象
        // PackageManager.GET_SIGNATURES已在API 28中弃用，推荐使用GET_SIGNING_CERTIFICATES
        val packageInfo: PackageInfo =
            pm.getPackageInfo(GameApp.instance.packageName, PackageManager.GET_SIGNING_CERTIFICATES)
        val signatures = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            packageInfo.signingInfo!!.apkContentsSigners
        } else {
            emptyArray()
        }
        // 循环遍历所有签名
        for (signature in signatures) {
            // 获取签名的哈希值
            val md = MessageDigest.getInstance("SHA")
            md.update(signature.toByteArray())
            val signatureHash = Base64.UrlSafe.encode(md.digest())
            // 打印或发送这个哈希值到服务器进行校验
//                ("SignatureHash $signatureHash").toast()
            return signatureHash.toString()
        }
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
    } catch (e: NoSuchAlgorithmException) {
        e.printStackTrace()
    }
    return ""
}

/*解密字节数组*/
private fun decrypt(content: ByteArray?, password: String): ByteArray? {
    try {
        val cipher = cipherLocal.get()
        cipher.init(Cipher.DECRYPT_MODE, getKey(password))
        return cipher.doFinal(content)
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return null
}