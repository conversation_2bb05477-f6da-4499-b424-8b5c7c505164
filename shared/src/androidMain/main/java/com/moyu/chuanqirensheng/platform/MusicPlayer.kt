package com.moyu.chuanqirensheng.platform

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.SoundPool
import android.net.Uri
import androidx.annotation.OptIn
import androidx.media3.common.MediaItem
import androidx.media3.common.Player.REPEAT_MODE_ONE
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.media.MusicPlayerInterface
import com.moyu.chuanqirensheng.media.MusicRes
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_SOUND
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.core.AppWrapper
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


// 给一个固定的线程给music
//private val musicDispatcher = Executors.newSingleThreadExecutor().asCoroutineDispatcher()
private val musicDispatcher = Dispatchers.Main

class MusicPlayer(override var musicVolumeCallback: () -> Float) : MusicPlayerInterface {
    private val soundPool = SoundPool.Builder().setMaxStreams(4).setAudioAttributes(
        AudioAttributes.Builder().setLegacyStreamType(AudioManager.STREAM_MUSIC).build()
    ).build()

    private var exoPlayer: ExoPlayer? = null

    override var soundHashMap: HashMap<SoundEffect, MusicRes>? = null
    var soundHashMap2: HashMap<SoundEffect, Int> = hashMapOf()
    private val streamIdMap = mutableMapOf<SoundEffect, Int>()
    private var lastMusicWithStop = ""
    private var lastMusic = ""

    private val lastPlayTimestamps = mutableMapOf<SoundEffect, Long>()
    private val MIN_INTERVAL = 200L  // 200ms

    private var musicJob: Job? = null
    private var muteByOthers = false

    override fun init() {
        // 假设你有一组 SoundEffect 枚举
        soundHashMap?.entries?.forEach {
            val uri = getLocalFilePath(it.value.value)
            val path = uri.removePrefix("file:///android_asset/")
            val fd = GameApp.instance.assets.openFd(path)
            val soundId = soundPool.load(fd, 1)
            soundHashMap2[it.key] = soundId
        }
    }

    @OptIn(UnstableApi::class)
    override fun playMusic(music: String) {
        musicJob?.cancel()
        musicJob = AppWrapper.globalScope.launch(musicDispatcher) {
            delay(500)
            try {
                if (AppWrapper.isForeground.value) {
                    if (lastMusicWithStop != music) {
                        lastMusicWithStop = music
                        lastMusic = music

                        if (exoPlayer == null) {
                            exoPlayer = ExoPlayer.Builder(GameApp.instance).build()
                        }

                        val mediaItem = MediaItem.fromUri(Uri.parse(getLocalFilePath(music)))
                        val dataSourceFactory = DefaultDataSource.Factory(GameApp.instance)
                        val mediaSource =
                            ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(mediaItem)

                        exoPlayer?.volume = musicVolumeCallback() * 0.35f
                        exoPlayer?.setMediaSource(mediaSource)
                        exoPlayer?.prepare()
                        exoPlayer?.seekTo(0)
                        exoPlayer?.repeatMode = REPEAT_MODE_ONE
                        exoPlayer?.play()
                    }
                }
            } catch (e: Exception) {
                println(e.message)
            }
        }
    }

    override fun resumeMusic() {
        AppWrapper.globalScope.launch(musicDispatcher) {
            playMusic(lastMusic)
        }
    }

    override fun stopAll() {
        AppWrapper.globalScope.launch(musicDispatcher) {
            try {
                lastMusicWithStop = ""
                exoPlayer?.stop()
            } catch (e: Exception) {
                println(e.message)
            }
        }
    }

    override fun stopSound(sound: SoundEffect) {
        AppWrapper.globalScope.launch(musicDispatcher) {
            try {
                // Retrieve the stream ID from the map
                streamIdMap[sound]?.let { streamId ->
                    soundPool.stop(streamId)
                    streamIdMap.remove(sound)
                }
            } catch (e: Exception) {
                println(e.message)
            }
        }
    }

    override fun playSound(sound: SoundEffect, loop: Boolean) {
        // 如果是同一个SoundEffect在200ms内重复调用，则不重复播放
        val currentTime = System.currentTimeMillis()
        val lastTime = lastPlayTimestamps[sound] ?: 0L
        if (currentTime - lastTime < MIN_INTERVAL) {
            // 在200ms内已播放过该音效，不再播放
            return
        }
        // 记录最新播放时间
        lastPlayTimestamps[sound] = currentTime

        // 以下是原有playSound的逻辑
        if (streamIdMap[sound] != null && loop) return
        AppWrapper.globalScope.launch(musicDispatcher) {
            try {
                if (getBooleanFlowByKey(KEY_MUTE_SOUND)) return@launch
                if (muteByOthers) return@launch
                val audioManager =
                    GameApp.instance.getSystemService(Context.AUDIO_SERVICE) as AudioManager
                val streamVolumeCurrent =
                    audioManager.getStreamVolume(AudioManager.STREAM_MUSIC).toFloat()
                val streamVolumeMax =
                    audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC).toFloat()
                val volume = streamVolumeCurrent / streamVolumeMax

                soundHashMap2[sound]?.let { soundId ->
                    val streamId = soundPool.play(
                        soundId,
                        volume,
                        volume,
                        1,
                        if (loop) -1 else 0,
                        if (sound.isBattle) 1 / GameSpeedManager.getCurrentSpeed().animationDurationFactor else 1f
                    )
                    if (streamId != 0 && loop) {
                        streamIdMap[sound] = streamId
                    }
                }
            } catch (e: Exception) {
                // ...
            }
        }
    }


    override fun doMuteState() {
        AppWrapper.globalScope.launch(musicDispatcher) {
            try {
                val volume = musicVolumeCallback()
                exoPlayer?.volume = volume
                if (streamIdMap.isNotEmpty()) {
                    streamIdMap.entries.first().let {
                        if (volume == 0f) {
                            soundPool.pause(it.value)
                        } else {
                            soundPool.resume(it.value)
                        }
                    }
                }
                muteByOthers = volume == 0f
            } catch (e: Exception) {
                println(e.message)
            }
        }
    }
}