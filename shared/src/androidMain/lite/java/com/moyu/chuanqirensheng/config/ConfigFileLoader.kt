package com.moyu.chuanqirensheng.config

import android.content.Context
import com.moyu.chuanqirensheng.application.GameApp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException
import java.io.InputStream
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

//suspend fun loadLocalFile(
//    fileName: String,
//    processor: (Int, String)->Unit) {
//    loadAssetFile(fileName, processor)
//}
//
//suspend fun loadAssetFile(
//    fileName: String,
//    processor: (Int, String) -> Unit
//): Boolean {
//    return withContext(Dispatchers.IO) {
//        suspendCoroutine { continuation ->
//            val string = getTextFromAsset(GameApp.instance, fileName)
//            string.lines().forEachIndexed { index, line ->
//                processor(index, line)
//            }
//            continuation.resume(true)
//        }
//    }
//}

fun getTextFromAsset(context: Context, fileName: String): String {
    var inputStream: InputStream? = null
    var result = ""
    try {
        inputStream = context.assets.open(fileName)
        val length: Int = inputStream.available()
        val buffer = ByteArray(length)
        inputStream.read(buffer)
        result = String(buffer, Charsets.UTF_8)
    } catch (e: IOException) {
        e.printStackTrace()
    } finally {
        inputStream?.close()
    }
    return result
}