package com.moyu.chuanqirensheng.application.inittask

import android.content.BroadcastReceiver
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.sub.bill.BillingManager.api
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.core.APPID
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory

/**
 * 微信订单sdk初始化
 */
class BillingTask : JobContent<Context> {

    override fun execute(context: Context) {
        // APP_ID 替换为你的应用从官方网站申请到的合法appID
        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {
            // 通过WXAPIFactory工厂，获取IWXAPI的实例
            api = WXAPIFactory.createWXAPI(GameApp.instance, null)

            // 将应用的appId注册到微信
            api?.registerApp(APPID)

            //建议动态监听微信启动广播进行注册到微信
            ContextCompat.registerReceiver(GameApp.instance, object : BroadcastReceiver() {
                override fun onReceive(context: android.content.Context?, intent: Intent?) {
                    // 将该app注册到微信
                    api?.registerApp(APPID)
                }
            }, IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP), ContextCompat.RECEIVER_EXPORTED)

        }
    }
}
