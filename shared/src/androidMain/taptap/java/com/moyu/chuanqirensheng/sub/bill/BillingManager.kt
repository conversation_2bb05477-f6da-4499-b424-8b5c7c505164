
package com.moyu.chuanqirensheng.sub.bill


import android.content.Context
import android.util.Base64
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.api.CommonResult
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_PAY_DATA
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.util.AESUtil.encodeToBase64
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.core.APPID
import com.moyu.core.AppWrapper
import com.moyu.core.merchantId
import com.moyu.core.model.Sell
import com.moyu.core.wcMusic
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.openapi.IWXAPI
import io.ktor.utils.io.core.toByteArray
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import org.jetbrains.compose.resources.getString
import shared.generated.resources.Res
import shared.generated.resources.child_pay_tips
import shared.generated.resources.error_order
import shared.generated.resources.error_order_info
import shared.generated.resources.is_paying
import shared.generated.resources.order_error
import shared.generated.resources.pay_blocking_tips
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec

object BillingManager {
    var api: IWXAPI? = null
    val payClientDataList = mutableStateListOf<PayClientData>()

    val isPaying = mutableStateOf(false)

    init {
        getListObject<PayClientData>(KEY_PAY_DATA).takeIf { it.isNotEmpty() }?.let {
            payClientDataList.addAll(it)
        }
    }

    //initiate purchase on consume button click
    fun consume(productId: String) {

    }

    fun queryAsync() {

    }

    suspend fun prepay(sell: Sell, award: () -> Unit) {
        if (isPaying.value) {
            AppWrapper.getStringKmp(Res.string.is_paying).toast()
            return
        }
        if (GameApp.instance.isAgeUnder8()) {
            AppWrapper.getStringKmp(Res.string.child_pay_tips).toast()
            return
        }

        isPaying.value = true
        AppWrapper.globalScope.launch(Dispatchers.IO) {
            async {
                // 防止玩家快速点击，每两秒最多下单1次
                delay(2000)
                isPaying.value = false
            }
        }
        val realMoney = if (DebugManager.oneCentShop) 1 else sell.price * 100
        val userId = GameApp.instance.getObjectId()!!
        getPrepay(userId, sell.name, realMoney)?.copy(award = award)
            ?.let { payClientData ->
                payClientDataList.add(payClientData)
                setListObject(KEY_PAY_DATA, payClientDataList)
                pay(payClientData)
            } ?: AppWrapper.getStringKmp(Res.string.order_error).toast()

//        PaymentHelper.processPayment(sell.price.toDouble(), onPaymentAllowed = {
//            isPaying.value = true
//            AppWrapper.globalScope.launch(Dispatchers.IO) {
//                async {
//                    // 防止玩家快速点击，每两秒最多下单1次
//                    delay(2000)
//                    isPaying.value = false
//                }
//            }
//            val realMoney = if (DebugManager.oneCentShop) 1 else sell.price * 100
//            val userId = GameApp.instance.getObjectId()!!
//            AppWrapper.globalScope.launch(Dispatchers.IO) {
//                getPrepay(userId, sell.name, realMoney)?.copy(award = award)
//                    ?.let { payClientData ->
//                        payClientDataList.add(payClientData)
//                        setListObject(KEY_PAY_DATA, payClientDataList)
//                        pay(payClientData)
//                    } ?: AppWrapper.getStringKmp(Res.string.order_error).toast()
//            }
//        }, onPaymentRestricted = {
//            AppWrapper.getStringKmp(Res.string.child_pay_tips2).toast()
//        }, onError = {
//            it.toast()
//        })
    }

    // todo 等有版号后需要删除测试代码
    suspend fun checkIfOk(payClientData: PayClientData): CommonResult {
        var repeat = 20
        while (repeat-- > 0) {
            delay(1000)
            checkPayOk(payClientData).takeIf { it.succeeded }?.let { return it }
            delay(2000)
        }
        return checkPayOk(payClientData)
    }

    fun pay(payClientData: PayClientData) {
        val request = PayReq().let { request ->
            request.appId = APPID
            request.prepayId = payClientData.prepayId
            request.partnerId = merchantId
            request.packageValue = "Sign=WXPay"
            request.timeStamp = getCurrentTime().toString()
            request.nonceStr = UUID.generateUUID().toString().take(32)
            val sdkSign = sha256AndBase64(
                request.appId + "\n" + request.timeStamp + "\n" + request.nonceStr + "\n" + request.prepayId + "\n",
                getPrivateKeyFromAssets(GameApp.instance, wcMusic)
            )
            request.sign = sdkSign
            request
        }
        api?.sendReq(request)
    }

    fun checkIfPayed(payClientData: PayClientData) {
        Dialogs.payBlockingDialog.value = AppWrapper.getStringKmp(Res.string.pay_blocking_tips)
        AppWrapper.globalScope.launch(Dispatchers.Main) {
            checkIfOk(payClientData).let {
                if (it.succeeded) {
                    removePayClientData(payClientData)
                    Dialogs.payBlockingDialog.value = null
                    checkIfCanAward(payClientData)
                } else {
                    if (Dialogs.payBlockingDialog.value != null) {
                        it.message.toast()
                        Dialogs.alertDialog.value = CommonAlert(
                            title = AppWrapper.getStringKmp(Res.string.error_order), content =
                                runBlocking {
                                    getString(
                                        Res.string.error_order_info,
                                        payClientData.orderName,
                                        payClientData.prepayId + payClientData.userId,
                                        GameApp.instance.getUserName() ?: "",
                                        it.message
                                    )
                                }
                        )
                    }
                    Dialogs.payBlockingDialog.value = null
                }
            }
        }
    }

    private fun checkIfCanAward(payClientData: PayClientData) {
        if (getBooleanFlowByKey(payClientData.tradeNo.reversed().replace("-", ""))) {
            return
        } else {
            setBooleanValueByKey(payClientData.tradeNo.reversed().replace("-", ""), true)
            payClientData.award()
//            PaymentHelper.reportPaymentSuccess(
//                amountInYuan = if (DebugManager.oneCentShop) 100.0 else payClientData.totalMoneyInCent.toDouble() / 100,
//                onSuccess = {
//
//                }
//            )
        }
    }

    fun removePayClientData(payData: PayClientData) {
        payClientDataList.remove(payData)
        setListObject(KEY_PAY_DATA, payClientDataList)
    }
}


fun sha256AndBase64(str: String, privateKey: PrivateKey): String {
    val signedData = signData(str, privateKey)
    return encodeToBase64(signedData)
}

fun signData(data: String, privateKey: PrivateKey): ByteArray {
    val signature = Signature.getInstance("SHA256withRSA")
    signature.initSign(privateKey)
    signature.update(data.toByteArray())
    return signature.sign()
}

fun getPrivateKeyFromAssets(context: Context, fileName: String): PrivateKey {
    context.assets.open(fileName).use { inputStream ->
        val keyBytes = inputStream.readBytes()
        // 移除 PEM 文件的头部和尾部标记
        val pem = keyBytes.decodeToString()
            .replace("-----BEGIN PRIVATE KEY-----", "")
            .replace("-----END PRIVATE KEY-----", "")
            .replace("\\s".toRegex(), "")

        val decoded = Base64.decode(pem, Base64.NO_WRAP)
        val spec = PKCS8EncodedKeySpec(decoded)
        val keyFactory = KeyFactory.getInstance("RSA")
        return keyFactory.generatePrivate(spec)
    }
}