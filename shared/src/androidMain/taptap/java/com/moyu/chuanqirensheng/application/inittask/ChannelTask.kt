package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent

/**
 * 穿山甲广告sdk初始化
 */
class ChannelTask : JobContent<Context> {
    override fun execute(context: Context) {
        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {

        }
    }
}