package com.moyu.chuanqirensheng.sub.bill

import com.moyu.chuanqirensheng.api.CommonResult
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.util.AESUtil
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.AppWrapper
import com.moyu.core.util.RANDOM
import kotlinx.serialization.json.Json
import shared.generated.resources.Res
import shared.generated.resources.network_error
import shared.generated.resources.order_error


suspend fun getPrepay(
    userId: String,
    orderName: String,
    totalMoneyInCent: Int
): PayClientData? {
    val data = PayClientData(userId, orderName, totalMoneyInCent, random = RANDOM.nextInt())
    val loginString = Json.encodeToString(PayClientData.serializer(), data)
    val encoded = AdUtil.encodeText(loginString) ?: ""
    try {
        val result = getPrepay(encoded, getVersionCode())
        result.succeeded.takeIf { it }?.let {
            AESUtil.decrypt(result.message, GameApp.instance.getObjectId() ?: "")?.let { jsonString->
                json.decodeFromString(PayClientData.serializer(), jsonString).takeIf {
                    it.random == data.random + 105 // 魔数
                }?.let {
                    return it
                }
            }
        }
    } catch (e: Exception) {
        AppWrapper.getStringKmp(Res.string.network_error).toast()
        println(e)
    }
    return null
}

suspend fun checkPayOk(payClientData: PayClientData): CommonResult {
    val data = payClientData.copy(random = RANDOM.nextInt())
    val loginString = Json.encodeToString(PayClientData.serializer(), data)
    val encoded = AdUtil.encodeText(loginString) ?: ""
    try {
        val result = checkPayOk(encoded, getVersionCode())
        result.succeeded.takeIf { it }?.let {
            AESUtil.decrypt(result.message, GameApp.instance.getObjectId() ?: "")?.let { jsonString->
                json.decodeFromString(PayClientData.serializer(), jsonString).takeIf {
                    it.random == data.random + 105 // 魔数
                }?.let {
                    return result
                }
            }
        }?: return result
    } catch (e: Exception) {
        AppWrapper.getStringKmp(Res.string.network_error).toast()
        println(e)
    }
    return CommonResult(false, AppWrapper.getStringKmp(Res.string.order_error))
}