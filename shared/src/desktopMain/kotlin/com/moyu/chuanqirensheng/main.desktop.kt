package com.moyu.chuanqirensheng

import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.moyu.chuanqirensheng.application.GameAppCompose
import com.moyu.chuanqirensheng.application.inittask.ConfigTask
import com.moyu.chuanqirensheng.application.inittask.DataStoreTask
import com.moyu.chuanqirensheng.application.inittask.LifecycleTask
import com.moyu.chuanqirensheng.application.inittask.MusicTask
import com.moyu.chuanqirensheng.application.inittask.ReportTask
import com.moyu.chuanqirensheng.platform.setAchievement
import com.moyu.chuanqirensheng.steam.SteamUtil
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.ContextImpl
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking

var isFirstLoad = false

@Composable
fun deskMainView() {
    val lifecycleOwner = LocalLifecycleOwner.current
    var gameContext: Context = ContextImpl(lifecycleOwner)

    if (!isFirstLoad) {
        isFirstLoad = true

        SteamUtil.loadSteamSdk()

        // 这个必须放到最前面，否则资源错误
        runBlocking {
            LanguageManager.init()

            val asyncTasks = listOf(
//            async { UncompressTask().execute(gameContext) },
                async { ConfigTask().execute(gameContext) },
                async { MusicTask().execute(gameContext) }
            )

            ReportTask().execute(gameContext)
            LifecycleTask().execute(gameContext)

            // Await all async tasks to ensure runBlocking waits for their completion
            asyncTasks.awaitAll()
        }

        LaunchedEffect(Unit) {
            DataStoreTask().execute(gameContext)
        }
    }

    GameAppCompose {}
}