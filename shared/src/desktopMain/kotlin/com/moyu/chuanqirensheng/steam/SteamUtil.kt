package com.moyu.chuanqirensheng.steam

import androidx.compose.runtime.MutableState
import androidx.compose.ui.graphics.ImageBitmap
import com.codedisaster.steamworks.SteamException
import com.codedisaster.steamworks.SteamUserStats
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.seiko.imageloader.asImageBitmap
import org.jetbrains.skia.Bitmap
import org.jetbrains.skia.ColorAlphaType
import org.jetbrains.skia.ColorType
import org.jetbrains.skia.ImageInfo
import java.io.File
import java.nio.ByteBuffer


object SteamUtil {

    private lateinit var steamClient: SteamClient

//    var avatarBitmap =  mutableStateOf<ImageBitmap?>(null) // 用户头像
    var avatarBitmap: ImageBitmap? = null // 用户头像

    fun getUserId(): String {
        println("=-== userid: ${steamClient.user.steamID.toString()}")
        return steamClient.user.steamID.toString()
    }

    fun getUserName(): String {
        return steamClient.friends.personaName
    }

    fun loadSteamSdk() {
        val resourcesDir = File(System.getProperty("compose.application.resources.dir"))
        val client = SteamClient()
        client.clientMain(resourcesDir.absolutePath)

        steamClient = client

        // native 的 accountId 转为 steamID64
//        val steamID64 = 76561197960265728UL + steamClient.user.steamID.accountID.toUInt()
//        println("=-== steamID64: $steamID64")

        getAvatar() // 获取用户头像
        loadLeaderBoard() // 获取榜单信息
    }

    // 达成 steam 中的成就。名称对应游戏中 task id
    fun setAchievement(name: String) {
        steamClient.userStats.setAchievement(name)
    }

    fun loadLeaderBoard() {
        steamClient.userStats.findLeaderboard(steamClient.endingNumBoardName)

//        AppWrapper.globalScope.launch {
//            steamClient.leaderboardFlow.collectLatest {
//                println("leaderboardFlow: $it")
//                val name = steamClient.userStats.getLeaderboardName(it)
//                println("leaderboardFlow name: $name")
//            }
//        }
    }

    fun refreshBoardList(type: Int, callback: ((list:List<RankData>) -> Unit)?) {
        if (type == 1) {
            steamClient.endingNumCallback = callback
            steamClient.userStats.downloadLeaderboardEntries(
                steamClient.endingNumBoard,
                SteamUserStats.LeaderboardDataRequest.Global,
                0,
                100
            )
        }
    }

    // 设置榜单分数
    fun setLeaderBoardScore(score: Int, type: Int) {
        steamClient.userStats.uploadLeaderboardScore(
            steamClient.endingNumBoard,
            SteamUserStats.LeaderboardUploadScoreMethod.KeepBest, score, intArrayOf()
        )
    }

    // 获取头像
    fun getAvatar() {
        val avatar = steamClient.friends.getLargeFriendAvatar(steamClient.user.steamID)
        if (avatar != 0) {
            val w = steamClient.utils.getImageWidth(avatar)
            val h = steamClient.utils.getImageHeight(avatar)
            println("=-== avatar size: " + w + "x" + h + " pixels")

            val image = ByteBuffer.allocateDirect(w * h * 4)
            try {
                if (steamClient.utils.getImageRGBA(avatar, image)) {
                    avatarBitmap = createImageBitmapFromByteBuffer(image, w, h)
                }
            } catch (e: SteamException) {
                e.printStackTrace()
            }
        }
    }

    private fun createImageBitmapFromByteBuffer(buffer: ByteBuffer?, width: Int, height: Int): ImageBitmap {
        val imageInfo = ImageInfo(width, height, ColorType.RGBA_8888, ColorAlphaType.OPAQUE)
        val bitmap = Bitmap().apply {
            allocPixels(imageInfo)
            buffer?.let {
                val byteArray = ByteArray(it.remaining())
                it.get(byteArray)
                installPixels(byteArray)
            }
        }

        return bitmap.asImageBitmap()
    }

}
