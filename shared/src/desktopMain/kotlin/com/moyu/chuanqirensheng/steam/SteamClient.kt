package com.moyu.chuanqirensheng.steam

import com.codedisaster.steamworks.SteamAPICall
import com.codedisaster.steamworks.SteamApps
import com.codedisaster.steamworks.SteamAuth.AuthSessionResponse
import com.codedisaster.steamworks.SteamAuthTicket
import com.codedisaster.steamworks.SteamException
import com.codedisaster.steamworks.SteamFriends
import com.codedisaster.steamworks.SteamFriends.PersonaChange
import com.codedisaster.steamworks.SteamFriendsCallback
import com.codedisaster.steamworks.SteamID
import com.codedisaster.steamworks.SteamLeaderboardEntriesHandle
import com.codedisaster.steamworks.SteamLeaderboardEntry
import com.codedisaster.steamworks.SteamLeaderboardHandle
import com.codedisaster.steamworks.SteamPublishedFileID
import com.codedisaster.steamworks.SteamRemoteStorage
import com.codedisaster.steamworks.SteamRemoteStorageCallback
import com.codedisaster.steamworks.SteamResult
import com.codedisaster.steamworks.SteamUGC
import com.codedisaster.steamworks.SteamUGC.ItemDownloadInfo
import com.codedisaster.steamworks.SteamUGC.ItemInstallInfo
import com.codedisaster.steamworks.SteamUGCCallback
import com.codedisaster.steamworks.SteamUGCDetails
import com.codedisaster.steamworks.SteamUGCHandle
import com.codedisaster.steamworks.SteamUGCQuery
import com.codedisaster.steamworks.SteamUser
import com.codedisaster.steamworks.SteamUserCallback
import com.codedisaster.steamworks.SteamUserStats
import com.codedisaster.steamworks.SteamUserStatsCallback
import com.codedisaster.steamworks.SteamUtils
import com.codedisaster.steamworks.SteamUtilsCallback
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.platform.createHttpClient
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.nio.ByteBuffer

import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

val webApiKey = "4E09B7F0D594212051E5AD856AB0EE32"

class SteamClient : SteamApp() {
    lateinit var user: SteamUser
    lateinit var userStats: SteamUserStats
    lateinit var remoteStorage: SteamRemoteStorage
    lateinit var ugc: SteamUGC
    lateinit var utils: SteamUtils
    lateinit var apps: SteamApps
    lateinit var friends: SteamFriends

    val endingNumBoardName = "endingsNumRanks"
    var endingNumBoard: SteamLeaderboardHandle? = null // 征服榜单

    var endingNumCallback: ((list:List<RankData>) -> Unit)? = null

    private val userCallback: SteamUserCallback = object : SteamUserCallback {
        override fun onAuthSessionTicket(authTicket: SteamAuthTicket, result: SteamResult) {
            println("==steam Auth session ticket result: $result")
        }

        override fun onValidateAuthTicket(steamID: SteamID, authSessionResponse: AuthSessionResponse, ownerSteamID: SteamID) {
            println(
                "==steam Validate auth ticket: steamID=" + steamID.accountID +
                        ", response=" + authSessionResponse.name + ", owner=" + ownerSteamID.accountID
            )
        }

        override fun onMicroTxnAuthorization(appID: Int, orderID: Long, authorized: Boolean) {
            println(
                "==steam Micro transaction authorization: appID=" + appID +
                        ", orderID=" + orderID + ", authorized=" + authorized
            )
        }

        override fun onEncryptedAppTicket(result: SteamResult) {
            println("==steam Encrypted app ticket result: $result")
        }
    }

    private val userStatsCallback: SteamUserStatsCallback = object : SteamUserStatsCallback {
        override fun onUserStatsReceived(gameId: Long, steamIDUser: SteamID, result: SteamResult) {
            println(
                "==steam User stats received: gameId=" + gameId + ", userId=" + steamIDUser.accountID +
                        ", result=" + result.toString()
            )

            val numAchievements = userStats.numAchievements
            println("Num of achievements: $numAchievements")

            for (i in 0 until numAchievements) {
                val name = userStats.getAchievementName(i)
                val achieved = userStats.isAchieved(name, false)
                println("# " + i + " : name=" + name + ", achieved=" + (if (achieved) "yes" else "no"))
            }
        }

        override fun onUserStatsStored(gameId: Long, result: SteamResult) {
            println(
                "==steam User stats stored: gameId=" + gameId +
                        ", result=" + result.toString()
            )
        }

        override fun onUserStatsUnloaded(steamIDUser: SteamID) {
            println("==steam User stats unloaded: userId=" + steamIDUser.accountID)
        }

        override fun onUserAchievementStored(
            gameId: Long, isGroupAchievement: Boolean, achievementName: String,
            curProgress: Int, maxProgress: Int
        ) {
            println(
                "==steam User achievement stored: gameId=" + gameId + ", name=" + achievementName +
                        ", progress=" + curProgress + "/" + maxProgress
            )
        }

        override fun onLeaderboardFindResult(leaderboard: SteamLeaderboardHandle, found: Boolean) {
            println(
                "==steam Leaderboard find result: handle=" + leaderboard.toString() +
                        ", found=" + (if (found) "yes" else "no")
            )

            if (found) {
                println(
                    "==steam Leaderboard: name=" + userStats.getLeaderboardName(leaderboard) +
                            ", entries=" + userStats.getLeaderboardEntryCount(leaderboard)
                )

                if (userStats.getLeaderboardName(leaderboard) == endingNumBoardName) {
                    endingNumBoard = leaderboard
//                    AppWrapper.globalScope.launch {
//                        leaderboardFlow.emit(leaderboard)
//                    }
                } else {
                    println("Leaderboard name not match")
                }
            }
        }

        override fun onLeaderboardScoresDownloaded(
            leaderboard: SteamLeaderboardHandle,
            entries: SteamLeaderboardEntriesHandle,
            numEntries: Int
        ) {
            println(
                "==steam Leaderboard scores downloaded: handle=" + leaderboard.toString() +
                        ", entries=" + entries.toString() + ", count=" + numEntries
            )

            if (userStats.getLeaderboardName(leaderboard) == endingNumBoardName) {
                val details = IntArray(16)


                var steamids: MutableList<String> = mutableListOf<String>()
                var rankDataList = mutableListOf<RankData>()
                for (i in 0 until numEntries) {
                    val entry = SteamLeaderboardEntry()
                    if (userStats.getDownloadedLeaderboardEntry(entries, i, entry, details)) {
                        val numDetails = entry.numDetails

                        println(
                            "==steam Leaderboard entry #" + i +
                                    ": accountID=" + entry.steamIDUser.accountID +
                                    ", globalRank=" + entry.globalRank +
                                    ", score=" + entry.score +
                                    ", numDetails=" + numDetails
                        )

                        if (friends.requestUserInformation(entry.steamIDUser, true)) {
                            println("  ... requested user information for entry")
                        } else {
                            // 本地已有用户数据
                            var data = RankData(
                                userId = entry.steamIDUser.toString(),
                                userName = friends.getFriendPersonaName(entry.steamIDUser),
                                userPic = "",
                                endingNum = entry.score,
                                time = 0,
                                versionCode = 0,
                                talentLevel = 0,
                                tcgValue = 0,
                                electric = 0,
                                level = 0,
                                liked = 0,
                                pvpScore = 0,
                                pvpLastScore = 0,
                                platformChannel = "desktop",
                            )

                            rankDataList.add(data)

                            val steamID64 = 76561197960265728UL + entry.steamIDUser.accountID.toUInt()
                            steamids.add("$steamID64")
                        }
                    }
                }

                val maps = getSteamAvatars(steamids, webApiKey)
                for (i in 0 until rankDataList.size) {
                    rankDataList[i].userPic = maps[steamids[i]] ?: ""
                }

                endingNumCallback?.invoke(rankDataList)
            }
        }

        override fun onLeaderboardScoreUploaded(
            success: Boolean,
            leaderboard: SteamLeaderboardHandle,
            score: Int,
            scoreChanged: Boolean,
            globalRankNew: Int,
            globalRankPrevious: Int
        ) {
            println(
                "Leaderboard score uploaded: " + (if (success) "yes" else "no") +
                        ", handle=" + leaderboard.toString() +
                        ", score=" + score +
                        ", changed=" + (if (scoreChanged) "yes" else "no") +
                        ", globalRankNew=" + globalRankNew +
                        ", globalRankPrevious=" + globalRankPrevious
            )
        }

        override fun onNumberOfCurrentPlayersReceived(success: Boolean, players: Int) {
            println("==steam Number of current players received: $players")
        }

        override fun onGlobalStatsReceived(gameId: Long, result: SteamResult) {
            println("==steam Global stats received: gameId=$gameId, result=$result")
        }
    }

    private val remoteStorageCallback: SteamRemoteStorageCallback = object : SteamRemoteStorageCallback {
        override fun onFileWriteAsyncComplete(result: SteamResult) {
        }

        override fun onFileReadAsyncComplete(fileReadAsync: SteamAPICall, result: SteamResult, offset: Int, read: Int) {
        }

        override fun onFileShareResult(fileHandle: SteamUGCHandle, fileName: String, result: SteamResult) {
            println(
                "==steam Remote storage file share result: handle='" + fileHandle.toString() +
                        ", name=" + fileName + "', result=" + result.toString()
            )
        }

        override fun onDownloadUGCResult(fileHandle: SteamUGCHandle, result: SteamResult) {
            println(
                "==steam Remote storage download UGC result: handle='" + fileHandle.toString() +
                        "', result=" + result.toString()
            )

            val buffer = ByteBuffer.allocateDirect(1024)
            var offset = 0
            var bytesRead: Int

            do {
                bytesRead = remoteStorage.ugcRead(
                    fileHandle, buffer, buffer.limit(), offset,
                    SteamRemoteStorage.UGCReadAction.ContinueReadingUntilFinished
                )
                offset += bytesRead
            } while (bytesRead > 0)

            println("==steam Read $offset bytes from handle=$fileHandle")
        }

        override fun onPublishFileResult(publishedFileID: SteamPublishedFileID, needsToAcceptWLA: Boolean, result: SteamResult) {
            println(
                "==steam Remote storage publish file result: publishedFileID=" + publishedFileID.toString() +
                        ", needsToAcceptWLA=" + needsToAcceptWLA + ", result=" + result.toString()
            )
        }

        override fun onUpdatePublishedFileResult(publishedFileID: SteamPublishedFileID, needsToAcceptWLA: Boolean, result: SteamResult) {
            println(
                "Remote storage update published file result: publishedFileID=" + publishedFileID.toString() +
                        ", needsToAcceptWLA=" + needsToAcceptWLA + ", result=" + result.toString()
            )
        }

        override fun onPublishedFileSubscribed(publishedFileID: SteamPublishedFileID, appID: Int) {
        }

        override fun onPublishedFileUnsubscribed(publishedFileID: SteamPublishedFileID, appID: Int) {
        }

        override fun onPublishedFileDeleted(publishedFileID: SteamPublishedFileID, appID: Int) {
        }
    }

    private val ugcCallback: SteamUGCCallback = object : SteamUGCCallback {
        override fun onUGCQueryCompleted(
            query: SteamUGCQuery, numResultsReturned: Int, totalMatchingResults: Int,
            isCachedData: Boolean, result: SteamResult
        ) {
            println(
                "UGC query completed: handle=" + query.toString() + ", " + numResultsReturned + " of " +
                        totalMatchingResults + " results returned, result=" + result.toString()
            )

            for (i in 0 until numResultsReturned) {
                val details = SteamUGCDetails()
                ugc.getQueryUGCResult(query, i, details)
                printUGCDetails("UGC details #$i", details)
            }

            ugc.releaseQueryUserUGCRequest(query)
        }

        override fun onSubscribeItem(publishedFileID: SteamPublishedFileID, result: SteamResult) {
            println("==steam Subscribe item result: publishedFileID=$publishedFileID, result=$result")
        }

        override fun onUnsubscribeItem(publishedFileID: SteamPublishedFileID, result: SteamResult) {
            println("==steam Unsubscribe item result: publishedFileID=$publishedFileID, result=$result")
        }

        override fun onRequestUGCDetails(details: SteamUGCDetails, result: SteamResult) {
            println("Request details result: result=$result")
            printUGCDetails("UGC details ", details)
        }

        override fun onCreateItem(publishedFileID: SteamPublishedFileID, needsToAcceptWLA: Boolean, result: SteamResult) {
        }

        override fun onSubmitItemUpdate(publishedFileID: SteamPublishedFileID, needsToAcceptWLA: Boolean, result: SteamResult) {
        }

        override fun onDownloadItemResult(appID: Int, publishedFileID: SteamPublishedFileID, result: SteamResult) {
        }

        override fun onUserFavoriteItemsListChanged(publishedFileID: SteamPublishedFileID, wasAddRequest: Boolean, result: SteamResult) {
        }

        override fun onSetUserItemVote(publishedFileID: SteamPublishedFileID, voteUp: Boolean, result: SteamResult) {
        }

        override fun onGetUserItemVote(publishedFileID: SteamPublishedFileID, votedUp: Boolean, votedDown: Boolean, voteSkipped: Boolean, result: SteamResult) {
        }

        private fun printUGCDetails(prefix: String, details: SteamUGCDetails) {
            println(
                prefix +
                        ": publishedFileID=" + details.publishedFileID.toString() +
                        ", result=" + details.result.name +
                        ", type=" + details.fileType.name +
                        ", title='" + details.title + "'" +
                        ", description='" + details.description + "'" +
                        ", tags='" + details.tags + "'" +
                        ", fileName=" + details.fileName +
                        ", fileHandle=" + details.fileHandle.toString() +
                        ", previewFileHandle=" + details.previewFileHandle.toString() +
                        ", url=" + details.url
            )
        }

        override fun onStartPlaytimeTracking(result: SteamResult) {
        }

        override fun onStopPlaytimeTracking(result: SteamResult) {
        }

        override fun onStopPlaytimeTrackingForAllItems(result: SteamResult) {
        }

        override fun onDeleteItem(publishedFileID: SteamPublishedFileID, result: SteamResult) {
        }
    }

    private val friendsCallback: SteamFriendsCallback = object : SteamFriendsCallback {
        override fun onSetPersonaNameResponse(success: Boolean, localSuccess: Boolean, result: SteamResult) {
        }

        override fun onPersonaStateChange(steamID: SteamID, change: PersonaChange) {
            // 调用 requestUserInformation 时，如果本地没有数据，会异步触发这个回调
            when (change) {
                PersonaChange.Name -> println(
                    "Persona name received: " +
                            "accountID=" + steamID.accountID +
                            ", name='" + friends.getFriendPersonaName(steamID) + "'"
                )

                else -> println(
                    "Persona state changed (unhandled): " +
                            "accountID=" + steamID.accountID +
                            ", change=" + change.name
                )
            }
        }

        override fun onGameOverlayActivated(active: Boolean) {
        }

        override fun onGameLobbyJoinRequested(steamIDLobby: SteamID, steamIDFriend: SteamID) {
        }

        override fun onAvatarImageLoaded(steamID: SteamID, image: Int, width: Int, height: Int) {
        }

        override fun onFriendRichPresenceUpdate(steamIDFriend: SteamID, appID: Int) {
        }

        override fun onGameRichPresenceJoinRequested(steamIDFriend: SteamID, connect: String) {
        }

        override fun onGameServerChangeRequested(server: String, password: String) {
        }
    }

//    private val clMessageHook = object : SteamAPIWarningMessageHook {
//        override fun onWarningMessage(level: Int, message: String) {
//            println("Steam API warning message: $message")
//        }
//    }

     private val utilsCallback = object : SteamUtilsCallback {
        override fun onSteamShutdown() {
            println("Steam client wants to shut down!")
        }
    }

    override fun registerInterfaces() {
        user = SteamUser(userCallback)
        userStats = SteamUserStats(userStatsCallback)
        remoteStorage = SteamRemoteStorage(remoteStorageCallback)
        ugc = SteamUGC(ugcCallback)
        utils = SteamUtils(utilsCallback)
//        utils.setWarningMessageHook(clMessageHook)

        apps = SteamApps()
        friends = SteamFriends(friendsCallback)

//        println("Local user account ID: " + user!!.steamID.accountID)
//        println("Local user steam ID: " + SteamID.getNativeHandle(user!!.steamID))
//        println("Local user friends name: " + friends.personaName)
//        println("App ID: " + utils.appID)
//        println("App build ID: " + apps.appBuildId)
//        println("App owner: " + apps.appOwner.accountID)
//        println("Current game language: " + apps.currentGameLanguage)
//        println("Available game languages: " + apps.availableGameLanguages)
    }

    override fun unregisterInterfaces() {
        user!!.dispose()
        userStats.dispose()
        remoteStorage.dispose()
        ugc.dispose()
        utils.dispose()
        apps.dispose()
        friends.dispose()
    }

    @Throws(SteamException::class)
    override fun processUpdate() {
    }

    @Throws(SteamException::class)
    override fun processInput(input: String) {
        if (input.startsWith("stats global ")) {
            val cmd = input.substring("stats global ".length).split(" ".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            if (cmd.size > 0) {
                if (cmd[0] == "request") {
                    var days = 0
                    if (cmd.size > 1) {
                        days = cmd[1].toInt()
                    }
                    userStats.requestGlobalStats(days)
                } else if (cmd[0] == "players") {
                    userStats.numberOfCurrentPlayers
                } else if (cmd[0] == "lget" && cmd.size > 1) {
                    var days = 0
                    if (cmd.size > 2) {
                        days = cmd[2].toInt()
                    }
                    if (days == 0) {
                        val value = userStats.getGlobalStat(cmd[1], -1)
                        println("global stat (L) '" + cmd[1] + "' = " + value)
                    } else {
                        val data = LongArray(days)
                        val count = userStats.getGlobalStatHistory(cmd[1], data)
                        print("global stat history (L) for $count of $days days:")
                        for (i in 0 until count) {
                            print(" " + data[i].toString())
                        }
                        println()
                    }
                } else if (cmd[0] == "dget" && cmd.size > 1) {
                    var days = 0
                    if (cmd.size > 2) {
                        days = cmd[2].toInt()
                    }
                    if (days == 0) {
                        val value = userStats.getGlobalStat(cmd[1], -1.0)
                        println("global stat (D) '" + cmd[1] + "' = " + value)
                    } else {
                        val data = DoubleArray(days)
                        val count = userStats.getGlobalStatHistory(cmd[1], data)
                        print("global stat history (D) for $count of $days days:")
                        for (i in 0 until count) {
                            print(" " + data[i].toString())
                        }
                        println()
                    }
                }
            }
        } else if (input == "stats request") {
            userStats.requestCurrentStats()
        } else if (input == "stats store") {
            userStats.storeStats()
        } else if (input.startsWith("achievement set ")) {
            val achievementName = input.substring("achievement set ".length)
            println("- setting $achievementName to 'achieved'")
            userStats.setAchievement(achievementName)
        } else if (input.startsWith("achievement clear ")) {
            val achievementName = input.substring("achievement clear ".length)
            println("- clearing $achievementName")
            userStats.clearAchievement(achievementName)
        } else if (input == "file list") {
            val numFiles = remoteStorage.fileCount
            println("Num of files: $numFiles")

            for (i in 0 until numFiles) {
                val sizes = IntArray(1)
                val file = remoteStorage.getFileNameAndSize(i, sizes)
                val exists = remoteStorage.fileExists(file)
                println("# " + i + " : name=" + file + ", size=" + sizes[0] + ", exists=" + (if (exists) "yes" else "no"))
            }
        } else if (input.startsWith("file write ")) {
            val path = input.substring("file write ".length)
            val file = File(path)
            try {
                FileInputStream(file).use { `in` ->
                    val remoteFile = remoteStorage.fileWriteStreamOpen(path)
                    if (remoteFile != null) {
                        val bytes = ByteArray(1024)
                        var bytesRead: Int
                        while ((`in`.read(bytes, 0, bytes.size).also { bytesRead = it }) > 0) {
                            val buffer = ByteBuffer.allocateDirect(bytesRead)
                            buffer.put(bytes, 0, bytesRead)
                            buffer.flip()
                            remoteStorage.fileWriteStreamWriteChunk(remoteFile, buffer)
                        }
                        remoteStorage.fileWriteStreamClose(remoteFile)
                    }
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        } else if (input.startsWith("file delete ")) {
            val path = input.substring("file delete ".length)
            if (remoteStorage.fileDelete(path)) {
                println("deleted file '$path'")
            }
        } else if (input.startsWith("file share ")) {
            remoteStorage.fileShare(input.substring("file share ".length))
        } else if (input.startsWith("file publish ")) {
            val paths = input.substring("file publish ".length).split(" ".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            if (paths.size >= 2) {
                println("publishing file: " + paths[0] + ", preview file: " + paths[1])
                remoteStorage.publishWorkshopFile(
                    paths[0], paths[1], utils.appID,
                    "Test UGC!", "Dummy UGC file published by test application.",
                    SteamRemoteStorage.PublishedFileVisibility.Private, null,
                    SteamRemoteStorage.WorkshopFileType.Community
                )
            }
        } else if (input.startsWith("file republish ")) {
            val paths = input.substring("file republish ".length).split(" ".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            if (paths.size >= 3) {
                println("republishing id: " + paths[0] + ", file: " + paths[1] + ", preview file: " + paths[2])

                val fileID = SteamPublishedFileID(paths[0].toLong())

                val updateHandle = remoteStorage.createPublishedFileUpdateRequest(fileID)
                if (updateHandle != null) {
                    remoteStorage.updatePublishedFileFile(updateHandle, paths[1])
                    remoteStorage.updatePublishedFilePreviewFile(updateHandle, paths[2])
                    remoteStorage.updatePublishedFileTitle(updateHandle, "Updated Test UGC!")
                    remoteStorage.updatePublishedFileDescription(updateHandle, "Dummy UGC file *updated* by test application.")
                    remoteStorage.commitPublishedFileUpdate(updateHandle)
                }
            }
        } else if (input == "ugc query") {
            val query = ugc.createQueryUserUGCRequest(
                user!!.steamID.accountID, SteamUGC.UserUGCList.Subscribed,
                SteamUGC.MatchingUGCType.UsableInGame, SteamUGC.UserUGCListSortOrder.TitleAsc,
                utils.appID, utils.appID, 1
            )

            if (query.isValid) {
                println("sending UGC query: $query")
                //ugc.setReturnTotalOnly(query, true);
                ugc.sendQueryUGCRequest(query)
            }
        } else if (input.startsWith("ugc download ")) {
            val name = input.substring("ugc download ".length)
            val handle = SteamUGCHandle(name.toLong(16))
            remoteStorage.ugcDownload(handle, 0)
        } else if (input.startsWith("ugc subscribe ")) {
            val id = input.substring("ugc subscribe ".length).toLong(16)
            ugc.subscribeItem(SteamPublishedFileID(id))
        } else if (input.startsWith("ugc unsubscribe ")) {
            val id = input.substring("ugc unsubscribe ".length).toLong(16)
            ugc.unsubscribeItem(SteamPublishedFileID(id))
        } else if (input.startsWith("ugc state ")) {
            val id = input.substring("ugc state ".length).toLong(16)
            val itemStates = ugc.getItemState(SteamPublishedFileID(id))
            println("UGC item states: " + itemStates.size)
            for (itemState in itemStates) {
                println("  " + itemState.name)
            }
        } else if (input.startsWith("ugc details ")) {
            println("requesting UGC details (deprecated API call)")
            val id = input.substring("ugc details ".length).toLong(16)
            ugc.requestUGCDetails(SteamPublishedFileID(id), 0)

            val query = ugc.createQueryUGCDetailsRequest(SteamPublishedFileID(id))
            if (query.isValid) {
                println("sending UGC details query: $query")
                ugc.sendQueryUGCRequest(query)
            }
        } else if (input.startsWith("ugc info ")) {
            val id = input.substring("ugc info ".length).toLong(16)
            val installInfo = ItemInstallInfo()
            if (ugc.getItemInstallInfo(SteamPublishedFileID(id), installInfo)) {
                println("  folder: " + installInfo.folder)
                println("  size on disk: " + installInfo.sizeOnDisk)
            }
            val downloadInfo = ItemDownloadInfo()
            if (ugc.getItemDownloadInfo(SteamPublishedFileID(id), downloadInfo)) {
                println("  bytes downloaded: " + downloadInfo.bytesDownloaded)
                println("  bytes total: " + downloadInfo.bytesTotal)
            }
        } else if (input.startsWith("leaderboard find ")) {
            val name = input.substring("leaderboard find ".length)
            userStats.findLeaderboard(name)
        } else if (input.startsWith("leaderboard list ")) {
            val params = input.substring("leaderboard list ".length).split(" ".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            if (endingNumBoard != null && params.size >= 2) {
                userStats.downloadLeaderboardEntries(
                    endingNumBoard,
                    SteamUserStats.LeaderboardDataRequest.Global,
                    params[0].toInt(), params[1].toInt()
                )
            }
        } else if (input.startsWith("leaderboard users ")) {
            val params = input.substring("leaderboard users ".length).split(" ".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            if (endingNumBoard != null && params.size > 0) {
                val users = arrayOfNulls<SteamID>(params.size)
                for (i in params.indices) {
                    users[i] = SteamID.createFromNativeHandle(params[i].toLong())
                }
                userStats.downloadLeaderboardEntriesForUsers(endingNumBoard, users)
            }
        } else if (input.startsWith("leaderboard score ")) {
            val score = input.substring("leaderboard score ".length)
            if (endingNumBoard != null) {
                println("uploading score " + score + " to leaderboard " + endingNumBoard.toString())
                userStats.uploadLeaderboardScore(
                    endingNumBoard,
                    SteamUserStats.LeaderboardUploadScoreMethod.KeepBest, score.toInt(), intArrayOf()
                )
            }
        } else if (input.startsWith("apps subscribed ")) {
            val appId = input.substring("apps subscribed ".length)
            val subscribed = apps.isSubscribedApp(appId.toInt())
            println("user described to app #" + appId + ": " + (if (subscribed) "yes" else "no"))
        } else if (input.startsWith("deck ")) {
            val cmd = input.substring("deck ".length)
            if (cmd == "status") {
                val isDeck = utils.isSteamRunningOnSteamDeck
                println("Steam is running on SteamDeck: " + (if (isDeck) "yes" else "no"))
            } else if (cmd == "input") {
                // supposed to fail, since we run w/o UI here
                val success = utils.showFloatingGamepadTextInput(
                    SteamUtils.FloatingGamepadTextInputMode.ModeSingleLine, 0, 0, 1280, 200
                )
                println("Show floating gamepad text input: " + (if (success) "success" else "failed"))
            }
        }
    }
}


// 数据模型
@Serializable
data class SteamResponse(
    val response: Players
)

@Serializable
data class SteamUserItem(
    val steamid: String,
    val personaname: String,
    val avatarfull: String  // 头像 URL
)

@Serializable
data class Players(
    val players: List<SteamUserItem>
)

// 获取多个 Steam 用户的头像
fun getSteamAvatars(steamIds: List<String>, apiKey: String): Map<String, String?> = runBlocking {

    val client = createHttpClient()
    // 将多个 Steam ID 拼接成字符串
    val steamIdsParam = steamIds.joinToString(",")

    val url = "https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/" +
            "?key=$apiKey&steamids=$steamIdsParam"

    return@runBlocking try {
        val response: SteamResponse = client.get(url).body()
        client.close()

        // 将 SteamID 映射到 avatarfull
        response.response.players.associate { it.steamid to it.avatarfull }
    } catch (e: Exception) {
        println("请求失败: ${e.message}")
        client.close()
        emptyMap()
    }
}