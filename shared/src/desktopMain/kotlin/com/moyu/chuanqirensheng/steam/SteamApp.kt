package com.moyu.chuanqirensheng.steam

import com.codedisaster.steamworks.SteamAPI
import com.codedisaster.steamworks.SteamAPIWarningMessageHook
import com.codedisaster.steamworks.SteamException
import com.codedisaster.steamworks.SteamUtils
import com.codedisaster.steamworks.SteamUtilsCallback
import java.util.Scanner
import java.util.Timer
import java.util.TimerTask
import kotlin.concurrent.Volatile

abstract class SteamApp {

    @Throws(SteamException::class)
    protected abstract fun registerInterfaces()

    @Throws(SteamException::class)
    protected abstract fun unregisterInterfaces()

    @Throws(SteamException::class)
    protected abstract fun processUpdate()

    @Throws(SteamException::class)
    protected abstract fun processInput(input: String)

    private val timer = Timer()

    @Throws(SteamException::class)
    private fun runAsClient(path: String): Boolean {

        SteamAPI.loadLibraries(path)

        // doesn't make much sense here, as normally you would call this before
        // SteamAPI.init() with your (kn)own app ID
//        if (SteamAPI.restartAppIfNecessary(clientUtils!!.appID)) {
//            println("SteamAPI_RestartAppIfNecessary returned 'false'")
//        }

        if (!SteamAPI.init()) {
            SteamAPI.printDebugInfo(System.err)
//            return false
        }

        SteamAPI.printDebugInfo(System.out)

        registerInterfaces()

        timer.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                SteamAPI.runCallbacks()
                processUpdate()
            }
        }, 0, 1000) // 0 表示任务立即开始，1000 表示任务每隔 1 秒执行一次

//        while (inputHandler.alive()
////            && SteamAPI.isSteamRunning()
//        ) {
//            SteamAPI.runCallbacks()
//
//            processUpdate()
//
//            try {
//                // sleep a little (Steam says it should poll at least 15 times/second)
//                Thread.sleep(MS_PER_TICK.toLong())
//            } catch (e: InterruptedException) {
//                // ignore
//            }
//        }

//        println("Shutting down Steam client API ...")
//        clientUtils!!.dispose()
//        unregisterInterfaces()
//        SteamAPI.shutdown()
//        timer.cancel()

        return true
    }

    fun clientMain(path: String) {
        try {
            if (!runAsClient(path)) {
                System.exit(-1)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            System.exit(-1)
        }
    }
}
