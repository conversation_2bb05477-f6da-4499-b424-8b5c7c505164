package com.moyu.chuanqirensheng.platform

//import com.moyu.chuanqirensheng.platform.desktop.iosLoadLocalFile

import androidx.compose.runtime.MutableState
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.eygraber.uri.Uri
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.media.MusicPlayerInterface
import com.moyu.chuanqirensheng.platform.desktop.AdPlayer
import com.moyu.chuanqirensheng.platform.desktop.GameDefaultSdkProcessor
import com.moyu.chuanqirensheng.platform.desktop.MusicPlayer
import com.moyu.chuanqirensheng.platform.desktop.ReportManager
import com.moyu.chuanqirensheng.steam.SteamUtil
import com.moyu.chuanqirensheng.sub.ad.AdInterface
import com.moyu.chuanqirensheng.sub.bill.PayClientData
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.sub.datastore.KEY_VERIFIED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.sub.report.ReportInterface
import com.moyu.chuanqirensheng.util.Platform
import com.moyu.core.model.Sell
import io.ktor.client.HttpClient
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.kotlinx.json.json
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.format
import kotlinx.datetime.format.byUnicodePattern
import kotlinx.datetime.toLocalDateTime
import kotlinx.serialization.json.Json
import org.jetbrains.compose.resources.ExperimentalResourceApi
import shared.generated.resources.Res
import kotlin.system.exitProcess

actual fun getPlatform(): Platform {
    return Platform.Desktop
}

actual fun getLocalFilePath(item: String): String {
    return getLocalFilePath("files", item)
}

//actual suspend fun p_loadLocalFile(
//    fileName: String,
//    processor: (Int, String)->Unit) {
//    iosLoadLocalFile(fileName, processor)
//}

actual fun getSystemFilesPath(): String {
    return System.getProperty("compose.application.resources.dir")
}

actual fun getSystemCacheDirPath(): String {
    return System.getProperty("compose.application.resources.dir")
}

@OptIn(ExperimentalResourceApi::class)
actual fun getLocalFilePath(path: String, item: String): String {
    return Res.getUri("${path}/${item}")
}

actual fun screenWidthInDp(): Float {
    return 480.0f
}

actual fun screenHeightInDp(): Float {
    return 1000.0f
}

actual fun screenDensity(): Float {
    return 2.0f
}

actual fun statusBarHeightInDp(): Dp {
    return 30.dp
}

actual fun bottomHeightInDp(): Dp {
    return 0.dp
}

actual fun topHeightInDp(): Dp {
    return 0.dp
}

actual fun hideKeyboard() {
//    UIApplication.sharedApplication.keyWindow?.endEditing(true)
}

actual fun triggerRebirth() {
    killSelf()
}

actual fun killSelf() {
    exitProcess(0)
}

actual fun lifecycleExecute(isForeground: MutableState<Boolean>) {

}

/**
 * 日期格式字符串转换成时间戳
 * @param format 如：yyyy-MM-dd HH:mm:ss
 * @return
 */
// yyyy-MM-dd HH:mm:ss
actual fun date2TimeStamp(timestampMill: Long, format: String?): String {
    var tmpFormat = format
    if (tmpFormat == null) {
        tmpFormat = "yyyy-MM-dd HH:mm:ss"
    }

    val instant = Instant.fromEpochMilliseconds(timestampMill)
    val time = instant.toLocalDateTime(TimeZone.currentSystemDefault())
    val result = time.format(LocalDateTime.Format { byUnicodePattern(tmpFormat) })
    return result
}

actual fun Long.millisToHoursMinutesSeconds(): String {
    val hours = this / 1000 / 60 / 60
    val minutes = this / 1000 / 60 % 60
    val seconds = this / 1000 % 60
    return "${hours.formate2()}:${minutes.formate2()}:${seconds.formate2()}"
}

private fun Long.formate2(): String {
    return this.toString().padStart(2, '0')
}

actual fun getElapsedTimeMillis(): Long {
    // todo: quding desktop
    return 0
//    return getSystemElapsedBootTimeMillis()
}

actual fun getVersion(): String {
    return "1.0.1"
}

actual fun getVersionCode(): Int {
    return "10001".toInt()
}

actual fun isLite(): Boolean {
    return !isReleasePackage()
}

actual fun isReleasePackage(): Boolean {
    return false
}

actual fun serverUrl(): String {
    return "http://43.134.0.148:9795/yuansuqiu/api/v2/"
}

private var gameSdkProcessor: GameSdkProcessor? = null
actual fun gameSdkDefaultProcessor(): GameSdkProcessor {
    if (gameSdkProcessor == null) {
        gameSdkProcessor = GameDefaultSdkProcessor()
    }
    return gameSdkProcessor!!
}

private var httpClient: HttpClient? = null
actual fun createHttpClient(): HttpClient {
    if (httpClient == null) {
        // https://ktor.io/docs/client-engines.html#darwin
        httpClient = HttpClient() {
                install(ContentNegotiation) {
                    json(Json {
                        ignoreUnknownKeys = true
                        useAlternativeNames = false
                    })
                }
            }
    }

    return httpClient!!
}

private var reportManager: ReportInterface? = null
actual fun reportManager(): ReportInterface {
    if (reportManager == null) {
        reportManager = ReportManager()
    }
    return reportManager!!
}

private var musicPlayer: MusicPlayerInterface? = null
actual fun createMusicPlayer(musicVolumeCallback: () -> Float): MusicPlayerInterface {
    if (musicPlayer == null) {
        musicPlayer = MusicPlayer(musicVolumeCallback)
    }
    return musicPlayer!!
}

actual fun openGamePage(uri: Uri) {
    print("=-== todo: openGamePage $uri")
}

actual fun needPrivacyCheck(): Boolean {
    return false
}

actual fun hasGoogleService(): Boolean {
    return true
}

actual fun hasBilling(): Boolean {
    return false
}

actual fun platformChannel(): String {
    return "desktop"
}

actual suspend fun billPrepay(sell: Sell, function: () -> Unit) {
//    iosPurchaseProduct?.let {
//        println("=-== bill sell.id: ${sell.id}")
//        it(sell.id.toString(), function)
//    }
}

actual fun antiAddictVerified(): Boolean {
    return getBooleanFlowByKey(
        KEY_VERIFIED, false)
}

actual fun privacyNeedShow(): Boolean {
    return getBooleanFlowByKey(
        KEY_NEED_SHOW_PRIVACY, false)
}

actual fun getLocalLanguage(): String {
    return "zh"
}

actual fun setLocaleLanguage(languageCode: String) {
//    iosSetAppLanguage?.let {
//        it(languageCode)
//    }
}

actual fun getLocalCountry(): String {
    return "usa" // 目前没用
}

actual fun billPayClientDataList(): List<PayClientData> {
    // TODO: 海外不需要实现
    return emptyList<PayClientData>()
}

actual fun billRemovePayClientData(it: PayClientData) {
    // TODO: 海外不需要实现
}

actual fun platformTaskExecute() {
//    BuglyTask().execute(GameApp.instance.gameContext)
//    TTRewardAdTask().execute(GameApp.instance.gameContext)
//    ChannelTask().execute(GameApp.instance.gameContext)
//    RootCheckerTask().execute(GameApp.instance.gameContext)
//    BillingTask().execute(GameApp.instance.gameContext)
}

actual fun getBuildFlavor(): String {
    return "oversea"
}

actual fun p_getfootPrint(): String {
    return "desktop" // 目前没用
}

actual fun p_fileRename(oldFileName: String, newFileName: String) {
//    val fileManager = NSFileManager.defaultManager
//    if (fileManager.fileExistsAtPath(oldFileName)) {
//        fileManager.moveItemAtPath(oldFileName, newFileName, null)
//    }
}

actual fun resetClipboard(text: String) {
//    UIPasteboard.generalPasteboard.setString(text)
}

actual fun p_requestInAppReview() {
//    SKStoreReviewController.requestReview()
}

actual fun p_getAdImp(): AdInterface {
    return AdPlayer
}

actual fun getHeadBitmap(): ImageBitmap? {
    return SteamUtil.avatarBitmap
}

actual fun refreshRankList(type: Int, callback: ((list:List<RankData>) -> Unit)?) {
    SteamUtil.refreshBoardList(type, callback)
}

actual fun setAchievement(name: String) {
    println("=-== steam setAchievement $name")
    SteamUtil.setAchievement(name)
}

actual fun setLeaderBoardScore(score: Int, type: Int) {
    SteamUtil.setLeaderBoardScore(score, type)
}

actual fun getTextFromFile(fileName: String): String {
    return ""
}

actual fun heartbeat() {

}