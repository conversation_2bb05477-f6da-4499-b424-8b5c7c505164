package com.moyu.chuanqirensheng.platform.desktop

import com.moyu.chuanqirensheng.media.MusicPlayerInterface
import com.moyu.chuanqirensheng.media.MusicRes
import com.moyu.chuanqirensheng.platform.getSystemFilesPath
import com.moyu.core.AppWrapper
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import java.io.ByteArrayInputStream
import java.io.File
import java.util.concurrent.Executors
import javax.sound.sampled.AudioSystem
import javax.sound.sampled.Clip
import javax.sound.sampled.FloatControl

class MusicPlayer(override var musicVolumeCallback: () -> Float) : MusicPlayerInterface {

    override var soundHashMap: HashMap<SoundEffect, MusicRes>? = null

    private val musicDispatcher = Executors.newSingleThreadExecutor().asCoroutineDispatcher()

    private var lastMusicWithStop = ""
    private var lastMusic = ""
    private var currentMusicClip: Clip? = null
    private var curSoundClip: Clip? = null; // 音效

    override fun playMusic(music: String) {
        if (AppWrapper.isForeground) {
            if (lastMusicWithStop != music) {
                lastMusicWithStop = music
                lastMusic = music

                AppWrapper.globalScope.launch(musicDispatcher) {
                    if (currentMusicClip != null) {
                        currentMusicClip?.stop()
                        currentMusicClip = null
                    }

                    try {
                        val name = music.replace("mp3", "wav")
                        val  filePath = "${getSystemFilesPath()}/$name"
                        val file = File(filePath)

                        val audioBytes = file.readBytes()  // 读取文件为字节数组
                        val inputStream = ByteArrayInputStream(audioBytes)
                        val audioInputStream = AudioSystem.getAudioInputStream(inputStream)
                        var clip = AudioSystem.getClip()
                        clip?.open(audioInputStream)
                        setVolume(clip, 1f)
                        clip?.loop(Clip.LOOP_CONTINUOUSLY)
                        clip?.start()

                        currentMusicClip = clip
                    } catch (e: Exception) {
                        println("Error playing music: ${e.message}")
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    private fun setVolume(clip: Clip?, volume: Float) {
        if (clip != null && clip.isControlSupported(FloatControl.Type.MASTER_GAIN)) {
            val gainControl = clip.getControl(FloatControl.Type.MASTER_GAIN) as FloatControl
            val range = gainControl.maximum - gainControl.minimum
            val gain = gainControl.minimum + range * volume
            gainControl.value = gain
        }
    }

    override fun resumeMusic() {
        playMusic(lastMusic)
    }

    override fun stopAll() {
        lastMusicWithStop = ""
        if (currentMusicClip != null) {
            currentMusicClip?.stop()
            currentMusicClip = null
        }
    }

    override fun playSound(sound: SoundEffect, loop: Boolean) {
        if (AppWrapper.isForeground) {
            soundHashMap?.get(sound)?.let {
                AppWrapper.globalScope.launch(musicDispatcher) {
                    if (curSoundClip != null) {
                        curSoundClip?.stop()
                        curSoundClip = null
                    }

                    try {
                        val name = it.value.replace("mp3", "wav")
                        val  filePath = "${getSystemFilesPath()}/$name"
                        val file = File(filePath)

                        val audioBytes = file.readBytes()  // 读取文件为字节数组
                        val inputStream = ByteArrayInputStream(audioBytes)
                        val audioInputStream = AudioSystem.getAudioInputStream(inputStream)
                        var clip = AudioSystem.getClip()
                        clip?.open(audioInputStream)
                        setVolume(clip, 1f)
                        clip?.loop(1)
                        clip?.start()

                        curSoundClip = clip
                    } catch (e: Exception) {
                        println("Error playing music: ${e.message}")
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    override fun doMuteState() {
        setVolume(currentMusicClip, 1f)
    }
}