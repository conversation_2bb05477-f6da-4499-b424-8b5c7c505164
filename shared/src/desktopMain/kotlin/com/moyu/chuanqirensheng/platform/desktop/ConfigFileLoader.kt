package com.moyu.chuanqirensheng.platform.desktop

import com.moyu.chuanqirensheng.platform.getLocalLanguage
import com.moyu.chuanqirensheng.sub.datastore.KEY_LANGUAGE
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.sub.language.LanguageManager.languageCodes
import com.moyu.chuanqirensheng.sub.language.LanguageManager.selectedLanguage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.jetbrains.compose.resources.ExperimentalResourceApi
import shared.generated.resources.Res
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

//suspend fun iosLoadLocalFile(
//    fileName: String,
//    processor: (Int, String)->Unit) {
//    loadConfigFile(fileName, processor)
//}
//
//suspend fun loadConfigFile(
//    fileName: String,
//    processor: (Int, String) -> Unit
//): Boolean {
//    return withContext(Dispatchers.IO) {
//        suspendCoroutine { continuation ->
//            val string = getTextFromAsset(fileName)
//
//            string.lines().forEachIndexed { index, line ->
//                processor(index, line)
//            }
//            continuation.resume(true)
//        }
//    }
//}
//
//@OptIn(ExperimentalResourceApi::class)
//fun getTextFromAsset(fileName: String): String {
//    var result = ""
//
//    runBlocking {
//        // 根据多语言找配置文件
////        val lang = LanguageManager.selectedLanguage.value
////        val lang = getStringFlowByKey(KEY_LANGUAGE)
//        var path = "en" // default
//
//        val language = getLocalLanguage()
//        languageCodes.forEachIndexed { index, code ->
//            if (language.startsWith("zh")) {
//                path = "config"
//            } else if (language.startsWith(code)) {
//                path = languageCodes[index]
//            }
//        }
//
////        println("=-== lang: $lang")
////        println("=-== path: $path")
//        val bytes = Res.readBytes("files/${path}/$fileName")
//        result = bytes.decodeToString()
//    }
//
//    return result
//}