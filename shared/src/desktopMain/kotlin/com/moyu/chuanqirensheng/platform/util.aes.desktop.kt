package com.moyu.chuanqirensheng.platform

/*   算法/模式/填充 */
private const val CipherMode = "AES/ECB/PKCS5Padding"

//
///*加密(结果为16进制字符串)  */
actual fun aes_encrypt(content: String, password: String): String? {
//    var data: ByteArray? = null
//    try {
//        data = content.toByteArray(charset("UTF-8"))
//    } catch (e: Exception) {
//        e.printStackTrace()
//    }
//    data = encrypt(data, password)
//    return byte2HexWithBlank(data)
    return null
}
//
///*解密16进制的字符串为字符串  */
actual fun aes_decrypt(content: String, password: String): String? {
//    var data: ByteArray? = null
//    try {
//        data = hex2Bytes(content)
//    } catch (e: Exception) {
//        e.printStackTrace()
//    }
//    data = decrypt(data, password)
//    if (data == null) return null
//    var result: String? = null
//    try {
//        result = String(data, Charsets.UTF_8)
//    } catch (e: UnsupportedEncodingException) {
//        e.printStackTrace()
//    }
//    return result

    return null
}
//
///*  创建密钥  */
//private fun createKey(password: String): SecretKeySpec {
//    var data: ByteArray? = null
//    val sb = StringBuffer(32)
//    sb.append(password)
//    while (sb.length < 32) {
//        sb.append("0")
//    }
//    if (sb.length > 32) {
//        sb.setLength(32)
//    }
//    try {
//        data = sb.toString().toByteArray(charset("UTF-8"))
//    } catch (e: UnsupportedEncodingException) {
//        e.printStackTrace()
//    }
//    return SecretKeySpec(data, "AES")
//}
//
//
actual fun getSignature(): String {
    return "4b48de895e5ffb24b2665631078db5a7bd8ce454"
}
///* 加密字节数据  */
//private fun encrypt(content: ByteArray?, password: String): ByteArray? {
//    try {
//        val key = createKey(password)
//        val cipher = Cipher.getInstance(CipherMode)
//        cipher.init(Cipher.ENCRYPT_MODE, key)
//        return cipher.doFinal(content)
//    } catch (e: Exception) {
//        e.printStackTrace()
//    }
//    return null
//}
//
///*解密字节数组*/
//private fun decrypt(content: ByteArray?, password: String): ByteArray? {
//    try {
//        val key = createKey(password)
//        val cipher = Cipher.getInstance(CipherMode)
//        cipher.init(Cipher.DECRYPT_MODE, key)
//        return cipher.doFinal(content)
//    } catch (e: Exception) {
//        e.printStackTrace()
//    }
//    return null
//}
//
//// 辅助方法，用于将字节数组转换为十六进制字符串
//private fun bytesToHex(bytes: ByteArray): String? {
//    val builder = java.lang.StringBuilder()
//    for (b in bytes) {
//        builder.append(String.format("%02x", b))
//    }
//    return builder.toString()
//}
//
//

