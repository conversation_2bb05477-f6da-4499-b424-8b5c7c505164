package com.moyu.chuanqirensheng.platform

import com.moyu.chuanqirensheng.iosUnzipBlock
import com.moyu.chuanqirensheng.iosZipBlock
import com.moyu.chuanqirensheng.sub.config.ConfigManager
import kotlinx.cinterop.ExperimentalForeignApi
import kotlinx.cinterop.addressOf
import kotlinx.cinterop.allocArrayOf
import kotlinx.cinterop.memScoped
import kotlinx.cinterop.usePinned
import platform.Foundation.NSData
import platform.Foundation.NSFileManager
import platform.Foundation.create
import platform.posix.memcpy
import platform.zlib.compress

//actual fun copyFileFromStream(copyTarget: File, inStream: InputStream): String {
//    if (copyTarget.exists()) {
//        copyTarget.delete()
//    }
//    try {
//        val fileOutputStream = FileOutputStream(copyTarget)
//        var byteread: Int
//        val buffer = ByteArray(1024)
//        while (inStream.read(buffer).also { byteread = it } != -1) {
//            fileOutputStream.write(buffer, 0, byteread)
//        }
//        fileOutputStream.flush()
//        inStream.close()
//        fileOutputStream.close()
//    } catch (e: IOException) {
//        e.printStackTrace()
//    }
//    return copyTarget.absolutePath
//}

actual fun compressZip4j(zipFilePath: String, compressFilePath: String, password: String) {
    iosZipBlock?.invoke(zipFilePath, compressFilePath, password)
}

//actual fun compressConfigs(zipFilePath: String, password: String) {
//    val zipFile = ZipFile(File(zipFilePath))
//    val zipParameters = ZipParameters()
//    zipParameters.compressionMethod = CompressionMethod.DEFLATE
//    zipParameters.compressionLevel = CompressionLevel.FASTEST // 压缩级别
//    if (password.isNotEmpty()) {  //是否要加密(加密会影响压缩速度)
//        zipParameters.isEncryptFiles = true
//        zipParameters.encryptionMethod = EncryptionMethod.ZIP_STANDARD // 加密方式
//    }
//    zipFile.setPassword(password.toCharArray())
//    configLoaders.forEach {
//        zipParameters.fileNameInZip = it.getKey()
//        zipFile.addStream(GameApp.instance.assets.open(it.getKey()), zipParameters)
//    }

//    ConfigManager.configLoaders.forEach {
//        SSZipArchive.createZipFileAtPath(path = zipFilePath, withContentsOfDirectory= it.getKey(), password)
//    }
//
//}

@OptIn(ExperimentalForeignApi::class)
actual fun uncompressZip4j(zipFilePath: String, filePath: String, password: String) {
    println("=-=== uncompressZip4j: $zipFilePath, $filePath, $password")
    iosUnzipBlock?.invoke(zipFilePath, filePath, password)
    // 调用这个方法， iOS 端会编译报错。目前无法解决。 改为由 Native 侧暴露方法
//    SSZipArchive.unzipFileAtPath(zipFilePath, toDestination = filePath, overwrite = true, password = password, progressHandler = null, completionHandler = null)
}

//读取文本文件
actual fun openText(path: String?): ByteArray? {
    println("=-=== openText: $path")
    if (path == null) {
        return null
    }

    NSFileManager.defaultManager.contentsAtPath(path)?.let {
        return it.toByteArray()
    }

    return null
}

//保存文本文件
actual fun saveText(path: String, txt: ByteArray) {
    println("=-=== saveText: $path")
    NSFileManager.defaultManager.createFileAtPath(path, txt.toNSData(), null)
}

@OptIn(ExperimentalForeignApi::class)
fun NSData.toByteArray(): ByteArray {
    return ByteArray(length.toInt()).apply {
        usePinned {
            memcpy(it.addressOf(0), bytes, length)
        }
    }
}

@OptIn(ExperimentalForeignApi::class)
fun ByteArray.toNSData(): NSData = memScoped {
    NSData.create(bytes = allocArrayOf(this@toNSData), length = <EMAIL>())
}