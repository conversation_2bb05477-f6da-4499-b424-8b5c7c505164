package com.moyu.chuanqirensheng.platform.ios

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.iosAppFlyerEvent
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_AD
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_PURCHASE1
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_PURCHASE2
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_SECOND_LOGIN
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.sub.report.AF_AD_ID
import com.moyu.chuanqirensheng.sub.report.AF_NEW_USER
import com.moyu.chuanqirensheng.sub.report.AF_SECOND_PURCHASE
import com.moyu.chuanqirensheng.sub.report.ReportInterface
import com.moyu.chuanqirensheng.util.getVersionCode

class ReportManager: ReportInterface {

    private val firstAFAd = mutableStateOf(false)
    private val firstAFPurchase = mutableStateOf(false)
    private val secondAFPurchase = mutableStateOf(false)
    private val secondAFLogin = mutableStateOf(false)

    override fun setup() {
        firstAFAd.value = getBooleanFlowByKey(KEY_REPORT_AF_AD)
        firstAFPurchase.value = getBooleanFlowByKey(KEY_REPORT_AF_PURCHASE1)
        secondAFPurchase.value = getBooleanFlowByKey(KEY_REPORT_AF_PURCHASE2)
        secondAFLogin.value = getBooleanFlowByKey(KEY_REPORT_AF_SECOND_LOGIN)
    }

    override fun onLogin() {
        val eventValues: MutableMap<String, Any> = HashMap()
        eventValues[AF_NEW_USER] = if (LoginManager.instance.newUser) 1 else 0
        eventValues["af_new_version"] = getVersionCode()
        eventValues["af_customer_user_id"] = gameSdkDefaultProcessor().getObjectId()?:"none"
        if (!LoginManager.instance.newUser && !secondAFLogin.value) {
            secondAFLogin.value = true
            setBooleanValueByKey(KEY_REPORT_AF_SECOND_LOGIN, true)
            iosAppFlyerEvent?.let { it("af_login", eventValues) }
        } else {
            if (LoginManager.instance.newUser) {
                iosAppFlyerEvent?.let { it("af_login", eventValues) }
            }
        }
    }

    override fun onAdCompletedAF(adId: String) {
        if (!firstAFAd.value) {
            firstAFAd.value = true
            setBooleanValueByKey(KEY_REPORT_AF_AD, true)
            val eventValues: MutableMap<String, Any> = HashMap()
            eventValues[AF_AD_ID] = adId
            eventValues[AF_NEW_USER] = if (LoginManager.instance.newUser) 1 else 0
            eventValues["af_new_version"] = getVersionCode()
            eventValues["af_customer_user_id"] = gameSdkDefaultProcessor().getObjectId()?:"none"
            iosAppFlyerEvent?.let { it("af_ad_view", eventValues) }
        }
    }

    override fun onPurchaseCompletedAF(purchaseId: String, amount: Double, number: Int) {
        val eventValues: MutableMap<String, Any> = HashMap()
        eventValues["af_revenue"] = amount
        eventValues["af_content_id"] = purchaseId
        eventValues[AF_NEW_USER] = if (LoginManager.instance.newUser) 1 else 0
        eventValues["af_new_version"] = getVersionCode()
        eventValues["af_quantity"] = number
        eventValues["af_customer_user_id"] = gameSdkDefaultProcessor().getObjectId()?:"none"
        if (!firstAFPurchase.value) {
            firstAFPurchase.value = true
            setBooleanValueByKey(KEY_REPORT_AF_PURCHASE1, true)
            eventValues[AF_SECOND_PURCHASE] = 0
            iosAppFlyerEvent?.let { it("af_purchase", eventValues) }
        } else if (!secondAFPurchase.value) {
            secondAFPurchase.value = true
            setBooleanValueByKey(KEY_REPORT_AF_PURCHASE2, true)
            eventValues[AF_SECOND_PURCHASE] = 1
            iosAppFlyerEvent?.let { it("af_purchase", eventValues) }
        }
    }

    override fun onShopPurchase(sellId: Int, price: Int, priceType: Int) {
    }

    override fun onNewGame(mode: Int) {
    }

    override fun onContinueGame(mode: Int) {
    }

    override fun pk(win: Int, score: Int) {
    }

    override fun battle(win: Int, mode: Int, stage: Int) {
    }

    override fun onLoadAd() {
    }

    override fun onPage(route: String) {
    }

    override fun onPurchaseCompletedAdjust(dollarPrice: Double, orderId: String) {}

    override fun guide(index: Int) {
    }

    override fun giftShow(id: Int) {
    }

    override fun itemMoneyBought(id: Int) {
    }

    override fun itemKeyBought(id: Int) {
    }

    override fun unlockItem(id: Int) {

    }
    override fun unlockMonthCard() {}
}