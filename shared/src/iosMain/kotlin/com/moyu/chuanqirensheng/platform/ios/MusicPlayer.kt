package com.moyu.chuanqirensheng.platform.ios

import androidx.compose.runtime.remember
import com.moyu.chuanqirensheng.iosPlaySound
import com.moyu.chuanqirensheng.media.MusicPlayerInterface
import com.moyu.chuanqirensheng.media.MusicRes
import com.moyu.chuanqirensheng.platform.getLocalFilePath
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.core.AppWrapper
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.p_getTimeMillis
import kotlinx.cinterop.ExperimentalForeignApi
import kotlinx.cinterop.ObjCObject
import kotlinx.cinterop.alloc
import kotlinx.cinterop.free
import kotlinx.cinterop.nativeHeap
import kotlinx.cinterop.objcPtr
import kotlinx.cinterop.ptr
import kotlinx.cinterop.value
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import platform.AVFAudio.AVAudioPlayer
import platform.AVFoundation.AVPlayerItem
import platform.AVFoundation.AVQueuePlayer
import platform.AVFoundation.pause
import platform.AVFoundation.play
import platform.AVFoundation.replaceCurrentItemWithPlayerItem
import platform.AudioToolbox.AudioServicesCreateSystemSoundID
import platform.AudioToolbox.AudioServicesPlaySystemSound
import platform.AudioToolbox.SystemSoundID
import platform.AudioToolbox.SystemSoundIDVar
import platform.CoreFoundation.CFURLRef
import platform.Foundation.NSURL

class MusicPlayer(override var musicVolumeCallback: () -> Float) : MusicPlayerInterface {

    val musicDispatcher = newSingleThreadContext("musicDispatcher")

    override var soundHashMap: HashMap<SoundEffect, MusicRes>? = null

    private var player: AVAudioPlayer? = null
//    private var soundPlayer: AVAudioPlayer? = null
    private var lastMusicWithStop = ""
    private var lastMusic = ""

    private val lastPlayTimestamps = mutableMapOf<SoundEffect, Long>()
    private val MIN_INTERVAL = 200L  // 200ms

    private fun createUrl(url: String): NSURL? {
        return if (url.startsWith("http://") || url.startsWith("https://")) {
            NSURL.URLWithString(url)
        } else {
            NSURL.fileURLWithPath(getLocalFilePath(url))
        }
    }

    @OptIn(ExperimentalForeignApi::class)
    override fun playMusic(music: String) {
        if (AppWrapper.isForeground.value) {
            AppWrapper.globalScope.launch(musicDispatcher) {
                if (lastMusicWithStop != music) {
                    lastMusicWithStop = music
                    lastMusic = music

                    if (player != null) {
                        player?.stop()
                        player = null
                    }

                    player = createUrl(music)?.let { AVAudioPlayer(contentsOfURL = it, error = null) }
                    player?.volume = musicVolumeCallback()
                    player?.numberOfLoops = -1

                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        player?.prepareToPlay()
                        player?.play()
                    }
                }
            }
        }
    }

    override fun resumeMusic() {
        playMusic(lastMusic)
    }

    override fun stopAll() {
        lastMusicWithStop = ""
        player?.pause()
    }

    @OptIn(ExperimentalForeignApi::class)
    override fun playSound(sound: SoundEffect, loop: Boolean) {
        // 如果是同一个SoundEffect在200ms内重复调用，则不重复播放
        val currentTime =  p_getTimeMillis()
        val lastTime = lastPlayTimestamps[sound] ?: 0L
        if (currentTime - lastTime < MIN_INTERVAL) {
            // 在200ms内已播放过该音效，不再播放
            return
        }
        // 记录最新播放时间
        lastPlayTimestamps[sound] = currentTime

        if (AppWrapper.isForeground.value) {
            AppWrapper.globalScope.launch(musicDispatcher) {
//                if (soundPlayer != null) {
//                    soundPlayer?.stop()
//                    soundPlayer = null
//                }

                soundHashMap?.get(sound)?.let {
                    iosPlaySound?.let { it1 -> it1(it.value) }


//                    soundPlayer = createUrl(it.value)?.let { AVAudioPlayer(contentsOfURL = it, error = null) }
//                    soundPlayer?.volume = musicVolumeCallback()
//                    soundPlayer?.numberOfLoops = if (loop) -1 else 0

//                    AppWrapper.globalScope.launch(Dispatchers.Main) {
//                        soundPlayer?.prepareToPlay()
//                        soundPlayer?.play()
//                    }
                }
            }
        }
    }

    override fun doMuteState() {
        player?.volume = musicVolumeCallback()
    }

    override fun init() {
        //
    }

    override fun stopSound(sound: SoundEffect) {
        // player?.volume = 0F
    }
}