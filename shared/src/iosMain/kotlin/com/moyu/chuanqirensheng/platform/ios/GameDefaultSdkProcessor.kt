package com.moyu.chuanqirensheng.platform.ios

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.iosCheckNetworkPermission
import com.moyu.chuanqirensheng.iosHasNetworkPermission
import com.moyu.chuanqirensheng.iosStartLoginAction
import com.moyu.chuanqirensheng.iosUserId
import com.moyu.chuanqirensheng.iosUserName
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_OBJECT_ID
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.sub.saver.CloudSaverManager
import com.moyu.chuanqirensheng.util.killSelf
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import platform.posix.exit

class GameDefaultSdkProcessor: GameSdkProcessor {

    private val antiAddictionContent = mutableStateOf("")
    private val loginStatus = mutableStateOf(false)
    private val avatar = mutableStateOf<String?>(null)
    private val userName = mutableStateOf<String?>(null)
    private val antiAddictionStatus = mutableStateOf(false)
    private val objectId = mutableStateOf<String?>(null)

    private var init = false

    override fun initGameSdk() {
        antiAddictionStatus.value = false
    }

    override fun initSDK() {
        if (init) return
        init = true

        if (iosHasNetworkPermission) {
            login()
        } else {
            iosCheckNetworkPermission?.let {
                it({
                  if (iosHasNetworkPermission) {
                      login()
                  } else {
                      "The game requires network permissions".toast()
                  }
                })
            }
        }
    }

    override fun login() {
        println("=-== $iosUserId, $iosUserName")

        if (iosUserId != null) {
            dealAfterLogin(getIOSUserName(), iosUserId!!, "")
        } else {
            // 唤起登录
            iosStartLoginAction?.let {
                it {
                    println("=-== afterLogin $iosUserId, $iosUserName")
                    dealAfterLogin(getIOSUserName(), iosUserId!!, "")
                }
            }
        }
    }

    private fun getIOSUserName(): String {
        return if (iosUserName != null) {
            return iosUserName!!
        } else {
            return "User"
        }
    }

    override fun antiAddictPassed(): MutableState<Boolean> {
        return antiAddictionStatus
    }

    override fun hasLogin(): Boolean {
        return loginStatus.value
    }

    override fun getAvatarUrl(): String? {
        return if (avatar.value.isNullOrEmpty() || avatar.value == "null") "role_101" else avatar.value!!
    }

    override fun getUserName(): String? {
        return userName.value
    }

    override fun getObjectId(): String? {
        return objectId.value
    }

    override fun getAntiAddictionContent(): String {
        return antiAddictionContent.value
    }

    override fun checkAntiAddiction() {
        //
    }

    override fun dealAfterLogin(name: String, id: String, avatarUrl: String) {
        loginStatus.value = true
        userName.value = name
        objectId.value = id
        avatar.value = avatarUrl
        checkAntiAddiction()
        AppWrapper.globalScope.launch {
            val oldAccount = getStringFlowByKey(KEY_OBJECT_ID)
            if (oldAccount.isEmpty()) {
                setStringValueByKey(KEY_OBJECT_ID, objectId.value ?: "")
            } else if (oldAccount != objectId.value) {
                "不支持账号切换，请卸载重装".toast()
                delay(2000)
                killSelf()
            }
        }

        tryLogin()
    }

    override fun isAgeUnder8(): Boolean {
        return false
    }

    override fun isAgeIn8To16(): Boolean {
        return false
    }

    override fun quitGame(onExit: () -> Unit) {
        exit(0)
    }

    fun tryLogin() {
        AppWrapper.globalScope.launch(Dispatchers.IO) {
            RetrofitModel.getLoginData()
            withContext(Dispatchers.Main) {
                repo.doInitAfterLogin()
            }
            if (LoginManager.instance.newUser) { // 新用户
                // 看下是不是有存档
                CloudSaverManager.checkIfNewUserHaveCloudSave()
            }
        }
    }
}