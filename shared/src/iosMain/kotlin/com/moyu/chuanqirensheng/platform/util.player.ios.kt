package com.moyu.chuanqirensheng.platform

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import kotlinx.cinterop.BetaInteropApi
import kotlinx.cinterop.ExperimentalForeignApi
import kotlinx.cinterop.ObjCAction
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import platform.AVFoundation.AVLayerVideoGravityResizeAspectFill
import platform.AVFoundation.AVPlayerItem
import platform.AVFoundation.AVPlayerItemDidPlayToEndTimeNotification
import platform.AVFoundation.AVQueuePlayer
import platform.AVFoundation.currentItem
import platform.AVFoundation.muted
import platform.AVFoundation.pause
import platform.AVFoundation.play
import platform.AVFoundation.replaceCurrentItemWithPlayerItem
import platform.AVFoundation.seekToTime
import platform.AVKit.AVPlayerViewController
import platform.CoreMedia.CMTimeMakeWithSeconds
import platform.Foundation.NSNotificationCenter
import platform.Foundation.NSSelectorFromString
import platform.Foundation.NSURL
import platform.UIKit.UIApplication
import platform.UIKit.UIView
import platform.darwin.NSObject

@OptIn(ExperimentalForeignApi::class, BetaInteropApi::class)
@Composable
actual fun CMPVideoPlayer(modifier: Modifier) {
    val playerItem = remember { mutableStateOf<AVPlayerItem?>(null) }

    val player: AVQueuePlayer by remember { mutableStateOf(AVQueuePlayer(playerItem.value)) }
    player.muted = true

    val avPlayerViewController = remember { AVPlayerViewController() }
    avPlayerViewController.player = player
    avPlayerViewController.showsPlaybackControls = false
    avPlayerViewController.videoGravity = AVLayerVideoGravityResizeAspectFill

    LaunchedEffect(true) {
        val url = getLocalFilePath("game_bg.mp4")
        val urlObject = createUrl(url)
        val newItem = urlObject?.let { AVPlayerItem(uRL = it) }
        playerItem.value = newItem
        playerItem.value?.let {
            player.replaceCurrentItemWithPlayerItem(it)
        }

        for (i in 0 until 1) {
            urlObject?.let { AVPlayerItem(uRL = it) }?.let {
                player.insertItem(it, afterItem = null)
            }
        }

        player.play()
    }

    androidx.compose.ui.viewinterop.UIKitView(
        factory = {
            avPlayerViewController.view
        },
        modifier = modifier,
        update = {
            MainScope().launch {
                player.play()
                UIApplication.sharedApplication.idleTimerDisabled = true
            }
        }
    )

    DisposableEffect(Unit) {
        val observerObject = object : NSObject() {
            @ObjCAction
            fun onPlayerItemDidPlayToEndTime() {
                player.currentItem?.let { item ->
                    player.seekToTime(CMTimeMakeWithSeconds(0.0, 1))
                    player.removeItem(item)
                    player.insertItem(item, afterItem = null)
                    player.play()
                }
            }
        }

        NSNotificationCenter.defaultCenter().addObserver(
            observerObject,
            NSSelectorFromString("onPlayerItemDidPlayToEndTime"),
            AVPlayerItemDidPlayToEndTimeNotification,
            player.currentItem
        )

        onDispose {
            UIApplication.sharedApplication.idleTimerDisabled = false
            player.pause()
            player.replaceCurrentItemWithPlayerItem(null)
            NSNotificationCenter.defaultCenter().removeObserver(observerObject)
        }
    }
}

internal fun createUrl(url: String): NSURL? {
    return if (url.startsWith("http://") || url.startsWith("https://")) {
        NSURL.URLWithString(url)
    } else {
        NSURL.fileURLWithPath(url)
    }
}
