package com.moyu.chuanqirensheng

import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.window.ComposeUIViewController
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.moyu.chuanqirensheng.application.GameAppCompose
import com.moyu.chuanqirensheng.application.inittask.ConfigTask
import com.moyu.chuanqirensheng.application.inittask.DataStoreTask
import com.moyu.chuanqirensheng.application.inittask.LifecycleTask
import com.moyu.chuanqirensheng.application.inittask.MusicTask
import com.moyu.chuanqirensheng.application.inittask.ReportTask
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.ContextImpl
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.util.initializeSentry
import com.moyu.core.AppWrapper
import com.moyu.core.model.Award
import com.moyu.core.model.toAward
//import com.moyu.chuanqirensheng.util.initializeSentry
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import platform.UIKit.UIViewController

fun IOSKtInit() {
    println("=-== iosInit")
    initializeSentry()
}

@OptIn(ExperimentalComposeUiApi::class)
fun ComposeEntryPoint(): UIViewController  {
    return ComposeUIViewController(
        configure = { enableBackGesture = false }
    ) {
        IOSGameApp()
    }
}

var isSetuped = false

@Composable
fun IOSGameApp() {
    val lifececyleOwner = LocalLifecycleOwner.current
    var gameContext: Context = ContextImpl(lifececyleOwner)

    if (!isSetuped) {
        isSetuped = true

        // 这个必须放到最前面，否则资源错误
        runBlocking {
            LanguageManager.init()
//        println("=-== IOSGameApp init")

            // Launch UncompressTask and ConfigTask in a child coroutine on the IO dispatcher
            val asyncTasks = listOf(
//            async { UncompressTask().execute(gameContext) },
                async { ConfigTask().execute(gameContext) },
                async { MusicTask().execute(gameContext) }
            )

            // Execute other tasks on the current thread
            ReportTask().execute(gameContext)
//        DebugTask().execute(gameContext)
            LifecycleTask().execute(gameContext)
//        BuglyTask().execute(gameContext)
//        BillingTask().execute(gameContext)
//        ChannelTask().execute(gameContext)
//        RootCheckerTask().execute(gameContext)

            // Await all async tasks to ensure runBlocking waits for their completion
            asyncTasks.awaitAll()
//        DataStoreTask().execute(gameContext)
//        TTRewardAdTask().execute(gameContext)
        }

        LaunchedEffect(Unit) {
//        println("=-== IOSGameApp LaunchedEffect")
            DataStoreTask().execute(gameContext)
        }

//    MainScope().launch {
//        println("=-== DataStoreTask launch")
//        DataStoreTask().execute(gameContext)
//    }
        iosPurchaseProductSuccessBlock = { productId ->
            println("Purchase success: $productId")

            if (productId != null) {
                val shopList = repo.gameCore.getSellPool().filter { it.isAifadian() }
                val sell = shopList.find { it.id.toString() == productId }

                sell?.let {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        var realAward = it.toAward()
                        AwardManager.realGainItem(it, realAward)
                    }
                }
            }
        }
    }

    GameAppCompose {
        //
    }
}

// 从 Native 获取系统启动时间
var systemElapsedBootTimeMillisBlock:(() -> Long)?  = null
fun getSystemElapsedBootTimeMillis(): Long {
    return systemElapsedBootTimeMillisBlock?.let { it() } ?: 0L
}

// aes 加密 & 解密
var iosEncryptBlock: ((String, String) -> String)? = null
var iosDecryptBlock: ((String, String) -> String)? = null

// 解压缩 zip
var iosUnzipBlock: ((String, String, String) -> Unit)? = null
var iosZipBlock: ((String, String, String) -> Unit)? = null

// ui 从native传过来的相关参数
var iosScreenDensity: Float = 0f
var iosScreenHeightInPixel: Float = 0f
var iosScreenWidthInDp: Float = 0f
var iosScreenHeightInDp: Float = 0f
var iosStatusBarHeightInDp: Float = 0f
var iosBottomHeightInDp: Float = 0f

// 登录
var iosUserId: String? = null
var iosUserName: String? = null
//var iosUserEmail: String? = null
var iosStartLoginAction: ((() -> Unit) -> Unit)? = null

var iosVersionName: String = ""
var iosVersionCode: String = ""

// language
var iosPhoneLanguage: String = "en" // 获取手机系统当前语言
var iosSetAppLanguage: ((String) -> Unit)? = null // 设置 App 的语言

// 异步获取网络权限，登录接口会确保有网络权限才调用。其他业务调用网络时，应该在登录接口之后
var iosHasNetworkPermission = false
var iosCheckNetworkPermission: ((() -> Unit) -> Unit)? = null

// 内购商品
var iosPurchaseProduct: ((String, String, () -> Unit) -> Unit)? = null
var iosPurchaseProductSuccessBlock: ((String) -> Unit)? = null

// appfly 埋点上报
var iosAppFlyerEvent: ((String, Map<String, Any>) -> Unit)? = null

// 广告
var iosAdLoadBlock: (((Boolean) -> Unit) -> Unit)? = null
var iosAdShowBlock: (((Boolean) -> Unit) -> Unit)? = null

// play sound
var iosPlaySound: ((String) -> Unit)? = null

