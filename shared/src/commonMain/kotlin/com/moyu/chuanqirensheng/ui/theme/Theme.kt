package com.moyu.chuanqirensheng.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material.MaterialTheme
import androidx.compose.material.darkColors
import androidx.compose.material.lightColors
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontFamily
import org.jetbrains.compose.resources.Font
import shared.generated.resources.Res
import shared.generated.resources.game


private val DarkColorPalette = darkColors(
    primary = RaceNameColor,
    primaryVariant = HealTextColor,
    secondary = SkillLevel1Color
)

private val LightColorPalette = lightColors(
    primary = RaceNameColor,
    primaryVariant = HealTextColor,
    secondary = SkillLevel1Color

    /* Other default colors to override
    background = Color.White,
    surface = Color.White,
    onPrimary = Color.White,
    onSecondary = Color.Black,
    onBackground = Color.Black,
    onSurface = Color.Black,
    */
)

@Composable
fun ComposedTheme(darkTheme: Boolean = isSystemInDarkTheme(), content: @Composable () -> Unit) {
    val colors = if (darkTheme) {
        DarkColorPalette
    } else {
        LightColorPalette
    }



    val typography = createTypography(
        FontFamily(Font(Res.font.game)),
//        FontFamily(
//            typeface = Typeface.createFromAsset(
//                GameApp.instance.assets,
//                "game.ttf"
//            )
//        )
         LocalDensity.current.fontScale
    )
    MaterialTheme(
        colors = colors,
        typography = typography,
        shapes = Shapes,
        content = content
    )
}