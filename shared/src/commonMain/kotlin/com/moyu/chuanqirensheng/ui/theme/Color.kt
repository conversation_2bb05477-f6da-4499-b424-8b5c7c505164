package com.moyu.chuanqirensheng.ui.theme

import androidx.compose.ui.graphics.Color
import com.moyu.core.model.Ally
import com.moyu.core.model.damage.DamageType

val B85 = Color(0xb9000000)
val B75 = Color(0xa9000000)
val B65 = Color(0xa4000000)
val B50 = Color(0x80000000)
val B35 = Color(0x50000000)
val B04 = Color(0x0a000000)
val B15 = Color(0x2A000000)
val B10 = Color(0x20000000)
val W10 = Color(0x25FFFFFF)
val W30 = Color(0x55FFFFFF)
val W50 = Color(0x80FFFFFF)
val W95 = Color(0xE3FFFFFF)
val Red50 = Color(0x80EE0000)
val DARK_RED = Color(0xffEE0000)
val HealTextColor = Color(0xFF009900)
val DarkGreen = Color(0xFF009900)
val RaceNameColor = Color(0xFF812EFF)
val SkillLevel1Color = Color(0xFF009900)
val SkillLevel2Color = Color(0xFF00ffff)
val SkillLevel5Color = Color(0xFFCCAA22)
val SkillLevel6Color = Color(0xffFB8759)

val SliderColor = Color(0xFFEAE3FF)
val SliderTrackColor = Color(0xFF000000)

val NormalDamageShieldColor = Color(0xFF009900)
val AllDamageShieldColor = Color(0xffdaa520)

val scrollColor = Color(0xBBE9DDB8)
val eventColor = Color(0xBBA48B73)
val allyColor = Color(0xBBB9AE99)
val equipColor = Color(0xBB9FA7AE)

val qualityColor1 = Color(0xff35a645) // 暗绿，奖励感，低饱和
val qualityColor2 = Color(0xff4a8bbe) // 暗蓝，选择感，低饱和
val qualityColor3 = Color(0xffc46528) // 暗橙，史诗感，低饱和
val qualityColor4 = Color(0xffae2e2e) // 暗红，紧张感，低饱和
val qualityColorPurple = Color(0xff573a9c) // 暗紫，战略感，低饱和
val qualityColorYellow = Color(0xfffcb90b) // 暗黄，随机感，低饱和


fun Int.toQualityColor(): Color {
    return when (this) {
        1 -> qualityColor1
        2 -> qualityColor2
        3 -> qualityColor3
        else -> qualityColor4
    }
}

fun Ally.getColor(): Color {
    return quality.toQualityColor()
}

fun DamageType.getTextColor(): Color {
    return when (this) {
        DamageType.DamageType1 -> Color.Red
        DamageType.DamageType2 -> SkillLevel2Color
        DamageType.DamageType3 -> Color.Yellow
        DamageType.DamageType4 -> SkillLevel6Color
        DamageType.DamageType5 -> Color.White
        else -> Color.White
    }
}