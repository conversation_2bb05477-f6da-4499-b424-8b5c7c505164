package com.moyu.chuanqirensheng.ui.theme

import com.moyu.chuanqirensheng.util.composeDp

val dialogWidth = 360.composeDp()
val dialogHeight = 600.composeDp()
val dialogBigHeight = 790.composeDp()
val dialogMediumPlusHeight = 500.composeDp()
val dialogMediumHeight = 480.composeDp()
val dialogSmallHeight = 350.composeDp()

val reputationHugeItemWidth = 165.composeDp()


val imageTiny = 14.composeDp()
val imageTinyPlus = 16.composeDp()
val imageSmall = 20.composeDp()
val imageSmallPlus = 28.composeDp()
val imageSmallPlusFrame = 30.composeDp()
val imageMediumMinus = 34.composeDp()
val imageMedium = 36.composeDp()
val imageLarge = 44.composeDp()
val imageLargeFrame = 52.composeDp()
val imageLargePlus = 58.composeDp()
val imageHugeLite = 68.composeDp()
val imageHugeLiteFrame = 72.composeDp()
val imageHuge = 86.composeDp()

val moneyWidth = 84.composeDp()
val moneyWidthMinus = 80.composeDp()
val moneyExtraWidth = 96.composeDp()
val moneyMinyWidth = 74.composeDp()
val moneyHeight = 26.composeDp()

val powerWidth = 106.composeDp()
val powerHeight = 38.composeDp()

val cardNumWidth = 200.composeDp()
val cardNumSmallWidth = 160.composeDp()
val cardNumHeight = 26.composeDp()
val cardNumSmallHeight = 20.composeDp()

// 战斗相关
val singleRoleWidth = 54.composeDp()
val singleRoleHeight = 66.composeDp()
val buffSize = 10.composeDp()

val padding0 = 0.composeDp()
val padding1 = 1.composeDp()
val padding2 = 2.composeDp()
val padding3 = 3.composeDp()
val padding4 = 4.composeDp()
val padding5 = 5.composeDp()
val padding6 = 6.composeDp()
val padding7 = 7.composeDp()
val padding8 = 8.composeDp()
val padding9 = 9.composeDp()
val padding10 = 10.composeDp()
val padding12 = 12.composeDp()
val padding14 = 14.composeDp()
val padding16 = 16.composeDp()
val padding17 = 17.composeDp()
val padding18 = 18.composeDp()
val padding19 = 19.composeDp()
val padding20 = 20.composeDp()
val padding22 = 22.composeDp()
val padding26 = 26.composeDp()
val padding28 = 28.composeDp()
val padding30 = 30.composeDp()
val padding32 = 32.composeDp()
val padding34 = 34.composeDp()
val padding36 = 36.composeDp()
val padding39 = 39.composeDp()
val padding40 = 40.composeDp()
val padding42 = 42.composeDp()
val padding44 = 44.composeDp()
val padding45 = 45.composeDp()
val padding48 = 48.composeDp()
val padding50 = 50.composeDp()
val padding51 = 51.composeDp()
val padding53 = 53.composeDp()
val padding54 = 54.composeDp()
val padding55 = 55.composeDp()
val padding58 = 58.composeDp()
val padding60 = 60.composeDp()
val padding62 = 62.composeDp()
val padding64 = 64.composeDp()
val padding66 = 66.composeDp()
val padding69 = 69.composeDp()
val padding70 = 70.composeDp()
val padding72 = 72.composeDp()
val padding76 = 76.composeDp()
val padding80 = 80.composeDp()
val padding82 = 82.composeDp()
val padding84 = 84.composeDp()
val padding86 = 86.composeDp()
val padding90 = 90.composeDp()
val padding93 = 93.composeDp()
val padding96 = 96.composeDp()
val padding100 = 100.composeDp()
val padding106 = 106.composeDp()
val padding108 = 108.composeDp()
val padding110 = 110.composeDp()
val padding120 = 120.composeDp()
val padding126 = 126.composeDp()
val padding130 = 130.composeDp()
val padding140 = 140.composeDp()
val padding145 = 145.composeDp()
val padding150 = 150.composeDp()
val padding158 = 158.composeDp()
val padding165 = 165.composeDp()
val padding170 = 170.composeDp()
val padding180 = 180.composeDp()
val padding186 = 186.composeDp()
val padding200 = 200.composeDp()
val padding212 = 212.composeDp()
val padding220 = 220.composeDp()
val padding226 = 226.composeDp()
val padding240 = 240.composeDp()
val padding244 = 244.composeDp()
val padding250 = 250.composeDp()
val padding260 = 260.composeDp()
val padding268 = 268.composeDp()
val padding280 = 280.composeDp()
val padding290 = 290.composeDp()
val padding300 = 300.composeDp()
val padding320 = 320.composeDp()
val padding340 = 340.composeDp()
val padding360 = 360.composeDp()
val padding380 = 380.composeDp()
val padding400 = 400.composeDp()
val padding420 = 420.composeDp()
val padding435 = 435.composeDp()
val padding440 = 440.composeDp()
val padding460 = 460.composeDp()
val padding480 = 480.composeDp()
val padding500 = 500.composeDp()
val padding520 = 520.composeDp()
val padding540 = 540.composeDp()
val padding570 = 570.composeDp()
val padding640 = 640.composeDp()
val padding670 = 670.composeDp()

val hugeButtonWidth = 182.composeDp()
val hugeButtonHeight = 120.composeDp()
val bigButtonWidth = 158.composeDp()
val bigButtonHeight = 70.composeDp()
val bottomItemSize = 84.composeDp()

val buttonMinusWidth = 92.composeDp()
val buttonMinusHeight = 42.composeDp()
val buttonWidth = 118.composeDp()
val buttonHeight = 50.composeDp()
val smallButtonWidth = 58.composeDp()
val smallButtonHeight = 32.composeDp()

val tabButtonHeight = 40.composeDp()
val tabButtonBigHeight = 44.composeDp()
val tabButtonWidth = 56.composeDp()
val tabButtonBigWidth = 108.composeDp()

val animateSmall = 12.composeDp()
val animateLarge = 24.composeDp()

val shakeDp = 3.composeDp()

val backIconHeight = 42.composeDp()
val titleHeight = 56.composeDp()

val roleEffectWidth = 220.composeDp()
val roleEffectHeight = 300.composeDp()


val codeInputWidth = 210.composeDp()
val codeInputHeight = 40.composeDp()

val textFieldHeight = 40.composeDp()

val hpHeight = 12.composeDp()

val filterWidth = 92.composeDp()
val filterHeight = 32.composeDp()

val propertyBigWidth = 96.composeDp()
val propertyWidth = 78.composeDp()
val propertyBigHeight = 22.composeDp()

val userHeadWidth = 142.composeDp()
val userHeadHeight = 40.composeDp()

val propertyBigImageSize = 20.composeDp()

val eventCardWidth = 180.composeDp()
val eventCardHeight = 248.composeDp()
val eventCardBigWidth = 226.composeDp()
val eventCardBigHeight = 280.composeDp()

val shopItemWidth = 118.composeDp()

val slideWidth = 180.composeDp()
val slideHeight = 36.composeDp()

val pvpRecordFrameHeight = 110.composeDp()

val eventTopLayoutHeight = 160.composeDp()

val labelWidth = 300.composeDp()
val labelHeight = 48.composeDp()

val cheatFrameHeight = 158.composeDp()
val cheatFrameWidth = 136.composeDp()

