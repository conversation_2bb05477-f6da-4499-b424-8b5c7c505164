package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.star
import shared.generated.resources.star2


@Composable
fun Stars(modifier: Modifier = Modifier, stars: Int, style: TextStyle = MaterialTheme.typography.h3) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Image(
            painter = painterResource(Res.drawable.star),
            contentDescription = null,
            modifier = Modifier.fillMaxSize()
        )
        StrokedText(
            text = "$stars",
            style = style,
        )
    }
}


@Composable
fun ClickableStars(
    modifier: Modifier = Modifier,
    starHeight: Dp = padding42,
    callback: (Int) -> Unit
) {
    val rank = remember {
        mutableStateOf(0)
    }
    Row(modifier = modifier, horizontalArrangement = Arrangement.spacedBy(padding4)) {
        repeat(5) {
            EffectButton(onClick = {
                rank.value = it
                callback(it + 1)
            }) {
                Image(
                    painter = painterResource(if (rank.value > it) Res.drawable.star else Res.drawable.star2),
                    contentDescription = null,
                    modifier = Modifier.height(
                        starHeight
                    ),
                    contentScale = ContentScale.FillHeight
                )
            }
        }
    }
}