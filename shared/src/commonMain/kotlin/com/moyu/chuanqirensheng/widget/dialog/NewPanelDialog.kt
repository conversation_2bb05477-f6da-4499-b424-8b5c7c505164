package com.moyu.chuanqirensheng.widget.dialog

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.dialogHeight
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.widget.common.CloseButton
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.detail_dialog_frame

@Composable
fun NewPanelDialog(
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
    dialogWidthDp: Dp = dialogWidth, // 假设这些是定义好的常量
    dialogHeightDp: Dp = dialogHeight,
    showClose: Boolean = true,
    resourceContent: @Composable BoxScope.() -> Unit = {},
    content: @Composable ColumnScope.() -> Unit
) {
    EmptyDialog(onDismissRequest = onDismissRequest, showTips = showClose) {
        Column(
            modifier = modifier
                .size(dialogWidthDp, dialogHeightDp)
                .paint(
                    painter = painterResource(Res.drawable.detail_dialog_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(horizontal = padding16),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(Modifier.size(padding16))
            content()
        }
        if (showClose) {
            CloseButton(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .graphicsLayer {
                        translationX = padding22.toPx()
                        translationY = -padding16.toPx()
                    },
                onClick = onDismissRequest
            )
        }
        Box(Modifier.align(Alignment.TopStart).padding(start = padding8, top = padding8)) {
            resourceContent()
        }
    }
}