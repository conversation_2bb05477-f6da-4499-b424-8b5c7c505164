package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.ui.theme.backIconHeight
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.titleHeight
import com.moyu.chuanqirensheng.util.BackUtil
import com.moyu.chuanqirensheng.util.immersionBarHeightInDp
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.HUGE_GAP
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_exit
import shared.generated.resources.quit_page
import shared.generated.resources.screen_title

@Composable
fun GameBackground(
    background: DrawableResource? = null,
    title: String = "",
    bgMask: Color? = null,
    showCloseIcon: Boolean = true,
    topContent: @Composable (BoxScope.() -> Unit)? = null,
    content: @Composable BoxScope.() -> Unit = {}
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 背景图
        background?.let {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                colorFilter = bgMask?.let { mask ->
                    ColorFilter.tint(mask, BlendMode.SrcAtop)
                },
                painter = kmpPainterResource(background),
                contentDescription = null
            )
        }

        Column(Modifier.fillMaxSize()) {
            // 顶部内容与标题栏
            if (topContent != null) {
                // 有自定义 topContent 时，包裹进一个 Box
                Box(contentAlignment = Alignment.Center) {
                    Box(content = topContent)
                    // 依旧可以放标题栏和关闭按钮
                    TitleBar(title = title, showCloseIcon = showCloseIcon)
                }
            } else {
                // 如果没有自定义的 topContent，则根据 showCloseIcon 决定要不要显示标题栏
                if (title.isNotEmpty()) {
                    TitleBar(title = title, showCloseIcon = showCloseIcon)
                }
            }

            // 主体内容
            Box(modifier = Modifier.fillMaxSize(), content = content)
        }
    }
}

@Composable
fun TitleCloseButton(
    modifier: Modifier = Modifier,
    onBackPressed: () -> Unit
) {
    EffectButton(modifier = modifier, clickGap = HUGE_GAP, onClick = {
        onBackPressed()
    }) {
        Image(
            modifier = Modifier.padding(end = padding28)
                .height(backIconHeight)
                .scale(1.3f),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(Res.drawable.common_exit),
            contentDescription = stringResource(Res.string.quit_page)
        )
    }
}

@Composable
private fun TitleBar(
    title: String,
    showCloseIcon: Boolean
) {
    Box(
        modifier = Modifier
            .padding(top = immersionBarHeightInDp)
            .fillMaxWidth()
            .height(titleHeight),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.fillMaxHeight(),
            painter = painterResource(Res.drawable.screen_title),
            contentScale = ContentScale.FillHeight,
            contentDescription = null
        )
        Row(verticalAlignment = Alignment.CenterVertically) {
            StrokedText(
                text = title,
                style = MaterialTheme.typography.h1,
                color = Color.White
            )
        }
        if (showCloseIcon) {
            TitleCloseButton(
                Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = padding10)
            ) {
                BackUtil.actionBack()
            }
        }
    }
}