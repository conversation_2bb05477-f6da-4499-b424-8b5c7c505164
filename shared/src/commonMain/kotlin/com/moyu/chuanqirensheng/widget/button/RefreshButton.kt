package com.moyu.chuanqirensheng.widget.button

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.menu_filter

@Composable
fun RefreshButton(modifier: Modifier = Modifier, text: String, callback: () -> Unit) {
    EffectButton(modifier = modifier, onClick = {
        callback()
    }) {
        Image(
            modifier = Modifier.width(imageLargePlus),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(Res.drawable.menu_filter),
            contentDescription = text
        )
    }
}