package com.moyu.chuanqirensheng.widget.effect

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.core.model.skill.Skill


val winBattleEffect = Pair("reward_", 16)
val loseBattleEffect = Pair("effect11_", 6)
val starUpEffect = Pair("starup_", 7)
val turnEffect = Pair("next_", 8)

val newTurnEffectState = mutableStateOf<Pair<String, Int>?>(turnEffect)
val castSkillEffectState = mutableIntStateOf(0)



val cardEffects = mutableStateOf<Skill?>(null)

fun restartEffect(state: MutableState<Pair<String, Int>?>, effect: Pair<String, Int>) {
    // todo
}