package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.padding165
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding400
import com.moyu.chuanqirensheng.ui.theme.padding480
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.close
import shared.generated.resources.common_exit
import shared.generated.resources.quit_page


enum class PanelSize(val width: Dp, val height: Dp) {
    Small(padding380, padding165),
    Medium(padding400, padding400),
    Normal(padding400, padding480),

}

@Composable
fun CloseButton(modifier: Modifier, onClick: () -> Unit) {
    EffectButton(modifier = modifier
        .semantics {
            contentDescription = AppWrapper.getStringKmp(Res.string.close)
        }
        .size(imageLargePlus),
        onClick = {
            onClick()
        }) {
        Image(
            modifier = Modifier
                .fillMaxSize(),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(Res.drawable.common_exit),
            contentDescription = stringResource(Res.string.quit_page)
        )
    }
}
