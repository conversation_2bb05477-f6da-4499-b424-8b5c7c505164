package com.moyu.chuanqirensheng.widget.effect

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.qualityColor1
import kotlin.random.Random


// 颜色工具：亮/暗一点
private fun Color.lighten(f: Float = .15f) =
    copy(red + (1 - red) * f, green + (1 - green) * f, blue + (1 - blue) * f)

private fun Color.darken(f: Float = .15f) =
    copy(red * (1 - f), green * (1 - f), blue * (1 - f))

/** 给魔法条加圆角、投影、底色、纹理、边框 */
fun Modifier.magicFrame(
    base: Color,
    borderColor: Color,
    corner: Dp = padding12,
    elevation: Dp = padding16,
    border: Dp = padding5,
    texture: Texture = Texture.DIAGONAL_STRIPE,  // 想换别的就改这里
) = composed {
    val shape = RoundedCornerShape(corner)
    clip(shape)
        .shadow(elevation, shape, clip = true)
        // 底色
        .background(base)
        // 纹理
        .applyTexture(texture, base)
        // 边框
        .border(border, borderColor, shape)
}

/* ====== 2 种纹理实现 ====== */

/** 45° 斜纹，像素风 UI 常用 */
private fun Modifier.diagonalStripeTexture(base: Color) = drawBehind {
    val stripeColor = base.darken(.18f).copy(alpha = .25f)   // 深一点、半透明
    val step = padding6.toPx()                                   // 条纹间距
    val stroke = padding1.toPx()

    // 左上到右下画平行线，起点从 -height 开始可覆盖满
    var x = -size.height
    while (x < size.width) {
        drawLine(
            color = stripeColor,
            start = Offset(x, 0f),
            end = Offset(x + size.height, size.height),
            strokeWidth = stroke
        )
        x += step
    }
}

/** 轻微水平噪声，类似纸张/布纹 */
private fun Modifier.horizontalNoiseTexture(base: Color) = drawWithContent {
    drawContent()

    val dark = base.darken(.1f)
    val light = base.lighten(.1f)
    val rowHeight = padding2.toPx() // 每条噪声带高度

    var y = 0f
    while (y < size.height) {
        val c = if ((y / rowHeight).toInt() % 2 == 0) dark else light
        drawRect(
            color = c.copy(alpha = .07f),
            topLeft = Offset(0f, y),
            size = Size(width = size.width, height = rowHeight)
        )
        y += rowHeight
    }
}

private fun Modifier.verticalWoodTexture(base: Color) = drawWithContent {
    drawContent()

    val stripeW = padding4.toPx()
    var x = 0f
    while (x < size.width) {
        val shade = if ((x / stripeW).toInt() % 2 == 0)
            base.lighten(.12f)
        else
            base.darken(.12f)
        drawRect(
            color = shade.copy(alpha = .20f),
            topLeft = Offset(x, 0f),
            size = Size(stripeW, size.height)
        )
        x += stripeW
    }
}


private fun Modifier.speckledNoiseTexture(base: Color) = drawWithContent {
    drawContent()
    val dotColor = base.darken(.25f)
    val density = 0.0015f            // 越大点越多

    val count = (size.width * size.height * density).toInt()
    repeat(count) {
        val x = Random.nextFloat() * size.width
        val y = Random.nextFloat() * size.height
        drawRect(
            color = dotColor.copy(alpha = .30f),
            topLeft = Offset(x, y),
            size = Size(1f, 1f)
        )
    }
}


private fun Modifier.gridTexture(base: Color) = drawWithContent {
    drawContent()

    val step = padding6.toPx()
    val stroke = padding1.toPx()
    val gridColor = base.darken(.18f).copy(alpha = .18f)

    // 竖线
    var x = 0f
    while (x <= size.width) {
        drawLine(
            gridColor,
            start = Offset(x, 0f),
            end   = Offset(x, size.height),
            strokeWidth = stroke
        ); x += step
    }
    // 横线
    var y = 0f
    while (y <= size.height) {
        drawLine(
            gridColor,
            start = Offset(0f, y),
            end   = Offset(size.width, y),
            strokeWidth = stroke
        ); y += step
    }
}


private fun Modifier.checkerTexture(base: Color) = drawWithContent {
    drawContent()

    val block = padding8.toPx()
    val (dark, light) = if (base == qualityColor1) {
        base.darken(.4f) to base.lighten(.4f)
    } else {
        base.darken(.2f) to base.lighten(.2f)
    }

    var y = 0f
    var toggleRow = false
    while (y < size.height) {
        var x = 0f
        var toggleCol = toggleRow
        while (x < size.width) {
            drawRect(
                color = if (toggleCol) dark else light,
                topLeft = Offset(x, y),
                size = Size(block, block),
                alpha = .10f
            )
            toggleCol = !toggleCol
            x += block
        }
        toggleRow = !toggleRow
        y += block
    }
}


private fun Modifier.vignetteTexture() = drawWithContent {
    drawContent()

    val radius = size.minDimension / 1.2f
    drawRect(
        brush = Brush.radialGradient(
            colors = listOf(Color.Black.copy(alpha = .25f), Color.Transparent),
            center = center,
            radius = radius
        ),
        size = size,
        blendMode = BlendMode.Multiply   // 只压暗，不影响色相
    )
}


fun Modifier.applyTexture(texture: Texture, base: Color) = when (texture) {
    Texture.DIAGONAL_STRIPE  -> diagonalStripeTexture(base)
    Texture.HORIZONTAL_NOISE -> horizontalNoiseTexture(base)
    Texture.VERTICAL_WOOD    -> verticalWoodTexture(base)
    Texture.SPECKLED_NOISE   -> speckledNoiseTexture(base)
    Texture.GRID             -> gridTexture(base)
    Texture.CHECKER          -> checkerTexture(base)
    Texture.VIGNETTE         -> vignetteTexture()
}

/** 可选纹理枚举，方便切换 */
enum class Texture {
    DIAGONAL_STRIPE,
    HORIZONTAL_NOISE,
    VERTICAL_WOOD,
    SPECKLED_NOISE,
    GRID,
    CHECKER,
    VIGNETTE
}
