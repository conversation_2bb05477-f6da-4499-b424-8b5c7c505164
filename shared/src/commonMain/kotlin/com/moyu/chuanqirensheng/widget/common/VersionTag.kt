package com.moyu.chuanqirensheng.widget.common

import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.drawscope.Stroke
import com.moyu.chuanqirensheng.util.getVersion
import com.moyu.chuanqirensheng.widget.effect.StrokedText


@Composable
fun VersionTag(modifier: Modifier = Modifier) {
    StrokedText(
        modifier = modifier,
        text = "V." + getVersion(),
        style = MaterialTheme.typography.h2,
    )
}