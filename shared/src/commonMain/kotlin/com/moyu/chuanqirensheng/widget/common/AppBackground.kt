package com.moyu.chuanqirensheng.widget.common

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.moyu.chuanqirensheng.media.VideoPlayer2
import com.moyu.chuanqirensheng.platform.getPlatform
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.util.Platform
import com.moyu.chuanqirensheng.util.designHeightInDp
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.game_bg_bottom

const val GAME_BG_PIC = "game_bg"

@Composable
fun AppLifecycleObserver(
    onResume: () -> Unit = {},
    onStart: () -> Unit = {},
    onPause: () -> Unit = {},
    onStop: () -> Unit = {},
) {
    val lifecycleOwner = LocalLifecycleOwner.current

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_RESUME -> onResume()
                Lifecycle.Event.ON_START -> onStart()
                Lifecycle.Event.ON_PAUSE -> onPause()
                Lifecycle.Event.ON_STOP -> onStop()
                else -> {}
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

@Composable
fun AppBackground() {
    val showVideo = remember {
        mutableStateOf(false)
    }

    if (getPlatform() == Platform.IOS) {
        AppLifecycleObserver(
            onResume = {
//                println("== onResume")
            },
            onStart = {
//                println("== onStart")
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    delay(1200)
                    showVideo.value = true
                }
            },
            onPause = {
//                println("== onPause")
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    delay(300)
                    showVideo.value = false
                }
            },
            onStop = {
//                println("== onStop")
            },
        )
    }

    LaunchedEffect(Unit) {
        delay(1200)
        showVideo.value = true
    }
    // TODO 现在视频不是全屏的，底部补一点背景图遮挡下，height是根据图片高度和总高度900dp换算的
    Box(Modifier.fillMaxSize()) {
        Image(
            modifier = Modifier.align(Alignment.BottomCenter).fillMaxWidth().height((designHeightInDp / 3.84f).dp),
            contentScale = ContentScale.Crop,
            painter = kmpPainterResource(Res.drawable.game_bg_bottom),
            contentDescription = null
        )
    }
    if (showVideo.value) {
        VideoPlayer2(modifier = Modifier.fillMaxSize().padding(bottom = padding90))
    }
    AnimatedVisibility(
        visible = !showVideo.value,
        enter = fadeIn(),
        exit = fadeOut(),
    ) {
        Image(
            modifier = Modifier.fillMaxSize().padding(bottom = padding90),
            contentScale = ContentScale.Crop,
            painter = kmpPainterResource(GAME_BG_PIC),
            alignment = Alignment.TopCenter,
            contentDescription = null
        )
    }
}