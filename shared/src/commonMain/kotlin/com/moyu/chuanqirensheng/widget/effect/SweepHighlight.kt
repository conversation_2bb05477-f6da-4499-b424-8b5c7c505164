package com.moyu.chuanqirensheng.widget.effect

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import kotlinx.coroutines.delay
import kotlin.random.Random


/**
 * 随机节拍的高光扫过：等待 (minDelay‒maxDelay) 毫秒后，
 * 以 (minDuration‒maxDuration) 毫秒扫过一次，再循环。
 */
fun Modifier.sweepHighlight(
    minDelay:  Int = 600,   // 最短等待
    maxDelay:  Int = 2400,  // 最长等待
    minDuration: Int = 1400,// 最慢扫速
    maxDuration: Int = 2600,// 最快扫速
    tilt: Float = .45f,
    widthFraction: Float = .25f
) = composed {

    // 进度：-1f = 左侧屏外，2f = 右侧屏外
    val progress = remember { Animatable(-1f) }

    // 协程循环：随机 delay + 随机 tween
    LaunchedEffect(Unit) {
        while (true) {
            delay(Random.nextLong(minDelay.toLong(), maxDelay.toLong()))

            val dur = Random.nextInt(minDuration, maxDuration)
            progress.snapTo(-1f) // 从左侧外缘起跑
            progress.animateTo(
                targetValue = 2f,
                animationSpec = tween(dur, easing = androidx.compose.animation.core.LinearEasing)
            )
        }
    }

    // 绘制
    drawWithContent {
        drawContent()

        val w = size.width
        val h = size.height
        val band = w * widthFraction
        val x = progress.value * w                    // -w…2w
        val start = Offset(x - band, 0f)
        val end   = Offset(x, h * tilt)

        drawRect(
            brush = Brush.linearGradient(
                listOf(
                    Color.Transparent,
                    Color.White.copy(alpha = .55f),
                    Color.Transparent
                ),
                start = start,
                end   = end
            ),
            blendMode = BlendMode.Lighten
        )
    }
}