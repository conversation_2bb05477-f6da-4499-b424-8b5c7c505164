package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.imageTiny
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.common_lock
import shared.generated.resources.red_icon
import shared.generated.resources.tab_selected

@Composable
fun TagView(
    modifier: Modifier = Modifier,
    title: DrawableResource,
    selected: Boolean = true,
    frame: DrawableResource = Res.drawable.tab_selected,
    red: Boolean,
    lock: Boolean
) {
    val colorFilter = if (lock)
        ColorFilter.tint(
            B50, BlendMode.SrcAtop
        )
    else null
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        if (selected) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillHeight,
                colorFilter = colorFilter,
                painter = painterResource(frame),
                contentDescription = null
            )
        }
        Image(
            modifier = Modifier.fillMaxSize().padding(padding4),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(title),
            colorFilter = colorFilter,
            contentDescription = null
        )
        if (red) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(imageTiny),
                colorFilter = colorFilter,
                painter = painterResource(Res.drawable.red_icon),
                contentDescription = null
            )
        }
        if (lock) {
            Image(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(imageSmallPlus)
                   ,
                painter = painterResource(Res.drawable.common_lock),
                contentDescription = null
            )
        }
    }
}