package com.moyu.chuanqirensheng.widget.filter

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.filterHeight
import com.moyu.chuanqirensheng.ui.theme.filterWidth
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.QUICK_GAP
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.button_gray_long
import shared.generated.resources.button_green_long


@Composable
fun <T> FilterLayout(
    modifier: Modifier,
    show: MutableState<Boolean>,
    filter: SnapshotStateList<ItemFilter<T>>,
    filterList: List<ItemFilter<T>>,
    callback: (()-> Unit)? = null
) {
    if (show.value) {
        Box(modifier = Modifier
            .fillMaxSize()
            .clickable {
                show.value = false
            }
            .background(B50))
    }
    AnimatedVisibility(
        modifier = modifier, visible = show.value
    ) {
        Column(modifier = Modifier.verticalScroll(rememberScrollState())) {
            repeat(filterList.size) { selectIndex ->
                val selected = filter.contains(filterList[selectIndex])
                EffectButton(clickGap = QUICK_GAP, onClick = {
                    if (filter.contains(filterList[selectIndex])) {
                        filter.remove(filterList[selectIndex])
                    } else {
                        filter.removeAll {
                            filterList[selectIndex].excludeTypes.intersect(it.excludeTypes.toSet())
                                .isNotEmpty()
                        }
                        filter.add(filterList[selectIndex])
                    }
                    callback?.invoke()
                }) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.padding(bottom = padding2)
                    ) {
                        val res = if (selected) Res.drawable.button_green_long
                        else Res.drawable.button_gray_long
                        Image(
                            modifier = Modifier.size(filterWidth, filterHeight),
                            painter = painterResource(res),
                            contentScale = ContentScale.FillBounds,
                            contentDescription = null
                        )
                        StrokedText(
                            text = filterList[selectIndex].name,
                            style = MaterialTheme.typography.h3,
                            color = filterList[selectIndex].color
                        )
                    }
                }
            }
        }
    }
}