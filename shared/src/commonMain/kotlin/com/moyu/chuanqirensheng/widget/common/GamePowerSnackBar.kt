package com.moyu.chuanqirensheng.widget.common

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.tween
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.ui.theme.SkillLevel1Color
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.power_tips

const val INIT_POWER = 180

data class PowerSnackInfo(
    val value: Int,
    val uniqueId: Long = getCurrentTime(true) // 或者 AtomicLong.incrementAndGet()
)

@Composable
fun GamePowerSnackBar() {
    if (AwardManager.getAllBattlePower() > INIT_POWER) {
        // 监听当前SnackInfo。当它变化时触发动画
        AnimatedContent(
            targetState = Dialogs.powerSnack.value,
            // 重点：transitionSpec决定了旧内容如何退出，新内容如何进入
            transitionSpec = {
                // 这里你可以根据“新旧uniqueId的大小”来分别指定动画
                // 如果只是想快速淡出+淡入：
                scaleIn(animationSpec = tween(durationMillis = 200))
                    .togetherWith(scaleOut(animationSpec = tween(durationMillis = 50)))
            }
        ) { snackInfo ->
            // 如果 value==0 不显示
            if (snackInfo.value > 0) {
                // 定时延迟 1600 毫秒后检查是否还是当前这条，如果是，则设置为0来收起
                LaunchedEffect(snackInfo.uniqueId) {
                    delay(1600)
                    if (Dialogs.powerSnack.value.uniqueId == snackInfo.uniqueId) {
                        Dialogs.powerSnack.value = PowerSnackInfo(0)
                    }
                }

                // 这里是你原先的toast UI
                Box(contentAlignment = Alignment.Center) {
                    EffectButton(modifier = Modifier, onClick = {
                        Dialogs.powerSnack.value = PowerSnackInfo(0)
                    }) {
                        Row(
                            Modifier.width(padding300)
                                .paint(
                                    painterResource(Res.drawable.power_tips),
                                    contentScale = ContentScale.FillWidth
                                )
                                .padding(horizontal = padding4, vertical = padding2),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center
                        ) {
                            Spacer(Modifier.size(padding36))
                            StrokedText(
                                modifier = Modifier.padding(start = padding2),
                                text = AwardManager.getAllBattlePower().toString(),
                                style = MaterialTheme.typography.h1.copy(fontWeight = FontWeight.Thin),
                                color = Color.White
                            )
                            Spacer(Modifier.size(padding6))
                            StrokedText(
                                text = "+" + snackInfo.value,
                                style = MaterialTheme.typography.h1.copy(fontWeight = FontWeight.Thin),
                                color = SkillLevel1Color
                            )
                        }
                    }
                }
            }
        }
    }
}