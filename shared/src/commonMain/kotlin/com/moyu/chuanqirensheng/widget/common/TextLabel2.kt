package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.sell_label


@Composable
fun TextLabel2(
    modifier: Modifier = Modifier,
    labelSize: LabelSize = LabelSize.Medium2,
    frame: DrawableResource = Res.drawable.sell_label,
    text: String,
    color: Color = Color.White,
    translateY: Dp = padding0
) {
    Box(
        modifier = modifier.size(labelSize.width, labelSize.height),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(frame),
            contentScale = ContentScale.FillBounds,
            contentDescription = null
        )
        StrokedText(
            modifier = Modifier.graphicsLayer {
                this.translationY = translateY.toPx()
            },
            text = text,
            style = if (text.length >= 10) labelSize.getTextStyleMinus() else labelSize.getTextStyle(),
            maxLines = 2,
            textAlign = TextAlign.Center,
            color = color
        )
    }
}