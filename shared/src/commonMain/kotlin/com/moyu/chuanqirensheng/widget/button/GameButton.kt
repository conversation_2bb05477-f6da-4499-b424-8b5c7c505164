package com.moyu.chuanqirensheng.widget.button


import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.feature.resource.DiamondPoint
import com.moyu.chuanqirensheng.feature.resource.KeyPoint
import com.moyu.chuanqirensheng.feature.resource.PvpPoint
import com.moyu.chuanqirensheng.feature.resource.RealMoneyPoint
import com.moyu.chuanqirensheng.feature.resource.ReputationPoint
import com.moyu.chuanqirensheng.feature.resource.ResourcesPoint
import com.moyu.chuanqirensheng.platform.hasBilling
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.bigButtonWidth
import com.moyu.chuanqirensheng.ui.theme.buttonHeight
import com.moyu.chuanqirensheng.ui.theme.buttonMinusHeight
import com.moyu.chuanqirensheng.ui.theme.buttonMinusWidth
import com.moyu.chuanqirensheng.ui.theme.buttonWidth
import com.moyu.chuanqirensheng.ui.theme.hugeButtonHeight
import com.moyu.chuanqirensheng.ui.theme.hugeButtonWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.smallButtonHeight
import com.moyu.chuanqirensheng.ui.theme.smallButtonWidth
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.button_blue
import shared.generated.resources.button_gray
import shared.generated.resources.button_gray_long
import shared.generated.resources.button_green
import shared.generated.resources.button_green_huge
import shared.generated.resources.button_green_long
import shared.generated.resources.common_lock
import shared.generated.resources.free
import shared.generated.resources.locked
import shared.generated.resources.new_ad_icon
import shared.generated.resources.real_money_dollar
import shared.generated.resources.real_money_unit
import shared.generated.resources.red_icon
import shared.generated.resources.touch_ad_tips


enum class ButtonStyle {
    Green, Gray, Blue, GreenLong, GrayLong
}

enum class ButtonSize {
    Small, MediumMinus, Medium, Big, Huge
}

enum class ButtonType {
    Normal, Diamond, Key, AiFaDian, Gold, Ad, Pvp, RealMoney, Reputation
}

@Composable
fun GameButton(
    modifier: Modifier = Modifier,
    text: String = "",
    textColor: Color? = null,
    buttonSize: ButtonSize = ButtonSize.Medium,
    enabled: Boolean = true,
    mute: Boolean = false,
    locked: Boolean = false,
    longPress: Boolean = false,
    clickGap: Int = LARGE_GAP,
    redIcon: Boolean = false,
    hapticFeedbackType: HapticFeedbackType = HapticFeedbackType.TextHandleMove,
    buttonType: ButtonType = ButtonType.Normal,
    buttonStyle: ButtonStyle = ButtonStyle.Green,
    onClick: () -> Unit = {},
) {
    val pressing = remember {
        mutableStateOf(false)
    }
    EffectButton(
        modifier = when (buttonSize) {
            ButtonSize.Huge -> modifier.size(hugeButtonWidth, hugeButtonHeight)
            ButtonSize.Big -> modifier.size(bigButtonWidth, bigButtonHeight)
            ButtonSize.MediumMinus -> modifier.size(buttonMinusWidth, buttonMinusHeight)
            ButtonSize.Medium -> modifier.size(buttonWidth, buttonHeight)
            else -> modifier.size(smallButtonWidth, smallButtonHeight)
        }, mute = mute, clickGap = clickGap, onClick = onClick, longPressEnabled = longPress, pressing = pressing
    ) {
        val colorFilter = if (locked || !enabled || pressing.value) {
            ColorFilter.tint(
                B50, BlendMode.SrcAtop
            )
        } else {
            null
        }
        val buttonSrc = when (buttonStyle) {
                ButtonStyle.Gray -> Res.drawable.button_gray      //更改为新图片
                ButtonStyle.GreenLong -> Res.drawable.button_green_long      //更改为新图片
                ButtonStyle.GrayLong -> Res.drawable.button_gray_long      //更改为新图片
                ButtonStyle.Green -> {
                    if (buttonSize == ButtonSize.Huge) {
                        Res.drawable.button_green_huge
                    } else {
                        Res.drawable.button_green
                    }
                } //新增
                ButtonStyle.Blue -> Res.drawable.button_blue //新增
            }
        Image(
            modifier = Modifier.fillMaxSize(),
            colorFilter = colorFilter,
            contentScale = ContentScale.FillBounds,
            painter = painterResource(buttonSrc),
            contentDescription = null
        )
        Row(verticalAlignment = Alignment.CenterVertically) {
            if (buttonType == ButtonType.Diamond) {
                if (text.toInt() != 0) {
                    DiamondPoint(cost = text.toInt())
                } else {
                    StrokedText(
                        modifier = Modifier.graphicsLayer {
                            translationY = buttonSize.fontTranslate().toPx()
                        }.scale(buttonSize.fontScale()),
                        text = stringResource(Res.string.free),
                        color = Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.Pvp) {
                if (text.toInt() != 0) {
                    PvpPoint(cost = text.toInt())
                } else {
                    StrokedText(
                        modifier = Modifier.graphicsLayer {
                            translationY = buttonSize.fontTranslate().toPx()
                        }.scale(buttonSize.fontScale()),
                        text = stringResource(Res.string.free),
                        color = if (pressing.value || locked || !enabled) W50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.Reputation) {
                if (text.toInt() != 0) {
                    ReputationPoint(cost = text.toInt())
                } else {
                    StrokedText(
                        modifier = Modifier.graphicsLayer {
                            translationY = buttonSize.fontTranslate().toPx()
                        }.scale(buttonSize.fontScale()),
                        text = stringResource(Res.string.free),
                        color = if (pressing.value || locked || !enabled) W50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.Key) {
                if (text.toInt() != 0) {
                    KeyPoint(cost = text.toInt())
                } else {
                    StrokedText(
                        modifier = Modifier.graphicsLayer {
                            translationY = buttonSize.fontTranslate().toPx()
                        }.scale(buttonSize.fontScale()),
                        text = stringResource(Res.string.free),
                        color = if (pressing.value || locked || !enabled) W50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.Gold) {
                if (text.toInt() != 0) {
                    ResourcesPoint(index = 0, cost = text.toInt())
                } else {
                    StrokedText(
                        modifier = Modifier.graphicsLayer {
                            translationY = buttonSize.fontTranslate().toPx()
                        }.scale(buttonSize.fontScale()),
                        text = stringResource(Res.string.free),
                        color = if (pressing.value || locked || !enabled) W50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.AiFaDian) {
                if (!hasBilling() || text.toDoubleOrNull() == null) {
                    StrokedText(
                        modifier = Modifier.graphicsLayer {
                            translationY = buttonSize.fontTranslate().toPx()
                        }.scale(buttonSize.fontScale()),
                        text = text,
                        color = if (pressing.value || locked || !enabled) W50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                } else {
                    StrokedText(
                        modifier = Modifier.graphicsLayer {
                            translationY = buttonSize.fontTranslate().toPx()
                        }.scale(buttonSize.fontScale()),
                        text = if (hasGoogleService()) text + stringResource(Res.string.real_money_dollar) else text + stringResource(Res.string.real_money_unit),
                        color = if (pressing.value || locked || !enabled) W50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.RealMoney) {
                if (!hasBilling()) {
                    RealMoneyPoint(cost = text.toInt())
                } else {
                    StrokedText(
                        modifier = Modifier.graphicsLayer {
                            translationY = buttonSize.fontTranslate().toPx()
                        }.scale(buttonSize.fontScale()),
                        text = text + stringResource(Res.string.real_money_dollar),
                        color = if (pressing.value || locked || !enabled) W50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.Ad) {
                Image(
                    modifier = Modifier.size(imageSmallPlus),
                    painter = painterResource(Res.drawable.new_ad_icon),
                    contentDescription = stringResource(Res.string.touch_ad_tips)
                )
            } else {
                StrokedText(
                    modifier = Modifier.graphicsLayer {
                        translationY = buttonSize.fontTranslate().toPx()
                    }.scale(buttonSize.fontScale()),
                    text = text,
                    color = textColor
                        ?: if (pressing.value || locked || !enabled) W50 else Color.White,
                    style = buttonSize.fontStyle(),
                    textAlign = TextAlign.Center
                )
            }
        }
        if (locked) {
            Image(
                modifier = Modifier.size(imageLarge),
                painter = painterResource(Res.drawable.common_lock),
                contentDescription = stringResource(Res.string.locked),
            )
        }
        if (redIcon) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(imageSmall)
                    .graphicsLayer {
                        translationX = padding6.toPx()
                        translationY = -padding4.toPx()
                    },
                painter = painterResource(Res.drawable.red_icon),
                contentDescription = null
            )
        }
    }
}


@Composable
fun ButtonSize.fontStyle(): TextStyle {
    return when (this) {
        ButtonSize.Big, ButtonSize.Huge -> MaterialTheme.typography.h1
        ButtonSize.Medium -> MaterialTheme.typography.h2
        ButtonSize.MediumMinus -> MaterialTheme.typography.h2
        else -> MaterialTheme.typography.h4
    }
}

@Composable
fun ButtonSize.fontScale(): Float {
    return when (this) {
        ButtonSize.Huge -> 1.3f
        else -> 1f
    }
}

fun ButtonSize.fontTranslate(): Dp {
    return when (this) {
        ButtonSize.Huge -> -padding6
        ButtonSize.Big -> -padding3
        else -> -padding1
    }
}