package com.moyu.chuanqirensheng.widget.effect

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathMeasure
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding3

object EffectColors {
    // Normal/Default color scheme - blue to cyan gradient with fade out
    val normal = listOf(
        Color.Blue,                     // Beam head - vibrant blue
        Color.Cyan,                     // Bright cyan for mid-section
        Color.Cyan.copy(alpha = 0.6f),  // Fading cyan
        Color.Cyan.copy(alpha = 0.3f),  // More transparent cyan
        Color.Transparent               // Beam tail completely fades out
    )

    // Combat/Tension color scheme - red to orange gradient with fade out
    val combat = listOf(
        Color(0xFFFF1744),              // Bright red for beam head
        Color(0xFFFF5252),              // Lighter red
        Color(0xFFFF8A65),              // Orange-red
        Color(0xFFFF8A65).copy(alpha = 0.4f), // Fading orange
        Color.Transparent               // Beam tail completely fades out
    )

    // Reward/Surprise color scheme - gold to yellow gradient with fade out
    val reward = listOf(
        Color(0xFFFFD700),              // Gold for beam head
        Color(0xFFFFC107),              // Amber
        Color(0xFFFFEB3B),              // Yellow
        Color(0xFFFFEB3B).copy(alpha = 0.5f), // Fading yellow
        Color.Transparent               // Beam tail completely fades out
    )
}


@Composable
fun EffectBorderBox(
    modifier: Modifier = Modifier,
    effectColor: List<Color>,
    rotationCount: Int? = null,        // null = 无限循环
    beamWidth: Dp = padding3,      // 光束粗细：默认 border 4×
    beamPercent: Float = .30f,         // 光束占全程长度百分比
    content: @Composable BoxScope.() -> Unit
) {
    /* -------- 动画进度 -------- */
    var rotationsDone by remember { mutableStateOf(0) }
    val playing = rotationCount == null || rotationsDone < rotationCount

    val prog by rememberInfiniteTransition("borderBeam")
        .animateFloat(
            0f, 1f,
            infiniteRepeatable(
                tween(600, easing = LinearEasing),
                RepeatMode.Restart
            ),
            label = "beamProgress"
        )

    LaunchedEffect(prog) {
        if (prog >= .99f && playing) rotationsDone++
    }

    val cur = if (!playing) 1f else prog        // 停止后停在终点

    /* --------- 绘制 --------- */
    Box(
        modifier = modifier.drawWithCache {
            /* 基础数据 */
            val strokeW = beamWidth.toPx()
            val corner  = padding12.toPx()
            val halfSW  = strokeW / 2
            val borderPath = Path().apply {
                addRoundRect(
                    RoundRect(
                        left   = halfSW,
                        top    = halfSW,
                        right  = size.width  - halfSW,
                        bottom = size.height - halfSW,
                        cornerRadius = CornerRadius(corner, corner)
                    )
                )
            }
            val meas = PathMeasure().apply { setPath(borderPath, true) }
            val len  = meas.length
            val beamLen = len * beamPercent

            /* 缓存在 lambda 外，提高性能 */
            onDrawWithContent {
                drawContent()                    // 先画原内容 + border

                if (!playing && rotationsDone >= (rotationCount ?: 0)) return@onDrawWithContent

                /* 计算光束起止 */
                val end   = cur * len
                val start = (end - beamLen + len) % len

                val beam = Path().apply {
                    // 把测距改成一次性采样，避免漏点
                    val steps = 60
                    (0..steps).forEach { i ->
                        val d = (start + beamLen * i / steps) % len
                        val pos = meas.getPosition(d)
                        if (i == 0) moveTo(pos.x, pos.y) else lineTo(pos.x, pos.y)
                    }
                }

                /* 渐变刷 */
                val sPos = meas.getPosition(start)
                val ePos = meas.getPosition(end)
                val grad = Brush.linearGradient(effectColor, sPos, ePos)

                /* 画光束（比 border 粗、在最上层） */
                drawPath(
                    path = beam,
                    brush = grad,
                    style = Stroke(
                        width = strokeW,
                        cap   = StrokeCap.Round,
                        join  = StrokeJoin.Round
                    )
                )
            }
        },
        content = content
    )
}
