package com.moyu.chuanqirensheng.widget.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.GamePowerSnackBar
import com.moyu.chuanqirensheng.widget.common.GameSnackBar
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.quit_tips

val EMPTY_DISMISS = {}

@Composable
fun CloseHintDialog(
    onDismissRequest: () -> Unit = EMPTY_DISMISS,
    showTips: Boolean = true,
    content: @Composable BoxScope.() -> Unit
) {
    Dialog(
        onDismissRequest = {
            onDismissRequest()
        }, properties = DialogProperties(
            usePlatformDefaultWidth = false,
        )
    ) {
        Box(
            contentAlignment = Alignment.Center, modifier = Modifier.fillMaxSize().background(B50).clickable {
                if (onDismissRequest != EMPTY_DISMISS || showTips) {
                    onDismissRequest()
                }
            }
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Box(content = content)
                if (onDismissRequest != EMPTY_DISMISS && showTips) {
                    Spacer(Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier.fillMaxWidth(), onClick = { onDismissRequest() }) {
                        StrokedText(
                            text = stringResource(Res.string.quit_tips),
                            style = MaterialTheme.typography.h3,
                            color = W50
                        )
                    }
                }
            }
            GameSnackBar()
            GamePowerSnackBar()
        }
    }
}