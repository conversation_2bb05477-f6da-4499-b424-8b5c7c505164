package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.DrawableResource
import shared.generated.resources.Res
import shared.generated.resources.frame_equip_quality_3


@Composable
fun IconView(
    modifier: Modifier = Modifier,
    res: DrawableResource? = null,
    frame: DrawableResource? = Res.drawable.frame_equip_quality_3,
    itemSize: ItemSize = ItemSize.LargePlus,
    resZIndex: Float = 0f,
    clipShape: RoundedCornerShape = RoundedCornerShape(itemSize.itemSize / 12),
    extraPadding: Dp = padding0,
    name: String? = null,
    callback: () -> Unit = {}
) {
    val pressing = remember {
        mutableStateOf(false)
    }
    val colorFilter = if (pressing.value) {
        ColorFilter.tint(
            B50, BlendMode.SrcAtop
        )
    } else {
        null
    }
    EffectButton(modifier = modifier, pressing = pressing, onClick = {
        callback()
    }) {
        res?.let {
            Image(
                modifier = Modifier.zIndex(resZIndex)
                    .size(itemSize.itemSize)
                    .padding(extraPadding)
                    .clip(clipShape),
                painter = kmpPainterResource(res),
                colorFilter = colorFilter,
                contentDescription = name
            )
        }
        frame?.let {
            Image(
                modifier = Modifier.size(itemSize.frameSize),
                colorFilter = colorFilter,
                painter = kmpPainterResource(frame),
                contentDescription = null
            )
        }
        name?.let {
            StrokedText(text = name, style = itemSize.getTextStyle())
        }
    }
}