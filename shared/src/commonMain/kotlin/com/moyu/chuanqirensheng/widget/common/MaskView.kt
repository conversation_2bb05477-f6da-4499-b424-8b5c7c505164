package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.em
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.util.shrinkNumber

@Composable
fun MaskView(
    modifier: Modifier,
    text: String,
    itemSize: ItemSize,
) {
    Box(
        modifier = modifier.width(itemSize.frameSize)
            .padding(horizontal = itemSize.frameSize / 20, vertical = itemSize.frameSize / 20).clip(
                RoundedCornerShape(
                    topStart = padding0,
                    topEnd = padding0,
                    bottomStart = itemSize.frameSize / 8,
                    bottomEnd = itemSize.frameSize / 8
                )
            ).background(B35).padding(horizontal = padding2, vertical = padding1),
        contentAlignment = Alignment.Center
    ) {
        StrokedText(
            text = text.shrinkNumber(!hasGoogleService()),
            style = itemSize.getTextStyle().copy(
                fontWeight = FontWeight.Thin, letterSpacing = (-0.05).em
            ),
            textAlign = TextAlign.Center,
            softWrap = false,
            overflow = TextOverflow.Visible,
            maxLines = 1
        )
    }
}