package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.input.ImeAction
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.hideKeyboard

@Composable
fun DecorateTextField(
    text: String,
    modifier: Modifier = Modifier,
    maxLength: Int = 300,           // ← 想改多少都行
    onValueChange: (String) -> Unit
) {
    Box(
        modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(padding6))
            .background(B50),
        contentAlignment = Alignment.Center
    ) {
        BasicTextField(
            value = text,
            onValueChange = { newInput ->
                // 若超出长度就截断
                if (newInput.length <= maxLength) {
                    onValueChange(newInput)
                } else {
                    onValueChange(newInput.take(maxLength))
                }
            },
            keyboardOptions  = KeyboardOptions.Default.copy(imeAction = ImeAction.Done),
            keyboardActions  = KeyboardActions(onDone = { hideKeyboard() }),
            textStyle        = MaterialTheme.typography.h4.copy(color = Color.White),
            cursorBrush      = SolidColor(Color.White),
            decorationBox    = { inner ->
                Box(
                    Modifier
                        .fillMaxSize()
                        .padding(vertical = padding10, horizontal = padding16)
                ) { inner() }
            }
        )
    }
}