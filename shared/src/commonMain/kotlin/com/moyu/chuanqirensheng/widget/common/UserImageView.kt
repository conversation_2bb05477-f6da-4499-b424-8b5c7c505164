package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.em
import coil3.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.platform.resetClipboard
import com.moyu.chuanqirensheng.ui.theme.SkillLevel5Color
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.userHeadHeight
import com.moyu.chuanqirensheng.ui.theme.userHeadWidth
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.shrinkNumber
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.account_frame
import shared.generated.resources.battle_power_icon
import shared.generated.resources.battle_power_tips
import shared.generated.resources.level_tips
import shared.generated.resources.user_head_frame

@Composable
fun UserImageView(
    headRes: String? = null,
    headBitmap: ImageBitmap? = null,
    name: String? = null,
) {
    Box(modifier = Modifier.width(userHeadWidth)) {
        Image(
            modifier = Modifier.fillMaxWidth(),
            painter = painterResource(Res.drawable.account_frame),
            contentScale = ContentScale.FillWidth,
            contentDescription = null
        )
        Box(Modifier.align(Alignment.TopStart).padding(start = padding4, top = padding4).clickable {
            if (isLite()) {
                "调试包才有的提示，你的UserId是：${gameSdkDefaultProcessor().getObjectId()}，已经自动复制到你的粘贴板".toast()
                resetClipboard("" + gameSdkDefaultProcessor().getObjectId())
            }
        }) {
            if (headBitmap != null) {
                Image(
                    bitmap = headBitmap,
                    modifier = Modifier
                        .size(userHeadHeight).padding(padding3)
                        .clip(RoundedCornerShape(padding4)),
                    contentDescription = null
                )
            } else {
                headRes?.let {
                    Image(
                        painter = if (it.startsWith("http")) rememberAsyncImagePainter(it) else kmpPainterResource(
                            it
                        ),
                        modifier = Modifier
                            .size(userHeadHeight).padding(padding3)
                            .clip(RoundedCornerShape(padding4)),
                        contentDescription = null
                    )
                }
            }
            Image(
                modifier = Modifier
                    .size(userHeadHeight),
                painter = painterResource(Res.drawable.user_head_frame),
                contentDescription = null
            )
        }
        StrokedText(
            text = "Lv${AwardManager.getMasterLevel()}",
            style = MaterialTheme.typography.h5.copy(
                fontWeight = FontWeight.Thin,
                letterSpacing = (-0.05).em
            ),
            modifier = Modifier.align(Alignment.BottomStart)
                .padding(start = padding4, bottom = padding4).width(userHeadHeight).clickable {
                    GameCore.instance.onBattleEffect(SoundEffect.Click)
                    AppWrapper.getStringKmp(Res.string.level_tips).toast()
                },
            textAlign = TextAlign.Center,
        )
        name?.let {
            StrokedText(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(start = userHeadHeight + padding6, top = padding12),
                text = name,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.h4.copy(fontWeight = FontWeight.Thin),
            )
        }
        Row(
            Modifier.align(Alignment.BottomStart)
                .padding(start = userHeadHeight + padding4, bottom = padding2).clickable {
                    GameCore.instance.onBattleEffect(SoundEffect.Click)
                    AppWrapper.getStringKmp(Res.string.battle_power_tips).toast()
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                modifier = Modifier.size(imageSmall),
                painter = painterResource(Res.drawable.battle_power_icon),
                contentDescription = null
            )
            StrokedText(
                text = AwardManager.getAllBattlePower().toString().shrinkNumber(!hasGoogleService()),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.h4.copy(
                    fontWeight = FontWeight.Thin,
                    letterSpacing = (-0.05).em
                ),
                color = SkillLevel5Color
            )
        }
    }
}