package com.moyu.chuanqirensheng.widget.effect

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.util.kmpPainterResource
import kotlin.math.roundToInt


@Composable
fun ForeverGif(modifier: Modifier,
               gifDrawable: String,
               gifCount: Int,
               translateY: Dp = padding0,
               needGap: Boolean = false,
               pace: Int = 70,
               scale: Float = 1.33f,
               repeatMode: RepeatMode = RepeatMode.Restart,
               contentScale: ContentScale = ContentScale.FillWidth) {
    val infiniteTransition = rememberInfiniteTransition(label = "")
    val index = infiniteTransition.animateFloat(
        initialValue = 1f, targetValue = if (needGap) gifCount * 2f else gifCount.toFloat(), animationSpec = infiniteRepeatable(
            animation = tween(if (needGap) gifCount * 2 * pace else gifCount * pace, easing = LinearEasing),
            repeatMode = repeatMode,
        ), label = ""
    )
    if (index.value.roundToInt() <= gifCount) { // 做一个间歇的效果
        Image(
            modifier = modifier
                .graphicsLayer {
                    translationY = translateY.toPx()
                }.scale(scale),
            contentScale = contentScale,
            painter = kmpPainterResource("${gifDrawable}${index.value.roundToInt()}"),
            contentDescription = null
        )
    }
}