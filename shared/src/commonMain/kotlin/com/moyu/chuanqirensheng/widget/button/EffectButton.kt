package com.moyu.chuanqirensheng.widget.button

import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.waitForUpOrCancellation
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.p_getTimeMillis
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

const val LONG_PRESS = 800L
const val LARGE_GAP = 500
const val HUGE_GAP = 1000
const val MEDIA_GAP = 200
const val QUICK_GAP = 100
var globalLastClickTime = 0L

@Composable
fun EffectButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
    pressing: MutableState<Boolean> = remember {
        mutableStateOf(false)
    },
    clickGap: Int = LARGE_GAP,
    mute: Boolean = false,
    hapticFeedbackType: HapticFeedbackType = HapticFeedbackType.TextHandleMove,
    longPressEnabled: Boolean = false,
    content: @Composable BoxScope.() -> Unit
) {
    // 记录上次点击时间，本地记忆状态
    val lastClickTime = remember { mutableLongStateOf(0L) }
    val currentClick by rememberUpdatedState(onClick) // ✨ 关键一行
    val haptic = LocalHapticFeedback.current
    /**
     * 统一做一次点击的逻辑封装：
     * 1. 检查和上次点击的间隔，若小于 clickGap 则忽略
     * 2. 检查和全局点击时间的间隔，若小于 MEDIA_GAP 也忽略
     * 3. 通过后才真正调用 onClick
     */
    fun safeClick() {
        val current = p_getTimeMillis()
        if (current - lastClickTime.longValue > clickGap && current - globalLastClickTime > MEDIA_GAP) {
            lastClickTime.longValue = current
            globalLastClickTime = current
            currentClick()
        }
        haptic.performHapticFeedback(hapticFeedbackType)
        if (!mute) {
            GameCore.instance.onBattleEffect(SoundEffect.Click)
        }
    }

    val interactionSource = remember { MutableInteractionSource() }
    pressing.value = interactionSource.collectIsPressedAsState().value

    // 根据 longPressEnabled 决定用哪种手势处理方式
    val pointerModifier = if (!longPressEnabled) {
        // 只用普通点击
        Modifier.clickable(
            interactionSource = interactionSource,
            indication = null,
            onClick = { safeClick() }
        )
    } else {
        /**
         * 用 pointerInput 自定义手势，以便在检测到长按阈值后，
         * 可以循环调用 safeClick()，直到抬手。
         */
        Modifier.pointerInput(Unit) {
            coroutineScope {
                while (true) {
                    // 1) 等待手指第一次按下
                    awaitPointerEventScope { awaitFirstDown() }
                    var wasLongPress = false
                    pressing.value = true

                    // 2) 启动一个协程，先等系统长按阈值，再开始以固定间隔重复点击
                    val job = launch {
                        // 等系统长按阈值（你也可用自定义时长）
                        delay(LONG_PRESS)
                        // 如果手指还没抬起，就进入长按状态
                        wasLongPress = true
                        // 不断重复点击，直到手指抬起时 job 会被取消
                        while (isActive) {
                            safeClick()
                            delay(150)
                        }
                        pressing.value = false
                    }

                    // 3) 再次等待手指抬起或者手势被取消
                    awaitPointerEventScope {
                        waitForUpOrCancellation()
                    }

                    // 手指抬起/取消后，取消这个循环点击的协程
                    job.cancel()


                    // 如果还没到长按阈值就抬起 => 这是短按 => 执行一次点击
                    if (!wasLongPress) {
                        pressing.value = false
                        safeClick()
                    }
                }
            }
        }
    }

    Box(
        modifier = modifier.then(pointerModifier),
        contentAlignment = Alignment.Center,
        content = content
    )
}
