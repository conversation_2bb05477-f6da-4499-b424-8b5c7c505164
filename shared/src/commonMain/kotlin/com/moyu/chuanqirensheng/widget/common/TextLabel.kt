package com.moyu.chuanqirensheng.widget.common

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding66

enum class LabelSize(val width: Dp, val height: Dp) {
    Small(imageSmallPlus, imageSmall),
    Medium(padding180, padding36),
    Medium2(padding100, padding34),
    Large(padding200, padding36),
    Huge(padding300, padding66),
}

@Composable
fun LabelSize.getTextStyle(): TextStyle {
    return when (this) {
        LabelSize.Small -> MaterialTheme.typography.h4
        LabelSize.Medium2 -> MaterialTheme.typography.h4
        LabelSize.Medium -> MaterialTheme.typography.h3
        LabelSize.Large -> MaterialTheme.typography.h2
        else -> MaterialTheme.typography.h1
    }
}

@Composable
fun LabelSize.getTextStyleMinus(): TextStyle {
    return when (this) {
        LabelSize.Small -> MaterialTheme.typography.h5
        LabelSize.Medium2 -> MaterialTheme.typography.h5
        LabelSize.Medium -> MaterialTheme.typography.h4
        LabelSize.Large -> MaterialTheme.typography.h3
        else -> MaterialTheme.typography.h2
    }
}
