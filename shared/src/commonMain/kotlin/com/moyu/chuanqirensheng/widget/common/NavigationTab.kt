package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.tabButtonBigHeight
import com.moyu.chuanqirensheng.ui.theme.tabButtonBigWidth
import com.moyu.chuanqirensheng.ui.theme.tabButtonHeight
import com.moyu.chuanqirensheng.ui.theme.tabButtonWidth
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.locked
import shared.generated.resources.tab_bottom_frame
import shared.generated.resources.tab_selected
import shared.generated.resources.tab_selected_big

@Composable
fun NavigationTab(
    modifier: Modifier = Modifier,
    pageState: MutableState<Int>,
    titles: List<DrawableResource>,
    redIcons: List<Boolean> = listOf(false, false, false, false, false, false, false, false),
    locks: List<Boolean> = listOf(false, false, false, false, false, false, false, false),
) {
    Row(
        modifier = modifier
            .fillMaxWidth().height(tabButtonBigHeight).paint(
                painterResource(Res.drawable.tab_bottom_frame),
                contentScale = ContentScale.FillBounds
            ).padding(horizontal = padding16)
            .zIndex(999f),
        horizontalArrangement = if (titles.size > 3) Arrangement.SpaceEvenly else Arrangement.spacedBy(
            padding12
        ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        titles.forEachIndexed { index, title ->
            EffectButton(onClick = {
                if (!locks[index]) {
                    pageState.value = index
                } else {
                    AppWrapper.getStringKmp(Res.string.locked).toast()
                }
            }) {
                TagView(
                    modifier = Modifier
                        .size(
                            if (titles.size > 3) tabButtonWidth else tabButtonBigWidth,
                            if (titles.size > 3) tabButtonHeight else tabButtonBigHeight
                        ).scale(if (index == pageState.value && titles.size > 3) 1.2f else 1f),
                    frame = if (titles.size > 3) Res.drawable.tab_selected else Res.drawable.tab_selected_big,
                    title = title,
                    selected = index == pageState.value,
                    red = redIcons[index],
                    lock = locks[index],
                )
            }
        }
    }
}