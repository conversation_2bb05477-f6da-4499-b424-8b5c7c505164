package com.moyu.chuanqirensheng.widget.common

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding53
import com.moyu.chuanqirensheng.ui.theme.padding58
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.padding84
import com.moyu.chuanqirensheng.ui.theme.padding90

enum class ItemSize(val frameSize: Dp, val itemSize: Dp) {
    Small(padding34, padding28),
    Medium(padding58, padding50),
    MediumPlus(padding50, padding42),
    Large(padding66, padding53),
    LargePlus(padding100, padding84),
    Huge(padding110, padding90),
}

@Composable
fun ItemSize.getTextStyle(): TextStyle {
    return when (this) {
        ItemSize.Small -> MaterialTheme.typography.h5
        ItemSize.Medium -> MaterialTheme.typography.h5
        ItemSize.MediumPlus -> MaterialTheme.typography.body1
        ItemSize.Large -> MaterialTheme.typography.h4
        ItemSize.LargePlus -> MaterialTheme.typography.h3
        else -> MaterialTheme.typography.h2
    }
}

@Composable
fun ItemSize.getTextStyleSmall(): TextStyle {
    return when (this) {
        ItemSize.Small -> MaterialTheme.typography.h6
        ItemSize.Medium -> MaterialTheme.typography.h6
        ItemSize.MediumPlus -> MaterialTheme.typography.body2
        ItemSize.Large -> MaterialTheme.typography.h5
        ItemSize.LargePlus -> MaterialTheme.typography.h4
        else -> MaterialTheme.typography.h3
    }
}