package com.moyu.chuanqirensheng.widget.dialog

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.dialogMediumHeight
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.padding19

@Composable
fun PanelDialog(
    onDismissRequest: () -> Unit = EMPTY_DISMISS,
    showClose: Boolean = true,
    dialogWidthDp: Dp = dialogWidth, // 假设这些是定义好的常量
    dialogHeightDp: Dp = dialogMediumHeight,
    contentBelow: @Composable RowScope.() -> Unit = {},
    content: @Composable BoxScope.() -> Unit
) {
    NewPanelDialog(
        onDismissRequest = onDismissRequest,
        dialogHeightDp = dialogHeightDp,
        dialogWidthDp = dialogWidthDp,
        showClose = showClose
    ) {
        Column(modifier = Modifier.clickable {
            // 弹窗以内不让点击关闭，用一个空的click占据
        }, horizontalAlignment = Alignment.CenterHorizontally) {
            Box(Modifier.weight(1f).fillMaxWidth()) {
                content()
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                contentBelow()
            }
            Spacer(modifier = Modifier.size(padding19))
        }
    }
}