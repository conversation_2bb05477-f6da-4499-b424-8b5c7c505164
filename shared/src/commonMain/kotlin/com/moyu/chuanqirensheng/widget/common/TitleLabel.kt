package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.resource_frame

@Composable
fun TitleLabel(
    modifier: Modifier = Modifier,
    text: String,
    color: Color = Color.White,
    style: TextStyle = MaterialTheme.typography.h3,
    frame: DrawableResource = Res.drawable.resource_frame,
    onClick: () -> Unit = {}
) {
    EffectButton(
        modifier = modifier, onClick = onClick
    ) {
        StrokedText(
            modifier = Modifier.paint(painterResource(frame))
                .padding(horizontal = padding4, vertical = padding1),
            text = text,
            style = style,
            color = color,
            textAlign = TextAlign.Center
        )
    }
}