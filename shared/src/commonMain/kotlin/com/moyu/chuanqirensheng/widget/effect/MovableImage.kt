package com.moyu.chuanqirensheng.widget.effect

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.util.kmpPainterResource
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.DrawableResource
import kotlin.random.Random

@Composable
fun MovableImage(
    modifier: Modifier = Modifier,
    imageResource: DrawableResource,
    clipSize: Dp = padding0,
) {
    val angle = remember { Animatable(0f) }
    val rotationY = remember { Animatable(0f) }

    Box(contentAlignment = Alignment.Center) {
        LaunchedEffect(Unit) {
            while (true) {
                delay(Random.nextLong(2300, 17500)) // add a short pause before next shake
                val durationMillis = 600

                // Shake animation
                angle.animateTo(
                    targetValue = 5f,
                    animationSpec = tween(
                        durationMillis / 8,
                        easing = FastOutSlowInEasing
                    )
                )
                angle.animateTo(
                    targetValue = -5f,
                    animationSpec = tween(
                        durationMillis / 4,
                        easing = FastOutSlowInEasing
                    )
                )
                angle.animateTo(
                    targetValue = 5f,
                    animationSpec = tween(
                        durationMillis / 4,
                        easing = FastOutSlowInEasing
                    )
                )
                angle.animateTo(
                    targetValue = -5f,
                    animationSpec = tween(
                        durationMillis / 4,
                        easing = FastOutSlowInEasing
                    )
                )
                angle.animateTo(
                    targetValue = 5f,
                    animationSpec = tween(
                        durationMillis / 4,
                        easing = FastOutSlowInEasing
                    )
                )
                angle.animateTo(
                    targetValue = 0f,
                    animationSpec = tween(
                        durationMillis / 8,
                        easing = FastOutSlowInEasing
                    )
                )

                // Rotation animation
                rotationY.animateTo(
                    targetValue = 180f,
                    animationSpec = tween(
                        durationMillis = 600,
                        easing = FastOutSlowInEasing
                    )
                )

                delay(Random.nextLong(2300, 17500)) // add a short pause before next shake

                rotationY.animateTo(
                    targetValue = 0f,
                    animationSpec = tween(
                        durationMillis = 600,
                        easing = FastOutSlowInEasing
                    )
                )
            }
        }

        // 斜向剪影
        Image(
            modifier = modifier
                .rotate(angle.value)
                .scale(1 + angle.value / 100)
                .graphicsLayer {
                    this.rotationY = rotationY.value
                    // 压扁和倾斜效果
                    scaleY = 0.3f
                    rotationZ = 10f
                    transformOrigin = TransformOrigin(0.5f, 1f) // 设置旋转中心为底部中心
                }
                .clip(RoundedCornerShape(clipSize)),
            painter = kmpPainterResource(imageResource),
            colorFilter = ColorFilter.tint(Color.Black, BlendMode.SrcIn),
            contentDescription = null
        )
        Image(
            modifier = modifier
                .rotate(angle.value)
                .scale(1 + angle.value / 100)
                .graphicsLayer {
                    this.rotationY = rotationY.value
                }
                .clip(RoundedCornerShape(clipSize)),
            painter = kmpPainterResource(imageResource),
            contentDescription = null
        )
    }
}