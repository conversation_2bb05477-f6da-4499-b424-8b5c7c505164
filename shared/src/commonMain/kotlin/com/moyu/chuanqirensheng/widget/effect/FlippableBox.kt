package com.moyu.chuanqirensheng.widget.effect

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextInteger
import kotlinx.coroutines.delay

@Composable
fun FlippableBox(
    duration: Int = 600,
    key: Int = 0,
    front: @Composable () -> Unit,
    back: @Composable () -> Unit
) {
    var flipped by remember(key) { mutableStateOf(false) }
    var hasFlipped by remember(key) { mutableStateOf(false) } // 新增的状态变量

    val rotation by animateFloatAsState(
        targetValue = if (flipped) 180f else 0f,
        animationSpec = tween(durationMillis = if (flipped) duration else 0, easing = LinearEasing), label = ""
    )

    LaunchedEffect(key) {
        if (!hasFlipped) { // 只有当没有翻转过时才播放动画
            delay(RANDOM.nextInteger(10, 1000).toLong())
            flipped = true
            hasFlipped = true // 标记为已翻转
        }
    }

    Box(
        modifier = Modifier
            .graphicsLayer {
                rotationY = rotation
                cameraDistance = 8 * density
            },
        contentAlignment = Alignment.Center
    ) {
        if (rotation <= 90f) {
            Box(modifier = Modifier.fillMaxSize()) {
                front()
            }
        } else {
            Box(modifier = Modifier
                .fillMaxSize()
                .graphicsLayer { rotationY = 180f }) {
                back()
            }
        }
    }
}