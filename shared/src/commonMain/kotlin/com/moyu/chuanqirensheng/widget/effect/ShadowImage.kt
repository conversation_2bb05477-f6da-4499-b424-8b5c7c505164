package com.moyu.chuanqirensheng.widget.effect

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.util.kmpPainterResource
import org.jetbrains.compose.resources.DrawableResource

@Composable
fun ShadowImage(
    modifier: Modifier = Modifier,
    imageResource: DrawableResource,
    colorFilter: ColorFilter? = null,
    clipSize: Dp = padding0,
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        // 斜向剪影
        Image(
            modifier = Modifier.fillMaxSize()
                .graphicsLayer {
                    rotationX = -75f            // 0 = 直立；90 = 完全贴地
                    transformOrigin = TransformOrigin(0.5f, 1f)   // 仍以脚跟为原点
                    cameraDistance = 8 * density                 // 拉远相机，减少透视畸变
                    alpha = 0.55f             // 影子稍暗
                    translationY = -padding2.toPx()
                }
                .clip(RoundedCornerShape(clipSize)),
            painter = kmpPainterResource(imageResource),
            colorFilter = ColorFilter.tint(Color.Black, BlendMode.SrcIn),
            contentDescription = null
        )

        Image(
            modifier = Modifier.fillMaxSize()
                .clip(RoundedCornerShape(clipSize)),
            painter = kmpPainterResource(imageResource),
            colorFilter = colorFilter,
            contentDescription = null
        )
    }
}