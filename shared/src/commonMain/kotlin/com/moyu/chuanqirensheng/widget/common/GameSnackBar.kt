package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.common_tips


@Composable
fun GameSnackBar() {
    if (Dialogs.snackbar.value.isNotEmpty()) {
        LaunchedEffect(Dialogs.snackbar.value) {
            delay(1600)
            Dialogs.snackbar.value = ""
        }
        Box(contentAlignment = Alignment.Center) {
            EffectButton(onClick = {
                Dialogs.snackbar.value = ""
            }, content = {
                StrokedText(
                    Modifier.fillMaxWidth().paint(
                        painterResource(Res.drawable.common_tips),
                        contentScale = ContentScale.FillBounds
                    )
                        .padding(horizontal = padding30, vertical = padding12),
                    text = Dialogs.snackbar.value.replace("\\n", "\n"),
                    style = if (Dialogs.snackbar.value.length >= 100) MaterialTheme.typography.h3 else MaterialTheme.typography.h2
                )
            })
        }
    }
}
