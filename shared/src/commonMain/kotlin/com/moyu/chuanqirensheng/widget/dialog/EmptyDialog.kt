package com.moyu.chuanqirensheng.widget.dialog

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

@Composable
fun EmptyDialog(
    onDismissRequest: () -> Unit = EMPTY_DISMISS,
    showTips: Boolean = true,
    content: @Composable BoxScope.() -> Unit
) {
    CloseHintDialog(onDismissRequest = onDismissRequest, showTips = showTips) {
        Box(
            modifier = Modifier.clickable(indication = null, interactionSource = null) {
                // 弹窗内容部分加一个点击，不做任何事情
                // CloseHintDialog里会点击dismiss
            }, contentAlignment = Alignment.Center, content = content
        )
    }
}