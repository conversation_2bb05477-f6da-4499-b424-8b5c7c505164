package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.ui.theme.labelHeight
import com.moyu.chuanqirensheng.ui.theme.labelWidth
import com.moyu.chuanqirensheng.ui.theme.padding7
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.scroll
import shared.generated.resources.stage_label

@Composable
fun GameLabel(modifier: Modifier = Modifier, content: @Composable BoxScope.() -> Unit) {
    Box(modifier = modifier.size(labelWidth, labelHeight), contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(Res.drawable.stage_label), contentDescription = null
        )
        Box(
            modifier = Modifier
                .fillMaxWidth().padding(bottom = padding7),
            contentAlignment = Alignment.Center
        ) {
            content()
        }
    }
}

@Composable
fun GameLabel3(
    modifier: Modifier = Modifier,
    frame: DrawableResource = Res.drawable.scroll,
    content: @Composable BoxScope.() -> Unit
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(frame), contentDescription = null
        )
        Box(
            modifier = Modifier
                .fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            content()
        }
    }
}