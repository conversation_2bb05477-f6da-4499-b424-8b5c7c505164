package com.moyu.chuanqirensheng.widget.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding18
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import shared.generated.resources.Res
import shared.generated.resources.cancel
import shared.generated.resources.confirm
import shared.generated.resources.please_confirm


data class CommonAlert(
    val title: String = AppWrapper.getStringKmp(Res.string.please_confirm),
    val content: String = "",
    val cancelText: String = AppWrapper.getStringKmp(Res.string.cancel),
    val confirmText: String = AppWrapper.getStringKmp(Res.string.confirm),
    val onlyConfirm: Boolean = false,
    val extraContent: @Composable ColumnScope.() -> Unit = {},
    val onConfirm: () -> Unit = {},
    val onClose: () -> Unit = {},
    val onCancel: () -> Unit = {},
)

@Composable
fun CommonAlertDialog(switch: MutableState<CommonAlert?>) {
    switch.value?.let { alert ->
        PanelDialog(onDismissRequest = {
            alert.onClose()
            switch.value = null
        }, contentBelow = {
            if (!alert.onlyConfirm) {
                GameButton(text = alert.cancelText, buttonStyle = ButtonStyle.Blue) {
                    alert.onCancel()
                    switch.value = null
                }
            }
            GameButton(text = alert.confirmText, buttonStyle = ButtonStyle.Green) {
                alert.onConfirm()
                switch.value = null
            }
        }) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth().padding(horizontal = padding16),
            ) {
                StrokedText(
                    text = alert.title,
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding18))
                Column(modifier = Modifier.weight(1f)) {
                    StrokedText(
                        modifier = Modifier.verticalScroll(
                                rememberScrollState()
                            ),
                        text = alert.content,
                        style = MaterialTheme.typography.h2,
                        color = Color.White
                    )
                    alert.extraContent.invoke(this)
                }
            }
        }
    }
}