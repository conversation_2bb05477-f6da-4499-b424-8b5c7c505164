package com.moyu.chuanqirensheng.widget.effect

import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding50

data class GifData(
    val gif: String,
    val count: Int,
    val pace: Int = 1,
    val size: Dp = dialogWidth,
    val translateY: Dp = padding0
)

val healGif = GifData(
    "heal_", 15
)

val guideGif = GifData(
    "guide_circle_", 12
)

val vipGif = GifData(
    "newlight_", 23
)

val singleDrawGif = GifData(
    "draw_", 7
)

val awardGif = GifData(
    "rewardcirculate_", 16
)

val equipGif = GifData(
    "equipon_", 15
)

val orangeItemGif = GifData(
    "quality3_", 16
)

val redItemGif = GifData(
    "quality4_", 16
)

val playerlevelUpGif = GifData(
    "playerlevelup_", 15, pace = 3, size = padding300
)

val starUpGif = GifData(
    "starup_", 10, translateY = padding150, size = padding100
)

val levelUpGif = GifData(
    "allylevelup_", 9, translateY = padding150, size = padding100
)

val talent1Gif = GifData(
    "talentup1_", 7, pace = 2
)

val talent2Gif = GifData(
    "talentup2_", 20, size = padding50
)

val talent3Gif = GifData(
    "talentup3_", 8, size = padding100
)