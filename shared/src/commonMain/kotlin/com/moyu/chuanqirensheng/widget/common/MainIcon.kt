package com.moyu.chuanqirensheng.widget.common


import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Unlock
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.red_icon

@Composable
fun MainIcon(
    itemSize: ItemSize,
    unlocks: List<Unlock>,
    click: () -> Unit,
    red: () -> Boolean,
    title: String,
    icon: DrawableResource
) {
    if (unlocks.map { UnlockManager.getUnlockedFlow(it) }.any { it }) {
        val pressing = remember {
            mutableStateOf(false)
        }
        val colorFilter = if (pressing.value) {
            ColorFilter.tint(
                B50, BlendMode.SrcAtop
            )
        } else {
            null
        }
        val color = if (pressing.value) W50 else Color.White
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            EffectButton(
                modifier = Modifier.width(itemSize.frameSize), pressing = pressing, onClick = {
                    click()
                }) {
                Image(
                    modifier = Modifier.height(itemSize.itemSize),
                    painter = painterResource(icon),
                    colorFilter = colorFilter,
                    contentDescription = null
                )
                if (red()) {
                    Image(
                        modifier = Modifier.align(Alignment.TopEnd).padding(itemSize.itemSize / 40)
                            .size(imageTinyPlus),
                        painter = painterResource(Res.drawable.red_icon),
                        colorFilter = colorFilter,
                        contentDescription = null
                    )
                }
            }
            StrokedText(
                modifier = Modifier.width(itemSize.frameSize * 1.1f),
                text = title,
                maxLines = 1,
                color = color,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.h4
            )
        }
    }
}
