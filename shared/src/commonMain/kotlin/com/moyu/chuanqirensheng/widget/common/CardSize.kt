package com.moyu.chuanqirensheng.widget.common

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.eventCardBigHeight
import com.moyu.chuanqirensheng.ui.theme.eventCardBigWidth
import com.moyu.chuanqirensheng.ui.theme.eventCardHeight
import com.moyu.chuanqirensheng.ui.theme.eventCardWidth
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding130
import com.moyu.chuanqirensheng.ui.theme.padding150


enum class CardSize(val width: Dp, val height: Dp) {
    Tiny(padding110, padding130),
    Small(padding130, padding150),
    Medium(eventCardWidth, eventCardHeight),
    Large(eventCardBigWidth, eventCardBigHeight),;
    fun getRadius(): Dp {
        return width / 22
    }
}

@Composable
fun CardSize.getTextStyle(): TextStyle {
    return when (this) {
        CardSize.Tiny -> MaterialTheme.typography.h4
        CardSize.Small -> MaterialTheme.typography.h4
        CardSize.Medium -> MaterialTheme.typography.h3
        CardSize.Large -> MaterialTheme.typography.h2
    }
}