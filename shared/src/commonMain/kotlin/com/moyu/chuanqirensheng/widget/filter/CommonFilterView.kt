package com.moyu.chuanqirensheng.widget.filter

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.illustration.TcgManager
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.toQualityColor
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.core.AppWrapper
import com.moyu.core.model.Ally
import com.moyu.core.model.Equipment
import com.moyu.core.model.Event
import com.moyu.core.model.Tcg
import core.generated.resources.group1
import core.generated.resources.group10
import core.generated.resources.group11
import core.generated.resources.group19
import core.generated.resources.group2
import core.generated.resources.group3
import core.generated.resources.group4
import core.generated.resources.group5
import core.generated.resources.group6
import core.generated.resources.group7
import core.generated.resources.group8
import core.generated.resources.group9
import core.generated.resources.race1
import core.generated.resources.race2
import core.generated.resources.race3
import core.generated.resources.race4
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.all
import shared.generated.resources.ally
import shared.generated.resources.already_collected
import shared.generated.resources.battle_power
import shared.generated.resources.collecting
import shared.generated.resources.equipment
import shared.generated.resources.filter
import shared.generated.resources.itemtype_1
import shared.generated.resources.itemtype_2
import shared.generated.resources.itemtype_3
import shared.generated.resources.itemtype_4
import shared.generated.resources.itemtype_5
import shared.generated.resources.itemtype_6
import shared.generated.resources.itemtype_7
import shared.generated.resources.itemtype_8
import shared.generated.resources.itemtype_9
import shared.generated.resources.level
import shared.generated.resources.menu_filter
import shared.generated.resources.menu_order
import shared.generated.resources.no_order
import shared.generated.resources.order
import shared.generated.resources.quality
import shared.generated.resources.quality_1
import shared.generated.resources.quality_2
import shared.generated.resources.quality_3
import shared.generated.resources.quality_4
import shared.generated.resources.star_level
import core.generated.resources.Res as CoreRes


data class ItemOrder<T, R : Comparable<R>>(
    val name: String,
    val color: Color,
    val excludeTypes: List<Int>,
    val order: ((T)-> R)? = null,
)

val equipOrderList = listOf<ItemOrder<Equipment, Int>>(
    ItemOrder(AppWrapper.getStringKmp(Res.string.no_order), Color.White, listOf(1)) {
        it.quality * 100 + it.star
    },
    ItemOrder(AppWrapper.getStringKmp(Res.string.quality), Color.White, listOf(1)) {
        it.quality
    },
    ItemOrder(AppWrapper.getStringKmp(Res.string.star_level), Color.White, listOf(1)) {
        it.star
    },
)

val allyOrderList = listOf<ItemOrder<Ally, Int>>(
    ItemOrder(AppWrapper.getStringKmp(Res.string.battle_power), Color.White, listOf(1)) {
        it.getAllPower()
    },
    ItemOrder(AppWrapper.getStringKmp(Res.string.quality), Color.White, listOf(1)) {
        it.quality
    },
    ItemOrder(AppWrapper.getStringKmp(Res.string.star_level), Color.White, listOf(1)) {
        it.star
    },
    ItemOrder(AppWrapper.getStringKmp(Res.string.level), Color.White, listOf(1)) {
        it.level
    },
)

data class ItemFilter<T>(
    val name: String,
    val color: Color,
    val excludeTypes: List<Int>,
    val filter: (T) -> Boolean
)


val equipFilterList = listOf<ItemFilter<Equipment>>(
    ItemFilter(AppWrapper.getStringKmp(Res.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_4), 7.toQualityColor(), listOf(2)) { card -> card.quality == 4 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_3), 5.toQualityColor(), listOf(2)) { card -> card.quality == 3 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_2), 3.toQualityColor(), listOf(2)) { card -> card.quality == 2 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_1), 1.toQualityColor(), listOf(2)) { card -> card.quality == 1 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.itemtype_1), 1.toQualityColor(), listOf(2)) { card -> card.type == 1 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.itemtype_2), 1.toQualityColor(), listOf(2)) { card -> card.type == 2 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.itemtype_3), 1.toQualityColor(), listOf(2)) { card -> card.type == 3 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.itemtype_4), 1.toQualityColor(), listOf(2)) { card -> card.type == 4 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.itemtype_5), 1.toQualityColor(), listOf(2)) { card -> card.type == 5 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.itemtype_6), 1.toQualityColor(), listOf(2)) { card -> card.type == 6 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.itemtype_7), 1.toQualityColor(), listOf(2)) { card -> card.type == 7 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.itemtype_8), 1.toQualityColor(), listOf(2)) { card -> card.type == 8 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.itemtype_9), 1.toQualityColor(), listOf(2)) { card -> card.type == 9 },
)

val allyFilterList = listOf<ItemFilter<Ally>>(
    ItemFilter(AppWrapper.getStringKmp(Res.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter(
        AppWrapper.getStringKmp(CoreRes.string.race1),
        Color.White,
        listOf(1)
    ) { card -> card.raceType == 1 },
    ItemFilter(
        AppWrapper.getStringKmp(CoreRes.string.race2),
        Color.White,
        listOf(1)
    ) { card -> card.raceType == 2 },
    ItemFilter(
        AppWrapper.getStringKmp(CoreRes.string.race3),
        Color.White,
        listOf(1)
    ) { card -> card.raceType == 3 },
    ItemFilter(
        AppWrapper.getStringKmp(CoreRes.string.race4),
        Color.White,
        listOf(1)
    ) { card -> card.raceType == 4 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_4), 4.toQualityColor(), listOf(2)) { card -> card.quality == 4 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_3), 3.toQualityColor(), listOf(2)) { card -> card.quality == 3 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_2), 2.toQualityColor(), listOf(2)) { card -> card.quality == 2 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_1), 1.toQualityColor(), listOf(2)) { card -> card.quality == 1 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group1), Color.White, listOf(3)) { card -> card.raceType2 == 1 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group2), Color.White, listOf(3)) { card -> card.raceType2 == 2 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group3), Color.White, listOf(3)) { card -> card.raceType2 == 3 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group4), Color.White, listOf(3)) { card -> card.raceType2 == 4 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group5), Color.White, listOf(3)) { card -> card.raceType2 == 5 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group6), Color.White, listOf(3)) { card -> card.raceType2 == 6 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group7), Color.White, listOf(3)) { card -> card.raceType2 == 7 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group8), Color.White, listOf(3)) { card -> card.raceType2 == 8 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group9), Color.White, listOf(3)) { card -> card.raceType2 == 9 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group10), Color.White, listOf(3)) { card -> card.raceType2 == 10 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group11), Color.White, listOf(3)) { card -> card.raceType2 == 11 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group19), Color.White, listOf(3)) { card -> card.raceType2 == 19 },
)

val pvpFilterList = listOf<ItemFilter<Ally>>(
    ItemFilter(AppWrapper.getStringKmp(Res.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter(
        AppWrapper.getStringKmp(CoreRes.string.race1),
        Color.White,
        listOf(1)
    ) { card -> card.raceType == 1 },
    ItemFilter(
        AppWrapper.getStringKmp(CoreRes.string.race2),
        Color.White,
        listOf(1)
    ) { card -> card.raceType == 2 },
    ItemFilter(
        AppWrapper.getStringKmp(CoreRes.string.race3),
        Color.White,
        listOf(1)
    ) { card -> card.raceType == 3 },
    ItemFilter(
        AppWrapper.getStringKmp(CoreRes.string.race4),
        Color.White,
        listOf(1)
    ) { card -> card.raceType == 4 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_4), 4.toQualityColor(), listOf(2)) { card -> card.quality == 4 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_3), 3.toQualityColor(), listOf(2)) { card -> card.quality == 3 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_2), 2.toQualityColor(), listOf(2)) { card -> card.quality == 2 },
    ItemFilter(AppWrapper.getStringKmp(Res.string.quality_1), 1.toQualityColor(), listOf(2)) { card -> card.quality == 1 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group1), Color.White, listOf(3)) { card -> card.raceType2 == 1 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group2), Color.White, listOf(3)) { card -> card.raceType2 == 2 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group3), Color.White, listOf(3)) { card -> card.raceType2 == 3 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group4), Color.White, listOf(3)) { card -> card.raceType2 == 4 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group5), Color.White, listOf(3)) { card -> card.raceType2 == 5 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group6), Color.White, listOf(3)) { card -> card.raceType2 == 6 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group7), Color.White, listOf(3)) { card -> card.raceType2 == 7 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group8), Color.White, listOf(3)) { card -> card.raceType2 == 8 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group9), Color.White, listOf(3)) { card -> card.raceType2 == 9 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group10), Color.White, listOf(3)) { card -> card.raceType2 == 10 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group11), Color.White, listOf(3)) { card -> card.raceType2 == 11 },
    ItemFilter(AppWrapper.getStringKmp(CoreRes.string.group19), Color.White, listOf(3)) { card -> card.raceType2 == 19 },
)


val tcgFilterList = listOf<ItemFilter<Tcg>>(
    ItemFilter(AppWrapper.getStringKmp(Res.string.all), Color.White, listOf(1, 2)) { true },
    ItemFilter(AppWrapper.getStringKmp(Res.string.collecting), Color.White, listOf(1)) { card -> !TcgManager.isGain(card.type, card.star) },
    ItemFilter(AppWrapper.getStringKmp(Res.string.already_collected), Color.White, listOf(1)) { card -> TcgManager.isGain(card.type, card.star) },
    ItemFilter(AppWrapper.getStringKmp(Res.string.ally), 3.toQualityColor(), listOf(2)) { card -> card.isAlly() },
    ItemFilter(AppWrapper.getStringKmp(Res.string.equipment), 3.toQualityColor(), listOf(2)) { card -> !card.isAlly() },
)

val eventFilterList = listOf<ItemFilter<Event>>(
    ItemFilter("全部", Color.White, listOf(1, 2, 3, 4)) { true },
    ItemFilter("直接奖励", Color.White, listOf(1)) { card -> card.play == 1 },
    ItemFilter("二选一奖励", Color.White, listOf(1)) { card -> card.play == 2 },
    ItemFilter("遭遇战", Color.White, listOf(1)) { card -> card.play == 3 },
    ItemFilter("战役", Color.White, listOf(1)) { card -> card.play == 4 },
    ItemFilter("攻城战", Color.White, listOf(1)) { card -> card.play == 5 },
    ItemFilter("转盘", Color.White, listOf(1)) { card -> card.play == 6 },
)


@Composable
fun CommonFilterView(modifier: Modifier = Modifier, showFilter: MutableState<Boolean>) {
    EffectButton(
        modifier = modifier, onClick = {
            showFilter.value = !showFilter.value
        }) {
        Image(
            modifier = Modifier.width(padding60),
            painter = painterResource(Res.drawable.menu_filter),
            contentScale = ContentScale.FillWidth,
            contentDescription = stringResource(Res.string.filter)
        )
    }
}

@Composable
fun CommonOrderView(modifier: Modifier = Modifier, showFilter: MutableState<Boolean>) {
    EffectButton(
        modifier = modifier, onClick = {
            showFilter.value = !showFilter.value
        }) {
        Image(
            modifier = Modifier.width(padding60),
            painter = painterResource(Res.drawable.menu_order),
            contentScale = ContentScale.FillWidth,
            contentDescription = stringResource(Res.string.order)
        )
    }
}

