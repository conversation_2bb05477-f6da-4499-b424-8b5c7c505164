package com.moyu.chuanqirensheng.widget.effect

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color

/**
 * 圆形聚光灯扫过效果。
 *
 * @param durationMillis  完成一次从左到右的时间
 * @param radiusFraction  Spotlight 半径占最小边的比例 (0‒1)
 * @param strength        聚光亮度，0 = 无；1 = 纯白
 * @param pingPong        true = 来回扫；false = 到右边后瞬间回到左侧
 */
fun Modifier.spotlightSweep(
    durationMillis: Int = 2600,
    radiusFraction: Float = .45f,
    strength: Float = .65f,
    pingPong: Boolean = false
) = composed {
    val progress = rememberInfiniteTransition(label = "spotSweep")
        .animateFloat(
            initialValue = 0f,
            targetValue  = 1f,
            animationSpec = infiniteRepeatable(
                tween(durationMillis, easing = LinearEasing),
                repeatMode = if (pingPong) RepeatMode.Reverse else RepeatMode.Restart
            ),
            label = "spotProgress"
        )

    drawWithContent {
        drawContent()                               // 先绘制原有纹理 / 背景

        val radius = size.minDimension * radiusFraction
        val cx = size.width * progress.value
        val cy = size.height / 2f                   // 水平扫；想斜线扫可改成随 progress 变化

        drawRect(
            brush = Brush.radialGradient(
                colors = listOf(
                    Color.White.copy(alpha = strength), // 中心最亮
                    Color.Transparent                  // 外圈渐隐
                ),
                center = Offset(cx, cy),
                radius = radius
            ),
            blendMode = BlendMode.Lighten             // 只提亮
        )
    }
}
