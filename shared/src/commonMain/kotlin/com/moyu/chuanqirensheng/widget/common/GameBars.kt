package com.moyu.chuanqirensheng.widget.common

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.dpToPixel
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.bar_blue
import shared.generated.resources.bar_empty
import kotlin.math.max

@Composable
fun HpBar(
    currentHp: Int,
    maxHp: Int,
    totalShield: Int = 0,
    borderColor: Color = Color(0xFF212121), // 更深的边框
    hpColor: Color = Color(0xFFE57373),// 柔和红
    showNum: Boolean = false,
    borderSize: Dp = padding2,
    cornerRadius: CornerRadius =
        CornerRadius(padding4.value.dpToPixel(), padding4.value.dpToPixel()),
    modifier: Modifier = Modifier
) {
    val realMax = if (maxHp + totalShield == 0) 100 else maxHp + totalShield

    // 卡通风格颜色，降低饱和度
    val shieldColor = Color(0xFFFFE082) // 柔和黄
    val backgroundColor = Color(0xFF424242) // 柔和深灰

    Box(
        modifier
            .fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier.fillMaxSize()
                .clip(RoundedCornerShape(50f)) // 保持四角圆角
                .background(borderColor)
                .padding(borderSize)
        ) {
            // 绘制背景
            drawRoundRect(
                color = backgroundColor,
                size = Size(size.width, size.height),
                cornerRadius = cornerRadius
            )

            // 绘制血条
            if (currentHp > 0) {
                drawRoundRect(
                    color = hpColor,
                    size = Size(
                        size.width * (currentHp.toFloat() / realMax),
                        size.height
                    ),
                    cornerRadius = cornerRadius
                )
            }

            // 绘制护盾
            if (totalShield > 0 && currentHp < realMax) {
                drawRoundRect(
                    color = shieldColor,
                    topLeft = Offset(
                        size.width * (currentHp.toFloat() / realMax),
                        0f
                    ),
                    size = Size(
                        size.width * ((currentHp + totalShield).toFloat() / realMax) -
                                size.width * (currentHp.toFloat() / realMax),
                        size.height
                    ),
                    cornerRadius = cornerRadius
                )
            }
        }
        if (showNum) {
            StrokedText(
                text = "$currentHp",
                style = MaterialTheme.typography.h6,
            )
        }
    }
}


@Composable
fun CommonBar(
    modifier: Modifier = Modifier,
    currentValue: Int,
    maxValue: Int,
    emptyRes: DrawableResource = Res.drawable.bar_empty,
    fullRes: DrawableResource = Res.drawable.bar_blue,
    showNum: Boolean = true,
    textColor: Color = Color.White,
    style: TextStyle = MaterialTheme.typography.h5,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
    ) {
        val expBuilder: Path.(size: Size, layoutDirection: LayoutDirection) -> Unit =
            { size: Size, _: LayoutDirection ->
                this.addRect(
                    Rect(
                        0f,
                        0f,
                        size.width * currentValue.toFloat() / max(1, maxValue),
                        size.height
                    )
                )
            }
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(emptyRes),
            contentDescription = null
        )
        Image(
            modifier = Modifier
                .fillMaxSize()
                .clip(GenericShape(expBuilder))
                .align(Alignment.CenterStart),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(fullRes),
            contentDescription = null
        )
        if (showNum) {
            StrokedText(
                text = "$currentValue/$maxValue",
                style = style,
                color = textColor
            )
        }
    }
}
