package com.moyu.chuanqirensheng.platform

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.unit.Dp
import com.eygraber.uri.Uri
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.media.MusicPlayerInterface
import com.moyu.chuanqirensheng.sub.ad.AdInterface
import com.moyu.chuanqirensheng.sub.bill.PayClientData
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.sub.report.ReportInterface
import com.moyu.chuanqirensheng.util.Platform
import com.moyu.core.model.Sell
import io.ktor.client.HttpClient

/*加密(结果为16进制字符串)  */
expect fun aes_encrypt(content: String, password: String): String?
/*解密字节数组*/
/*解密16进制的字符串为字符串  */
expect fun aes_decrypt(content: String, password: String): String?

expect fun getPlatform(): Platform

expect fun getSignature(): String

expect fun getLocalFilePath(item: String): String
expect fun getLocalFilePath(path: String, item: String): String

expect fun screenWidthInDp(): Float
expect fun screenHeightInDp(): Float

expect fun screenDensity(): Float

expect fun statusBarHeightInDp(): Dp
expect fun bottomHeightInDp(): Dp
expect fun topHeightInDp(): Dp

expect fun hideKeyboard()
expect fun triggerRebirth()
expect fun killSelf()
expect fun lifecycleExecute(isForeground: MutableState<Boolean>)

expect fun date2TimeStamp(timestampMill: Long, format: String?): String
expect fun Long.millisToHoursMinutesSeconds(): String
expect fun getElapsedTimeMillis(): Long

expect fun getVersion(): String
expect fun getVersionCode(): Int

expect fun isLite(): Boolean
expect fun isReleasePackage(): Boolean
expect fun serverUrl(): String

expect fun gameSdkDefaultProcessor(): GameSdkProcessor

expect fun createHttpClient(): HttpClient

expect fun openGamePage(uri: Uri)

expect fun needPrivacyCheck(): Boolean

expect fun hasGoogleService(): Boolean
expect fun hasBilling(): Boolean

expect fun platformChannel(): String

expect fun reportManager(): ReportInterface

expect suspend fun billPrepay(sell: Sell, function: () -> Unit)
expect fun billPayClientDataList(): List<PayClientData>

expect fun billRemovePayClientData(it: PayClientData)


expect fun antiAddictVerified(): Boolean
expect fun privacyNeedShow(): Boolean

expect fun getSystemFilesPath(): String

expect fun getSystemCacheDirPath(): String

expect fun getLocalLanguage(): String

expect fun setLocaleLanguage(languageCode: String)

expect fun getLocalCountry(): String

//expect suspend fun p_loadLocalFile(
//    fileName: String,
//    processor: (Int, String)->Unit)

expect fun platformTaskExecute()

expect fun getBuildFlavor(): String

expect fun createMusicPlayer(musicVolumeCallback: () -> Float): MusicPlayerInterface

expect fun p_getfootPrint(): String

// 对应实现：本地文件 move 操作
expect fun p_fileRename(oldFileName: String, newFileName: String)

// 文件操作
expect fun compressZip4j(zipFilePath: String, compressFilePath: String, password: String)
//expect fun compressConfigs(zipFilePath: String, password: String)
expect fun uncompressZip4j(zipFilePath: String, filePath: String, password: String)
expect fun openText(path: String?): ByteArray?
expect fun saveText(path: String, txt: ByteArray)

expect fun resetClipboard(text: String)
expect fun p_requestInAppReview()

expect fun p_getAdImp(): AdInterface

@Composable
expect fun CMPVideoPlayer(modifier: Modifier)

expect fun getHeadBitmap(): ImageBitmap?
expect fun refreshRankList(type: Int, callback: ((list:List<RankData>) -> Unit)?)
expect fun setAchievement(name: String)
expect fun setLeaderBoardScore(score: Int, type: Int)

expect fun getTextFromFile(fileName: String): String

expect fun heartbeat() // 心跳上报