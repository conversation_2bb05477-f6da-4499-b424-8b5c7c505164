package com.moyu.chuanqirensheng.thread

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.newSingleThreadContext

//import kotlinx.coroutines.asCoroutineDispatcher
//import java.util.concurrent.Executors
//
//val gameDispatcher = Executors.newSingleThreadExecutor().asCoroutineDispatcher()
//val jsonDispatcher = Executors.newSingleThreadExecutor().asCoroutineDispatcher()
//val jsonDispatcher2 = Executors.newSingleThreadExecutor().asCoroutineDispatcher()

val gameDispatcher = newSingleThreadContext("gameDispatcher")
val jsonDispatcher = newSingleThreadContext("jsonDispatcher")
val jsonDispatcher2 = newSingleThreadContext("jsonDispatcher2")