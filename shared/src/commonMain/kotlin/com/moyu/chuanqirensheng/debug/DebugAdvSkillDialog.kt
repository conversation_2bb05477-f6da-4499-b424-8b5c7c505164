package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.SearchView
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure


@Composable
fun DebugAdvSkillDialog(callback: MutableState<((Skill) -> Unit)?>) {
    if (isLite()) {
        callback.value?.let {
            PanelDialog(onDismissRequest = {
                callback.value = null
            }) {
                Column(Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
                    val search = remember {
                        mutableStateOf("")
                    }
                    val skills =
                        repo.gameCore.getSkillPool().filter { it.isAdventure() }.filter {
                            if (search.value.isNotEmpty()) {
                                it.name.contains(search.value) || it.desc.contains(search.value)
                            } else true
                        }
                    SearchView(search)
                    LazyVerticalGrid(
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        modifier = Modifier.fillMaxWidth(),
                        columns = GridCells.Fixed(5)
                    ) {
                        items(skills.size) { index ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                SingleSkillView(skill = skills[index])
                                GameButton(text = "学习") {
                                    callback.value?.invoke(skills[index])
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}