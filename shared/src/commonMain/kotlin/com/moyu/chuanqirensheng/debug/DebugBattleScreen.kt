package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.battle.ui.BattleFieldLayout
import com.moyu.chuanqirensheng.feature.router.DEBUG_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.common.GameBackground

@Composable
fun DebugBattleScreen() {
    if (isLite()) {
        BackPressHandler {
            repo.battle.value.terminate()
            repo.onGameOver()
            goto(DEBUG_SCREEN)
        }
        GameBackground(showCloseIcon = true) {
            Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                BattleFieldLayout(repo.battleRoles)
            }
        }
    }
}