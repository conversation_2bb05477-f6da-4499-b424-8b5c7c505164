package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.platform.statusBarHeightInDp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.text.indexToResourceName
import com.moyu.chuanqirensheng.ui.theme.textFieldHeight
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.CardSize
import com.moyu.chuanqirensheng.widget.common.DecorateTextField
import com.moyu.chuanqirensheng.widget.common.getTextStyle
import com.moyu.core.AppWrapper
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.Event
import com.moyu.core.model.skill.isMagic
import com.moyu.core.model.skill.isTalentAdv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun EventIdTag(modifier: Modifier, event: Event, cardSize: CardSize) {
    if (isLite()) {
        Column(modifier = modifier) {
            Text(
                text = event.id.toString(), style = cardSize.getTextStyle()
            )
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun EventDebugButton(modifier: Modifier) {
    if (isLite()) {

        val show = remember {
            mutableStateOf(false)
        }
        val showJinNang = remember {
            mutableStateOf(false)
        }
        val showZhanJiShu = remember {
            mutableStateOf(false)
        }
        FlowRow(
            modifier = modifier.padding(top = statusBarHeightInDp())
                .verticalScroll(rememberScrollState()),
            overflow = FlowRowOverflow.Visible,
        ) {
            val text = remember {
                mutableStateOf("")
            }
            GameButton(text = "局内调试", buttonSize = ButtonSize.Medium) {
                show.value = !show.value
            }
            if (show.value) {
                GameButton(text = "跳过1天") {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        EventManager.gotoNextEvent(event = null, true, 1)
                    }
                }
                GameButton(text = "跳过4天") {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        EventManager.gotoNextEvent(event = null, true, 4)
                    }
                }
                GameButton(text = "跳过10天") {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        EventManager.gotoNextEvent(event = null, true, 10)
                    }
                }
                GameButton(text = "跳过50天") {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        EventManager.gotoNextEvent(event = null, true, 50)
                    }
                }
                GameButton(text = "回滚1天") {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        EventManager.gotoNextEvent(event = null, true, -1)
                    }
                }
                GameButton(text = "回滚30天") {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        EventManager.gotoNextEvent(event = null, true, -30)
                    }
                }
                GameButton(text = "经验1000") {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        AwardManager.gainAward(award = Award(accountExp = 1000))
                    }
                }
                GameButton(text = "经验10000") {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        AwardManager.gainAward(award = Award(accountExp = 10000))
                    }
                }
                (0..7).forEach {
                    GameButton(text = "${it.indexToResourceName()}5000") {
                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                            AwardManager.gainAward(award = Award(resources = EMPTY_RESOURCES.toMutableList().apply {
                                // 资源index=0 5000
                                this[it] = 5000
                            }))
                        }
                    }
                }

                GameButton(text = "所有盟友掉血30%") {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        BattleManager.getGameAllies().forEach {
                            BattleManager.hurtAllyInGame(it, 30)
                        }
                    }
                }
                GameButton(text = "显示魔法") {
                    showJinNang.value = !showJinNang.value
                }
                if (showJinNang.value) {
                    DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text = text.value) {
                        text.value = it
                    }
                    repo.gameCore.getSkillPool().filter { it.isMagic() && it.level == StageManager.currentStage.value.id / 2 }
                        .filter { it.name.contains(text.value) }.forEach {
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                SingleSkillView(skill = it, showStars = false)
                                GameButton(
                                    buttonSize = ButtonSize.Small,
                                    buttonStyle = ButtonStyle.Blue,
                                    text = "学习",
                                    onClick = {
                                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                                            BattleManager.gainSkillInGame(target = it)
                                        }
                                    })
                            }
                        }
                }
                GameButton(text = "显示天赋") {
                    showZhanJiShu.value = !showZhanJiShu.value
                }
                if (showZhanJiShu.value) {
                    DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text = text.value) {
                        text.value = it
                    }
                    repo.gameCore.getSkillPool().filter { it.isTalentAdv() && it.level == 1 }
                        .filter { it.name.contains(text.value) }.forEach {
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                SingleSkillView(skill = it, showStars = false)
                                GameButton(
                                    buttonSize = ButtonSize.Small,
                                    buttonStyle = ButtonStyle.Blue,
                                    text = "学习",
                                    onClick = {
                                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                                            BattleManager.gainSkillInGame(target = it)
                                        }
                                    })
                            }
                        }
                }
            }
        }
    }
}