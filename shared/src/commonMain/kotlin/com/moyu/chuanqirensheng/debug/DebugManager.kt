package com.moyu.chuanqirensheng.debug

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.moyu.chuanqirensheng.platform.isLite

/**
 * 测试能力开关
 */
object DebugManager : GameDebugConfig {
    var debugBattle = false
    var debug = isLite()

    override var unlockAll: Boolean
            by mutableStateOf(false)
    override var uploadRank: Boolean
            by mutableStateOf(false)
    override var allEvent: Boolean
            by mutableStateOf(false)
    override var easyEvent: Boolean
            by mutableStateOf(false)
    override var eventWin: Boolean
            by mutableStateOf(false)
    override var eventLose: Boolean
            by mutableStateOf(false)
    override var questDone: Boolean
            by mutableStateOf(false)
    override var unlockTalents: Boolean
            by mutableStateOf(false)
    override var hp100: Boolean
            by mutableStateOf(false)
    override var easySkill: Boolean
            by mutableStateOf(false)
    override var unbreakable: Boolean
            by mutableStateOf(false)
    override var attack100: Boolean
            by mutableStateOf(false)
    override var defense100: Boolean
            by mutableStateOf(false)
    override var dodge100: Boolean
            by mutableStateOf(false)
    override var fatal100: Boolean
            by mutableStateOf(false)
    override var singleStep: Boolean
            by mutableStateOf(false)
    override var repeatEvent: Boolean
            by mutableStateOf(false)
    override var oneCentShop: Boolean
            by mutableStateOf(false)
    override var freeShop: Boolean
            by mutableStateOf(false)
}