package com.moyu.chuanqirensheng.debug

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.router.DEBUG_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.widget.button.GameButton

@Composable
fun DebugButton(modifier: Modifier) {
    if (isLite()) {
        GameButton(
            modifier = modifier,
            text = "调试",
            onClick = {
                goto(DEBUG_SCREEN)
            }
        )
    }
}