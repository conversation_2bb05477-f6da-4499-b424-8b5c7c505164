package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Checkbox
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Slider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.Dialogs.debugSkillDialog
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.award.AwardManager.accountExp
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS1_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.router.DEBUG_BATTLE
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.stage.StageManager.maxStage
import com.moyu.chuanqirensheng.feature.story.toReputationName
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_PASS_MAX_STAGE_ID
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueIfBiggerByKey
import com.moyu.chuanqirensheng.text.indexToResourceName
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.slideHeight
import com.moyu.chuanqirensheng.ui.theme.slideWidth
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.core.AppWrapper
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.role.ALLY_ROW1_FIRST
import com.moyu.core.logic.role.ALLY_ROW2_FOURTH
import com.moyu.core.logic.role.ENEMY_ROW1_FIRST
import com.moyu.core.logic.role.ENEMY_ROW2_FIRST
import com.moyu.core.logic.role.positionList
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_REPUTATION
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.getRaceGroupName
import com.moyu.core.model.getRaceTypeName
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame

val allRaceIds = (1..5).toList()
val allGroupsIds = (1..11).toList()

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun DebugScreen() {
    if (isLite()) {

        BackPressHandler {
            DebugManager.debugBattle = false
            goto(LOGIN_SCREEN)
        }
        val enemySpeciesDialog = remember {
            mutableStateOf(false)
        }
        val enemyGroupDialog = remember {
            mutableStateOf(false)
        }
        val enemySpecies = remember {
            mutableStateOf(1)
        }
        val enemyGroup = remember {
            mutableStateOf(1)
        }
        val enemySkills = remember {
            mutableListOf<Skill>()
        }
        val enemyMap = remember {
            mutableStateListOf(ALLY_ROW1_FIRST, ENEMY_ROW1_FIRST)
        }
        val enemyNumDialog = remember {
            mutableStateOf(false)
        }
        val playerSkills = remember {
            mutableListOf<Skill>()
        }
        GameBackground(title = "调试页") {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
                modifier = Modifier
                    .paint(
                        painterResource(Res.drawable.common_big_frame),
                        contentScale = ContentScale.FillBounds
                    )
                    .padding(top = padding8)
                    .verticalScroll(rememberScrollState())
            ) {
                Row {
                    Column(
                        horizontalAlignment = Alignment.Start,
                        modifier = Modifier
                            .weight(1f)
                            .padding(padding2)
                    ) {
                        DebugItem(
                            DebugManager.easySkill, "技能概率100%"
                        ) {
                            DebugManager.easySkill = it
                        }
                        DebugItem(
                            DebugManager.dodge100, "格挡率100%"
                        ) {
                            DebugManager.dodge100 = it
                        }
                        DebugItem(
                            DebugManager.fatal100, "暴击率100%"
                        ) {
                            DebugManager.fatal100 = it
                        }
                        DebugItem(
                            DebugManager.attack100, "攻击100倍"
                        ) {
                            DebugManager.attack100 = it
                        }
                        DebugItem(
                            DebugManager.defense100, "防御100倍"
                        ) {
                            DebugManager.defense100 = it
                        }
                        DebugItem(
                            DebugManager.hp100, "100倍血量"
                        ) {
                            DebugManager.hp100 = it
                        }
                        DebugItem(
                            DebugManager.singleStep, "单步调试"
                        ) {
                            DebugManager.singleStep = it
                        }
                        DebugItem(
                            DebugManager.unbreakable, "无敌秒杀"
                        ) {
                            DebugManager.unbreakable = it
                        }
                        DebugItem(
                            DebugManager.uploadRank, "Lite包上传排行榜"
                        ) {
                            DebugManager.uploadRank = it
                        }
                    }
                    Column(
                        horizontalAlignment = Alignment.Start,
                        modifier = Modifier
                            .weight(1f)
                            .padding(padding2)
                    ) {
                        DebugItem(
                            DebugManager.unlockAll, "解锁全部"
                        ) {
                            DebugManager.unlockAll = it
                        }
                        DebugItem(
                            DebugManager.easyEvent, "事件条件满足"
                        ) {
                            DebugManager.easyEvent = it
                        }
                        DebugItem(
                            DebugManager.oneCentShop, "商品1分钱"
                        ) {
                            DebugManager.oneCentShop = it
                        }
                        DebugItem(
                            DebugManager.freeShop, "付费商品直接获得"
                        ) {
                            DebugManager.freeShop = it
                        }
                        DebugItem(
                            DebugManager.eventWin, "事件必定胜利"
                        ) {
                            DebugManager.eventWin = it
                        }
                        DebugItem(
                            DebugManager.eventLose, "事件必定失败"
                        ) {
                            DebugManager.eventLose = it
                        }
                        DebugItem(
                            DebugManager.allEvent, "显示所有事件"
                        ) {
                            DebugManager.allEvent = it
                        }
                        DebugItem(
                            DebugManager.unlockTalents, "天赋不锁定"
                        ) {
                            DebugManager.unlockTalents = it
                        }
                        DebugItem(
                            DebugManager.repeatEvent, "可重复进入事件"
                        ) {
                            DebugManager.repeatEvent = it
                        }
                        DebugItem(
                            DebugManager.questDone, "新手任务解锁全部"
                        ) {
                            DebugManager.questDone = it
                        }
                    }
                }
                Row {
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            val cheat = "是否作弊=" + (AwardManager.electric.value < AwardManager.getAllVipRequire())
                            val allies = repo.allyManager.data.mapNotNull {
                                if (it.vipValue == 0) null else "${it.star}星${it.name}兵种需要的VIP：${it.vipValue},"
                            }.reduceRightOrNull { it1, it2 -> it1 + "\n" + it2 }
                            val equips = repo.equipManager.data.mapNotNull {
                                if (it.vipValue == 0) null else "${it.star}星${it.name}装备需要的VIP：${it.vipValue},"
                            }.reduceRightOrNull { it1, it2 -> it1 + "\n" + it2 }
                            Dialogs.alertDialog.value = CommonAlert(
                                title = cheat,
                                content =  allies + "\n" + equips + "\n" + "最大：${AwardManager.getAllVipRequire()}, 实际有${
                                    AwardManager.electric.value
                                }",
                            )
                        }) {
                        Text(
                            text = "检查作弊", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                val allies = repo.gameCore.getAllyPool().filter {
                                    it.star == 1 && it.level == 1
                                }
                                repo.allyManager.gain(allies.map {
                                    it.copy(
                                        num = 1000,
                                        new = true
                                    )
                                })
                                repo.allyManager.save()
                            }
                            "已获得".toast()
                        }) {
                        Text(
                            text = "军团1000", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.allyManager.data.clear()
                                repo.allyManager.save()
                            }
                            "已删".toast()
                        }) {
                        Text(
                            text = "删军团", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                val allies = repo.gameCore.getEquipPool().filter {
                                    it.star == 1
                                }
                                repo.equipManager.gain(allies.map {
                                    it.copy(
                                        num = 1000,
                                        new = true
                                    )
                                })
                                repo.equipManager.save()
                            }
                            "已获得".toast()
                        }) {
                        Text(
                            text = "装备1000", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.equipManager.data.clear()
                                repo.equipManager.save()
                            }
                            "已删".toast()
                        }) {
                        Text(
                            text = "删装备", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                TalentManager.talents.clear()
                                TalentManager.save()
                            }
                            "已删".toast()
                        }) {
                        Text(
                            text = "删天赋", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                AwardManager.gainAward(Award(couponEquip = 38))
                            }
                            "已获得".toast()
                        }) {
                        Text(
                            text = "38英雄抽", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                AwardManager.gainAward(Award(couponAlly = 38))
                            }
                            "已获得".toast()
                        }) {
                        Text(
                            text = "38兵种抽", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                AwardManager.gainAward(Award(pvpScore = 1000))
                            }
                            "已获得".toast()
                        }) {
                        Text(
                            text = "pvp积分1k", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                AwardManager.gainAward(Award(pvpScore = 10000))
                            }
                            "已获得".toast()
                        }) {
                        Text(
                            text = "pvp积分1w", style = MaterialTheme.typography.h4
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row {
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AwardManager.gainAccountExp(99)
                        }) {
                        Text(
                            text = "账号经验99", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AwardManager.gainAccountExp(9999)
                        }) {
                        Text(
                            text = "账号经验9999", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            accountExp.value = 0
                        }) {
                        Text(
                            text = "账号经验清零", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AwardManager.gainAllyExp(2000000)
                        }) {
                        Text(
                            text = "兵种经验200w", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AwardManager.allyExp.value = 0
                        }) {
                        Text(
                            text = "兵种经验清零", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(power = 100).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "体力+100", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(power = -100).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "体力-100", style = MaterialTheme.typography.h4
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                    repeat(20) { star ->
                        EffectButton(
                            modifier = Modifier
                                .height(bigButtonHeight)
                                .background(B50),
                            onClick = {
                                AppWrapper.globalScope.launch {
                                    increaseIntValueByKey(
                                        KEY_GAME_LOGIN_DAY,
                                        star + 1
                                    )
                                }
                                "已获得".toast()
                            }) {
                            Text(
                                text = "登录${star + 1}天", style = MaterialTheme.typography.h4
                            )
                        }
                        Spacer(modifier = Modifier.size(padding4))
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                AwardManager.resources.forEachIndexed { index, _ ->
                                    AwardManager.resources[index] = 0
                                }
                            }
                            "已删".toast()
                        }) {
                        Text(
                            text = "删资源", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding4))
                    repeat(8) { star ->
                        EffectButton(
                            modifier = Modifier
                                .height(bigButtonHeight)
                                .background(B50),
                            onClick = {
                                AppWrapper.globalScope.launch {
                                    AwardManager.gainAward(
                                        Award(
                                            resources = EMPTY_RESOURCES.toMutableList().let {
                                                it[star] = 500000
                                                it
                                            })
                                    )
                                }
                                "已获得".toast()
                            }) {
                            Text(
                                text = star.indexToResourceName() + "+50w",
                                style = MaterialTheme.typography.h4
                            )
                        }
                        Spacer(modifier = Modifier.size(padding4))
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                val listState = rememberLazyListState()
                val targetItem = TowerManager.maxLevel.value
                // Scroll to the target item
                LaunchedEffect(targetItem) {
                    listState.animateScrollToItem(targetItem)
                }
                LazyRow(state = listState) {
                    items(2000) { star ->
                        EffectButton(
                            modifier = Modifier
                                .height(bigButtonHeight)
                                .background(B50)
                                .padding(end = padding4), // 用右边距替代 Spacer
                            onClick = { TowerManager.maxLevel.value = star }
                        ) {
                            Text(
                                text = "爬塔${star}层",
                                style = MaterialTheme.typography.h4
                            )
                        }
                        Spacer(modifier = Modifier.size(padding6))
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                LazyRow {
                    items(100) {
                        EffectButton(
                            modifier = Modifier
                                .height(bigButtonHeight)
                                .background(B50),
                            onClick = {
                                maxStage.value = it
                                setIntValueIfBiggerByKey(KEY_PASS_MAX_STAGE_ID, it)
                                "已解锁".toast()
                            }) {
                            Text(
                                text = "关卡解锁${it + 1}", style = MaterialTheme.typography.h4
                            )
                        }
                        Spacer(modifier = Modifier.size(padding4))
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row(Modifier.horizontalScroll(rememberScrollState())) {
                    repeat(11) { index ->
                        GameButton(
                            buttonSize = ButtonSize.Small,
                            text = "${(index + 1).toReputationName()} 5k"
                        ) {
                            AwardManager.gainReputations(EMPTY_REPUTATION.toMutableList().apply {
                                this[index] = 5000
                            })
                        }
                    }
                }
                Row {
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(reputationMoney = 500000).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "声望币50w", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(pvpDiamond = 5000).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "Pvp货币5000", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(realMoney = 1000).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "英雄币1000", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(lotteryMoney = 100).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "红宝石100", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            TalentManager.talents.clear()
                            repo.gameCore.getTalent1Pool().filter {
                                it.level == 1
                            }.forEach {
                                TalentManager.talents[it.mainId] =
                                    if (it.levelLimit >= 10) 10 else it.levelLimit
                            }
                            repo.gameCore.getTalent2Pool().filter {
                                it.level == 1
                            }.forEach {
                                TalentManager.talents[it.mainId] =
                                    if (it.levelLimit >= 10) 10 else it.levelLimit
                            }
                            repo.gameCore.getTalent3Pool().filter {
                                it.level == 1
                            }.forEach {
                                TalentManager.talents[it.mainId] =
                                    if (it.levelLimit >= 10) 10 else it.levelLimit
                            }
                            TalentManager.save()
                            "已10级".toast()
                        }) {
                        Text(
                            text = "10级天赋", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(talentPoint = 999).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "999天赋点", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            TalentManager.talents.clear()
                            repo.gameCore.getTalent2Pool().filter {
                                it.level == it.levelLimit
                            }.forEach {
                                TalentManager.talents[it.mainId] = it.level
                            }
                            repo.gameCore.getTalent3Pool().filter {
                                it.level == it.levelLimit
                            }.forEach {
                                TalentManager.talents[it.mainId] = it.level
                            }
                            repo.gameCore.getTalent1Pool().forEach {
                                TalentManager.talents[it.mainId] = it.level
                            }
                            TalentManager.save()
                            "已满级".toast()
                        }) {
                        Text(
                            text = "满级天赋", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            repo.gameCore.getTalent1Pool().sortedBy { it.conditionNum }
                                .take(50).forEach {
                                    TalentManager.talents[it.mainId] = it.level
                                }
                            TalentManager.save()
                        }) {
                        Text(
                            text = "1天赋50级", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            repo.gameCore.getTalent1Pool().sortedBy { it.conditionNum }
                                .take(100).forEach {
                                    TalentManager.talents[it.mainId] = it.level
                                }
                            TalentManager.save()
                        }) {
                        Text(
                            text = "1天赋100级", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            repo.gameCore.getTalent1Pool().filter { it.isType1() }.forEach {
                                TalentManager.talents[it.mainId] = it.level
                            }
                            TalentManager.save()
                            "已满级".toast()
                        }) {
                        Text(
                            text = "满级天赋1", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            repo.gameCore.getTalent2Pool().filter { it.isType2() && it.level == 1 }
                                .forEach {
                                    TalentManager.talents[it.mainId] = it.levelLimit
                                }
                            TalentManager.save()
                            "已满级".toast()
                        }) {
                        Text(
                            text = "满级天赋2", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            TalentManager.talents.clear()
                            TalentManager.save()
                            "已清除".toast()
                        }) {
                        Text(
                            text = "删天赋", style = MaterialTheme.typography.h4
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row {
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            repo.allyManager.data.removeAll { it.quality == 4 }
                            repo.allyManager.save()
                        }) {
                        Text(
                            text = "删除红兵种", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding1))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.allyManager.data.forEachIndexed { index, ally ->
                                    repo.allyManager.data[index] =
                                        repo.allyManager.data[index].levelTo(1)
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "军团1级", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding1))
                    listOf(
                        1, 5, 10, 50, 100
                    ).forEach { level ->
                        EffectButton(
                            modifier = Modifier
                                .height(bigButtonHeight)
                                .weight(1f)
                                .background(B50),
                            onClick = {
                                AppWrapper.globalScope.launch {
                                    repo.allyManager.data.forEachIndexed { index, ally ->
                                        repo.allyManager.data[index] =
                                            repo.allyManager.data[index].levelTo(level + ally.level)
                                    }
                                    repo.allyManager.save()
                                }
                            }) {
                            Text(
                                text = "军团+${level}级", style = MaterialTheme.typography.h4
                            )
                        }
                        Spacer(modifier = Modifier.size(padding1))
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row {
                    listOf(1, 5, 10, 50, 100).forEach { level ->
                        EffectButton(
                            modifier = Modifier
                                .height(bigButtonHeight)
                                .weight(1f)
                                .background(B50),
                            onClick = {
                                AppWrapper.globalScope.launch {
                                    accountExp.value =
                                        (AwardManager.getMasterLevel() + level - 1) * 100
                                }
                            }) {
                            Text(
                                text = "领主+${level}级", style = MaterialTheme.typography.h4
                            )
                        }
                        Spacer(modifier = Modifier.size(padding1))
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row {
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.allyManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 3) {
                                        repo.allyManager.data[index] =
                                            repo.allyManager.data[index].starTo(5)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "橙卡5星", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.allyManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 3) {
                                        repo.allyManager.data[index] =
                                            repo.allyManager.data[index].starTo(20)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "橙卡20星", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.allyManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 3) {
                                        repo.allyManager.data[index] =
                                            repo.allyManager.data[index].starTo(50)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "橙卡50星", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.allyManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 4) {
                                        repo.allyManager.data[index] =
                                            repo.allyManager.data[index].starTo(20)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "红卡20星", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.allyManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 4) {
                                        repo.allyManager.data[index] =
                                            repo.allyManager.data[index].starTo(50)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "红卡50星", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.equipManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 3) {
                                        repo.equipManager.data[index] =
                                            repo.equipManager.data[index].starTo(5)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "橙装5星", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.equipManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 3) {
                                        repo.equipManager.data[index] =
                                            repo.equipManager.data[index].starTo(20)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "橙装20星", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.equipManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 3) {
                                        repo.equipManager.data[index] =
                                            repo.equipManager.data[index].starTo(50)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "橙装50星", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.equipManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 4) {
                                        repo.equipManager.data[index] =
                                            repo.equipManager.data[index].starTo(5)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "红装5星", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.equipManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 4) {
                                        repo.equipManager.data[index] =
                                            repo.equipManager.data[index].starTo(20)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "红装20星", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                repo.equipManager.data.forEachIndexed { index, ally ->
                                    if (ally.quality == 4) {
                                        repo.equipManager.data[index] =
                                            repo.equipManager.data[index].starTo(50)
                                    }
                                }
                                repo.allyManager.save()
                            }
                        }) {
                        Text(
                            text = "红装50星", style = MaterialTheme.typography.h4
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row {
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(diamond = 100000).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "荣誉点10w", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                AwardManager.diamond.value = 0
                            }
                        }) {
                        Text(
                            text = "删荣誉点", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(key = 36).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "钻石36", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(key = 30000).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "钻石3w", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                AwardManager.lotteryMoney.value += 20000
                            }
                        }) {
                        Text(
                            text = "2w转盘币",
                            style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(unlockList = listOf(KEY_WAR_PASS1_UNLOCK_EVIDENCE)).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }) {
                        Text(
                            text = "解锁战令1", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(unlockList = listOf(KEY_WAR_PASS2_UNLOCK_EVIDENCE)).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }) {
                        Text(
                            text = "解锁战令2", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                Dialogs.awardDialog.value = Award(holidayMoney = 500).apply {
                                    AwardManager.gainAward(this)
                                }
                            }
                        }) {
                        Text(
                            text = "圣诞币500", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                HolidayLotteryManager.holidaySpinTotal.value += 10
                                HolidayLotteryManager.uploadHolidayRank()
                            }
                        }) {
                        Text(
                            text = "转盘数+10", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                HolidayLotteryManager.holidaySpinTotal.value -= 10
                                HolidayLotteryManager.uploadHolidayRank()
                            }
                        }) {
                        Text(
                            text = "转盘数-10", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                HolidayLotteryManager.holidaySpinTotal.value += 3
                                HolidayLotteryManager.uploadHolidayRank()
                            }
                        }) {
                        Text(
                            text = "转盘数+3", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            AppWrapper.globalScope.launch {
                                HolidayLotteryManager.holidaySpinTotal.value -= 3
                                HolidayLotteryManager.uploadHolidayRank()
                            }
                        }) {
                        Text(
                            text = "转盘数-3", style = MaterialTheme.typography.h4
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row {
                    val electric = remember {
                        mutableStateOf(0f)
                    }
                    Column(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(2f),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            GameButton(text = "+1", clickGap = 0, buttonSize = ButtonSize.Small) {
                                electric.value += 1
                            }
                            GameButton(
                                text = if (electric.value == 0f) "特权值" else "${electric.value}",
                                buttonSize = ButtonSize.MediumMinus
                            ) {
                                AppWrapper.globalScope.launch {
                                    Dialogs.awardDialog.value =
                                        Award(electric = electric.value.toInt()).apply {
                                            AwardManager.gainAward(this)
                                        }
                                }
                            }
                            GameButton(text = "-1", clickGap = 0, buttonSize = ButtonSize.Small) {
                                electric.value -= 1
                            }
                        }

                        Slider(
                            modifier = Modifier
                                .size(slideWidth, slideHeight),
                            steps = 10,
                            value = electric.value,
                            onValueChange = { electric.value = it.toInt().toFloat() },
                            valueRange = 0f..80000f,
                        )
                    }
                }
                Row {
                    val warpass = remember {
                        mutableStateOf(0f)
                    }
                    Column(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(2f),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            GameButton(text = "+1", clickGap = 0, buttonSize = ButtonSize.Small) {
                                warpass.value += 1
                            }
                            GameButton(
                                text = if (warpass.value == 0f) "通行证" else "${warpass.value}",
                                buttonSize = ButtonSize.MediumMinus
                            ) {
                                AppWrapper.globalScope.launch {
                                    Dialogs.awardDialog.value =
                                        Award(warPass1 = warpass.value.toInt()).apply {
                                            AwardManager.gainAward(this)
                                        }
                                }
                            }
                            GameButton(text = "-1", clickGap = 0, buttonSize = ButtonSize.Small) {
                                warpass.value -= 1
                            }
                        }
                        Slider(
                            modifier = Modifier
                                .size(slideWidth, slideHeight),
                            steps = 10,
                            value = warpass.value,
                            onValueChange = { warpass.value = it.toInt().toFloat() },
                            valueRange = 0f..100f,
                        )
                    }
                }
                Row {
                    val warpass2 = remember {
                        mutableStateOf(0f)
                    }
                    Column(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(2f),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            GameButton(text = "+1", clickGap = 0, buttonSize = ButtonSize.Small) {
                                warpass2.value += 1
                            }
                            GameButton(
                                text = if (warpass2.value == 0f) "通行证2" else "${warpass2.value}",
                                buttonSize = ButtonSize.MediumMinus
                            ) {
                                AppWrapper.globalScope.launch {
                                    Dialogs.awardDialog.value =
                                        Award(warPass2 = warpass2.value.toInt()).apply {
                                            AwardManager.gainAward(this)
                                        }
                                }
                            }
                            GameButton(text = "-1", clickGap = 0, buttonSize = ButtonSize.Small) {
                                warpass2.value -= 1
                            }
                        }
                        Slider(
                            modifier = Modifier
                                .size(slideWidth, slideHeight),
                            steps = 10,
                            value = warpass2.value,
                            onValueChange = { warpass2.value = it.toInt().toFloat() },
                            valueRange = 0f..100f,
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row {
                    val warpass3 = remember {
                        mutableStateOf(0f)
                    }
                    Column(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(2f),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            GameButton(text = "+1", clickGap = 0, buttonSize = ButtonSize.Small) {
                                warpass3.value += 1
                            }
                            GameButton(
                                text = if (warpass3.value == 0f) "通行证3" else "${warpass3.value}",
                                buttonSize = ButtonSize.MediumMinus
                            ) {
                                AppWrapper.globalScope.launch {
                                    Dialogs.awardDialog.value =
                                        Award(warPass3 = warpass3.value.toInt()).apply {
                                            AwardManager.gainAward(this)
                                        }
                                }
                            }
                            GameButton(text = "-1", clickGap = 0, buttonSize = ButtonSize.Small) {
                                warpass3.value -= 1
                            }
                        }
                        Slider(
                            modifier = Modifier
                                .size(slideWidth, slideHeight),
                            steps = 10,
                            value = warpass3.value,
                            onValueChange = { warpass3.value = it.toInt().toFloat() },
                            valueRange = 0f..100f,
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding6))
                Row {
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            enemySkills.clear()
                            debugSkillDialog.value = {
                                enemySkills.add(it)
                            }
                        }) {
                        Text(
                            text = "敌人技能", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            enemySpeciesDialog.value = true
                        }) {
                        Text(
                            text = "敌人种族", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            enemyGroupDialog.value = true
                        }) {
                        Text(
                            text = "敌人阵营", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            enemyNumDialog.value = true
                        }) {
                        Text(
                            text = "双方阵型", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            playerSkills.clear()
                            debugSkillDialog.value = {
                                playerSkills.add(it)
                            }
                        }) {
                        Text(
                            text = "玩家技能", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding6))
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .weight(1f)
                            .background(B50),
                        onClick = {
                            DebugManager.debugBattle = true
                            val roleHashMap = mutableMapOf<Int, Role?>()
                            positionList.forEach {
                                roleHashMap[it] = null
                            }
                            enemyMap.forEachIndexed { index, position ->
                                if (position in ALLY_ROW1_FIRST..ALLY_ROW2_FOURTH) {
                                    DefaultAllyCreator.create(
                                        repo.gameCore.getAllyPool()
                                            .filter { it.star == 1 && it.level <= 8 }[index],
                                        Property(),
                                        identifier = Identifier.player(name = "玩家$index")
                                    ).apply {
                                        setSkills(emptyList())
                                        if (roleHashMap.values.mapNotNull { it }
                                                .none { it.isPlayerSide() }) {
                                            // 设置的技能只有第一个敌人学习，不然有点乱
                                            playerSkills.forEach {
                                                learnSkill(it, this.roleIdentifier)
                                            }
                                            learnSkill(
                                                repo.gameCore.getSkillById(2001),
                                                this.roleIdentifier
                                            )
                                        } else {
                                            // 其他只有普攻
                                            if (roleHashMap.values.mapNotNull { it }
                                                    .filter { it.isPlayerSide() }.size == 1) {
                                                learnSkill(
                                                    repo.gameCore.getSkillById(2001),
                                                    this.roleIdentifier
                                                )
                                            } else {
                                                learnSkill(
                                                    repo.gameCore.getSkillById(2001),
                                                    this.roleIdentifier
                                                )
                                            }
                                        }
                                        roleHashMap[position] = this
                                    }
                                } else {
                                    DefaultAllyCreator.create(
                                        repo.gameCore.getAllyPool()
                                            .filter { it.star == 1 && it.level <= 8 }.filter {
                                                if (enemySpecies.value == 5) {
                                                    it.raceType == enemySpecies.value
                                                } else {
                                                    it.raceType == enemySpecies.value && it.raceType2 == enemyGroup.value
                                                }
                                            }[index],
                                        Property(),
                                        identifier = Identifier.enemy(name = "敌人$index")
                                    ).apply {
                                        setSkills(emptyList())
                                        if (roleHashMap.values.mapNotNull { it }
                                                .none { !it.isPlayerSide() }) {
                                            // 设置的技能只有第一个敌人学习，不然有点乱
                                            setSkills(emptyList())
                                            enemySkills.forEach {
                                                learnSkill(it, this.roleIdentifier)
                                            }
                                            learnSkill(
                                                repo.gameCore.getSkillById(2001),
                                                this.roleIdentifier
                                            )
                                        } else {
                                            // 其他只有普攻
                                            if (roleHashMap.values.mapNotNull { it }
                                                    .filter { !it.isPlayerSide() }.size == 1) {
                                                learnSkill(
                                                    repo.gameCore.getSkillById(2001),
                                                    this.roleIdentifier
                                                )
                                            } else {
                                                learnSkill(
                                                    repo.gameCore.getSkillById(2001),
                                                    this.roleIdentifier
                                                )
                                            }
                                        }
                                        roleHashMap[position] = this
                                    }
                                }
                            }
                            repo.battleRoles.clear()
                            repo.battleRoles.putAll(roleHashMap)
                            repo.startBattle()
                            goto(DEBUG_BATTLE)
                        }) {
                        Text(
                            text = "开始战斗", style = MaterialTheme.typography.h4
                        )
                    }
                }
            }
        }
        if (enemySpeciesDialog.value) {
            PanelDialog(onDismissRequest = {
                enemySpeciesDialog.value = false
            }) {
                LazyVerticalGrid(
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    modifier = Modifier.fillMaxWidth(),
                    columns = GridCells.Fixed(5)
                ) {
                    items(allRaceIds.size) { index ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(
                                text = (index + 1).getRaceTypeName(),
                                style = MaterialTheme.typography.h4
                            )
                            GameButton(text = "选择") {
                                enemySpecies.value = allRaceIds[index]
                                enemySpeciesDialog.value = false
                            }
                        }
                    }
                }
            }
        }
        if (enemyGroupDialog.value) {
            PanelDialog(onDismissRequest = {
                enemyGroupDialog.value = false
            }) {
                LazyVerticalGrid(
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    modifier = Modifier.fillMaxWidth(),
                    columns = GridCells.Fixed(5)
                ) {
                    items(allGroupsIds.size) { index ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(
                                text = (index + 1).getRaceGroupName(),
                                style = MaterialTheme.typography.h4
                            )
                            GameButton(text = "选择") {
                                enemyGroup.value = allGroupsIds[index]
                                enemyGroupDialog.value = false
                            }
                        }
                    }
                }
            }
        }
        if (enemyNumDialog.value) {
            PanelDialog(onDismissRequest = {
                enemyNumDialog.value = false
            }) {
                FlowRow(
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    modifier = Modifier.fillMaxWidth(),
                    overflow = FlowRowOverflow.Visible,
                    maxItemsInEachRow = 4
                ) {
                    repeat(4) { index ->
                        val position = ENEMY_ROW2_FIRST + index
                        val text = if (enemyMap.contains(position)) {
                            "有敌人"
                        } else {
                            "无敌人"
                        }
                        GameButton(
                            text = text,
                            buttonSize = ButtonSize.Small,
                            buttonStyle = if (enemyMap.contains(position)) ButtonStyle.Blue else ButtonStyle.Green
                        ) {
                            if (enemyMap.contains(position)) {
                                enemyMap.remove(position)
                            } else {
                                enemyMap.add(position)
                            }
                        }
                    }
                    repeat(4) { index ->
                        val position = ENEMY_ROW1_FIRST + index
                        val text = if (enemyMap.contains(position)) {
                            "有敌人"
                        } else {
                            "无敌人"
                        }
                        GameButton(
                            text = text,
                            buttonSize = ButtonSize.Small,
                            buttonStyle = if (enemyMap.contains(position)) ButtonStyle.Blue else ButtonStyle.Green
                        ) {
                            if (enemyMap.contains(position)) {
                                enemyMap.remove(position)
                            } else {
                                enemyMap.add(position)
                            }
                        }
                    }
                    Spacer(
                        modifier = Modifier
                            .height(padding80)
                            .fillMaxWidth()
                    )
                    repeat(8) { index ->
                        val position = ALLY_ROW1_FIRST + index
                        val text = if (enemyMap.contains(position)) {
                            "有盟友"
                        } else {
                            "无盟友"
                        }
                        GameButton(
                            text = text,
                            buttonSize = ButtonSize.Small,
                            buttonStyle = if (enemyMap.contains(position)) ButtonStyle.Blue else ButtonStyle.Green
                        ) {
                            if (enemyMap.contains(position)) {
                                enemyMap.remove(position)
                            } else {
                                enemyMap.add(position)
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DebugItem(checked: Boolean, content: String, onChecked: suspend (Boolean) -> Unit) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Checkbox(
            checked = checked, onCheckedChange = {
                AppWrapper.globalScope.launch {
                    onChecked(it)
                }
            }, modifier = Modifier.size(
                padding30
            )
        )
        Spacer(modifier = Modifier.size(padding4))
        Text(
            text = content.trimIndent(), style = MaterialTheme.typography.h4
        )
    }
}
