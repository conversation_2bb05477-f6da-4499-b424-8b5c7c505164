package com.moyu.chuanqirensheng.debug

//import androidx.activity.OnBackPressedCallback
//import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.runtime.*

@Composable
fun BackPressHandler(onBack: () -> Unit = {}) {
    val currentOnBack by rememberUpdatedState(onBack)
    // TODO: 兼容 Android的返回操作 byquding
//    val backCallback = remember {
//        object : OnBackPressedCallback(true) {
//            override fun handleOnBackPressed() {
//                currentOnBack()
//            }
//        }
//    }
//    LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher?.let {
//        DisposableEffect(it) {
//            // 注意，这里需要传入lifecycle，否则切后台后再返回，这个回调的优先级会降低（因为activity本身的两个onBack会反注册和重新注册）
//            it.addCallback(GameApp.instance.activity, backCallback)
//            //Key值变化或所在可组合项从组合树中移除时调用
//            onDispose {
//                backCallback.remove()
//            }
//        }
//    }
}