package com.moyu.chuanqirensheng.sub.review

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.platform.p_requestInAppReview
import com.moyu.chuanqirensheng.sub.datastore.KEY_RANK_STAR
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.core.AppWrapper
import shared.generated.resources.Res
import shared.generated.resources.review_result_toast

object GameReviewManager {
    private val alreadyChooseStar = mutableStateOf(false)

    fun init() {
        alreadyChooseStar.value = getBooleanFlowByKey(KEY_RANK_STAR)
    }

    fun checkTriggerReviewDialog(age: Int) {
        if (!alreadyChooseStar.value && age == 25 && StageManager.currentStage.value.id == 2) {
            Dialogs.gameReviewDialog.value = true
            setBooleanValueByKey(KEY_RANK_STAR, true)
        }
    }

    fun doRankStar(star: Int) {
        alreadyChooseStar.value = true
        setBooleanValueByKey(KEY_RANK_STAR, true)
        if (star == 5) {
            p_requestInAppReview()
        } else {
            AppWrapper.getStringKmp(Res.string.review_result_toast).toast()
        }
        Dialogs.gameReviewDialog.value = false
    }
}