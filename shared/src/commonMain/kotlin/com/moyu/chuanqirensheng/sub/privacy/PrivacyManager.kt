package com.moyu.chuanqirensheng.sub.privacy

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PERMISSION
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.sub.datastore.KEY_VERIFIED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.platform.antiAddictVerified
import com.moyu.chuanqirensheng.platform.privacyNeedShow

object PrivacyManager {
    var privacyNeedShow by mutableStateOf(false)
    var permissionNeedShow by mutableStateOf(false)
    var antiAddictVerified by mutableStateOf(false)

    fun init() {
        antiAddictVerified = antiAddictVerified()
        privacyNeedShow = privacyNeedShow()
        Dialogs.showPrivacyDialog.value = privacyNeedShow
        permissionNeedShow = getBooleanFlowByKey(KEY_NEED_SHOW_PERMISSION, true)
        if (!privacyNeedShow && permissionNeedShow) {
            Dialogs.showPermissionDialog.value = true
        }
    }
}