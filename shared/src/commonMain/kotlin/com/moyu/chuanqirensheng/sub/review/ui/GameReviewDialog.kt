package com.moyu.chuanqirensheng.sub.review.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.sub.review.GameReviewManager
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.widget.common.ClickableStars
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.rank_star_title
import shared.generated.resources.review_text

@Composable
fun GameReviewDialog(info: MutableState<Boolean>) {
    if (info.value) {
        PanelDialog(
            onDismissRequest = {
                GameReviewManager.doRankStar(1)
            }) {
            Column(
                modifier = Modifier.fillMaxSize().padding(horizontal = padding4),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                StrokedText(
                    text = stringResource(Res.string.rank_star_title),
                    style = MaterialTheme.typography.h2,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding19))
                StrokedText(
                    text = stringResource(Res.string.review_text),
                    style = MaterialTheme.typography.h2,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding10))
                ClickableStars {
                    GameReviewManager.doRankStar(it)
                }
            }
        }
    }
}