package com.moyu.chuanqirensheng.sub.bill.pay

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.cancel
import shared.generated.resources.order_confirming

@Composable
fun PayBlockDialog(
    switch: MutableState<String?>
) {
    if (switch.value != null) {
        val canCancel = remember {
            mutableStateOf(false)
        }
        LaunchedEffect(Unit) {
            delay(10000)
            canCancel.value = true
        }
        PanelDialog(
            onDismissRequest = { },
            contentBelow = {
                if (canCancel.value) {
                    GameButton(text = stringResource(Res.string.cancel)) {
                        switch.value = null
                    }
                }
            }
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxSize()
                    .padding(horizontal = padding22)
            ) {
                Spacer(modifier = Modifier.size(padding2))
                StrokedText(
                    text = stringResource(Res.string.order_confirming),
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding22))
                StrokedText(
                    text = switch.value ?: "",
                    style = MaterialTheme.typography.h2,
                    color = Color.White
                )
            }
        }
    }
}
