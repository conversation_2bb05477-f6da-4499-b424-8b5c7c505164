package com.moyu.chuanqirensheng.sub.loginsdk

import androidx.compose.runtime.MutableState

/**
 * 隔离游戏sdk能力，后续可能会替换成非tap sdk版本
 */
interface GameSdkProcessor {
    fun initGameSdk()
    fun login()
    fun initSDK()
    fun antiAddictPassed(): MutableState<Boolean>
    fun hasLogin(): Boolean
    fun getAvatarUrl(): String?
    fun getUserName(): String?
    fun getObjectId(): String?
    fun getAntiAddictionContent(): String
    fun checkAntiAddiction()
    fun dealAfterLogin(name: String, id: String, avatarUrl: String)
    fun isAgeUnder8(): Boolean
    fun isAgeIn8To16(): Boolean
    fun quitGame(onExit: ()->Unit)
//    fun checkPaymentLimit(amount: Int, onSuccess: (Boolean) -> Unit, onError: (String) -> Unit)
}