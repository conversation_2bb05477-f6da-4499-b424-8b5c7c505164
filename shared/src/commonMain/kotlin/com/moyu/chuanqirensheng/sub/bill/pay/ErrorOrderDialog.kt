package com.moyu.chuanqirensheng.sub.bill.pay

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.platform.billPayClientDataList
import com.moyu.chuanqirensheng.platform.billRemovePayClientData
import com.moyu.chuanqirensheng.platform.resetClipboard
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.copied
import shared.generated.resources.copy
import shared.generated.resources.delete
import shared.generated.resources.error_order
import shared.generated.resources.error_order_tips
import shared.generated.resources.order_id
import shared.generated.resources.order_money

@Composable
fun ErrorOrderDialog(show: MutableState<Boolean>) {
    show.value.takeIf { it }?.let {
        PanelDialog(onDismissRequest = {
            show.value = false
        }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = padding4)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                StrokedText(
                    text = stringResource(Res.string.error_order),
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding16))
                billPayClientDataList().forEach {
                    Column(
                        Modifier
                            .padding(horizontal = padding10, vertical = padding6)
                            .clip(
                                RoundedCornerShape(padding4)
                            )
                            .background(B50)
                            .padding(horizontal = padding10, vertical = padding6)
                    ) {
                        val money = stringResource(
                            Res.string.order_money,
                            it.totalMoneyInCent / 100
                        )
                        Row {
                            StrokedText(text = it.orderName, style = MaterialTheme.typography.h5)
                            Spacer(modifier = Modifier.size(padding4))
                            StrokedText(
                                text = money, style = MaterialTheme.typography.h5
                            )
                        }
                        val orderId = stringResource(Res.string.order_id, it.tradeNo + it.userId.reversed())
                        StrokedText(
                            text = orderId,
                            style = MaterialTheme.typography.h5
                        )
                        StrokedText(
                            text = stringResource(Res.string.error_order_tips, LoginManager.instance.loginData.value.customServiceTips),
                            style = MaterialTheme.typography.h5
                        )
                        Spacer(modifier = Modifier.size(padding4))
                        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                            GameButton(
                                buttonSize = ButtonSize.MediumMinus,
                                text = stringResource(Res.string.copy),
                                onClick = {
                                    resetClipboard(it.orderName + "," + money + "," + orderId)
                                    AppWrapper.getStringKmp(Res.string.copied).toast()
                                })
                            GameButton(
                                buttonSize = ButtonSize.MediumMinus,
                                text = stringResource(Res.string.delete),
                                onClick = {
                                    billRemovePayClientData(it)

                                    if (billPayClientDataList().isEmpty()) {
                                        show.value = false
                                    }
                                })
                        }
                    }
                    Spacer(modifier = Modifier.size(padding16))
                }
                Spacer(modifier = Modifier.size(padding16))
            }
        }
    }
}