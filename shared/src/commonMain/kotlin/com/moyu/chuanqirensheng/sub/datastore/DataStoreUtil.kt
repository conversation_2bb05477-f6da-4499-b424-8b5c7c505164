package com.moyu.chuanqirensheng.sub.datastore

import androidx.compose.runtime.mutableStateMapOf
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.moyu.chuanqirensheng.thread.jsonDispatcher
import com.moyu.chuanqirensheng.thread.jsonDispatcher2
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.core.AppWrapper
import com.moyu.core.INT_ENCRYPT
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.KSerializer
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.builtins.MapSerializer
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

val json = Json {
    ignoreUnknownKeys = true
    encodeDefaults = false     // 不写出默认值
    explicitNulls = false      // (可选) 不写出 null，进一步压缩
}
val mapData = mutableStateMapOf<String, String>()
// todo 注意，局内的未完成游戏，独立一个存档，和云存档隔离
val mapData2 = mutableStateMapOf<String, String>()
// todo 服务器ID独立的存储
val mapData3 = mutableStateMapOf<String, String>()

/**
 * 获得实际的加密字符串，为固定字符串加上key
 */
fun getRealEncrypt(key: String): String {
    return INT_ENCRYPT + key
}


suspend fun clearDataStore1() {
    mapData.clear()
    dataStore.edit { settings ->
        settings.clear()
    }
}

suspend fun clearDataStore2() {
    mapData2.clear()
    dataStore2.edit { settings ->
        settings.clear()
    }
}

/**
 * 读取boolean对象，将从内存map读取
 */
fun getBooleanFlowByKey(key: String, default: Boolean = false): Boolean {
    return getLongFlowByKey(key, if (default) 1 else 0) > 0
}

/**
 * 读取int对象，将从内存map读取
 */
fun getIntFlowByKey(key: String, default: Int = 0): Int {
    return getLongFlowByKey(key, default.toLong()).toInt()
}

/**
 * 读取long对象，将从内存map读取
 */
fun getLongFlowByKey(key: String, default: Long = 0): Long {
    return mapData[key]?.takeIf { it.isNotEmpty() }?.let {
        AdUtil.decodeText(it)?.replace(getRealEncrypt(key), "")?.toLong() ?: default
    } ?: default
}

/**
 * 读取字符串对象，将从内存map读取
 */
fun getStringFlowByKey(key: String, default: String = ""): String {
    return mapData[key]?.takeIf { it.isNotEmpty() }?.let {
        AdUtil.decodeText(it)?.replace(getRealEncrypt(key), "") ?: ""
    }?: default
}

/**
 * 读取对象，将从内存map读取
 */
inline fun <reified T : Any> getObject(key: String): T? {
    return mapData[key]?.takeIf { it.isNotEmpty() }?.let {
        json.decodeFromString(it.let { string ->
            AdUtil.decodeText(string)?.replace(getRealEncrypt(key), "") ?: ""
        })
    }
}

/**
 * 读取List对象，将从内存map读取
 */
inline fun <reified T : Any> getListObject(key: String): List<T> {
    return mapData[key]?.takeIf { it.isNotEmpty() }?.let {
        json.decodeFromString(it.let { string ->
            AdUtil.decodeText(string)?.replace(getRealEncrypt(key), "") ?: ""
        })
    }?: emptyList()
}

/**
 * 读取List对象，将从内存map读取
 */
fun <T : Any> getListObject(key: String, serializer: KSerializer<T>): List<T> {
    return mapData[key]?.takeIf { it.isNotEmpty() }?.let {
        json.decodeFromString(
            ListSerializer(serializer),
            it.let { string ->
                AdUtil.decodeText(string)?.replace(getRealEncrypt(key), "") ?: ""
            })
    }?: emptyList()
}

/**
 * 读取Map对象，将从内存map读取
 */
fun <K, T> getMapObject(
    key: String,
    serializer1: KSerializer<K>,
    serializer2: KSerializer<T>
): Map<K, T> {
    return mapData[key]?.takeIf { it.isNotEmpty() }?.let {
        json.decodeFromString(
            MapSerializer(
                serializer1,
                serializer2
            ), it.let { string ->
                AdUtil.decodeText(string)?.replace(getRealEncrypt(key), "") ?: ""
            })
    }?: emptyMap()
}

fun setBooleanValueByKey(key: String, value: Boolean) {
    setLongValueByKey(key, if (value) 1 else 0)
}

fun setIntValueByKey(key: String, value: Int) {
    return setLongValueByKey(key, value.toLong())
}

// 最终保存数值的地方
fun setLongValueByKey(key: String, value: Long) {
    val string = getRealEncrypt(key) + value
    innerSetStringValueByKey(key, AdUtil.encodeText(string) ?: "")
}

fun setStringValueByKey(key: String, value: String) {
    innerSetStringValueByKey(key, AdUtil.encodeText(value) ?: "")
}

private fun innerSetStringValueByKey(key: String, value: String) {
    mapData[key] = value
    AppWrapper.globalScope.launch(jsonDispatcher) {
        // 更新内存数据
        dataStore.edit { settings ->
            settings[stringPreferencesKey(key)] = value
        }
    }
}

inline fun <reified T : Any> setObject(key: String, t: T?) {
    val value = if (t == null) "" else
        json.encodeToString(t).let {
            AdUtil.encodeText(it) ?: ""
        }
    mapData[key] = value
    AppWrapper.globalScope.launch(jsonDispatcher) {
        dataStore.edit { settings ->
            settings[stringPreferencesKey(key)] = value
        }
    }
}

fun <T : Any> setListObject(
    key: String,
    list: List<T>,
    elementSerializer: KSerializer<T>
) {
    val string = json.encodeToString(ListSerializer(elementSerializer), list)
    val value = AdUtil.encodeText(string) ?: ""
    mapData[key] = value
    AppWrapper.globalScope.launch(jsonDispatcher) {
        dataStore.edit { settings ->
            settings[stringPreferencesKey(key)] = value
        }
    }
}

inline fun <reified T : Any> setListObject(key: String, list: List<T>) {
    val string = json.encodeToString(list)
    val value = AdUtil.encodeText(string) ?: ""
    mapData[key] = value
    AppWrapper.globalScope.launch(jsonDispatcher) {
        dataStore.edit { settings ->
            settings[stringPreferencesKey(key)] = value
        }
    }
}

fun <K, T> setMapObject(
    key: String,
    list: Map<K, T>,
    serializer1: KSerializer<K>,
    serializer2: KSerializer<T>
) {
    val string = json.encodeToString(MapSerializer(serializer1, serializer2), list)
    val value = AdUtil.encodeText(string) ?: ""
    mapData[key] = value
    AppWrapper.globalScope.launch(jsonDispatcher) {
        dataStore.edit { settings ->
            settings[stringPreferencesKey(key)] = value
        }
    }
}

fun increaseIntValueByKey(key: String, value: Int = 1) {
    val current = getIntFlowByKey(key)
    setIntValueByKey(key, current + value)
}

fun setIntValueIfBiggerByKey(key: String, value: Int = 1) {
    val current = getIntFlowByKey(key)
    if (current < value) {
        setIntValueByKey(key, value)
    }
}



suspend fun setBooleanValueByKeySync(key: String, value: Boolean) {
    setLongValueByKeySync(key, if (value) 1 else 0)
}

// 最终保存数值的地方
suspend fun setLongValueByKeySync(key: String, value: Long) {
    val string = getRealEncrypt(key) + value
    innerSetStringValueByKeySync(key, AdUtil.encodeText(string) ?: "")
}

private suspend fun innerSetStringValueByKeySync(key: String, value: String) {
    mapData[key] = value
    withContext(jsonDispatcher) {
        // 更新内存数据
        dataStore.edit { settings ->
            settings[stringPreferencesKey(key)] = value
        }
    }
}

suspend inline fun <reified T : Any> setListObjectSync(key: String, list: List<T>) {
    val string = json.encodeToString(list)
    val value = AdUtil.encodeText(string) ?: ""
    mapData[key] = value
    withContext(jsonDispatcher) {
        dataStore.edit { settings ->
            settings[stringPreferencesKey(key)] = value
        }
    }
}

inline fun <reified T : Any> getObjectFromStore2(key: String): T? {
    return mapData2[key]?.takeIf { it.isNotEmpty() }?.let {
        json.decodeFromString(it.let { string ->
            AdUtil.decodeText(string)?.replace(getRealEncrypt(key), "") ?: ""
        })
    }
}

inline fun <reified T : Any> setObjectToStore2(key: String, t: T?) {
    val value = if (t == null) "" else
        json.encodeToString(t).let {
            AdUtil.encodeText(it) ?: ""
        }
    mapData2[key] = value
    AppWrapper.globalScope.launch(jsonDispatcher2) {
        dataStore2.edit { settings ->
            settings[stringPreferencesKey(key)] = value
        }
    }
}

fun getServerIdFromStore3SyncNoEncrypt(): Int {
    // 如果没有服务器id数据，那么可能两种情况，老用户升级，需要返回服务器id0（老用户都当做服务器0），真的新用户，返回-1，表示让后端决定
    return mapData3[KEY_SERVER_ID]?.toIntOrNull()?: if (getBooleanFlowByKey(KEY_NEW_USER, true)) -1 else 0
}

suspend fun setServerIdToStore3SyncNoEncrypt(serverId: Int) {
    mapData3[KEY_SERVER_ID] = serverId.toString()
    dataStore3.edit { settings ->
        settings[stringPreferencesKey(KEY_SERVER_ID)] = serverId.toString()
    }
}

suspend fun removeKeysSync(keys: List<String>) {
    // 从内存 mapData 中批量移除
    keys.forEach { key ->
        mapData.remove(key)
    }

    // 从 DataStore 中批量同步移除
    withContext(jsonDispatcher) {
        dataStore.edit { settings ->
            keys.forEach { key ->
                settings.remove(stringPreferencesKey(key))
            }
        }
    }
}