package com.moyu.chuanqirensheng.sub.saver

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.auto_sync_tips

object CloudSaverManager {
    fun init() {
        AppWrapper.globalScope.launch(Dispatchers.IO) {
            async {
                delay(15 * 60 * 1000)
                CloudSaverDefault.uploadCurrentSave().apply {
                    AppWrapper.getStringKmp(Res.string.auto_sync_tips).toast()
                }
                EndingManager.uploadRank()
                delay(5 * 60 * 1000)
            }
        }
    }

    fun checkIfNewUserHaveCloudSave() {
        AppWrapper.globalScope.launch(Dispatchers.IO) {
            delay(3000)
            CloudSaverDefault.getCloudSave()?.let {
                Dialogs.useSaveDialog.value = it
            }
        }
    }
}