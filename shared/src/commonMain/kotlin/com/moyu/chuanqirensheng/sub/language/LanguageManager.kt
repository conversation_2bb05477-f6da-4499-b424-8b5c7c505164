package com.moyu.chuanqirensheng.sub.language

import androidx.compose.foundation.layout.FlowRow
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.platform.getLocalLanguage
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.setLocaleLanguage
import com.moyu.chuanqirensheng.sub.datastore.KEY_INIT_LANGUAGE
import com.moyu.chuanqirensheng.sub.datastore.KEY_LANGUAGE
import com.moyu.chuanqirensheng.sub.datastore.dataStore
import com.moyu.chuanqirensheng.sub.datastore.dataStore2
import com.moyu.chuanqirensheng.sub.datastore.dataStore3
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.mapData
import com.moyu.chuanqirensheng.sub.datastore.mapData2
import com.moyu.chuanqirensheng.sub.datastore.mapData3
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.util.triggerRebirth
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AD_UNIT_ID_T1
import com.moyu.core.AppWrapper
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.language_switch_tips
import shared.generated.resources.language_title

fun Map<String, String>.mapToCurrentLanguage(): String {
    return if (LanguageManager.selectedLanguage.value == "zh") {
        this["zh"] ?: this["zh"] ?: ""
    } else if (LanguageManager.selectedLanguage.value.startsWith("zh")) {
        this["zh-rTW"] ?: this["zh"] ?: ""
    } else {
        this[LanguageManager.selectedLanguage.value] ?: this["zh"] ?: ""
    }
}

object LanguageManager {
    val languages = arrayOf("English", "中文", "日本語", "한국어")
    val languageCodes = arrayOf("en", "zh-rTW", "ja", "ko")

    val minLineCodes = listOf("zh-rTW")
    val selectedLanguage = mutableStateOf("")

    suspend fun init() {
        // TODO 存档初始化，应该独立一个地方做，不要放这里
        mapData.putAll(dataStore.data.first().asMap().map { it.key.name to it.value.toString() })
        mapData2.putAll(dataStore2.data.first().asMap().map { it.key.name to it.value.toString() })
        mapData3.putAll(dataStore3.data.first().asMap().map { it.key.name to it.value.toString() })

        if (hasGoogleService()) {
            if (getStringFlowByKey(KEY_LANGUAGE).isEmpty() || getStringFlowByKey(KEY_LANGUAGE) !in languageCodes) {
                val language = getLocalLanguage()
                languageCodes.forEachIndexed { index, code ->
                    if (language.startsWith(code)) {
                        selectedLanguage.value = languageCodes[index]
                    } else if (language.startsWith("zh")) {
                        // 多种中文都用繁体
                        selectedLanguage.value = languageCodes[1]
                    }
                }
                if (selectedLanguage.value.isEmpty()) {
                    selectedLanguage.value = languageCodes[0]
                }
                setStringValueByKey(KEY_LANGUAGE, selectedLanguage.value)
            } else {
                selectedLanguage.value = getStringFlowByKey(KEY_LANGUAGE)
            }
            setLocaleLanguage(selectedLanguage.value)
        } else {
            selectedLanguage.value = languageCodes[1]
            setLocaleLanguage(selectedLanguage.value)
        }
        if (getStringFlowByKey(KEY_INIT_LANGUAGE).isEmpty()) {
            // 初始语言记录，用于区分中文区还是英文区用户，对于抽卡有做两个模式
            setStringValueByKey(KEY_INIT_LANGUAGE, selectedLanguage.value)
        }
    }

    @Composable
    fun LanguageSelectorView() {
        StrokedText(
            text = stringResource(Res.string.language_title),
            style = MaterialTheme.typography.h1
        )
        FlowRow {
            languages.forEachIndexed { index, language ->
                val selected = languageCodes[index] == selectedLanguage.value
                GameButton(
                    text = language,
                    buttonSize = ButtonSize.MediumMinus,
                    buttonStyle = if (selected) ButtonStyle.Blue else ButtonStyle.Green
                ) {
                    if (selectedLanguage.value != languageCodes[index]) {
                        Dialogs.alertDialog.value = CommonAlert(
                            title = AppWrapper.getStringKmp(Res.string.language_title),
                            content = AppWrapper.getStringKmp(Res.string.language_switch_tips),
                            onConfirm = {
                                selectedLanguage.value = languageCodes[index]
                                setStringValueByKey(KEY_LANGUAGE, selectedLanguage.value)
                                updateLocale(languageCodes[index])
                            }
                        )
                    }
                }
            }
        }
    }


    fun updateLocale(languageCode: String) {
        setLocaleLanguage(languageCode)
        AppWrapper.globalScope.launch {
            delay(1000)
            triggerRebirth()
        }
    }

    fun getLocalizedAssetFileName(fileName: String): String {
        val language = selectedLanguage.value
        languageCodes.forEach {
            if (language == it) {
                if (language == "zh-rTW") {
                    // 默认
                    return fileName
                }
                return it + "_" + fileName
            }
        }
        return fileName
    }

    fun getCountryAdId(): String {
        return AD_UNIT_ID_T1
    }

    fun getTextLines(): Int {
        return if (selectedLanguage.value in minLineCodes) {
            1
        } else {
            2
        }
    }

    fun getSoftWrap(): Boolean {
        return selectedLanguage.value !in minLineCodes
    }
}