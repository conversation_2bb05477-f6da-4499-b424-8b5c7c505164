package com.moyu.chuanqirensheng.sub.saver

import com.moyu.chuanqirensheng.api.CommonApiData
import kotlinx.serialization.Serializable

@Serializable
data class GameSaver(
    val userId: String = "",
    val userName: String = "",
    val timestamp: Long = 0L,
    val save: String = "",
    val version: Int = 0,
    val lastUseTimestamp: Long = 0L,
    val usedCount: Int = 0,
    val footPrint: String = "",
    val valid: Boolean = true,
    val message: String = "",
    override val randomInt: Int,
    val serverId: Int = 0, // 服务器ID，新增
): CommonApiData