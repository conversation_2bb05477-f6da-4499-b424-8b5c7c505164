package com.moyu.chuanqirensheng.sub.antiaddict

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PERMISSION
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.privacy.PrivacyManager
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText

// todo translate
@Composable
fun AntiAddictDialog(confirm: () -> Unit = {}) {
    val privacyNeedShow = getBooleanFlowByKey(KEY_NEED_SHOW_PRIVACY, true)
    val permissionNeedShow = getBooleanFlowByKey(KEY_NEED_SHOW_PERMISSION, true)
    if (!privacyNeedShow && !permissionNeedShow) {
        if (!gameSdkDefaultProcessor().antiAddictPassed().value && !PrivacyManager.antiAddictVerified) {
            PanelDialog(
                onDismissRequest = { },
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxSize()
                ) {
                    StrokedText(
                        text = "防沉迷认证中，请等待",
                        style = MaterialTheme.typography.h1,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.size(padding30))
                    StrokedText(
                        text = "根据国家法律法规要求，必须完成实名认证后，才可以进入游戏。请点击按钮认证：",
                        style = MaterialTheme.typography.h3,
                        color = Color.White
                    )
                }
            }
        }
    }
}