package com.moyu.chuanqirensheng.sub.anticheat

import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.sub.datastore.KEY_CHEATING
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.core.AppWrapper
import com.moyu.core.model.Ally
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Quest
import com.moyu.core.model.Sell
import com.moyu.core.model.Talent
import com.moyu.core.model.Vip
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

object AntiCheatManager {
    fun init() {
        AppWrapper.globalScope.launch(Dispatchers.IO) {
            async {
                while (true) {
                    delay(30 * 1000)
                    checkPoolCheck()
                }
            }
        }
    }

    suspend fun isCheating(): Boolean {
        val isCheat = VipManager.isCheat()
        setBooleanValueByKey(KEY_CHEATING, isCheat)
        if (isCheat) {
            if (isLite()) {
                "电力作弊".toast()
                delay(2000)
                return false
            } else {
                return true
            }
        }
        return false
    }

    fun isPostCheating(): Boolean {
        return false
    }

    fun checkPoolCheck() {
//        ConfigManager.configLoaders.filter { it.getKey() in antiConfigs }.forEach {
//            val list2 = it.reLoadConfig()
//            val list1 = repo.gameCore.getPoolByKeyAny(it.getKey())
//            list1.forEachIndexed { index, configData ->
//                if (configData != list2[index]) {
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${gameSdkDefaultProcessor().getObjectId()}")
//                }
//            }
//        }
    }

    fun checkSell(sell: Sell) {
//        ConfigManager.configLoaders.first { it.getKey() == SELL_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Sell).id == sell.id }.let {
//                if (it != sell.copy(storage = (it as Sell).storage, award = null)) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${gameSdkDefaultProcessor().getObjectId()}")
//                }
//            }
//        }
    }

    fun checkTalent(talent: Talent) {
//        ConfigManager.configLoaders.first { it.getKey() == TALENT_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Talent).id == talent.id }.let {
//                if (it != talent) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${gameSdkDefaultProcessor().getObjectId()}")
//                }
//            }
//        }
    }

    fun checkQuest(task: Quest) {
//        ConfigManager.configLoaders.first { it.getKey() == TASK_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Quest).id == task.id }.let {
//                if (it != task.copy(opened = false, done = false, needRemoveCount = 0)) {
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${gameSdkDefaultProcessor().getObjectId()}")
//                }
//            }
//        }
    }

    fun checkAlly(ally: Ally) {
//        ConfigManager.configLoaders.first { it.getKey() == ALLY_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Ally).id == ally.id }.let {
//                val configAlly = it as Ally
//                if (configAlly.star != ally.star) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${gameSdkDefaultProcessor().getObjectId()}")
//                }
//            }
//        }
    }

    fun checkVip(vip: Vip) {
//        ConfigManager.configLoaders.first { it.getKey() == VIP_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Vip).id == vip.id }.let {
//                val configAlly = it as Vip
//                if (configAlly.level != vip.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${gameSdkDefaultProcessor().getObjectId()}")
//                }
//            }
//        }
    }

    fun checkWarPass(battlePass: BattlePass) {
//        ConfigManager.configLoaders.first { it.getKey() == WAR_PASS_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as BattlePass).id == battlePass.id }.let {
//                val configAlly = it as BattlePass
//                if (configAlly.level != battlePass.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${gameSdkDefaultProcessor().getObjectId()}")
//                }
//            }
//        }
    }

    fun checkWarPass2(battlePass: BattlePass) {
//        ConfigManager.configLoaders.first { it.getKey() == WAR_PASS_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as BattlePass).id == battlePass.id }.let {
//                val configAlly = it as BattlePass
//                if (configAlly.level != battlePass.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${gameSdkDefaultProcessor().getObjectId()}")
//                }
//            }
//        }
    }

    fun checkWarPass3(battlePass: BattlePass) {
//        ConfigManager.configLoaders.first { it.getKey() == WAR_PASS2_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as BattlePass).id == battlePass.id }.let {
//                val configAlly = it as BattlePass
//                if (configAlly.level != battlePass.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${gameSdkDefaultProcessor().getObjectId()}")
//                }
//            }
//        }
    }

    fun checkWarPass4(battlePass: BattlePass) {
//        ConfigManager.configLoaders.first { it.getKey() == WAR_PASS2_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as BattlePass).id == battlePass.id }.let {
//                val configAlly = it as BattlePass
//                if (configAlly.level != battlePass.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${gameSdkDefaultProcessor().getObjectId()}")
//                }
//            }
//        }
    }
}