package com.moyu.chuanqirensheng.sub.loginsdk.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.eygraber.uri.Uri
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.platform.openGamePage
import com.moyu.chuanqirensheng.sub.datastore.KEY_LOGIN_MESSAGE_ID
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.util.kmpStringResource
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import shared.generated.resources.Res
import shared.generated.resources.confirm
import shared.generated.resources.login_fail_tips
import shared.generated.resources.please_shut_down

@Composable
fun LoginResultDialog() {
    val savedMessageId = getIntFlowByKey(KEY_LOGIN_MESSAGE_ID, 0)
    var show by remember {
        mutableStateOf(false)
    }

    val loginData by LoginManager.instance.loginData
    LaunchedEffect(loginData) {
        show =
            !LoginManager.instance.loginData.value.verified
                    || (!LoginManager.instance.newUser && LoginManager.instance.loginData.value.showDialog
                    && savedMessageId < LoginManager.instance.loginData.value.messageId)
    }

    if (show) {
        PanelDialog(
            onDismissRequest = {
                if (LoginManager.instance.loginData.value.verified) {
                    LoginManager.instance.loginData.value =
                        LoginManager.instance.loginData.value.copy(showDialog = false)
                    setIntValueByKey(
                        KEY_LOGIN_MESSAGE_ID,
                        LoginManager.instance.loginData.value.messageId
                    )
                } else {
                    AppWrapper.getStringKmp(Res.string.please_shut_down).toast()
                }
            },
            contentBelow = {
                val buttonText = LoginManager.instance.loginData.value.buttonText.ifEmpty {
                    kmpStringResource(Res.string.confirm)
                }
                GameButton(text = buttonText, buttonStyle = ButtonStyle.Blue) {
                    if (LoginManager.instance.loginData.value.buttonLink.isNotEmpty()) {
                        val uri: Uri = Uri.parse(LoginManager.instance.loginData.value.buttonLink)

                        openGamePage(uri)
                    }
                    if (LoginManager.instance.loginData.value.verified) {
                        LoginManager.instance.loginData.value =
                            LoginManager.instance.loginData.value.copy(showDialog = false)
                        setIntValueByKey(
                            KEY_LOGIN_MESSAGE_ID,
                            LoginManager.instance.loginData.value.messageId
                        )
                    } else {
                        AppWrapper.getStringKmp(Res.string.login_fail_tips).toast()
                    }
                }
            }
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding26)
            ) {
                Spacer(modifier = Modifier.size(padding40))
                StrokedText(
                    text = LoginManager.instance.loginData.value.dialogText,
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
            }
        }
    }
}