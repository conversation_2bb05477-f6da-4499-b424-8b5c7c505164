package com.moyu.chuanqirensheng.sub.saver.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.sub.saver.CloudSaverDefault
import com.moyu.chuanqirensheng.sub.saver.GameSaver
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.util.kmpStringResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.coroutines.delay
import shared.generated.resources.Res
import shared.generated.resources.confirm
import shared.generated.resources.have_save_content
import shared.generated.resources.have_save_tips

@Composable
fun UseCloudSaverDialog(cloudSaverDialog: MutableState<GameSaver?>) {
    cloudSaverDialog.value?.let { cloudSave ->
        LaunchedEffect(Unit) {
            if (isLite()) {
                delay(5000)
            } else {
                delay(1000)
            }
            CloudSaverDefault.useThisCloudSave(cloudSave)
            cloudSaverDialog.value = null
        }
        PanelDialog(
            onDismissRequest = {
                if (isLite()) {
                    cloudSaverDialog.value = null
                }
            },
            showClose = false,
            contentBelow = {
                GameButton(
                    text = kmpStringResource(Res.string.confirm),
                    buttonSize = ButtonSize.Big,
                    onClick = {

                    })
            }
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .padding(horizontal = padding26)
                    .padding(bottom = padding48)
            ) {
                Spacer(modifier = Modifier.size(padding40))
                StrokedText(
                    text = kmpStringResource(Res.string.have_save_tips),
                    style = MaterialTheme.typography.h1,
                )
                Spacer(modifier = Modifier.size(padding30))
                StrokedText(
                    text = kmpStringResource(Res.string.have_save_content),
                    style = MaterialTheme.typography.h2,
                )
            }
        }
    }
}