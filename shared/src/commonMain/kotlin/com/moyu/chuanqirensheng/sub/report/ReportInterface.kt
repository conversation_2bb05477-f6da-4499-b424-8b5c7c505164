package com.moyu.chuanqirensheng.sub.report

const val AF_AD_ID = "af_ad_id"
const val AF_NEW_USER = "af_new_user"
const val AF_SECOND_PURCHASE = "af_second_purchase"

const val KEY_PURCHASE = "purchase_in_shop"
const val KEY_START_GAME = "start_game"
const val KEY_CONTINUE_GAME = "continue_game"
const val KEY_PK_GAME = "pk_game"
const val KEY_BATTLE_GAME = "battle_in_game"
const val KEY_REVIEW_GAME = "review_game"
const val KEY_PRELOAD_AD = "preload_ad"
const val KEY_LOAD_AD = "load_ad"
const val KEY_PAGE = "page"
const val KEY_GUIDE = "my_guide"
const val KEY_GIFT_SHOW = "my_gift_show"
const val KEY_PAY_BOUGHT = "my_pay_bought"
const val KEY_KEY_BOUGHT = "my_key_bought"
const val KEY_ITEM_UNLOCK = "my_item_unlock"
const val KEY_MONTH_CARD_UNLOCK = "my_month_card_unlock"


const val PARAM_GAME_MODE = "param_game_mode"
const val PARAM_PURCHASE_TYPE = "param_purchase_type"
const val PARAM_BATTLE_RESULT = "param_battle_result"
const val PARAM_PVP_SCORE = "param_pvp_score"
const val PARAM_STAGE = "param_stage"
const val PARAM_STAR = "param_star"
const val PARAM_KEY = "param_key"
const val PARAM_DIAMOND = "param_diamond"
const val PARAM_ELECTRIC = "param_electric"
const val PARAM_TALENT = "param_talent_sum"
const val PARAM_PRELOAD_TYPE = "param_preload_type" // 0 进入Ad页面 1 观看广告
const val PARAM_VERSION = "param_version"
const val PARAM_USERID = "param_user_id"
const val PARAM_USERNAME = "param_user_name"
const val PARAM_ID = "param_id"


interface ReportInterface {
    fun setup()
    fun onLogin()
    fun onAdCompletedAF(adId: String)
    fun onPurchaseCompletedAF(purchaseId: String, amount: Double, number: Int)
    fun onShopPurchase(sellId: Int, price: Int, priceType: Int)
    fun onNewGame(mode: Int)
    fun onContinueGame(mode: Int)
    fun pk(win: Int, score: Int)
    fun battle(win: Int, mode: Int, stage: Int)
    fun onLoadAd()
    fun onPage(route: String)
    fun onPurchaseCompletedAdjust(dollarPrice: Double, orderId: String)
    fun guide(index: Int)
    fun giftShow(id: Int)
    fun itemMoneyBought(id: Int)
    fun itemKeyBought(id: Int)
    fun unlockItem(id: Int)
    fun unlockMonthCard()
}