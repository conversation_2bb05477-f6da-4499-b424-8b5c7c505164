package com.moyu.chuanqirensheng.sub.saver

import com.moyu.chuanqirensheng.api.CommonResult
import com.moyu.chuanqirensheng.api.doEncodedApi
import com.moyu.chuanqirensheng.api.doEncodedApiNoRandomCheck
import com.moyu.chuanqirensheng.api.getGameSave
import com.moyu.chuanqirensheng.api.postSave
import com.moyu.chuanqirensheng.api.tryUseGameSave
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.getSystemFilesPath
import com.moyu.chuanqirensheng.platform.p_fileRename
import com.moyu.chuanqirensheng.sub.config.compressZip4j
import com.moyu.chuanqirensheng.sub.config.openText
import com.moyu.chuanqirensheng.sub.config.saveText
import com.moyu.chuanqirensheng.sub.config.uncompressZip4j
import com.moyu.chuanqirensheng.sub.datastore.clearDataStore1
import com.moyu.chuanqirensheng.sub.datastore.clearDataStore2
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.sub.loginsdk.LoginUser
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getPreferencesPath
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.chuanqirensheng.util.getVersions
import com.moyu.chuanqirensheng.util.triggerRebirth
import com.moyu.core.AppWrapper
import com.moyu.core.DS_NAME
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import shared.generated.resources.Res
import shared.generated.resources.error_operate
import shared.generated.resources.error_sync2
import shared.generated.resources.error_sync3
import shared.generated.resources.id_error
import shared.generated.resources.no_save
import shared.generated.resources.pack_error
import shared.generated.resources.switch_server_ok_tips
import shared.generated.resources.sync_error
import shared.generated.resources.sync_tips
import shared.generated.resources.too_many_use_save_tips
import shared.generated.resources.upload_exception
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi

private val savePath = getSaverPath()

private fun getSaverPath(): String {
    return getPreferencesPath(DS_NAME)
}

private fun fileSeparator(): String {
    return "/"
}

object CloudSaverDefault {

    var switching = false

    suspend fun switchServerId(serverId: Int) {
        if (switching) return
        val backUpServerId = LoginManager.instance.getSavedServerId()
        switching = true
        if (uploadCurrentSave().succeeded) {
            LoginManager.instance.setSavedServerId(serverId)
            getCloudSave(mute = true)?.let {
                // 这里是切到的目标服务器有存档，使用这个存档
                clearDataStore2()
                AppWrapper.getStringKmp(Res.string.switch_server_ok_tips).toast()
                useThisCloudSave(it, mute = true)
            }?: run {
                // 这里是切到的目标服务器没有存档，新号重启
                clearDataStore1()
                clearDataStore2()
                AppWrapper.getStringKmp(Res.string.switch_server_ok_tips).toast()
                delay(1000)
                triggerRebirth()
            }
        } else {
            AppWrapper.getStringKmp(Res.string.error_sync3).toast()
        }
        // 走到这里就是切换失败了，回滚服务器id
        LoginManager.instance.setSavedServerId(backUpServerId)
        switching = false
    }

    suspend fun uploadCurrentSave(): CommonResult {
        gameSdkDefaultProcessor().getObjectId()?.let { objectId ->
            getCurrentSave()?.let { save ->
                val gameSaveData = GameSaver(
                    userId = objectId,
                    timestamp = getCurrentTime(),
                    userName = gameSdkDefaultProcessor().getUserName() ?: "none",
                    footPrint = getFootPrint(),
                    version = getVersionCode(),
                    save = save,
                    randomInt = RANDOM.nextInt(),
                    serverId = LoginManager.instance.getSavedServerId()
                )
                return doEncodedApiNoRandomCheck(gameSaveData, GameSaver.serializer()) {
                    postSave(it)
                } ?: CommonResult(false, AppWrapper.getStringKmp(Res.string.sync_error))
            } ?: AppWrapper.getStringKmp(Res.string.pack_error).toast()
        }
        return CommonResult(false, AppWrapper.getStringKmp(Res.string.error_sync2))
    }

    suspend fun getCloudSave(mute: Boolean = false): GameSaver? {
        return try {
            doEncodedApi(
                LoginManager.instance.getLoginUser(),
                LoginUser.serializer(),
                GameSaver.serializer(),
                mute = mute
            ) {
                getGameSave(it)
            }?.let { result ->
                if (result.valid) {
                    result
                } else {
                    result.message.toast()
                    null
                }
            }
        } catch (e: Exception) {
//            Timber.e(e)
            if (!mute) {
                AppWrapper.getStringKmp(Res.string.no_save).toast()
            }
            null
        }
    }

    @OptIn(ExperimentalEncodingApi::class)
    suspend fun useThisCloudSave(save: GameSaver, mute: Boolean = false) {
        withContext(Dispatchers.IO) {
            try {
                doEncodedApiNoRandomCheck(LoginManager.instance.getLoginUser(), LoginUser.serializer()) {
                    tryUseGameSave(it)
                }?.let {
                    val filePath =
                        getSystemFilesPath() + fileSeparator() + "tmp.save"
                    saveText(filePath, Base64.UrlSafe.decode(save.save))
                    val uncompressTargetFile =
                        getSystemFilesPath() + fileSeparator() + "uncompressed.save"
                    gameSdkDefaultProcessor().getObjectId()?.let {
                        uncompressZip4j(
                            filePath,
                            uncompressTargetFile,
                            it + getVersions()
                        )
                        p_fileRename(uncompressTargetFile + fileSeparator() + DS_NAME + ".preferences_pb", savePath)
                    } ?: AppWrapper.getStringKmp(Res.string.id_error).toast()
                    if (!mute) {
                        AppWrapper.getStringKmp(Res.string.sync_tips).toast()
                    }
                    // todo 隔离了未完成的游戏，所以任意时候读档，需要删除未完成的游戏
                    ContinueManager.clearSave()

                    delay(2000)
                    triggerRebirth()
                }?:  AppWrapper.getStringKmp(Res.string.too_many_use_save_tips).toast()
            } catch (e: Exception) {
//                Timber.e(e)
                (AppWrapper.getStringKmp(Res.string.error_operate) + e.message).toast()
            }
        }
    }
}

@OptIn(ExperimentalEncodingApi::class)
suspend fun getCurrentSave(): String? {
    return withContext(Dispatchers.IO) {
        try {
            val zipFilePath =
                getSystemFilesPath() + fileSeparator() + "compressed.zip"
            gameSdkDefaultProcessor().getObjectId()?.let { userId ->
                compressZip4j(zipFilePath, savePath, userId + getVersions())
            } ?: AppWrapper.getStringKmp(Res.string.upload_exception).toast()
            Base64.UrlSafe.encode(openText(zipFilePath)!!)
        } catch (e: Exception) {
            val zipFilePath =
                getSystemFilesPath() + fileSeparator() + UUID.generateUUID()
                    .toString().take(16) + ".zip"
            gameSdkDefaultProcessor().getObjectId()?.let { userId ->
                compressZip4j(zipFilePath, savePath, userId + getVersions())
            } ?: AppWrapper.getStringKmp(Res.string.upload_exception).toast()
            Base64.UrlSafe.encode(openText(zipFilePath)!!)
        }
    }
}