package com.moyu.chuanqirensheng.sub.config

import com.moyu.chuanqirensheng.sub.config.ConfigManager.configLoaders

//fun copyFileFromStream(copyTarget: File, inStream: InputStream): String {
//    if (copyTarget.exists()) {
//        copyTarget.delete()
//    }
//    try {
//        val fileOutputStream = FileOutputStream(copyTarget)
//        var byteread: Int
//        val buffer = ByteArray(1024)
//        while (inStream.read(buffer).also { byteread = it } != -1) {
//            fileOutputStream.write(buffer, 0, byteread)
//        }
//        fileOutputStream.flush()
//        inStream.close()
//        fileOutputStream.close()
//    } catch (e: IOException) {
//        e.printStackTrace()
//    }
//    return copyTarget.absolutePath
//}

fun compressZip4j(zipFilePath: String, compressFilePath: String, password: String) {
    com.moyu.chuanqirensheng.platform.compressZip4j(zipFilePath, compressFilePath, password)
}


//fun compressConfigs(zipFilePath: String, password: String) {
//    com.moyu.chuanqirensheng.platform.compressConfigs(zipFilePath, password)
//}

fun uncompressZip4j(zipFilePath: String, filePath: String, password: String) {
    com.moyu.chuanqirensheng.platform.uncompressZip4j(zipFilePath, filePath, password)
}

//读取文本文件
fun openText(path: String?): ByteArray? {
    return com.moyu.chuanqirensheng.platform.openText(path)
}

//保存文本文件
fun saveText(path: String, txt: ByteArray) {
    com.moyu.chuanqirensheng.platform.saveText(path, txt)
}
