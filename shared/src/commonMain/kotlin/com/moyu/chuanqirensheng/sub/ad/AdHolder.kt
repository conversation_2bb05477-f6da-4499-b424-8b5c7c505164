package com.moyu.chuanqirensheng.sub.ad

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.quest.onTaskWatchAd
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.platform.p_getAdImp
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.greater_than_10_seconds

val isWatching = mutableStateOf(false)

const val KEY_BUY_AD_ITEM = "KEY_BUY_AD_ITEM"

object AdHolder : AdInterface {
    var playCount = 0
    var adInit = false

    override fun playAd(adId: String, callback: () -> Unit) {
        if (isWatching.value) {
            AppWrapper.getStringKmp(Res.string.greater_than_10_seconds).toast()
            return
        }
        isWatching.value = true
        AppWrapper.globalScope.launch(Dispatchers.Main) {
            if (VipManager.isSkipAd()) {
                AwardManager.adNum.value += 1
                onTaskWatchAd()
                callback()
                isWatching.value = false
            } else {
                realShowAd(adId, callback)
                delay(10000)
                isWatching.value = false
            }
        }
    }

    private fun realShowAd(reportKey: String, callback: () -> Unit) {
        p_getAdImp().playAd(reportKey, callback)
        playCount += 1
    }
}