package com.moyu.chuanqirensheng.sub.config

import com.moyu.core.config.ConfigParser
import fileLoadConfigFile
import kotlin.random.Random

class ConfigLoader<T>(
    private val fileName: String,
    private val configParser: ConfigParser<T>,
    private val needShuffle: Boolean = false,
) {
    private val pool: MutableList<T> = ArrayList()
    suspend fun loadConfig() {
        if (pool.isEmpty()) {
            val temp = ArrayList<T>()

            fileLoadConfigFile(fileName) { _, line ->
                if (line.trim().isEmpty()) return@fileLoadConfigFile
                temp.add(configParser.parse(line))
            }
            if (needShuffle) {
                pool.addAll(temp.shuffled(Random(fileName.hashCode())))
            } else {
                pool.addAll(temp)
            }
        }
    }

    suspend fun reLoadConfig(): List<T> {
        val temp = ArrayList<T>()
        fileLoadConfigFile(fileName) { _, line ->
            if (line.trim().isEmpty()) return@fileLoadConfigFile
            temp.add(configParser.parse(line))
        }
        return if (needShuffle) {
            temp.shuffled(Random(fileName.hashCode()))
        } else {
            temp
        }
    }

    fun getConfig(): List<T> {
        return pool
    }

    fun getKey(): String {
        return fileName
    }
}