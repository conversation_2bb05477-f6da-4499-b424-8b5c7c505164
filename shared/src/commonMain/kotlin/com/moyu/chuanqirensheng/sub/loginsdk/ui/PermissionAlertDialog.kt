package com.moyu.chuanqirensheng.sub.loginsdk.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.needPrivacyCheck
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.kmpStringResource
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import shared.generated.resources.Res
import shared.generated.resources.app_list_permission
import shared.generated.resources.associated_boot
import shared.generated.resources.click_ok_confirm
import shared.generated.resources.confirm
import shared.generated.resources.location_permission
import shared.generated.resources.permission_content
import shared.generated.resources.permission_tips
import shared.generated.resources.read_write_permission


@Composable
fun PermissionAlertDialog(
    switch: MutableState<Boolean>,
    confirm: () -> Unit,
) {
    if (switch.value) {
        LaunchedEffect(Unit) {
            // todo 不需要权限弹窗，则直接回调confirm
            if (!needPrivacyCheck()) {
                switch.value = false
                confirm()
            }
        }
        val clickedOk = remember {
            mutableStateOf(false)
        }
        PanelDialog(
            onDismissRequest = {
                if (clickedOk.value) {
                    confirm()
                    switch.value = false
                } else {
                    AppWrapper.getStringKmp(Res.string.click_ok_confirm).toast()
                }
            },
            showClose = false,
            contentBelow = {
                GameButton(
                    text = kmpStringResource(Res.string.confirm),
                    buttonStyle = ButtonStyle.Blue
                ) {
                    clickedOk.value = true
                    switch.value = false
                    confirm()
                }
            }
        ) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                StrokedText(
                    text = kmpStringResource(Res.string.permission_content),
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Column(
                    horizontalAlignment = Alignment.Start,
                ) {
                    Spacer(modifier = Modifier.size(padding36))
                    StrokedText(
                        text = kmpStringResource(Res.string.permission_tips),
                        style = MaterialTheme.typography.h3,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.size(padding10))
                    if (!hasGoogleService()) {
                        StrokedText(
                            text = kmpStringResource(Res.string.associated_boot),
                            style = MaterialTheme.typography.h4,
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.size(padding4))
                    }
                    StrokedText(
                        text = kmpStringResource(Res.string.read_write_permission),
                        style = MaterialTheme.typography.h4,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    StrokedText(
                        text = kmpStringResource(Res.string.location_permission),
                        style = MaterialTheme.typography.h4,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    if (!hasGoogleService()) {
                        StrokedText(
                            text = kmpStringResource(Res.string.app_list_permission),
                            style = MaterialTheme.typography.h4,
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}