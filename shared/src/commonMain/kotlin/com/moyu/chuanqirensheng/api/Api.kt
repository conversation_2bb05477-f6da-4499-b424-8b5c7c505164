package com.moyu.chuanqirensheng.api


import com.moyu.chuanqirensheng.api.RetrofitManager.httpClient
import com.moyu.chuanqirensheng.feature.feedback.FeedbackReq
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.RankWrapper
import com.moyu.chuanqirensheng.platform.serverUrl
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.chuanqirensheng.util.AdUtil.decodeText
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.serialization.json.Json

val serverUrl = serverUrl()

suspend fun uploadLikedData(data: String): CommonResult {
    return httpClient.post(serverUrl + "upload_liked_v3") {
        contentType(ContentType.Application.Json)
        setBody(data)
    }.body()
}

suspend fun getLoginData(data: String): CommonResult {
    return httpClient.get(serverUrl + "login_user_v3") {
        url {
            parameters.append("data", data)
        }
    }.body()
}

suspend fun getShareData(data: String): CommonResult {
    return httpClient.get(serverUrl + "share_info_v3") {
        url {
            parameters.append("data", data)
        }
    }.body()
}

suspend fun getPvpByScore(platform: String, pvpScore: Int): CommonResult {
    return httpClient.get(serverUrl + "rank_data_pvp_v3") {
        url {
            parameters.append("platform", platform)
            parameters.append("pvpScore", pvpScore.toString())
            parameters.append("serverId", LoginManager.instance.getSavedServerId().toString())
        }
    }.body()
}

suspend fun getPvp2ByScore(platform: String, pvpScore: Int): CommonResult {
    return httpClient.get(serverUrl + "rank_data_pvp2_v3") {
        url {
            parameters.append("platform", platform)
            parameters.append("pvpScore", pvpScore.toString())
            parameters.append("serverId", LoginManager.instance.getSavedServerId().toString())
        }
    }.body()
}

suspend fun getRanks(platform: String, type: Int): CommonResult {
    return httpClient.get(serverUrl + "rank_data_v3") {
        url {
            parameters.append("platform", platform)
            parameters.append("type", type.toString())
            parameters.append("serverId", LoginManager.instance.getSavedServerId().toString())
        }
    }.body()
}

suspend fun postRankData(rankData: RankData) {
    val rankString = Json.encodeToString(RankData.serializer(), rankData)
    val encoded = AdUtil.encodeText(rankString) ?: ""

    return httpClient.post(serverUrl + "rank_data_v4") {
        contentType(ContentType.Application.Json)
        setBody(RankWrapper(data = encoded))
    }.body()
}

suspend fun postPvpRankData(rankData: RankData) {
    val rankString = Json.encodeToString(RankData.serializer(), rankData)
    val encoded = AdUtil.encodeText(rankString) ?: ""

    return httpClient.post(serverUrl + "rank_pvp_data_v4") {
        contentType(ContentType.Application.Json)
        setBody(RankWrapper(data = encoded))
    }.body()
}

suspend fun postPvp2RankData(rankData: RankData) {
    val rankString = Json.encodeToString(RankData.serializer(), rankData)
    val encoded = AdUtil.encodeText(rankString) ?: ""

    return httpClient.post(serverUrl + "rank_pvp2_data_v4") {
        contentType(ContentType.Application.Json)
        setBody(RankWrapper(data = encoded))
    }.body()
}

suspend fun postTowerRankData(rankData: RankData) {
    val rankString = Json.encodeToString(RankData.serializer(), rankData)
    val encoded = AdUtil.encodeText(rankString) ?: ""

    return httpClient.post(serverUrl + "rank_tower_data_v4") {
        contentType(ContentType.Application.Json)
        setBody(RankWrapper(data = encoded))
    }.body()
}

suspend fun getGameSave(data: String): CommonResult {
    return httpClient.get(serverUrl + "save_v3") {
        url {
            parameters.append("data", data)
        }
    }.body()
}

suspend fun postSave(data: String): CommonResult {
    return httpClient.post(serverUrl + "save_v3") {
        contentType(ContentType.Application.Json)
        setBody(data)
    }.body()
}

suspend fun tryUseGameSave(data: String): CommonResult {
    return httpClient.get(serverUrl + "try_use_save_v3") {
        url {
            parameters.append("data", data)
        }
    }.body()
}

suspend fun useShareCode(codes: String, data: String, versionCode: Int): String {
    return httpClient.get(serverUrl + "use_share_code_v3") {
        url {
            parameters.append("codes", codes)
            parameters.append("data", data)
            parameters.append("versionCode", versionCode.toString())
        }
    }.body()
}

suspend fun getAwards(codes: String, data: String, versionCode: Int): String {
    return httpClient.get(serverUrl + "awards_v3") {
        url {
            parameters.append("codes", codes)
            parameters.append("data", data)
            parameters.append("versionCode", versionCode.toString())
        }
    }.body()
}

suspend fun postHolidayRankData(rankData: RankData) {
    return httpClient.post(serverUrl + "rank_holiday_data") {
        contentType(ContentType.Application.Json)
        setBody(rankData)
    }.body()
}

suspend fun getHolidayRanks(platform: String): CommonResult {
    return httpClient.get(serverUrl + "holiday_rank") {
        url {
            parameters.append("platform", platform)
            parameters.append("serverId", LoginManager.instance.getSavedServerId().toString())
        }
    }.body()
}

suspend fun getServerRankAward(platform: String, type: Int): CommonResult {
    return httpClient.get(serverUrl + "server_rank_award") {
        url {
            parameters.append("platform", platform)
            parameters.append("type", type.toString())
            parameters.append("serverId", LoginManager.instance.getSavedServerId().toString())
        }
    }.body<CommonResult>().let { result ->
        val resultText = decodeText(result.message)?: ""
        result.copy(message = resultText)
    }
}

suspend fun postFeedBack(feedback: FeedbackReq) {
    return httpClient.post(serverUrl + "add_feedback") {
        contentType(ContentType.Application.Json)
        setBody(feedback)
    }.body()
}

suspend fun getEmails(data: String, serverId: Int): CommonResult {
    return httpClient.get(serverUrl + "query_email") {
        url {
            contentType(ContentType.Application.Json)
            parameters.append("data", data)
            parameters.append("data2", serverId.toString())
        }
    }.body()
}