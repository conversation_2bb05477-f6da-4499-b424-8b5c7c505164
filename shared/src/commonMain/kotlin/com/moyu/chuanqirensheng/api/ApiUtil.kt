package com.moyu.chuanqirensheng.api

import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.util.AESUtil
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.core.AppWrapper
import kotlinx.serialization.KSerializer
import kotlinx.serialization.json.Json
import shared.generated.resources.Res
import shared.generated.resources.network_error

//import timber.log.Timber

interface CommonApiData {
    val randomInt: Int
}

inline fun <reified T : CommonApiData, reified K : CommonApiData> doEncodedApi(
    data: T,
    inputSerializer: KSerializer<T>,
    outputSerializer: KSerializer<K>,
    mute: Boolean = false,
    callApi: (String) -> CommonResult
): K? {
    val result = try {
        val loginString = Json.encodeToString(inputSerializer, data)
        val encoded = AdUtil.encodeText(loginString) ?: ""
        val result = callApi(encoded)
        val loginJson = AESUtil.decrypt(result.message, gameSdkDefaultProcessor().getObjectId() ?: "")
        loginJson?.let {
            json.decodeFromString(outputSerializer, loginJson).takeIf {
                it.randomInt == data.randomInt + 105 // 魔数
            }
        }
    } catch (e: Exception) {
        if (!mute) {
            AppWrapper.getStringKmp(Res.string.network_error).toast()
        }
        null
    }
    return result
}

inline fun <reified T> doEncodedApiNoRandomCheck(
    data: T,
    inputSerializer: KSerializer<T>,
    callApi: (String) -> CommonResult
): CommonResult? {
    try {
        val loginString = Json.encodeToString(inputSerializer, data)
        val encoded = AdUtil.encodeText(loginString) ?: ""
        return callApi(encoded)
    } catch (e: Exception) {
        println("2doEncodedApi error: ${e.message}")
        AppWrapper.getStringKmp(Res.string.network_error).toast()
//        Timber.e(e)
        null
    }
    return null
}