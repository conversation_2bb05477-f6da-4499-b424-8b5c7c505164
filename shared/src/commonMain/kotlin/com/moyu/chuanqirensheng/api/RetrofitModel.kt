package com.moyu.chuanqirensheng.api

import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardFromServer
import com.moyu.chuanqirensheng.feature.rank.LikedData
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.platform.getElapsedTimeMillis
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_USER
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.LoginData
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.sub.loginsdk.LoginUser
import com.moyu.chuanqirensheng.util.AESUtil
import com.moyu.core.AppWrapper
import com.moyu.core.logic.role.ALLY_ROW1_FIRST
import com.moyu.core.model.Award
import com.moyu.core.model.toAward
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import shared.generated.resources.Res
import shared.generated.resources.network_error

object RetrofitModel {
    suspend fun uploadLikedData(likedData: LikedData) {
        doEncodedApiNoRandomCheck(
            data = likedData.copy(serverId = LoginManager.instance.getSavedServerId()),
            inputSerializer = LikedData.serializer(),
        ) {
            uploadLikedData(it)
        }
    }


    suspend fun useShareCode(
        userId: String, codes: String, encryptedUserId: String, versionCode: Int
    ): Award {
        return try {
            val awardString = useShareCode(codes, encryptedUserId, versionCode)
            val codeAwardJson = AESUtil.decrypt(awardString, userId)
            codeAwardJson?.let {
                json.decodeFromString(AwardFromServer.serializer(), it).toAward()
            } ?: Award(resultCode = 99)
        } catch (e: Exception) {
            AppWrapper.getStringKmp(Res.string.network_error).toast()
            Award(resultCode = 99)
        }
    }

    suspend fun getCodeAwards(
        userId: String, codes: String, encodedUserId: String, versionCode: Int
    ): Award {
        return try {
            val awardString = getAwards(codes, encodedUserId, versionCode)
            val codeAwardJson = AESUtil.decrypt(awardString, userId)
            codeAwardJson?.let {
                json.decodeFromString(AwardFromServer.serializer(), it).toAward().apply {
                    // 保存解锁信息
                    unlockList.map { id ->
                        UnlockManager.unlockCode(id)
                    }
                }
            } ?: Award(resultCode = 99)
        } catch (e: Exception) {
            AppWrapper.getStringKmp(Res.string.network_error).toast()
            Award(resultCode = 99)
        }
    }

    suspend fun getLoginData() {
        val result = doEncodedApi(
            data = LoginManager.instance.getLoginUser(),
            inputSerializer = LoginUser.serializer(),
            outputSerializer = LoginData.serializer()
        ) {
            getLoginData(it)
        } ?: LoginData(0, verified = false, showDialog = true)


        AppWrapper.lastNetWorkTime.longValue = result.time
        AppWrapper.elapsedDiffTime = getElapsedTimeMillis() - AppWrapper.lastNetWorkTime.longValue
        LoginManager.instance.loginData.value = result
        LoginManager.instance.setSavedServerId(result.serverData.serverId)
        reportManager().onLogin()

        if (getBooleanFlowByKey(KEY_NEW_USER, true)) {
            // 首次赠送物品放这里
            val award = repo.gameCore.getPoolById(repo.gameCore.getFirstAwardPoolId()).toAward()
            award.outAllies.forEachIndexed { index, ally ->
                repo.allyManager.gain(
                    ally.copy(
                        new = true, selected = true, battlePosition = ALLY_ROW1_FIRST + index, uuid = UUID.generateUUID().toString()
                    )
                )
            }
            // 首次进入游戏，直接开一局
            repo.startStageMode()
        }
        setBooleanValueByKey(KEY_NEW_USER, false)
    }
}