package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.platform.lifecycleExecute
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.core.AppWrapper

/**
 * 另外音乐会根据前后台处理
 */
class LifecycleTask : JobContent<Context> {
    override fun execute(context: Context) {
        lifecycleExecute(AppWrapper.isForeground)
    }
}