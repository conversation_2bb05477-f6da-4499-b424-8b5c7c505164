package com.moyu.chuanqirensheng.application

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.savedstate.SavedState
import androidx.savedstate.read
import com.moyu.chuanqirensheng.application.Dialogs.accountLevelUpDialog
import com.moyu.chuanqirensheng.application.Dialogs.alertDialog
import com.moyu.chuanqirensheng.application.Dialogs.allyDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.allyStarUpDialog
import com.moyu.chuanqirensheng.application.Dialogs.awardDialog
import com.moyu.chuanqirensheng.application.Dialogs.battlePassDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.battlePassUnlockDialog
import com.moyu.chuanqirensheng.application.Dialogs.buffDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.chooseAllyDialog
import com.moyu.chuanqirensheng.application.Dialogs.chooseEquipDialog
import com.moyu.chuanqirensheng.application.Dialogs.chooseSkillDialog
import com.moyu.chuanqirensheng.application.Dialogs.commonBlockDialog
import com.moyu.chuanqirensheng.application.Dialogs.debugAdvSkillDialog
import com.moyu.chuanqirensheng.application.Dialogs.debugSkillDialog
import com.moyu.chuanqirensheng.application.Dialogs.detailTalent1Dialog
import com.moyu.chuanqirensheng.application.Dialogs.detailTalentDialog
import com.moyu.chuanqirensheng.application.Dialogs.drawActivityResultDialog
import com.moyu.chuanqirensheng.application.Dialogs.drawPoolDialog
import com.moyu.chuanqirensheng.application.Dialogs.drawResultDialog
import com.moyu.chuanqirensheng.application.Dialogs.endingDialog
import com.moyu.chuanqirensheng.application.Dialogs.equipDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.equipStarUpDialog
import com.moyu.chuanqirensheng.application.Dialogs.errorOrderDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameAwardsDialog
import com.moyu.chuanqirensheng.application.Dialogs.gamePropertyDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameReviewDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameSkillDialog
import com.moyu.chuanqirensheng.application.Dialogs.giftDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.infoDialog
import com.moyu.chuanqirensheng.application.Dialogs.moneyTransferDialog
import com.moyu.chuanqirensheng.application.Dialogs.payBlockingDialog
import com.moyu.chuanqirensheng.application.Dialogs.pvpLevelUpDialog
import com.moyu.chuanqirensheng.application.Dialogs.reputationRewardDialog
import com.moyu.chuanqirensheng.application.Dialogs.roleDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.selectAllyToGameDialog
import com.moyu.chuanqirensheng.application.Dialogs.sellPoolDialog
import com.moyu.chuanqirensheng.application.Dialogs.settingDialog
import com.moyu.chuanqirensheng.application.Dialogs.showPermissionDialog
import com.moyu.chuanqirensheng.application.Dialogs.showPrivacyDialog
import com.moyu.chuanqirensheng.application.Dialogs.skillDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.unlockStatusDialog
import com.moyu.chuanqirensheng.application.Dialogs.useSaveDialog
import com.moyu.chuanqirensheng.application.Dialogs.vipDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.vipLevelUpDialog
import com.moyu.chuanqirensheng.debug.DebugAdvSkillDialog
import com.moyu.chuanqirensheng.debug.DebugBattleScreen
import com.moyu.chuanqirensheng.debug.DebugScreen
import com.moyu.chuanqirensheng.debug.DebugSkillDialog
import com.moyu.chuanqirensheng.feature.activities.ui.ActivitiesScreen
import com.moyu.chuanqirensheng.feature.ally.ui.AllyDetailDialog
import com.moyu.chuanqirensheng.feature.ally.ui.AllyDetailScreen
import com.moyu.chuanqirensheng.feature.ally.ui.AllyStarUpDialog
import com.moyu.chuanqirensheng.feature.ally.ui.GamePropertyDialog
import com.moyu.chuanqirensheng.feature.ally.ui.OutAllyScreen
import com.moyu.chuanqirensheng.feature.ally.ui.SelectAllyToGameDialog
import com.moyu.chuanqirensheng.feature.award.ui.AccountLevelDialog
import com.moyu.chuanqirensheng.feature.award.ui.AwardDialog
import com.moyu.chuanqirensheng.feature.award.ui.GameAwardsDialog
import com.moyu.chuanqirensheng.feature.award.ui.VipLevelDialog
import com.moyu.chuanqirensheng.feature.battle.ui.BuffDetailDialog
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePassAllScreen
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePassDialog
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePassScreen
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePassUnlockDialog
import com.moyu.chuanqirensheng.feature.draw.ui.DrawResultDialog
import com.moyu.chuanqirensheng.feature.drawactivity.ui.DrawActivityResultDialog
import com.moyu.chuanqirensheng.feature.drawactivity.ui.DrawAllScreen
import com.moyu.chuanqirensheng.feature.drawactivity.ui.DrawPoolDialog
import com.moyu.chuanqirensheng.feature.ending.ui.EndingDialog
import com.moyu.chuanqirensheng.feature.equip.ui.EquipDetailDialog
import com.moyu.chuanqirensheng.feature.equip.ui.EquipStarUpDialog
import com.moyu.chuanqirensheng.feature.equip.ui.OutEquipScreen
import com.moyu.chuanqirensheng.feature.event.ui.ChooseAllyDialog
import com.moyu.chuanqirensheng.feature.event.ui.ChooseEquipDialog
import com.moyu.chuanqirensheng.feature.event.ui.ChooseSkillDialog
import com.moyu.chuanqirensheng.feature.event.ui.EventSelectScreen
import com.moyu.chuanqirensheng.feature.feedback.ui.FeedBackScreen
import com.moyu.chuanqirensheng.feature.gift.ui.GiftDetailDialog
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE9
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideMask
import com.moyu.chuanqirensheng.feature.holiday.ui.HolidayAllScreen
import com.moyu.chuanqirensheng.feature.illustration.ui.TcgScreen
import com.moyu.chuanqirensheng.feature.info.InfoDialog
import com.moyu.chuanqirensheng.feature.login.LoginBottomItems
import com.moyu.chuanqirensheng.feature.login.LoginScreen
import com.moyu.chuanqirensheng.feature.lottery.ui.CheapLotteryScreen
import com.moyu.chuanqirensheng.feature.lottery.ui.ExpensiveLotteryScreen
import com.moyu.chuanqirensheng.feature.more.MailsScreen
import com.moyu.chuanqirensheng.feature.more.MoreScreen
import com.moyu.chuanqirensheng.feature.newTask.ui.SevenDayChargeTaskScreen
import com.moyu.chuanqirensheng.feature.newTask.ui.SevenDayCollectTaskScreen
import com.moyu.chuanqirensheng.feature.newTask.ui.SevenDayCostTaskScreen
import com.moyu.chuanqirensheng.feature.newTask.ui.SevenDayPackageScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.DungeonScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.Pvp2BattleScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.Pvp2ChooseEnemyScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.Pvp2EntryScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.Pvp2QuestScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.Pvp2RankScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpBattleScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpChooseEnemyScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpEntryScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpLevelDialog
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpQuestScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpRankScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpSellScreen
import com.moyu.chuanqirensheng.feature.quest.ui.DailyQuestScreen
import com.moyu.chuanqirensheng.feature.quest.ui.NewQuestScreen
import com.moyu.chuanqirensheng.feature.rank.ui.RankScreen
import com.moyu.chuanqirensheng.feature.reputation.ui.ReputationAwardDialog
import com.moyu.chuanqirensheng.feature.reputation.ui.ReputationLevelScreen
import com.moyu.chuanqirensheng.feature.reputation.ui.ReputationScreen
import com.moyu.chuanqirensheng.feature.reputation.ui.ReputationShopScreen
import com.moyu.chuanqirensheng.feature.reputation.ui.ReputationTaskScreen
import com.moyu.chuanqirensheng.feature.resource.MoneyTransferDialog
import com.moyu.chuanqirensheng.feature.role.ui.RoleDetailDialog
import com.moyu.chuanqirensheng.feature.router.ACTIVITIES_SCREEN
import com.moyu.chuanqirensheng.feature.router.BATTLE_PASS_ALL_SCREEN
import com.moyu.chuanqirensheng.feature.router.BATTLE_PASS_PREFIX
import com.moyu.chuanqirensheng.feature.router.DEBUG_BATTLE
import com.moyu.chuanqirensheng.feature.router.DEBUG_SCREEN
import com.moyu.chuanqirensheng.feature.router.DETAIL_ALLY
import com.moyu.chuanqirensheng.feature.router.DRAW_ACTIVITY_SCREEN
import com.moyu.chuanqirensheng.feature.router.DUNGEON_SCREEN
import com.moyu.chuanqirensheng.feature.router.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.feature.router.FEEDBACK_SCREEN
import com.moyu.chuanqirensheng.feature.router.HOLIDAY_SCREEN
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN1
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN2
import com.moyu.chuanqirensheng.feature.router.MAILS_SCREEN
import com.moyu.chuanqirensheng.feature.router.MORE_SCREEN
import com.moyu.chuanqirensheng.feature.router.NEW_TASK_SCREEN
import com.moyu.chuanqirensheng.feature.router.OUT_ALLY
import com.moyu.chuanqirensheng.feature.router.OUT_EQUIP
import com.moyu.chuanqirensheng.feature.router.PAGE_PARAM_TAB_INDEX
import com.moyu.chuanqirensheng.feature.router.PVP2_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_SELL_SCREEN
import com.moyu.chuanqirensheng.feature.router.QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.REPUTATION_LEVEL
import com.moyu.chuanqirensheng.feature.router.REPUTATION_SCREEN
import com.moyu.chuanqirensheng.feature.router.REPUTATION_SHOP_PREFIX
import com.moyu.chuanqirensheng.feature.router.REPUTATION_TASK
import com.moyu.chuanqirensheng.feature.router.RouterManager
import com.moyu.chuanqirensheng.feature.router.SELL_SCREEN_PREFIX
import com.moyu.chuanqirensheng.feature.router.SERVER_RANK_PREFIX
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN1
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN2
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN3
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN4
import com.moyu.chuanqirensheng.feature.router.SIGN_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT1_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT2_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT3_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT_ALL_SCREEN
import com.moyu.chuanqirensheng.feature.router.TCG_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_BATTLER_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SELL_SCREEN
import com.moyu.chuanqirensheng.feature.router.VIP_SCREEN
import com.moyu.chuanqirensheng.feature.router.selectedRouter
import com.moyu.chuanqirensheng.feature.sell.ui.SellAllScreen
import com.moyu.chuanqirensheng.feature.sell.ui.SellPoolDialog
import com.moyu.chuanqirensheng.feature.serverrank.ui.ServerRankAllScreen
import com.moyu.chuanqirensheng.feature.setting.SettingDialog
import com.moyu.chuanqirensheng.feature.sign.ui.SignScreen
import com.moyu.chuanqirensheng.feature.skill.ui.GameSkillDialog
import com.moyu.chuanqirensheng.feature.skill.ui.SkillDetailDialog
import com.moyu.chuanqirensheng.feature.talent.ui.Talent1DetailDialog
import com.moyu.chuanqirensheng.feature.talent.ui.TalentAllScreen
import com.moyu.chuanqirensheng.feature.talent.ui.TalentDetailDialog
import com.moyu.chuanqirensheng.feature.talent.ui.TalentScreen1
import com.moyu.chuanqirensheng.feature.talent.ui.TalentScreen2
import com.moyu.chuanqirensheng.feature.talent.ui.TalentScreen3
import com.moyu.chuanqirensheng.feature.tower.ui.TowerBattleScreen
import com.moyu.chuanqirensheng.feature.tower.ui.TowerRankScreen
import com.moyu.chuanqirensheng.feature.tower.ui.TowerScreen
import com.moyu.chuanqirensheng.feature.tower.ui.TowerSellScreen
import com.moyu.chuanqirensheng.feature.unlock.ui.UnlockStatusDialog
import com.moyu.chuanqirensheng.feature.vip.ui.VipDialog
import com.moyu.chuanqirensheng.feature.vip.ui.VipScreen
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.heartbeat
import com.moyu.chuanqirensheng.platform.platformTaskExecute
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.sub.bill.pay.ErrorOrderDialog
import com.moyu.chuanqirensheng.sub.bill.pay.PayBlockDialog
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PERMISSION
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.ui.LoginResultDialog
import com.moyu.chuanqirensheng.sub.loginsdk.ui.PermissionAlertDialog
import com.moyu.chuanqirensheng.sub.loginsdk.ui.PrivacyAlertDialog
import com.moyu.chuanqirensheng.sub.review.ui.GameReviewDialog
import com.moyu.chuanqirensheng.sub.saver.ui.UseCloudSaverDialog
import com.moyu.chuanqirensheng.ui.theme.ComposedTheme
import com.moyu.chuanqirensheng.util.killSelf
import com.moyu.chuanqirensheng.widget.common.AppBackground
import com.moyu.chuanqirensheng.widget.common.GamePowerSnackBar
import com.moyu.chuanqirensheng.widget.common.GameSnackBar
import com.moyu.chuanqirensheng.widget.common.StatusBarMask
import com.moyu.chuanqirensheng.widget.dialog.CommonAlertDialog
import com.moyu.chuanqirensheng.widget.dialog.CommonBlockDialog
import com.moyu.core.AppWrapper
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun GameAppCompose(registerBackCallback: @Composable () -> Unit) {
    heartbeat()

    val navController = rememberNavController()
    ComposedTheme {
        Scaffold { padding ->
            val heathTipsVisible = remember {
                mutableStateOf(true)
            }
            GuideReporter()
            AppBackground()
            if (!heathTipsVisible.value || hasGoogleService()) {
                RegisterScreens(navController, padding)
                if (selectedRouter.value != -1) {
                    LoginBottomItems()
                }
                RegisterDialogs()
                GuideMask()
                RegisterRouter(navController)
                RegisterSnackBar()
                registerBackCallback()
            } else {
                HeathTipsLayout(heathTipsVisible)
            }
            StatusBarMask()
        }
    }
}

@Composable
fun GuideReporter() {
    LaunchedEffect(GuideManager.guideIndex.value) {
        if (GuideManager.guideIndex.value > 0
            && GuideManager.guideIndex.value < GUIDE_STAGE9) {
            reportManager().guide(GuideManager.guideIndex.value)
        }
    }
}

@Composable
fun RegisterScreens(
    navController: NavHostController,
    padding: PaddingValues,
) {
    NavHost(
        navController = navController,
        startDestination = LOGIN_SCREEN,
        modifier = Modifier.padding(padding),
    ) {
        composable(route = LOGIN_SCREEN) { LoginScreen() }
        composable(route = OUT_EQUIP) { OutEquipScreen() }
        composable(route = OUT_ALLY) { OutAllyScreen() }
        composable(route = DETAIL_ALLY) { AllyDetailScreen() }
        composable(route = TALENT_ALL_SCREEN) { TalentAllScreen() }
        composable(route = TALENT1_SCREEN) { TalentScreen1() }
        composable(route = TALENT2_SCREEN) { TalentScreen2() }
        composable(route = TALENT3_SCREEN) { TalentScreen3() }
        composable(route = SIGN_SCREEN) { SignScreen() }
        composable(route = "$SELL_SCREEN_PREFIX{$PAGE_PARAM_TAB_INDEX}") {
            val tabIndex = it.arguments?.let {
                it.read {
                    getString(PAGE_PARAM_TAB_INDEX).toInt()
                }
            } ?: 0
            SellAllScreen(tabIndex)
        }
        composable(route = QUEST_SCREEN) { DailyQuestScreen() }
        composable(route = RANK_SCREEN) { RankScreen() }
        composable(route = MORE_SCREEN) { MoreScreen() }
        composable(route = DRAW_ACTIVITY_SCREEN) { DrawAllScreen() }
        composable(route = BATTLE_PASS_ALL_SCREEN) { BattlePassAllScreen() }
        composable(route = EVENT_SELECT_SCREEN) { EventSelectScreen() }
        composable(route = VIP_SCREEN) { VipScreen() }
        composable(route = NEW_TASK_SCREEN) { NewQuestScreen() }
        composable(route = MAILS_SCREEN) { MailsScreen() }
        composable(route = FEEDBACK_SCREEN) { FeedBackScreen() }
        composable(route = PVP_SCREEN) { PvpEntryScreen() }
        composable(route = PVP2_SCREEN) { Pvp2EntryScreen() }
        composable(route = PVP_SELL_SCREEN) { PvpSellScreen() }
        composable(route = PVP_CHOOSE_ENEMY_SCREEN) { PvpChooseEnemyScreen() }
        composable(route = PVP2_CHOOSE_ENEMY_SCREEN) { Pvp2ChooseEnemyScreen() }
        composable(route = PVP_RANK_SCREEN) { PvpRankScreen() }
        composable(route = PVP2_RANK_SCREEN) { Pvp2RankScreen() }
        composable(route = PVP_QUEST_SCREEN) { PvpQuestScreen() }
        composable(route = PVP2_QUEST_SCREEN) { Pvp2QuestScreen() }
        composable(route = PVP_BATTLE_SCREEN) { PvpBattleScreen() }
        composable(route = PVP2_BATTLE_SCREEN) { Pvp2BattleScreen() }
        composable(route = DUNGEON_SCREEN) { DungeonScreen() }

        composable(route = SEVEN_DAY_SCREEN1) { SevenDayCollectTaskScreen() }
        composable(route = SEVEN_DAY_SCREEN2) { SevenDayPackageScreen() }
        composable(route = SEVEN_DAY_SCREEN3) { SevenDayCostTaskScreen() }
        composable(route = SEVEN_DAY_SCREEN4) { SevenDayChargeTaskScreen() }
        composable(route = LOTTERY_SCREEN1) { CheapLotteryScreen() }
        composable(route = LOTTERY_SCREEN2) { ExpensiveLotteryScreen() }
        composable(route = SERVER_RANK_PREFIX + "{${PAGE_PARAM_TAB_INDEX}}") {
            val type = it.arguments?.let {
                it.read {
                    getString(PAGE_PARAM_TAB_INDEX).toInt()
                }
            } ?: 0
            ServerRankAllScreen(type)
        }
        composable(route = REPUTATION_SCREEN) { ReputationScreen() }
        composable(route = REPUTATION_LEVEL) { ReputationLevelScreen() }
        composable(route = REPUTATION_TASK) { ReputationTaskScreen() }
        composable(route = BATTLE_PASS_PREFIX + "{${PAGE_PARAM_TAB_INDEX}}") {
            val type = it.arguments?.let {
                it.read {
                    getString(PAGE_PARAM_TAB_INDEX).toInt()
                }
            } ?: 0
            BattlePassScreen(type)
        }
        composable(route = REPUTATION_SHOP_PREFIX + "{${PAGE_PARAM_TAB_INDEX}}") {
            val type = it.arguments?.let {
                it.read {
                    getString(PAGE_PARAM_TAB_INDEX).toInt()
                }
            } ?: 0
            ReputationShopScreen(type)
        }
        composable(route = HOLIDAY_SCREEN) { HolidayAllScreen() }
        composable(route = TOWER_BATTLER_SCREEN) { TowerBattleScreen() }
        composable(route = TOWER_SCREEN) { TowerScreen() }
        composable(route = TOWER_SELL_SCREEN) { TowerSellScreen() }
        composable(route = TOWER_RANK_SCREEN) { TowerRankScreen() }
        composable(route = ACTIVITIES_SCREEN) { ActivitiesScreen() }
        composable(route = TCG_SCREEN) { TcgScreen() }
        composable(route = DEBUG_SCREEN) { DebugScreen() }
        composable(route = DEBUG_BATTLE) { DebugBattleScreen() }
    }
}

@Composable
fun RegisterDialogs() {
    ErrorOrderDialog(errorOrderDialog)
    GiftDetailDialog(giftDetailDialog)

    VipDialog(vipDetailDialog)
    BattlePassDialog(battlePassDetailDialog)
    BattlePassUnlockDialog(battlePassUnlockDialog)
    SellPoolDialog(sellPoolDialog)
    RoleDetailDialog(roleDetailDialog)
    SkillDetailDialog(skillDetailDialog)
    EquipDetailDialog(equipDetailDialog)
    EquipStarUpDialog(equipStarUpDialog)
    AllyDetailDialog(allyDetailDialog)
    AllyStarUpDialog(allyStarUpDialog)
    Talent1DetailDialog(detailTalent1Dialog)
    TalentDetailDialog(detailTalentDialog)
    BuffDetailDialog(buffDetailDialog)
    InfoDialog(infoDialog)
    ReputationAwardDialog(reputationRewardDialog)
    DrawPoolDialog(drawPoolDialog)
    UnlockStatusDialog(unlockStatusDialog)
    ChooseSkillDialog(chooseSkillDialog)
    ChooseEquipDialog(chooseEquipDialog)
    ChooseAllyDialog(chooseAllyDialog)

    SelectAllyToGameDialog(selectAllyToGameDialog)
    SettingDialog(settingDialog)
    GameSkillDialog(gameSkillDialog)
    GamePropertyDialog(gamePropertyDialog)
    GameAwardsDialog(gameAwardsDialog)
    EndingDialog(endingDialog)
    PayBlockDialog(payBlockingDialog)
    ErrorOrderDialog(errorOrderDialog)
    GameReviewDialog(gameReviewDialog)
    CommonBlockDialog(commonBlockDialog)
    AccountLevelDialog(accountLevelUpDialog)
    PvpLevelDialog(pvpLevelUpDialog)
    VipLevelDialog(vipLevelUpDialog)

    DebugSkillDialog(debugSkillDialog)
    DebugAdvSkillDialog(debugAdvSkillDialog)
    CommonAlertDialog(alertDialog)
    LoginResultDialog()
    MoneyTransferDialog(moneyTransferDialog)
    AwardDialog(awardDialog)
    DrawResultDialog(drawResultDialog)
    DrawActivityResultDialog(drawActivityResultDialog)
    UseCloudSaverDialog(useSaveDialog)

    PrivacyAlertDialog(switch = showPrivacyDialog, quit = { killSelf() }, confirm = {
        setBooleanValueByKey(KEY_NEED_SHOW_PRIVACY, false)
        showPermissionDialog.value = true
    })
    PermissionAlertDialog(switch = showPermissionDialog, confirm = {
        setBooleanValueByKey(KEY_NEED_SHOW_PERMISSION, false)
        gameSdkDefaultProcessor().initSDK()
        AppWrapper.globalScope.launch {
            delay(1500) //有时候会拉不起来登录，delay下其他操作
            platformTaskExecute()
        }
    })
}

@Composable
fun RegisterRouter(navController: NavHostController) {
    LaunchedEffect(Unit) {
        RouterManager.instance.navController = navController
        navController.addOnDestinationChangedListener { _: NavController, _: NavDestination, _: SavedState? ->
            AppWrapper.globalScope.launch {
                // todo 不delay的话，这里判定时候，页面还没有完成退出
                delay(100)
                playerMusicByScreen()
            }
        }
    }
}

@Composable
fun RegisterSnackBar() {
    Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        GameSnackBar()
        GamePowerSnackBar()
    }
}
