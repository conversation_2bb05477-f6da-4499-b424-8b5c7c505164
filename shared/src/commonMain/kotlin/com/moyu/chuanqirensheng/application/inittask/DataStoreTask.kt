package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_MUSIC
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_SOUND
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEWEST_VERSION
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_USER
import com.moyu.chuanqirensheng.sub.datastore.KEY_SPEED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.sub.privacy.PrivacyManager
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.AppWrapper
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import shared.generated.resources.Res
import shared.generated.resources.backward_version
import shared.generated.resources.data_format_error

class DataStoreTask : JobContent<Context> {
    override fun execute(context: Context) {
        runBlocking {
            LoginManager.instance.newUser = getBooleanFlowByKey(KEY_NEW_USER, true)

            val newestVersion = getIntFlowByKey(KEY_NEWEST_VERSION, 0)
            if (newestVersion > getVersionCode() && !isLite()) {
                AppWrapper.getStringKmp(Res.string.backward_version).toast()
                delay(2000)
                error(AppWrapper.getStringKmp(Res.string.data_format_error))
            }
            setIntValueByKey(KEY_NEWEST_VERSION, getVersionCode())

            // 引导 6, 8, 17, 22, 25, 30, 34, 39
            GuideManager.guideIndex.intValue = getIntFlowByKey(KEY_GUIDE_INDEX)

            gameSdkDefaultProcessor().initGameSdk()
            PrivacyManager.init()

            // 游戏速度和声音设置
            getIntFlowByKey(KEY_SPEED, 0).let { speed ->
                GameSpeedManager.setSpeed(speed)
            }

            MusicManager.muteMusic = getBooleanFlowByKey(KEY_MUTE_MUSIC)
            MusicManager.muteSound = getBooleanFlowByKey(KEY_MUTE_SOUND)
            MusicManager.doMuteState()

            repo.doInit()
        }
    }
}