package com.moyu.chuanqirensheng.application

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.sub.saver.GameSaver
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.widget.common.PowerSnackInfo
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Buff
import com.moyu.core.model.Equipment
import com.moyu.core.model.Gift
import com.moyu.core.model.Vip
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill

fun String.toast() {
    Dialogs.snackbar.value = this
}

fun Int.powerToast() {
    // 如果要保证 same value 也能触发动画，可以直接每次都生成一个新的 uniqueId
    Dialogs.powerSnack.value = PowerSnackInfo(value = this, uniqueId = getCurrentTime(true))
}

object Dialogs {
    val giftDetailDialog = mutableStateOf<Gift?>(null)
    val vipDetailDialog = mutableStateOf<Vip?>(null)
    val battlePassUnlockDialog = mutableStateOf<Int?>(null)
    val battlePassDetailDialog = mutableStateOf<BattlePass?>(null)
    val sellPoolDialog = mutableStateOf<Award?>(null)
    val payBlockingDialog = mutableStateOf<String?>(null)
    val commonBlockDialog = mutableStateOf<String?>(null)
    val errorOrderDialog = mutableStateOf(false)
    val roleDetailDialog = mutableStateOf<Role?>(null)
    val skillDetailDialog = mutableStateOf<Skill?>(null)
    val equipDetailDialog = mutableStateOf<Equipment?>(null)
    val equipStarUpDialog = mutableStateOf<Equipment?>(null)

    val allyDetailDialog = mutableStateOf<Ally?>(null)
    val allyStarUpDialog = mutableStateOf<Ally?>(null)
    val detailTalent1Dialog = mutableStateOf<Int?>(null) // Talent mainId
    val detailTalentDialog = mutableStateOf<Int?>(null) // Talent mainId
    val awardDialog = mutableStateOf<Award?>(null)
    val drawResultDialog = mutableStateOf<Award?>(null)
    val drawActivityResultDialog = mutableStateOf<Award?>(null)
    val infoDialog = mutableStateOf(false)
    val buffDetailDialog = mutableStateOf<Pair<Buff, Role>?>(null)
    val snackbar = mutableStateOf("")
    var powerSnack = mutableStateOf(PowerSnackInfo(0))
    val showPrivacyDialog = mutableStateOf(false)
    val showPermissionDialog = mutableStateOf(false)
    val reputationRewardDialog = mutableStateOf(-1)
    val unlockStatusDialog = mutableStateOf<Pair<List<Boolean>, List<Boolean>>?>(null)

    val settingDialog = mutableStateOf(false)
    val gameSkillDialog = mutableStateOf(false)
    val gamePropertyDialog = mutableStateOf(false)
    val gameAwardsDialog = mutableStateOf(false)
    val selectAllyToGameDialog = mutableStateOf<Boolean?>(null)
    val endingDialog = mutableStateOf<Ending?>(null)
    val drawPoolDialog = mutableStateOf(false)

    val moneyTransferDialog = mutableStateOf(false)

    val useSaveDialog = mutableStateOf<GameSaver?>(null)
    val gameReviewDialog = mutableStateOf(false)
    val accountLevelUpDialog = mutableStateOf(false)
    val pvpLevelUpDialog = mutableStateOf(false)
    val vipLevelUpDialog = mutableStateOf(false)
    val chooseSkillDialog = mutableStateOf<List<Award>>(emptyList())
    val chooseAllyDialog = mutableStateOf<List<Award>>(emptyList())
    val chooseEquipDialog = mutableStateOf<List<Award>>(emptyList())

    val alertDialog = mutableStateOf<CommonAlert?>(null) // confirm cancel
    val debugSkillDialog = mutableStateOf<((Skill) -> Unit)?>(null)
    val debugAdvSkillDialog = mutableStateOf<((Skill) -> Unit)?>(null)
}