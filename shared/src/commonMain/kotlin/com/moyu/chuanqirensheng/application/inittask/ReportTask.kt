package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent

class ReportTask : JobContent<Context> {
    override fun execute(context: Context) {
//        val config = InitConfig("20001508", GameApp.instance.resources.getString(
//            Res.string.platform_channel))
//        config.uriConfig = UriConfig.createByDomain("https://gator.volces.com", null);
//        config.isAutoTrackEnabled = false
//        config.isOaidEnabled = false
//        config.isAndroidIdEnabled = false
//        config.isGaidEnabled = false
//        config.isMacEnable = false
//        config.isImeiEnable = false
//        config.isOaidEnabled = false
//        config.isLogEnable = false
//        config.setAutoStart(false)
//        AppLog.setEncryptAndCompress(true)
//        AppLog.init(context, config)
//        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {
//            AppLog.start()
//        }
    }
}