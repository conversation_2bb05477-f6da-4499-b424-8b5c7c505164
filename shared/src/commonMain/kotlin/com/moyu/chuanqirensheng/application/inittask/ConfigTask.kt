package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.config.ConfigManager
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

class ConfigTask : JobContent<Context> {
    override fun execute(context: Context) {
        runBlocking {
            withContext(Dispatchers.Default) {
                ConfigManager.loadConfigs(repo.gameCore)
            }
        }
    }
}