package com.moyu.chuanqirensheng.media

import com.moyu.core.music.SoundEffect

interface MusicPlayerInterface {

    var musicVolumeCallback: () -> Float

    var soundHashMap: HashMap<SoundEffect, MusicRes>?

    fun playMusic(music: String)
    fun resumeMusic()
    fun stopAll()

    fun playSound(sound: SoundEffect, loop: Boolean = false)
    fun stopSound(sound: SoundEffect)

    fun doMuteState()
    fun init()
}