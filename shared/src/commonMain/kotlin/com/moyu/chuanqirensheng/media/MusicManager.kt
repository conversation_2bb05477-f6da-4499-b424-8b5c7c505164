package com.moyu.chuanqirensheng.media

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.event.isKeyBattle
import com.moyu.chuanqirensheng.feature.router.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_BATTLER_SCREEN
import com.moyu.chuanqirensheng.feature.router.isCurrentRoute
import com.moyu.chuanqirensheng.platform.createMusicPlayer
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_MUSIC
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_SOUND
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.core.music.SoundEffect


enum class MusicRes(val value: String) {
    Audio1("audio_1.mp3"),
    Audio2("audio_2.mp3"),
    Audio3("audio_3.mp3"),
    Audio4("audio_4.mp3"),
    Audio5("audio_5.mp3"),
    Audio6("audio_6.mp3"),

    BattleEffect1("battleeffect_1.mp3"),
    BattleEffect2("battleeffect_2.mp3"),
    BattleEffect3("battleeffect_3.mp3"),
    BattleEffect4("battleeffect_4.mp3"),
    BattleEffect5("battleeffect_5.mp3"),
    BattleEffect6("battleeffect_6.mp3"),
    BattleEffect7("battleeffect_7.mp3"),
    BattleEffect8("battleeffect_8.mp3"),
    BattleEffect9("battleeffect_9.mp3"),
    BattleEffect10("battleeffect_10.mp3"),
    BattleEffect11("battleeffect_11.mp3"),

    Effect1("effect_1.mp3"),
    Effect2("effect_2.mp3"),
    Effect3("effect_3.mp3"),
    Effect4("effect_4.mp3"),
    Effect5("effect_5.mp3"),
    Effect6("effect_6.mp3"),
    Effect7("effect_7.mp3"),
    Effect8("effect_8.mp3"),
    Effect9("effect_9.mp3"),
    Effect10("effect_10.mp3"),
    Effect11("effect_11.mp3"),
    Effect12("effect_12.mp3"),
    Effect13("effect_13.mp3"),
    Effect14("effect_14.mp3"),
    Effect15("effect_15.mp3"),
    Effect16("effect_16.mp3"),
    Effect17("effect_17.mp3"),
    Effect18("effect_18.mp3"),
    Effect19("effect_19.mp3"),
    Effect20("effect_20.mp3"),
    Effect21("effect_21.mp3"),
    Effect22("effect_22.mp3"),
    Effect23("effect_23.mp3"),
    Effect24("effect_24.mp3"),
    Effect25("effect_25.mp3"),
    Effect26("effect_26.mp3"),
    Effect27("effect_27.mp3"),
    Effect28("effect_28.wav"),
    Effect29("effect_29.mp3"),
    Effect30("effect_30.mp3"),
    Effect31("effect_31.mp3"),
    Effect32("effect_32.mp3"),
}

fun playerMusicByScreen() {
    if (Dialogs.endingDialog.value != null) {
        // 死亡弹窗，不播放音乐
        return
    }
    if (isCurrentRoute(LOGIN_SCREEN)) {
        MusicManager.playBackground()
    } else if (isCurrentRoute(EVENT_SELECT_SCREEN)) {
        if (repo.inBattle.value) {
            MusicManager.playBattle(EventManager.selectedEvent.value?.isKeyBattle() == true)
        } else {
            MusicManager.playSelectEvent(BattleManager.currentBgMusic.value)
        }
    } else if (isCurrentRoute(PVP_BATTLE_SCREEN) || isCurrentRoute(PVP2_BATTLE_SCREEN) || isCurrentRoute(
            TOWER_BATTLER_SCREEN
        )) {
        MusicManager.playBattle(true)
    } else {
        MusicManager.resumeMusic()
    }
}

object MusicManager {
    var muteSound by mutableStateOf(false)
    var muteMusic by mutableStateOf(false)
    private var muteByAd = false

    private var muteByBackground = false

    var soundHashMap = HashMap<SoundEffect, MusicRes>()
    private var musicPlayer : MusicPlayerInterface = createMusicPlayer({ getMusicVolume()})

    // 初始化声音池的方法
    fun initSoundPool() {
        muteMusic = getBooleanFlowByKey(KEY_MUTE_MUSIC)

        soundHashMap[SoundEffect.Click] = MusicRes.Effect1
        soundHashMap[SoundEffect.StartGame] = MusicRes.Effect2
        soundHashMap[SoundEffect.SelectEvent] = MusicRes.Effect3
        soundHashMap[SoundEffect.EventWin] = MusicRes.Effect4
        soundHashMap[SoundEffect.BuyGood] = MusicRes.Effect5
        soundHashMap[SoundEffect.UpgradeTalent1] = MusicRes.Effect6
        soundHashMap[SoundEffect.UpgradeTalent2] = MusicRes.Effect7
        soundHashMap[SoundEffect.UpgradeTalent3] = MusicRes.Effect8
        soundHashMap[SoundEffect.EquipItem] = MusicRes.Effect9
        soundHashMap[SoundEffect.StarUpItem] = MusicRes.Effect10
        soundHashMap[SoundEffect.GameOver] = MusicRes.Effect11
        soundHashMap[SoundEffect.GainAward] = MusicRes.Effect12
        soundHashMap[SoundEffect.Draw] = MusicRes.Effect13
        soundHashMap[SoundEffect.CardLevelUp] = MusicRes.Effect14
        soundHashMap[SoundEffect.HorseWalk] = MusicRes.Effect15
        soundHashMap[SoundEffect.OpenChest] = MusicRes.Effect16
        soundHashMap[SoundEffect.PropertyAward] = MusicRes.Effect17
        soundHashMap[SoundEffect.ReputationAward] = MusicRes.Effect18
        soundHashMap[SoundEffect.ResourcesAward] = MusicRes.Effect19
        soundHashMap[SoundEffect.DiamondAward] = MusicRes.Effect20
        soundHashMap[SoundEffect.MoneyAward] = MusicRes.Effect21
        soundHashMap[SoundEffect.HealAward] = MusicRes.Effect22
        soundHashMap[SoundEffect.AllyExpAward] = MusicRes.Effect23
        soundHashMap[SoundEffect.EquipAward] = MusicRes.Effect24
        soundHashMap[SoundEffect.AllyAward] = MusicRes.Effect25
        soundHashMap[SoundEffect.Resources8Award] = MusicRes.Effect26
        soundHashMap[SoundEffect.ShipSail] = MusicRes.Effect27
        soundHashMap[SoundEffect.LotteryRing] = MusicRes.Effect28
        soundHashMap[SoundEffect.DrawOrange] = MusicRes.Effect29
        soundHashMap[SoundEffect.DrawRed] = MusicRes.Effect30
        soundHashMap[SoundEffect.DoubleReward] = MusicRes.Effect31
        soundHashMap[SoundEffect.DoublePunish] = MusicRes.Effect32


        soundHashMap[SoundEffect.Damage1] = MusicRes.BattleEffect1
        soundHashMap[SoundEffect.Damage2] = MusicRes.BattleEffect2
        soundHashMap[SoundEffect.Dispel] = MusicRes.BattleEffect3
        soundHashMap[SoundEffect.Control] = MusicRes.BattleEffect4
        soundHashMap[SoundEffect.Heal] = MusicRes.BattleEffect5
        soundHashMap[SoundEffect.AvoidDeath] = MusicRes.BattleEffect6
        soundHashMap[SoundEffect.RealDie] = MusicRes.BattleEffect7
        soundHashMap[SoundEffect.GetBuff] = MusicRes.BattleEffect8
        soundHashMap[SoundEffect.GetDebuff] = MusicRes.BattleEffect9
        soundHashMap[SoundEffect.BattleWin] = MusicRes.BattleEffect10
        soundHashMap[SoundEffect.Shield] = MusicRes.BattleEffect11

        musicPlayer.soundHashMap = soundHashMap
        musicPlayer.init()
    }

    fun stopAll() {
        musicPlayer.stopAll()
    }

    fun playBackground() {
        playMusic(MusicRes.Audio1)
    }

    fun playSelectEvent(music: MusicRes) {
        playMusic(music)
    }

    fun getRandomDungeonMusic() : MusicRes {
        return listOf(MusicRes.Audio2, MusicRes.Audio3, MusicRes.Audio4).random()
    }

    fun playBattle(isBoss: Boolean) {
        if (isBoss) {
            playMusic(MusicRes.Audio6)
        } else {
            playMusic(MusicRes.Audio5)
        }
    }

    private fun playMusic(music: MusicRes) {
        playMusic(music.value)
    }

    private fun playMusic(music: String) {
        musicPlayer.playMusic(music)
    }

    // 播放声音的方法
    fun playSound(sound: SoundEffect, loop: Boolean = false) { // 获取AudioManager引用
        musicPlayer.playSound(sound, loop)
    }

    fun stopSound(sound: SoundEffect) {
        musicPlayer.stopSound(sound)
    }

    fun muteByBackGround(value: Boolean) {
        muteByBackground = value
        doMuteState()
    }

    fun muteByAd(value: Boolean) {
        muteByAd = value
        doMuteState()
    }

    fun switchMuteSound() {
        muteSound = !muteSound
        doMuteState()
        setBooleanValueByKey(KEY_MUTE_SOUND, muteSound)
    }

    fun switchMuteMusic() {
        muteMusic = !muteMusic
        setBooleanValueByKey(KEY_MUTE_MUSIC, muteMusic)
        doMuteState()
    }

    fun doMuteState() {
        musicPlayer.doMuteState()
    }

    private fun getMusicVolume(): Float {
        return if (muteMusic || muteByBackground || muteByAd) 0f else 1f
    }

    fun resumeMusic() {
        musicPlayer.resumeMusic()
    }
}