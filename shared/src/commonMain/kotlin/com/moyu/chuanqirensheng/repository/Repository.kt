package com.moyu.chuanqirensheng.repository

import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.ally.AllyManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.battle.BattleManager.getGameMaster
import com.moyu.chuanqirensheng.feature.battle.BattleManager.getMyRoleByAlly
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass1Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass3Manager
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.equip.EquipManager
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.gamemode.GameMode
import com.moyu.chuanqirensheng.feature.gamemode.MODE_NORMAL
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.illustration.TcgManager
import com.moyu.chuanqirensheng.feature.judge.GameJudge
import com.moyu.chuanqirensheng.feature.judge.GameJudgeManager
import com.moyu.chuanqirensheng.feature.limit.GameLimitManager
import com.moyu.chuanqirensheng.feature.mission.MissionManager
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.more.MailManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.onTaskStartFight
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.router.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.serverrank.ServerRankManager
import com.moyu.chuanqirensheng.feature.setting.SettingManager
import com.moyu.chuanqirensheng.feature.sign.SignManager
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager
import com.moyu.chuanqirensheng.sub.review.GameReviewManager
import com.moyu.chuanqirensheng.sub.saver.CloudSaverManager
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.chuanqirensheng.widget.effect.cardEffects
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.role.positionListAllies
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.MASTER_MAIN_ID
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.environment.Normal
import com.moyu.core.model.info.BattleInfo
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isMagic
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.jetbrains.compose.resources.getString
import shared.generated.resources.Res
import shared.generated.resources.cancel
import shared.generated.resources.continue_contents
import shared.generated.resources.continue_game
import shared.generated.resources.continue_or_not
import shared.generated.resources.pass_prev_tips
import shared.generated.resources.pass_prev_tips2

val repo = Repo()

class Repo(private val judge: GameJudge = GameJudgeManager()) : GameJudge by judge {
    val gameCore = GameCore(battleCallback)
    val battle = mutableStateOf(GameCore.EMPTY)
    val battleEnvironment = mutableStateOf(Normal)
    val battleRoles = mutableStateMapOf<Int, Role?>()
    val battleInfo = mutableStateListOf<BattleInfo>()
    val lifeInfo = mutableStateListOf<BattleInfo>()
    val inGame = mutableStateOf(false)
    val gameMode = mutableStateOf(GameMode())

    val battleTurn = mutableIntStateOf(0)
    // pic + skill
    val fullScreenSkillEffect = mutableStateOf<Triple<String, Skill, Boolean>?>(null)

    val allyManager = AllyManager()
    val equipManager = EquipManager()

    suspend fun doInit() {
        allyManager.init()
        equipManager.init()
        AwardManager.init()
        TalentManager.init()
        UnlockManager.init()
        EndingManager.init()
        CloudSaverManager.init()
        AntiCheatManager.init()
        VipManager.init()
        BattlePass1Manager.init()
        BattlePass2Manager.init()
        BattlePass3Manager.init()
        AntiCheatManager.init()
        GameLimitManager.init()
        SettingManager.init()
        SignManager.init()
        MissionManager.init()
        reportManager().setup()
        GameReviewManager.init()
        GiftManager.init()
        HolidayManager.init()
        TowerManager.init()
        StageManager.init()
        StoryManager.init()
        MailManager.init()
        TcgManager.init()
    }

    suspend fun doInitAfterLogin() {
//        if (getCurrentTime() - 1745944930487 >= 20 * 24 * 60 * 60 * 1000) {
//            AppWrapper.getStringKmp(Res.string.backward_version).toast()
//            delay(2000)
//            error(AppWrapper.getStringKmp(Res.string.data_format_error))
//        }
        QuestManager.init()
        SellManager.init()
        PvpManager.init()
        Pvp2Manager.init()
        SevenDayManager.init()
        ReputationManager.init()
        MonthCardManager.init()
        ServerRankManager.init()
        if (ContinueManager.haveSaver()) {
            Dialogs.alertDialog.value = CommonAlert(
                title = AppWrapper.getStringKmp(Res.string.continue_or_not),
                content = AppWrapper.getStringKmp(Res.string.continue_contents),
                confirmText = AppWrapper.getStringKmp(Res.string.continue_game),
                cancelText = AppWrapper.getStringKmp(Res.string.cancel),
                onConfirm = {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        ContinueManager.recreateGame()
                        repo.continueGame()
                    }
                }, onCancel = {
                    ContinueManager.clearSave()
                }
            )
        }
    }

    fun getMasterAllyFromPool(): Ally {
        return repo.gameCore.getAllyPool(MASTER_MAIN_ID).first { it.level == AwardManager.getMasterLevel() }
    }

    fun startStageMode() {
        repo.gameMode.value = GameMode(MODE_NORMAL)
        fullScreenSkillEffect.value = null
        // 如果有存档，弹窗
        DebugManager.debugBattle = false
        if (StageManager.isCurrentUnlocked()) {
            repo.startGame()
        } else {
            if (StageManager.currentStage.value.condition > 0) {
                runBlocking {
                    getString(
                        Res.string.pass_prev_tips2,
                        StageManager.currentStage.value.condition
                    ).toast()
                }
            } else {
                runBlocking {
                    getString(Res.string.pass_prev_tips).toast()
                }
            }
        }
    }

    fun startGame() {
        if (!repo.inGame.value) {
            GameLimitManager.tryPlay {
                gameMode.value = GameMode(MODE_NORMAL)
                repo.inGame.value = true
                lifeInfo.clear()
                cardEffects.value = null
                reportManager().onNewGame(MODE_NORMAL)
                EventManager.onNewGame()
                BattleManager.onNewGame()
                DetailProgressManager.onNewGame()
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    EventManager.startGame()
                    goto(EVENT_SELECT_SCREEN)
                    playerMusicByScreen()
                    // 这一步之后，属性才是带上了天赋的属性，天赋是在EventManager.startGame()触发了一次
                    BattleManager.initialProperty.value = getMyRoleByAlly(getGameMaster()).getCurrentProperty()
                }
            }
        }
    }

    fun continueGame() {
        gameMode.value = GameMode(MODE_NORMAL)
        reportManager().onContinueGame(MODE_NORMAL)
        repo.inGame.value = true
        lifeInfo.clear()
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    fun startBattle() {
        gameOver.value = false
        // 保护，防止用户作弊
        if (inBattle.value) return
        inBattle.value = true

        battleInfo.clear()
        battleEnvironment.value = Normal
        battleTurn.value = 0
        fullScreenSkillEffect.value = null
        playerMusicByScreen() // 音乐
        onTaskStartFight(battleRoles.values.filter { it?.isPlayerSide() == true }.mapNotNull { it?.getAlly() })
        AppWrapper.globalScope.launch(gameDispatcher) {
            battleRoles.values.mapNotNull { it }.forEach {
                it.setBuffList(emptyList())
                if (gameMode.value.isTowerMode() || gameMode.value.isAnyPvpMode()) {
                    // todo https://xkff20230903033446466.pingcode.com/pjm/workitems/TyMWL_l9?
                    //#YXW-871 爬塔，战败的时候点击+号上阵框，就可以继续上阵兵种，继续打，一直重复操作可以打到敌方死亡为止
                    it.setPropertyToDefault()
                }
            }
            battle.value = gameCore.createBattleField(battleRoles.toMutableMap())
            battle.value.startBattle()
        }
    }

    fun onBattleInfo(string: String, type: BattleInfoType, play: Int, award: Award? = null, day: Int = 0) {
        if (lifeInfo.size >= 300) {
            val temp = lifeInfo.takeLast(100)
            lifeInfo.clear()
            lifeInfo.addAll(temp)
        }
        lifeInfo.add(BattleInfo(content = string, type = type, play = play, award = award, day = day))
    }

    suspend fun onBattleUpdate(battleField: BattleField) {
        withContext(Dispatchers.Main) {
            battleRoles.clear()
            battleRoles.putAll(battleField.getRoleMap())
            battleTurn.intValue = battleField.getTurn()

            battleRoles.forEach { role->
                role.value?.getStateList()?.firstOrNull { it.isState(ActionStateType.DoSkill) }?.let { state->
                    fullScreenSkillEffect.value = state.skill?.takeIf { it.isMagic() }?.let { skill ->
                        Triple(role.value?.getRace()?.pic?: "", skill,
                            role.value?.isPlayerSide() != false
                        )
                    }
                }
            }
        }
    }

    fun setCurrentEnemies(enemies: Map<Int, Role?>) {
        positionListEnemy.forEach {
            battleRoles[it] = null
        }
        battleRoles.putAll(enemies)
    }

    fun isCurrentEnemyEmptyOrDead(): Boolean {
        return positionListEnemy.none { battleRoles[it] != null && !battleRoles[it]!!.isDeath() }
    }

    fun setCurrentAllies(allies: Map<Int, Role?>) {
        positionListAllies.forEach {
            battleRoles[it] = null
        }
        battleRoles.putAll(allies)
    }
}