package com.moyu.chuanqirensheng.repository

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager.currentTarget
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.router.DEBUG_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.widget.effect.castSkillEffectState
import com.moyu.chuanqirensheng.widget.effect.newTurnEffectState
import com.moyu.chuanqirensheng.widget.effect.restartEffect
import com.moyu.chuanqirensheng.widget.effect.turnEffect
import com.moyu.core.GameCore
import com.moyu.core.debug.CoreDebugConfig
import com.moyu.core.logic.battle.BattleCallback
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.model.GameSpeed
import com.moyu.core.model.environment.Environment
import com.moyu.core.model.info.BattleInfo
import com.moyu.core.model.info.BattleInfoLevel
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.p_getTimeMillis


val battleCallback = object : BattleCallback {
    override fun onBattleInfo(info: BattleInfo) {
        if (info.type == BattleInfoType.ExtraSkill) {
            if (BattleManager.getAge() > 0) {
                repo.lifeInfo.add(info)
            }
        } else {
            if (info.battleInfoLevel.value > BattleInfoLevel.DEBUG.value) {
                repo.battleInfo.add(info.copy(turn = repo.battleTurn.intValue))
                if (info.type == BattleInfoType.Dungeon) {
                    info.content.toast()
                }
            }
        }
    }

    override fun onBattleEffect(type: SoundEffect) {
        MusicManager.playSound(type)
    }

    override fun onToast(string: String) {
        string.toast()
    }

    override fun onTurnBegin() {
        restartEffect(newTurnEffectState, turnEffect)
    }

    override suspend fun onBattleEnd(
        gameOver: Boolean,
        turn: Int,
        allies: List<Role>,
        enemies: List<Role>
    ) {
        repo.battleTurn.value = 0
        // 战斗结果
        if (DebugManager.debugBattle) {
            repo.inBattle.value = false
            goto(DEBUG_SCREEN)
        } else if (repo.gameMode.value.isPvp1Mode()) {
            Dialogs.skillDetailDialog.value = null
            Dialogs.allyDetailDialog.value = null
            if (gameOver) {
                goto(PVP_CHOOSE_ENEMY_SCREEN)
                PvpManager.pkFailed(allies, enemies)
            } else {
                goto(PVP_CHOOSE_ENEMY_SCREEN)
                PvpManager.pkWined(allies, enemies)
            }
            currentTarget.value = RankData(p_getTimeMillis())
        } else if (repo.gameMode.value.isPvp2Mode()) {
            Dialogs.skillDetailDialog.value = null
            Dialogs.allyDetailDialog.value = null
            if (gameOver) {
                goto(PVP2_CHOOSE_ENEMY_SCREEN)
                Pvp2Manager.pkFailed(allies, enemies)
            } else {
                goto(PVP2_CHOOSE_ENEMY_SCREEN)
                Pvp2Manager.pkWined(allies, enemies)
            }
            Pvp2Manager.currentTarget.value = RankData(p_getTimeMillis())
        } else if (repo.gameMode.value.isTowerMode()) {
            Dialogs.skillDetailDialog.value = null
            Dialogs.allyDetailDialog.value = null
            if (gameOver) {
                GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
                TowerManager.failed(allies, enemies)
                goto(TOWER_SCREEN)
            } else {
                GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
                TowerManager.win(allies, enemies)
                goto(TOWER_SCREEN)
            }
        } else {
            Dialogs.skillDetailDialog.value = null
            Dialogs.allyDetailDialog.value = null

            DetailProgressManager.addDefeatEnemies(enemies.filter { it.isDeath() })

            if (gameOver) {
                repo.onBattleLose(allies, enemies)
            } else {
                repo.onBattleWin(allies, enemies)
            }
        }
    }

    override suspend fun onBattleUIUpdate(battleField: BattleField) {
        repo.onBattleUpdate(battleField)
    }

    override fun extraIsGameFailed(): Boolean {
        return EventManager.extraIsGameFailed()
    }


    override fun extraIsGameWin(): Boolean {
        return EventManager.extraIsGameWin()
    }

    override fun getDebugConfig(): CoreDebugConfig {
        return DebugManager
    }

    override fun onCastSkill(skill: Skill) {
        castSkillEffectState.intValue = skill.id
    }

    override fun gameSpeed(): GameSpeed {
        return GameSpeedManager
    }

    override fun onPermanentDiff(target: Role, diff: Property) {
        BattleManager.onPermanentDiff(target, diff)
    }

    override fun onEnvironmentUpdate(environment: Environment) {
        repo.battleEnvironment.value = environment
    }

    override fun canLearnSkill(skill: Skill): Boolean {
        return BattleManager.canLearnSkill(skill)
    }
}