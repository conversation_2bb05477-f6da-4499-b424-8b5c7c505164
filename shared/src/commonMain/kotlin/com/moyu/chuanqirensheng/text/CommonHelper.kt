package com.moyu.chuanqirensheng.text

import com.moyu.chuanqirensheng.platform.hasBilling
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.core.AppWrapper
import com.moyu.core.model.Ally
import com.moyu.core.model.Equipment
import com.moyu.core.model.PropertyAward
import com.moyu.core.model.Sell
import com.moyu.core.model.getRaceGroupName
import com.moyu.core.model.getRaceTypeName
import org.jetbrains.compose.resources.DrawableResource
import shared.generated.resources.Res
import shared.generated.resources.ally_type1
import shared.generated.resources.ally_type2
import shared.generated.resources.ally_type3
import shared.generated.resources.ally_type4
import shared.generated.resources.ally_type5
import shared.generated.resources.both_team
import shared.generated.resources.equipment_type1
import shared.generated.resources.equipment_type2
import shared.generated.resources.equipment_type3
import shared.generated.resources.equipment_type4
import shared.generated.resources.equipment_type5
import shared.generated.resources.equipment_type6
import shared.generated.resources.equipment_type7
import shared.generated.resources.equipment_type8
import shared.generated.resources.equipment_type9
import shared.generated.resources.frame_equip_quality_0
import shared.generated.resources.frame_equip_quality_1
import shared.generated.resources.frame_equip_quality_2
import shared.generated.resources.frame_equip_quality_3
import shared.generated.resources.frame_equip_quality_4
import shared.generated.resources.frame_quality_0
import shared.generated.resources.frame_quality_1
import shared.generated.resources.frame_quality_2
import shared.generated.resources.frame_quality_3
import shared.generated.resources.frame_quality_4
import shared.generated.resources.go_and_get
import shared.generated.resources.itemtype_desc_1
import shared.generated.resources.itemtype_desc_2
import shared.generated.resources.itemtype_desc_3
import shared.generated.resources.itemtype_desc_4
import shared.generated.resources.itemtype_desc_5
import shared.generated.resources.itemtype_desc_6
import shared.generated.resources.itemtype_desc_7
import shared.generated.resources.itemtype_desc_8
import shared.generated.resources.itemtype_desc_9
import shared.generated.resources.my_team
import shared.generated.resources.peer_team
import shared.generated.resources.quality_1
import shared.generated.resources.quality_2
import shared.generated.resources.quality_3
import shared.generated.resources.quality_4
import shared.generated.resources.race1_tips
import shared.generated.resources.race2_tips
import shared.generated.resources.race3_tips
import shared.generated.resources.race4_tips
import shared.generated.resources.real_money_dollar
import shared.generated.resources.real_money_unit
import shared.generated.resources.resource1
import shared.generated.resources.resource2
import shared.generated.resources.resource3
import shared.generated.resources.resource4
import shared.generated.resources.resource5
import shared.generated.resources.resource6
import shared.generated.resources.resource7
import shared.generated.resources.resource8
import shared.generated.resources.resource_desc_1
import shared.generated.resources.resource_desc_2
import shared.generated.resources.resource_desc_3
import shared.generated.resources.resource_desc_4
import shared.generated.resources.resource_desc_5
import shared.generated.resources.resource_desc_6
import shared.generated.resources.resource_desc_7
import shared.generated.resources.resource_desc_8
import shared.generated.resources.skill_tag1
import shared.generated.resources.skill_tag2
import shared.generated.resources.skill_tag3
import shared.generated.resources.skill_tag4
import shared.generated.resources.skill_tag5
import shared.generated.resources.user_frame1
import shared.generated.resources.user_frame2
import shared.generated.resources.user_frame3

val resourcesName = listOf(
    AppWrapper.getStringKmp(Res.string.resource1),
    AppWrapper.getStringKmp(Res.string.resource2),
    AppWrapper.getStringKmp(Res.string.resource3),
    AppWrapper.getStringKmp(Res.string.resource4),
    AppWrapper.getStringKmp(Res.string.resource5),
    AppWrapper.getStringKmp(Res.string.resource6),
    AppWrapper.getStringKmp(Res.string.resource7),
    AppWrapper.getStringKmp(Res.string.resource8)
)

val resourcesTips = listOf(
    AppWrapper.getStringKmp(Res.string.resource_desc_1),
    AppWrapper.getStringKmp(Res.string.resource_desc_2),
    AppWrapper.getStringKmp(Res.string.resource_desc_3),
    AppWrapper.getStringKmp(Res.string.resource_desc_4),
    AppWrapper.getStringKmp(Res.string.resource_desc_5),
    AppWrapper.getStringKmp(Res.string.resource_desc_6),
    AppWrapper.getStringKmp(Res.string.resource_desc_7),
    AppWrapper.getStringKmp(Res.string.resource_desc_8),
)

fun Int.toRaceTips(): String {
    return when (this) {
        1 -> AppWrapper.getStringKmp(Res.string.race1_tips)
        2 -> AppWrapper.getStringKmp(Res.string.race2_tips)
        3 -> AppWrapper.getStringKmp(Res.string.race3_tips)
        else -> AppWrapper.getStringKmp(Res.string.race4_tips)
    }
}

fun Int.toGroupTips(): String {
    return repo.gameCore.getStoryPool()[this - 1].desc
}

fun Int.indexToResourceName(): String {
    return resourcesName[this]
}

fun Int.indexToResourceIcon(): DrawableResource {
    return kmpDrawableResource("element_${this + 1}")
}

fun Int.typeToMagicIcon(): DrawableResource {
    return kmpDrawableResource("magic_type${this}")
}

fun Int.indexToResourceTips(): String {
    return resourcesTips[this]
}

fun Sell.getPriceText(): String {
    return if (isAifadian()) {
        if (!hasGoogleService()) price.toString() else priceDollar.toString()
    } else price.toString()
}

fun Sell.getPriceTextWithUnit(): String {
    return if (isAifadian()) {
        if (!hasBilling()) AppWrapper.getStringKmp(Res.string.go_and_get)
        else if (hasGoogleService())
            priceDollar.toString() + AppWrapper.getStringKmp(Res.string.real_money_dollar)
        else
            price.toString() + AppWrapper.getStringKmp(Res.string.real_money_unit)
    } else price.toString()
}

fun Int.getQualityName(): String {
    return when (this) {
        1 -> AppWrapper.getStringKmp(Res.string.quality_1)
        2 -> AppWrapper.getStringKmp(Res.string.quality_2)
        3 -> AppWrapper.getStringKmp(Res.string.quality_3)
        else -> AppWrapper.getStringKmp(Res.string.quality_4)
    }
}

fun Int.levelFrame() = kmpDrawableResource(
    "level_frame${this}"
)

fun Int.getEquipQualityFrame(): DrawableResource {
    return when (this) {
        1 -> Res.drawable.frame_equip_quality_1
        2 -> Res.drawable.frame_equip_quality_2
        3 -> Res.drawable.frame_equip_quality_3
        4 -> Res.drawable.frame_equip_quality_4
        else -> Res.drawable.frame_equip_quality_0
    }
}

fun Int.getQualityFrame(): DrawableResource {
    return when (this) {
        1 -> Res.drawable.frame_quality_1
        2 -> Res.drawable.frame_quality_2
        3 -> Res.drawable.frame_quality_3
        4 -> Res.drawable.frame_quality_4
        else -> Res.drawable.frame_quality_0
    }
}

fun Int.getRankFrame(): DrawableResource {
    return when (this) {
        0 -> Res.drawable.user_frame3
        1 -> Res.drawable.user_frame2
        2 -> Res.drawable.user_frame1
        else -> Res.drawable.user_frame1
    }
}

fun Equipment.getTypeImage(): DrawableResource {
    return when (type) {
        1 -> Res.drawable.equipment_type1
        2 -> Res.drawable.equipment_type2
        3 -> Res.drawable.equipment_type3
        4 -> Res.drawable.equipment_type4
        5 -> Res.drawable.equipment_type5
        6 -> Res.drawable.equipment_type6
        7 -> Res.drawable.equipment_type7
        8 -> Res.drawable.equipment_type8
        else -> Res.drawable.equipment_type9
    }
}

fun Ally.getTypeRes(): DrawableResource {
    return when (raceType) {
        1 -> Res.drawable.ally_type1
        2 -> Res.drawable.ally_type2
        3 -> Res.drawable.ally_type3
        4 -> Res.drawable.ally_type4
        else -> Res.drawable.ally_type5
    }
}

fun Int.getEquipTypeTips(): String {
    return when (this) {
        1 -> AppWrapper.getStringKmp(Res.string.itemtype_desc_1)
        2 -> AppWrapper.getStringKmp(Res.string.itemtype_desc_2)
        3 -> AppWrapper.getStringKmp(Res.string.itemtype_desc_3)
        4 -> AppWrapper.getStringKmp(Res.string.itemtype_desc_4)
        5 -> AppWrapper.getStringKmp(Res.string.itemtype_desc_5)
        6 -> AppWrapper.getStringKmp(Res.string.itemtype_desc_6)
        7 -> AppWrapper.getStringKmp(Res.string.itemtype_desc_7)
        8 -> AppWrapper.getStringKmp(Res.string.itemtype_desc_8)
        9 -> AppWrapper.getStringKmp(Res.string.itemtype_desc_9)
        else -> ""
    }
}

fun Int.toEquipSlotRes() = kmpDrawableResource("equipment_slot${this}")

fun Int.skillTag(): String {
    return when (this) {
        1 -> AppWrapper.getStringKmp(Res.string.skill_tag1)
        2 -> AppWrapper.getStringKmp(Res.string.skill_tag2)
        3 -> AppWrapper.getStringKmp(Res.string.skill_tag3)
        4 -> AppWrapper.getStringKmp(Res.string.skill_tag4)
        5 -> AppWrapper.getStringKmp(Res.string.skill_tag5)
        else -> AppWrapper.getStringKmp(Res.string.skill_tag1)
    }
}

fun PropertyAward.toPrefix(muteTeam: Boolean = false): String {
    val stringBuilder = StringBuilder()
    if (!muteTeam) {
        if (teamType == 3) {
            stringBuilder.append(AppWrapper.getStringKmp(Res.string.both_team))
        } else if (teamType == 1) {
            if (race != 5) {
                stringBuilder.append(AppWrapper.getStringKmp(Res.string.my_team))
            }
        } else {
            stringBuilder.append(AppWrapper.getStringKmp(Res.string.peer_team))
        }
    }
    if (group != 0) {
        stringBuilder.append(group.getRaceGroupName())
    }
    if (race != 0) {
        stringBuilder.append(race.getRaceTypeName())
    }
    return stringBuilder.toString()
}