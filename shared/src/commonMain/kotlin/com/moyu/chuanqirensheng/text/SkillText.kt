package com.moyu.chuanqirensheng.text

import com.moyu.core.AppWrapper
import com.moyu.core.model.damage.DamageType
import shared.generated.resources.Res
import shared.generated.resources.skill_label1
import shared.generated.resources.skill_label2
import shared.generated.resources.skill_label3
import shared.generated.resources.skill_label4
import shared.generated.resources.skill_label5

val SKILL_LABELS = hashMapOf(
    1 to AppWrapper.getStringKmp(Res.string.skill_label1),
    2 to AppWrapper.getStringKmp(Res.string.skill_label2),
    3 to AppWrapper.getStringKmp(Res.string.skill_label3),
    4 to AppWrapper.getStringKmp(Res.string.skill_label4),
    5 to AppWrapper.getStringKmp(Res.string.skill_label5),
)


fun Int.getSkillTypeName(): String {
    return when (this) {
        1 -> DamageType.DamageType1.getDamageName()
        2 -> DamageType.DamageType2.getDamageName()
//        3 -> DamageType.DamageType3.getDamageName()
//        4 -> DamageType.DamageType4.getDamageName()
//        5 -> DamageType.DamageType5.getDamageName()
        else -> ""
    }
}

fun Int.getSkillElementName(): String {
    return SKILL_LABELS[this] ?: ""
}
