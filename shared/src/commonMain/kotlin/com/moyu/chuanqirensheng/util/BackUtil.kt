package com.moyu.chuanqirensheng.util

import com.moyu.chuanqirensheng.application.Dialogs.alertDialog
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.router.DEBUG_BATTLE
import com.moyu.chuanqirensheng.feature.router.DEBUG_SCREEN
import com.moyu.chuanqirensheng.feature.router.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_BATTLER_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.router.isCurrentRoute
import com.moyu.chuanqirensheng.feature.router.popTop
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.cancel
import shared.generated.resources.guide_block_quit
import shared.generated.resources.quit_battle_content
import shared.generated.resources.quit_battle_title
import shared.generated.resources.quit_life_content
import shared.generated.resources.quit_life_title
import shared.generated.resources.quit_pvp_content
import shared.generated.resources.quit_pvp_title

object BackUtil {
    fun actionBack() {
        if (!GuideManager.canBack()) {
            AppWrapper.getStringKmp(Res.string.guide_block_quit).toast() // 引导时候全局block返回按钮
        } else if (isCurrentRoute(PVP_BATTLE_SCREEN)) {
            alertDialog.value = CommonAlert(
                title = AppWrapper.getStringKmp(Res.string.quit_pvp_title),
                content = AppWrapper.getStringKmp(Res.string.quit_pvp_content),
                onConfirm = {
                    repo.battle.value.terminate()
                    repo.inBattle.value = false
                    PvpManager.pkFailed(emptyList(), repo.battleRoles.values.mapNotNull { it })
                }
            )
        } else if (isCurrentRoute(PVP2_BATTLE_SCREEN)) {
            alertDialog.value = CommonAlert(
                title = AppWrapper.getStringKmp(Res.string.quit_pvp_title),
                content = AppWrapper.getStringKmp(Res.string.quit_pvp_content),
                onConfirm = {
                    repo.battle.value.terminate()
                    repo.inBattle.value = false
                    Pvp2Manager.pkFailed(emptyList(), repo.battleRoles.values.mapNotNull { it })
                }
            )
        } else if (repo.gameMode.value.isTowerMode() && isCurrentRoute(TOWER_BATTLER_SCREEN)) {
            alertDialog.value = CommonAlert(
                title = AppWrapper.getStringKmp(Res.string.quit_battle_title),
                content = AppWrapper.getStringKmp(Res.string.quit_battle_content),
                onConfirm = {
                    repo.battle.value.terminate()
                    repo.inBattle.value = false
                    TowerManager.failed(
                        emptyList(),
                        repo.battleRoles.values.mapNotNull { it })
                    goto(TOWER_SCREEN)
                }
            )
        } else if (isCurrentRoute(DEBUG_BATTLE)) {
            repo.battle.value.terminate()
            repo.inBattle.value = false
            goto((DEBUG_SCREEN))
        } else if (repo.inBattle.value) {
            alertDialog.value = CommonAlert(
                title = AppWrapper.getStringKmp(Res.string.quit_battle_title),
                content = AppWrapper.getStringKmp(Res.string.quit_battle_content),
                onConfirm = {
                    val forceKill = repo.inBattle.value
                    repo.battle.value.terminate()
                    repo.inBattle.value = false
                    EventManager.doEventBattleResult(
                        EventManager.selectedEvent.value,
                        result = false,
                        forceKill = forceKill,
                    )
                }
            )
        } else if (isCurrentRoute(EVENT_SELECT_SCREEN)) {
            doQuitGame()
        } else {
            popTop()
        }
    }
}


fun doQuitGame() {
    alertDialog.value = CommonAlert(
        title = AppWrapper.getStringKmp(Res.string.quit_life_title),
        content = AppWrapper.getStringKmp(Res.string.quit_life_content),
        confirmText = AppWrapper.getStringKmp(Res.string.quit_life_title),
        cancelText = AppWrapper.getStringKmp(Res.string.cancel),
        onConfirm = {
            AppWrapper.globalScope.launch(Dispatchers.Main) {
                val story = EndingManager.saveEnding(
                    BattleManager.you.value,
                    EventManager.eventRecorder.usedEvents,
                    false
                )
                EndingManager.ending(story)
                if (story == null) {
                    // story为空，需要手动跳转
                    repo.inGame.value = false
                    goto(LOGIN_SCREEN)
                }
                ContinueManager.clearSave()
            }
        }
    )
}