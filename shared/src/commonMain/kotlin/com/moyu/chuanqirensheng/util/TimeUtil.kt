package com.moyu.chuanqirensheng.util

import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.platform.getElapsedTimeMillis
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.platform.millisToHoursMinutesSeconds
import com.moyu.core.AppWrapper
import com.moyu.core.util.p_getTimeMillis
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import shared.generated.resources.Res
import shared.generated.resources.time_left_day

/**
 * 日期格式字符串转换成时间戳
 * @param format 如：yyyy-MM-dd HH:mm:ss
 * @return
 */
// yyyy-MM-dd HH:mm:ss
fun date2TimeStamp(timestampMill: Long, format: String = "yyyy-MM-dd HH:mm:ss"): String {
    return com.moyu.chuanqirensheng.platform.date2TimeStamp(timestampMill, format)
}

fun Long.millisToHoursMinutesSeconds(): String {
    return millisToHoursMinutesSeconds()
}

fun isSameDay(millis1: Long, millis2: Long): Boolean {
    val interval = millis1 - millis2
    return interval < 86400000 && interval > -86400000 && millis2Days(
        millis1
    ) == millis2Days(millis2)
}

fun millisToMidnight(now: Long): Long {
    val todayStartMillis = (now + 28800000L) % 86400000L  // 从当天00:00:00算起已经过的毫秒数
    return 86400000L - todayStartMillis  // 今天剩下的毫秒数到午夜
}

fun isYesterday(millis1: Long, millis2: Long): Boolean {
    val interval = millis2 - millis1
    return interval < 2 * 86400000 && interval > 0 && millis2Days(
        millis1
    ) == millis2Days(millis2) - 1
}

fun isAfterEightAM(now: Long): Boolean {
    val millisSinceMidnight = (now + 28800000L) % 86400000L  // 从今天的00:00算起已经过的毫秒数（东八区）
    val eightAMMillis = 8 * 60 * 60 * 1000  // 从00:00到08:00的毫秒数（8小时）
    return millisSinceMidnight > eightAMMillis  // 判断当前时间是否在8点之后
}


fun inSomeHours(millis1: Long, millis2: Long, hours: Int): Boolean {
    val interval = millis1 - millis2
    return (interval < 3600 * 1000 * hours) && (interval > -3600 * 1000 * hours)
}

fun millis2Days(millis: Long): Long {
    return (28800000L + millis) / 86400000
}

suspend fun refreshNetTime() {
    if (AppWrapper.lastNetWorkTime.longValue == 0L) {
        RetrofitModel.getLoginData()
    }
}

fun isNetTimeValid(): Boolean {
    return if (isLite()) true else AppWrapper.lastNetWorkTime.longValue != 0L
}

fun getCurrentTime(forceLocal: Boolean = false): Long {
    var time = if (isLite() || forceLocal) {
        p_getTimeMillis()
    } else {
        getElapsedTimeMillis() - AppWrapper.elapsedDiffTime
    }

    return time
}

fun getCurrentDay(initTime: Long): Int {
    if (isNetTimeValid()) {
        return ((getCurrentTime() - initTime) / (1000 * 60 * 60 * 24)).toInt() + 1
    } else error("Net time is not valid")
}

fun getCurrentDay(): Int {
    return millis2Days(getCurrentTime()).toInt()
}


private fun millis2Weeks(millis: Long): Long {
    val daysFrom1970Thursday = (28800000L + millis) / 86400000
    return (daysFrom1970Thursday + 3) / 7
}

fun isSameWeek(millis1: Long, millis2: Long): Boolean {
    val interval = millis1 - millis2
    return interval < 86400000 * 7 && interval > -86400000 * 7 && millis2Weeks(
        millis1
    ) == millis2Weeks(millis2)
}

fun isBetween23_45And00_15(millis1: Long): Boolean {
    return !isSameDay(millis1, millis1 - 60 * 1000 * 15) || !isSameDay(
        millis1,
        millis1 + 60 * 1000 * 15
    )
}

fun timeLeft(now: Long, initTime: Long, keepDays: Int): Long {
    val targetTime = initTime + keepDays * 86400000L
    return targetTime - now
}

fun gapWeek(now: Long, initTime: Long): Int {
    val gap = now - initTime
    return (gap / 1000 / 60 / 60 / 24 / 7).toInt()
}

fun Long.toDayHourMinuteSecond(): String {
    val days = this / 1000 / 60 / 60 / 24
    return if (days > 0) {
        "$days" + AppWrapper.getStringKmp(Res.string.time_left_day)
    } else {
        millisToHoursMinutesSeconds()
    }
}

fun getDaySinceDec24(): Int {
    val currentMillis = getCurrentTime()

    val newYorkZoneId = TimeZone.of("America/New_York")
    val newYorkDateTime = LocalDateTime(2024, 12, 24, 0, 0)
    val newYorkZonedDateTime = newYorkDateTime.toInstant(newYorkZoneId)
    val dec24StartMillis = newYorkZonedDateTime.toEpochMilliseconds()

    // 计算当前时间距离 2024 年 12 月 24 日的天数
    val dayDifference = (currentMillis - dec24StartMillis) / (24 * 60 * 60 * 1000L)
    return dayDifference.toInt() + 1 // 返回第几天，需从 1 开始
}

// 获取纽约时间 2024 年 12 月 24 日 0 时的时间戳（毫秒）
fun getNYStartTimeMillis(): Long {
    val newYorkZoneId = TimeZone.of("America/New_York")
    val newYorkDateTime = LocalDateTime(2024, 12, 24, 0, 0)
    val newYorkZonedDateTime = newYorkDateTime.toInstant(newYorkZoneId)
    return newYorkZonedDateTime.toEpochMilliseconds()
}


/**
 * 计算距离下次自动 +1 体力还剩多少毫秒
 * 如果当前体力已达上限，则直接返回 0
 */
fun calculateTimeToNextInc(
    currentPower: Int,
    maxPower: Int,
    lastIncTime: Long,
    currentTime: Long
): Long {
    if (currentPower >= maxPower) return 0

    val now = currentTime
    val HOUR_IN_MILLIS = 3600000L
    val passedMs = now - lastIncTime
    if (passedMs < 0) {
        // 时间倒退，返回 0，或者自己做相应处理
        return 0
    }

    val remainder = passedMs % HOUR_IN_MILLIS  // 已经过了多少毫秒（对 1 小时取余）
    return HOUR_IN_MILLIS - remainder         // 剩下多少毫秒到达整小时
}

/** 把毫秒数转成 HH:mm:ss 的格式字符串 */
fun formatMillisToHMS(millis: Long): String {
    if (millis <= 0) return "00:00:00"

    val totalSeconds = millis / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    val seconds = totalSeconds % 60

    val hh = hours.toString().padStart(2, '0')
    val mm = minutes.toString().padStart(2, '0')
    val ss = seconds.toString().padStart(2, '0')

    return "$hh:$mm:$ss"
}
