package com.moyu.chuanqirensheng.util

import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.moyu.chuanqirensheng.platform.getPlatform
import com.moyu.chuanqirensheng.platform.screenDensity
import com.moyu.chuanqirensheng.platform.screenHeightInDp
import com.moyu.chuanqirensheng.platform.statusBarHeightInDp

const val designHeightInDp = 900
val screenHeightInDp by lazy { screenHeightInDp() }

val immersionBarHeightInDp by lazy {
    statusBarHeightInDp()
}

val isSmallScreen by lazy {
    getPlatform() == Platform.IOS && screenHeightInDp < designHeightInDp
}

fun Int.pixelToDp(): Dp {
    val m: Float = screenDensity()
    return (this / m + 0.5f).dp
}

fun Float.dpToPixel(): Float {
    val m: Float = screenDensity()
    return this * m + 0.5f
}

fun Dp.toPixel(): Int {
    val m: Float = screenDensity()
    return (this.value * m + 0.5f).toInt()
}

fun Number.composeDp() = Dp(toFloat() * screenHeightInDp / designHeightInDp)

fun Number.composeSp() = (toFloat() * screenHeightInDp / designHeightInDp).sp
