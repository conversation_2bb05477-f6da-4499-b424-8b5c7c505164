package com.moyu.chuanqirensheng.util

import com.moyu.chuanqirensheng.platform.getSystemFilesPath


fun hideKeyboard() {
    com.moyu.chuanqirensheng.platform.hideKeyboard()
}

fun triggerRebirth() {
    com.moyu.chuanqirensheng.platform.triggerRebirth()
}


fun killSelf() {
    com.moyu.chuanqirensheng.platform.killSelf()
}

//val Context.dataStore2: DataStore<Preferences> by preferencesDataStore(name = DS_NAME2)
// Android的preferencesDataStore方法只需要一个名字，内部的文件路径为： File(applicationContext.filesDir, "datastore/$fileName").preferences_pb
fun getPreferencesPath(fileName: String): String {
    val path = getSystemFilesPath() + "/datastore/" + fileName + ".preferences_pb"
//    println("=-== getPreferencesPath: $path")
    return path
}

enum class Platform {
    Android,
    IOS,
    Desktop,
    Web,
    Unknown
}