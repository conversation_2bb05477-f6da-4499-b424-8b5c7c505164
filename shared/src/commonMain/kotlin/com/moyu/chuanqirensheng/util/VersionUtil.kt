package com.moyu.chuanqirensheng.util

import kotlin.math.PI
import kotlin.math.E
import kotlin.math.pow
import kotlin.math.roundToLong

fun getVersion(): String {
    return com.moyu.chuanqirensheng.platform.getVersion()
}

fun getVersionCode(): Int {
    return com.moyu.chuanqirensheng.platform.getVersionCode()
}

fun getVersions(): String {
    return ((PI + E) * (10.0.pow(16))).roundToLong().toString()
}

fun getSimpleVersions(): String {
    return ((PI * E) * (2.0.pow(12))).roundToLong().toString()
}
