package com.moyu.chuanqirensheng.util

//import timber.log.Timber
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.painter.Painter
import com.moyu.core.model.damage.DamageType
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.ExperimentalResourceApi
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.allDrawableResources
import shared.generated.resources.damage2_minus
import shared.generated.resources.damage2_num_0
import shared.generated.resources.damage2_num_1
import shared.generated.resources.damage2_num_2
import shared.generated.resources.damage2_num_3
import shared.generated.resources.damage2_num_4
import shared.generated.resources.damage2_num_5
import shared.generated.resources.damage2_num_6
import shared.generated.resources.damage2_num_7
import shared.generated.resources.damage2_num_8
import shared.generated.resources.damage2_num_9
import shared.generated.resources.damage2_plus
import shared.generated.resources.damage_num_0
import shared.generated.resources.damage_num_1
import shared.generated.resources.damage_num_2
import shared.generated.resources.damage_num_3
import shared.generated.resources.damage_num_4
import shared.generated.resources.damage_num_5
import shared.generated.resources.damage_num_6
import shared.generated.resources.damage_num_7
import shared.generated.resources.damage_num_8
import shared.generated.resources.damage_num_9
import shared.generated.resources.damage_num_minus
import shared.generated.resources.damage_num_plus
import shared.generated.resources.element_1
import kotlin.math.absoluteValue

@Composable
fun LogRecompose(tag: String) {
    val count = remember { mutableStateOf(0) }
    SideEffect { println("Recompose $tag #${count.value++}") }
}

@Composable
fun kmpPainterResource(imgName: String): Painter {
    return painterResource(kmpDrawableResource(imgName))
}

@Composable
fun kmpPainterResource(imgRes: DrawableResource): Painter {
    return painterResource(imgRes)
}

@OptIn(ExperimentalResourceApi::class)
fun kmpDrawableResource(imgName: String): DrawableResource {
    Res.allDrawableResources[imgName]?.let {
        return it
    } ?: run {
        println("kmpDrawableResource not found $imgName")
    }

    return Res.drawable.element_1
}

fun getDamageNumber(
    damage: Int,
    type: DamageType? = DamageType.DamageType1
): List<DrawableResource> {
    val damageNumberList = mutableListOf<DrawableResource>()

    // 添加负号
    if (type == DamageType.DamageType2) {
        if (damage >= 0) {
            damageNumberList.add(Res.drawable.damage2_minus)
        } else {
            damageNumberList.add(Res.drawable.damage2_plus)
        }
    } else {
        if (damage >= 0) {
            damageNumberList.add(Res.drawable.damage_num_minus)
        } else {
            damageNumberList.add(Res.drawable.damage_num_plus)
        }
    }

    // 将 damage 转换为字符串，然后逐个字符转换为对应的图片资源
    val damageString = damage.absoluteValue.toString()
    for (char in damageString) {
        val index = char.toString().toInt()
        damageNumberList.add(
            if (type == DamageType.DamageType2) getSingleDamage2Number(index) else getSingleDamageNumber(
                index
            )
        )
    }

    return damageNumberList
}

fun getHealNumber(heal: Int): List<DrawableResource> {
    val healNumberList = mutableListOf<DrawableResource>()

    // 添加正号
    if (heal >= 0) {
        healNumberList.add(Res.drawable.damage_num_plus)
    } else {
        healNumberList.add(Res.drawable.damage_num_minus)
    }

    // 将 heal 转换为字符串，然后逐个字符转换为对应的图片资源
    val healString = heal.absoluteValue.toString()
    for (char in healString) {
        val index = char.toString().toInt()
        healNumberList.add(getSingleDamageNumber(index))
    }

    return healNumberList
}

fun getSingleDamage2Number(index: Int): DrawableResource {
    return when (index) {
        1 -> Res.drawable.damage2_num_1
        2 -> Res.drawable.damage2_num_2
        3 -> Res.drawable.damage2_num_3
        4 -> Res.drawable.damage2_num_4
        5 -> Res.drawable.damage2_num_5
        6 -> Res.drawable.damage2_num_6
        7 -> Res.drawable.damage2_num_7
        8 -> Res.drawable.damage2_num_8
        9 -> Res.drawable.damage2_num_9
        else -> Res.drawable.damage2_num_0
    }
}


fun getSingleDamageNumber(index: Int): DrawableResource {
    return when (index) {
        1 -> Res.drawable.damage_num_1
        2 -> Res.drawable.damage_num_2
        3 -> Res.drawable.damage_num_3
        4 -> Res.drawable.damage_num_4
        5 -> Res.drawable.damage_num_5
        6 -> Res.drawable.damage_num_6
        7 -> Res.drawable.damage_num_7
        8 -> Res.drawable.damage_num_8
        9 -> Res.drawable.damage_num_9
        else -> Res.drawable.damage_num_0
    }
}
