package com.moyu.chuanqirensheng.util

import dev.whyoleg.cryptography.CryptographyProvider
import dev.whyoleg.cryptography.algorithms.SHA256
import kotlinx.coroutines.runBlocking
import kotlin.io.encoding.ExperimentalEncodingApi

object AESUtil {
    val charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"

    /*加密(结果为16进制字符串)  */
    fun encrypt(content: String, password: String): String? {
        return com.moyu.chuanqirensheng.platform.aes_encrypt(content, password)
    }

    /*解密16进制的字符串为字符串  */
    fun decrypt(content: String, password: String): String? {
        return com.moyu.chuanqirensheng.platform.aes_decrypt(content, password)
    }

    fun simpleEncryption(input: String, key: String): String {
        val realInput = "sos$input"
        return realInput.mapIndexed { index, char ->
            val keyChar = key[index % key.length]
            val sum = charset.indexOf(char) + charset.indexOf(keyChar)
            charset[sum % charset.length]
        }.joinToString("")
    }

    fun simpleDecryption(encrypted: String, key: String): String {
        return encrypted.mapIndexed { index, char ->
            val keyChar = key[index % key.length]
            val diff = charset.indexOf(char) - charset.indexOf(keyChar) + charset.length
            charset[diff % charset.length]
        }.joinToString("").substring(3)
    }

    fun simpleDecryptionInner(encrypted: String, key: String): String {
        return encrypted.mapIndexed { index, char ->
            val keyChar = key[index % key.length]
            val diff = charset.indexOf(char) - charset.indexOf(keyChar) + charset.length
            charset[diff % charset.length]
        }.joinToString("")
    }

    fun isShareCode(text: String, key: String): Boolean {
        return simpleDecryptionInner(text, key).startsWith("sos")
    }

    @OptIn(ExperimentalEncodingApi::class)
    fun encodeToBase64(byteArray: ByteArray): String {
        return kotlin.io.encoding.Base64.encode(byteArray)
    }

    @OptIn(ExperimentalStdlibApi::class)
    suspend fun sha256Async(base: String): String  {
        val provider = CryptographyProvider.Default
        val hasher = provider.get(SHA256).hasher()
        val digest = hasher.hash(base.encodeToByteArray()).toHexString()

        return digest
    }

    fun sha256(base: String): String  {
        return runBlocking {
            sha256Async(base)
        }
    }

    fun getSignature(): String {
        val result = com.moyu.chuanqirensheng.platform.getSignature()
        return result
    }
}
