import com.moyu.chuanqirensheng.platform.getLocalLanguage
import com.moyu.chuanqirensheng.platform.getPlatform
import com.moyu.chuanqirensheng.platform.getTextFromFile
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.sub.language.LanguageManager.languageCodes
import com.moyu.chuanqirensheng.util.Platform
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.jetbrains.compose.resources.ExperimentalResourceApi
import shared.generated.resources.Res
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

suspend fun fileLoadConfigFile(
    fileName: String,
    processor: (Int, String) -> Unit
): Boolean {
    return withContext(Dispatchers.IO) {
        suspendCoroutine { continuation ->
            val string =  if (!isLite() && getPlatform() == Platform.Android) {
                getTextFromFile(fileName)
            } else {
                getTextFromAsset(fileName)
            }

            string.lines().forEachIndexed { index, line ->
                processor(index, line)
            }
            continuation.resume(true)
        }
    }
}

@OptIn(ExperimentalResourceApi::class)
private fun getTextFromAsset(fileName: String): String {
    var result = ""

    runBlocking {
        // 根据多语言找配置文件 todo 调试中
//        val lang = LanguageManager.selectedLanguage.value
//        val lang = getStringFlowByKey(KEY_LANGUAGE)
        var path = "en" // default

        val language = getLocalLanguage()
        languageCodes.forEachIndexed { index, code ->
            if (getPlatform() == Platform.Desktop) {
                if (language.startsWith("zh")) {
                    path = "desktop_config"
                } else if (language.startsWith(code)) {
                    path = "desktop_${languageCodes[index]}"
                }
            } else {
                if (language.startsWith("zh")) {
                    path = "config"
                } else if (language.startsWith(code)) {
                    path = languageCodes[index]
                }
            }
        }

//        println("=-== lang: $lang")
//        println("=-== path: $path")
        val bytes = Res.readBytes("files/${path}/$fileName")
        result = bytes.decodeToString()
    }

    return result
}