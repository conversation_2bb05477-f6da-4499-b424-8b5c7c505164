package com.moyu.chuanqirensheng.util

import io.sentry.kotlin.multiplatform.Sentry

// ios
fun initializeSentry() {
    Sentry.init { options ->
        options.dsn = "https://<EMAIL>/4509451399856128"
    }
}

fun desktopInitializeSentry() {
//    Sentry.init { options ->
//        options.dsn = "https://<EMAIL>/4508992191397888"
//    }
}

fun tryException() {
    try {
        throw Exception("This is a test.")
    } catch (e: Exception) {
        Sentry.captureException(e)
    }
}