package com.moyu.chuanqirensheng.feature.login

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentPowerPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentResourcesPoint
import com.moyu.chuanqirensheng.ui.theme.moneyMinyWidth
import com.moyu.chuanqirensheng.ui.theme.moneyWidthMinus
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding40
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.top_resouce_frame


@Composable
fun TopItemsRight(modifier: Modifier = Modifier) {
    Row(
        modifier.fillMaxWidth().height(padding40).paint(
            painter = painterResource(Res.drawable.top_resouce_frame),
            contentScale = ContentScale.FillBounds
        ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.End
    ) {
        CurrentPowerPoint(showPlus = true, boxWidth = moneyMinyWidth)
        CurrentKeyPoint(showPlus = true, boxWidth = moneyWidthMinus)
        CurrentResourcesPoint(index = 0, showPlus = true)
        Spacer(Modifier.size(padding4))
    }
}