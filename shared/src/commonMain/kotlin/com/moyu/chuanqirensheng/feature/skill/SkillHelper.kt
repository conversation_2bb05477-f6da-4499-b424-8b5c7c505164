package com.moyu.chuanqirensheng.feature.skill

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import com.moyu.chuanqirensheng.sub.datastore.KEY_TEXT_FIXED_COLOR
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.core.logic.skill.getRealDesc
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.skill.Skill

private val damageDisplayList = DamageType.entries.map { it.display }
private val damage1 = damageDisplayList.first()
private val damage2 = damageDisplayList[1]

private data class BracketMarker(
    val open: Char,
    val close: Char,
    val color: Color,
)

private val bracketMarkers = listOf(
    BracketMarker('<', '>', Color(0xFF66BB6A)),   // 绿
    BracketMarker('「', '」', Color(0xFFFFCA28)), // 黄
    BracketMarker('《', '》', Color(0xFFAB47BC)), // 紫
)

private val literalColors = mapOf(
    damage1 to Color(0xFFEF5350), // 红
    damage2 to Color(0xFF42A5F5)  // 蓝
)

/* ───────── Skill → 彩色字符串 ───────── */

@Composable
fun Skill.getRealDescColorful(
    spanStyle: SpanStyle = MaterialTheme.typography.h4.toSpanStyle(),
): AnnotatedString = getRealDesc().toSkillAnnotatedString(spanStyle)

/* ───────── String → AnnotatedString ───────── */

@Composable
fun String.toSkillAnnotatedString(
    spanStyle: SpanStyle = MaterialTheme.typography.h4.toSpanStyle(),
): AnnotatedString {
    if (getBooleanFlowByKey(KEY_TEXT_FIXED_COLOR)) return AnnotatedString(this)

    /* -------- 1) 先构建纯文本（去掉括号外壳） -------- */
    val builder = AnnotatedString.Builder()
    data class StackEntry(val marker: BracketMarker, val start: Int)
    val stack = ArrayDeque<StackEntry>()

    // 收集样式区间
    data class StyleRange(val start: Int, val end: Int, val color: Color)
    val bracketRanges = mutableListOf<StyleRange>()   // later
    val damageRanges  = mutableListOf<StyleRange>()   // first

    for (ch in this) {

        /* ---------- 开括号 ---------- */
        val openMk = bracketMarkers.find { it.open == ch }
        if (openMk != null) {
            stack.addLast(StackEntry(openMk, builder.length))   // 不写入开括号
            continue                                            // ← 现在合法
        }

        /* ---------- 闭括号 ---------- */
        val closeMk = bracketMarkers.find { it.close == ch }
        if (closeMk != null) {
            val idx = stack.indexOfLast { it.marker == closeMk }
            if (idx != -1) {
                val (mk, start) = stack.removeAt(idx)
                builder.addStyle(spanStyle.copy(color = mk.color), start, builder.length)
            } else {
                builder.append(ch)                              // 未配对，原样输出
            }
            continue
        }

        /* ---------- 普通字符 ---------- */
        builder.append(ch)
    }

    val plain = builder.toAnnotatedString().text

    /* -------- 2) damage1 / damage2 区间 -------- */
    literalColors.forEach { (lit, color) ->
        if (lit.isEmpty()) return@forEach
        Regex(Regex.escape(lit)).findAll(plain).forEach { mr ->
            damageRanges += StyleRange(mr.range.first, mr.range.last + 1, color)
        }
    }

    /* -------- 3) 依顺序写入样式：damage → bracket -------- */
    (damageRanges + bracketRanges).forEach { (s, e, c) ->
        builder.addStyle(spanStyle.copy(color = c), s, e)
    }

    return builder.toAnnotatedString()
}