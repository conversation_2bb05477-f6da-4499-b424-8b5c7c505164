package com.moyu.chuanqirensheng.feature.guide.ui

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.repeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE2
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE3
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE4
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE5
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE6
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE7
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE8
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE9
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.router.DETAIL_ALLY
import com.moyu.chuanqirensheng.feature.router.DUNGEON_SCREEN
import com.moyu.chuanqirensheng.feature.router.OUT_ALLY
import com.moyu.chuanqirensheng.feature.router.OUT_EQUIP
import com.moyu.chuanqirensheng.feature.router.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT1_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT2_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT3_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT_ALL_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.router.gotoSellWithTabIndex
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.bigButtonWidth
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding9
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.effect.ForeverGif
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.guideGif
import com.moyu.chuanqirensheng.widget.effect.levelUpGif
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.guide_1
import shared.generated.resources.guide_2
import shared.generated.resources.guide_3
import shared.generated.resources.guide_4

const val GUIDE_UPGRADE_ALLY_MAIN_ID = 1001
const val GUIDE_UPGRADE_TALENT1_ID = 818500
const val GUIDE_UPGRADE_TALENT2_MAIN_ID = 8301
const val GUIDE_UPGRADE_TALENT3_MAIN_ID = 8018

@Composable
fun GuideMask() {
    if (GuideManager.showGuide.value) {
        val gifShowed = remember {
            mutableStateOf(false)
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .clickable {
                    MusicManager.playSound(SoundEffect.Click)
                    when (GuideManager.guideIndex.intValue) {
                        6 -> {
                            GuideManager.guideIndex.value = 7
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE2)
                            gotoSellWithTabIndex(0)
                        }

                        7 -> {
                            GuideManager.guideIndex.value = 8
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE2)
                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                DrawManager.buyAllyCoupon()
                            }
                            GuideManager.showGuide.value = false
                        }

                        9 -> {
                            GuideManager.guideIndex.value = 10
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                            goto(OUT_ALLY)
                        }

                        10 -> {
                            GuideManager.guideIndex.value = 11
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                            repo.allyManager.currentShowAllyMainId.value =
                                GUIDE_UPGRADE_ALLY_MAIN_ID
                            goto(DETAIL_ALLY)
                        }

                        11 -> {
                            GuideManager.guideIndex.value = 12
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                            repo.allyManager.levelUp(repo.allyManager.data.first { it.mainId == GUIDE_UPGRADE_ALLY_MAIN_ID })
                                ?.let {
                                    repo.allyManager.currentShowAllyMainId.value = it.mainId
                                    gifShowed.value = true
                                } ?: run {
                                GameCore.instance.onBattleEffect(SoundEffect.Click)
                            }
                        }

                        12 -> {
                            GuideManager.guideIndex.value = 13
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                            Dialogs.allyStarUpDialog.value =
                                repo.allyManager.data.first { it.mainId == GUIDE_UPGRADE_ALLY_MAIN_ID }
                        }

                        15 -> {
                            GuideManager.guideIndex.value = 16
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                            repo.allyManager.selectedTab.value = 1
                        }

                        16 -> {
                            GuideManager.guideIndex.value = 17
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                            repo.allyManager.oneShotSelect()
                            GuideManager.showGuide.value = false
                        }

                        18 -> {
                            GuideManager.guideIndex.value = 19
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE4)
                            goto(TALENT_ALL_SCREEN)
                        }

                        19 -> {
                            GuideManager.guideIndex.value = 20
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE4)
                            goto(TALENT1_SCREEN)
                        }

                        20 -> {
                            GuideManager.guideIndex.value = 21
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE4)
                            Dialogs.detailTalent1Dialog.value = GUIDE_UPGRADE_TALENT1_ID
                            GuideManager.showGuide.value = false
                        }

                        23 -> {
                            GuideManager.guideIndex.value = 24
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE5)
                            goto(OUT_EQUIP)
                        }

                        24 -> {
                            GuideManager.guideIndex.value = 25
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE5)
                            repo.equipManager.oneShotSelect()
                            GuideManager.showGuide.value = false
                        }

                        26 -> {
                            GuideManager.guideIndex.value = 27
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE6)
                            goto(TALENT_ALL_SCREEN)
                        }

                        27 -> {
                            GuideManager.guideIndex.value = 28
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE6)
                            goto(TALENT2_SCREEN)
                        }

                        28 -> {
                            GuideManager.guideIndex.value = 29
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE6)
                            Dialogs.detailTalentDialog.value = GUIDE_UPGRADE_TALENT2_MAIN_ID
                            GuideManager.showGuide.value = false
                        }

                        31 -> {
                            GuideManager.guideIndex.value = 32
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE7)
                            goto(DUNGEON_SCREEN)
                        }

                        32 -> {
                            GuideManager.guideIndex.value = 33
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE7)
                            goto(PVP_SCREEN)
                        }

                        33 -> {
                            GuideManager.guideIndex.value = 34
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE7)
                            goto(PVP_CHOOSE_ENEMY_SCREEN)
                            GuideManager.showGuide.value = false
                        }

                        35 -> {
                            GuideManager.guideIndex.value = 36
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE8)
                            goto(DUNGEON_SCREEN)
                        }

                        36 -> {
                            GuideManager.guideIndex.value = 37
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE8)
                            goto(TOWER_SCREEN)
                        }

                        37 -> {
                            GuideManager.guideIndex.value = 39
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE8)
                            TowerManager.fight(tower = repo.gameCore.getTowerPool().first())
                            GuideManager.showGuide.value = false
                        }
                        40 -> {
                            GuideManager.guideIndex.value = 41
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE9)
                            goto(TALENT_ALL_SCREEN)
                        }

                        41 -> {
                            GuideManager.guideIndex.value = 42
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE9)
                            goto(TALENT3_SCREEN)
                        }

                        42 -> {
                            GuideManager.guideIndex.value = 43
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE9)
                            Dialogs.detailTalentDialog.value = GUIDE_UPGRADE_TALENT3_MAIN_ID
                            GuideManager.showGuide.value = false
                        }
                    }
                }, horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when (GuideManager.guideIndex.intValue) {
                11 -> {
                    Row(
                        Modifier.fillMaxSize().padding(bottom = padding26),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Box(Modifier.size(bigButtonWidth, bigButtonHeight)) {
                            GifView(
                                modifier = Modifier.size(bigButtonWidth, bigButtonHeight).scale(4f),
                                enabled = true,
                                gifCount = levelUpGif.count,
                                gifDrawable = levelUpGif.gif,
                                pace = levelUpGif.pace,
                            ) {
                                gifShowed.value = false
                            }
                        }
                        Box(Modifier.size(bigButtonWidth, bigButtonHeight)) {

                        }
                    }
                }
            }
        }
    }
}

enum class HandType(val value: Int) {
    UP_HAND(1), DOWN_HAND(2), LEFT_HAND(3), RIGHT_HAND(4), NO_HAND(5)
}

@Composable
fun GuideHand(modifier: Modifier = Modifier, handType: HandType = HandType.UP_HAND) {
    // 创建一个动画状态，用于处理跳动效果
    val animatedOffsetY = remember { Animatable(0f) }

    // 当组件进入或手指类型改变时，触发跳动动画
    LaunchedEffect(Unit) {
        animatedOffsetY.animateTo(
            targetValue = -20f, // 设置动画的目标值
            animationSpec = repeatable( // 使用repeatable重复动画
                iterations = 999, // 设置动画无限重复
                animation = tween(
                    durationMillis = 500, // 设置动画持续时间
                    easing = FastOutSlowInEasing // 设置动画的缓动效果
                ),
                repeatMode = RepeatMode.Reverse // 设置动画反向重复
            )
        )
    }
    when (handType) {
        HandType.UP_HAND -> {
            Box(modifier = modifier) {
                ForeverGif(Modifier.align(Alignment.TopCenter).graphicsLayer {
                    translationY = -padding26.toPx()
                    translationX = -padding6.toPx()
                }.scale(1.2f), guideGif.gif, guideGif.count)
                Image(
                    modifier = Modifier.fillMaxHeight()
                        .graphicsLayer {
                            translationY = animatedOffsetY.value
                        },
                    contentScale = ContentScale.FillHeight,
                    painter = kmpPainterResource(Res.drawable.guide_3),
                    contentDescription = ""
                )
            }
        }

        HandType.LEFT_HAND -> {
            Box(modifier = modifier) {
                ForeverGif(Modifier.align(Alignment.CenterStart).graphicsLayer {
                    translationX = -padding30.toPx()
                    translationY = -padding9.toPx()
                }.scale(1.2f), guideGif.gif, guideGif.count)
                Image(
                    modifier = Modifier.fillMaxHeight()
                        .graphicsLayer {
                            translationX = animatedOffsetY.value
                        },
                    contentScale = ContentScale.FillHeight,
                    painter = kmpPainterResource(Res.drawable.guide_4),
                    contentDescription = ""
                )
            }
        }

        HandType.RIGHT_HAND -> {
            Box(modifier = modifier) {
                ForeverGif(Modifier.align(Alignment.CenterEnd).graphicsLayer {
                    translationX = padding30.toPx()
                    translationY = -padding9.toPx()
                }.scale(1.2f), guideGif.gif, guideGif.count)
                Image(
                    modifier = Modifier.fillMaxHeight()
                        .graphicsLayer {
                            translationX = animatedOffsetY.value
                        },
                    contentScale = ContentScale.FillHeight,
                    painter = kmpPainterResource(Res.drawable.guide_1),
                    contentDescription = ""
                )
            }
        }

        HandType.DOWN_HAND -> {
            Box(modifier = modifier) {
                ForeverGif(Modifier.align(Alignment.BottomCenter).graphicsLayer {
                    translationY = padding26.toPx()
                    translationX = padding6.toPx()
                }.scale(1.2f), guideGif.gif, guideGif.count)
                Image(
                    modifier = Modifier.fillMaxHeight()
                        .graphicsLayer {
                            translationY = animatedOffsetY.value
                        },
                    contentScale = ContentScale.FillHeight,
                    painter = kmpPainterResource(Res.drawable.guide_2),
                    contentDescription = ""
                )
            }
        }

        HandType.NO_HAND -> {

        }
    }
}