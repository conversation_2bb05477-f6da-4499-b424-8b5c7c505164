package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.lucky.AdManager
import com.moyu.chuanqirensheng.feature.lucky.AdManager.refreshAdMoney
import com.moyu.chuanqirensheng.feature.lucky.ui.AdRewardBar
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.feature.vip.ui.VipScreen
import com.moyu.chuanqirensheng.platform.statusBarHeightInDp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding93
import com.moyu.chuanqirensheng.ui.theme.padding96
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.activity_frame
import shared.generated.resources.card_pool
import shared.generated.resources.daily_gift_icon
import shared.generated.resources.free_label1
import shared.generated.resources.free_label2
import shared.generated.resources.shop_label
import shared.generated.resources.shop_label1
import shared.generated.resources.shop_label2
import shared.generated.resources.shop_label3
import shared.generated.resources.shop_label4
import shared.generated.resources.shop_label5
import shared.generated.resources.shop_label6
import shared.generated.resources.tab_gift
import shared.generated.resources.tab_special
import shared.generated.resources.vip_card
import shared.generated.resources.vip_sell_icon
import shared.generated.resources.vip_shop_label


@Composable
fun SellAllScreen(initTab: Int = 0) {
    val listTabItems = remember(VipManager.getVipLevel()) {
        mutableStateListOf(
            Res.drawable.card_pool,
            Res.drawable.daily_gift_icon,
            Res.drawable.tab_gift,
            Res.drawable.tab_special,
            Res.drawable.vip_card,
            Res.drawable.vip_sell_icon,
        ).let {
            // 最后一个Vip商店需要看是否有解锁了的商品，否则不显示
            if (SellManager.items.filter { it.isVipItem() }.any {
                    UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(it.unlock))
                }) {
                it
            } else it.dropLast(1)
        }
    }
    val pagerState = remember {
        mutableStateOf(initTab)
    }

    LaunchedEffect(Unit) {
        refreshAdMoney()
    }
    GameBackground(showCloseIcon = false) {
        Column(Modifier.fillMaxSize().padding(top = statusBarHeightInDp()), horizontalAlignment = Alignment.CenterHorizontally) {
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
            ) {
                when (pagerState.value) {
                    0 -> SellDrawPage()
                    1 -> SellPage(listOf(1, 31)) {
                        // 礼包的banner
                        Spacer(Modifier.size(padding4))
                        Box {
                            Image(
                                modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120).padding(padding2).clip(
                                    RoundedCornerShape(padding10)
                                ),
                                painter = painterResource(Res.drawable.shop_label1),
                                contentScale = ContentScale.FillBounds,
                                contentDescription = null
                            )
                            Image(
                                modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120),
                                painter = painterResource(Res.drawable.activity_frame),
                                contentScale = ContentScale.FillBounds,
                                contentDescription = null
                            )
                            Column(
                                Modifier.padding(start = padding40, top = padding30),
                            ) {
                                StrokedText(
                                    modifier = Modifier.scale(1.2f),
                                    text = stringResource(Res.string.free_label1),
                                    style = MaterialTheme.typography.h1
                                )
                                Spacer(Modifier.size(padding22))
                                StrokedText(
                                    modifier = Modifier.padding(start = padding40).scale(1.2f),
                                    text = stringResource(Res.string.free_label2),
                                    style = MaterialTheme.typography.h1
                                )
                            }
                        }
                        Spacer(Modifier.size(padding4))
                        AdRewardBar(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(padding96)
                                .padding(horizontal = padding36),
                            currentValue = AwardManager.adMoney.value,
                            maxValue = AdManager.luckyList.maxOf { it.price }
                        )
                    }

                    2 -> SellPage(listOf(2)) {
                        // 礼包的banner
                        Spacer(Modifier.size(padding4))
                        Box {
                            Image(
                                modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120).padding(padding2).clip(
                                    RoundedCornerShape(padding10)
                                ),
                                painter = painterResource(Res.drawable.shop_label),
                                contentScale = ContentScale.FillBounds,
                                contentDescription = null
                            )
                            Image(
                                modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120),
                                painter = painterResource(Res.drawable.activity_frame),
                                contentScale = ContentScale.FillBounds,
                                contentDescription = null
                            )
                            Column(
                                Modifier.padding(start = padding40, top = padding30),
                            ) {
                                StrokedText(
                                    modifier = Modifier.scale(1.2f),
                                    text = stringResource(Res.string.shop_label1),
                                    style = MaterialTheme.typography.h1
                                )
                                Spacer(Modifier.size(padding22))
                                StrokedText(
                                    modifier = Modifier.padding(start = padding40).scale(1.2f),
                                    text = stringResource(Res.string.shop_label2),
                                    style = MaterialTheme.typography.h1
                                )
                            }
                        }
                    }

                    3 -> SellPage(listOf(3), showLabel = false) {
                        // 礼包的banner
                        Spacer(Modifier.size(padding4))
                        Box {
                            Box {
                                Image(
                                    modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120).padding(padding2).clip(
                                        RoundedCornerShape(padding10)
                                    ),
                                    painter = painterResource(Res.drawable.shop_label2),
                                    contentScale = ContentScale.FillBounds,
                                    contentDescription = null
                                )
                                Image(
                                    modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120),
                                    painter = painterResource(Res.drawable.activity_frame),
                                    contentScale = ContentScale.FillBounds,
                                    contentDescription = null
                                )
                                Column(
                                    Modifier.padding(start = padding40, top = padding30),
                                ) {
                                    StrokedText(
                                        modifier = Modifier.scale(1.2f),
                                        text = stringResource(Res.string.shop_label3),
                                        style = MaterialTheme.typography.h1
                                    )
                                    Spacer(Modifier.size(padding22))
                                    StrokedText(
                                        modifier = Modifier.padding(start = padding40).scale(1.2f),
                                        text = stringResource(Res.string.shop_label4),
                                        style = MaterialTheme.typography.h1
                                    )
                                }
                            }
                        }
                    }
                    4 -> VipScreen()
                    else -> {
                        SellPage(listOf(6), showLockedItem = false) {
                            Spacer(Modifier.size(padding4))
                            Box {
                                Image(
                                    modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120).padding(padding2).clip(
                                        RoundedCornerShape(padding10)
                                    ),
                                    painter = painterResource(Res.drawable.vip_shop_label),
                                    contentScale = ContentScale.FillBounds,
                                    contentDescription = null
                                )
                                Image(
                                    modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120),
                                    painter = painterResource(Res.drawable.activity_frame),
                                    contentScale = ContentScale.FillBounds,
                                    contentDescription = null
                                )
                                Column(
                                    Modifier.padding(start = padding40, top = padding30),
                                ) {
                                    StrokedText(
                                        modifier = Modifier.scale(1.2f),
                                        text = stringResource(Res.string.shop_label5),
                                        style = MaterialTheme.typography.h1
                                    )
                                    Spacer(Modifier.size(padding22))
                                    StrokedText(
                                        modifier = Modifier.padding(start = padding40).scale(1.2f),
                                        text = stringResource(Res.string.shop_label6),
                                        style = MaterialTheme.typography.h1
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
        NavigationTab(
            modifier = Modifier.align(Alignment.BottomCenter).padding(bottom = padding93),
            pageState = pagerState,
            titles = listTabItems,
            redIcons = List(listTabItems.size) { index ->
                SellManager.getRedFree(index)
            }
        )
    }
}