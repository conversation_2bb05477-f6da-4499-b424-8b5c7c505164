package com.moyu.chuanqirensheng.feature.ally.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.resource.CurrentAllyExpPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentResourcesPoint
import com.moyu.chuanqirensheng.feature.resource.PowerLabel
import com.moyu.chuanqirensheng.feature.role.ui.MainPropertyLine
import com.moyu.chuanqirensheng.feature.router.OUT_ALLY
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.text.getQualityName
import com.moyu.chuanqirensheng.text.getTypeRes
import com.moyu.chuanqirensheng.text.toGroupTips
import com.moyu.chuanqirensheng.text.toRaceTips
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.cardNumSmallHeight
import com.moyu.chuanqirensheng.ui.theme.cardNumSmallWidth
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageLargeFrame
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding220
import com.moyu.chuanqirensheng.ui.theme.padding250
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding360
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.powerHeight
import com.moyu.chuanqirensheng.ui.theme.powerWidth
import com.moyu.chuanqirensheng.ui.theme.toQualityColor
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.QUICK_GAP
import com.moyu.chuanqirensheng.widget.common.CommonBar
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.Stars
import com.moyu.chuanqirensheng.widget.effect.MovableImage
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.Ally
import com.moyu.core.model.role.Role
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.ally_properties
import shared.generated.resources.bar_blue
import shared.generated.resources.bar_empty
import shared.generated.resources.battle_prop
import shared.generated.resources.common_arrow_left
import shared.generated.resources.common_arrow_right
import shared.generated.resources.common_big_frame
import shared.generated.resources.common_choose
import shared.generated.resources.name_label
import shared.generated.resources.skills
import shared.generated.resources.unlock_skill_10
import shared.generated.resources.unlock_skill_5


@Composable
fun AllyDetailScreen() {
    val currentAlly = repo.allyManager.currentShowAllyMainId.value.takeIf { it != -1 }?.let { mainId ->
        repo.allyManager.data.firstOrNull { it.mainId == mainId }
    }
    LaunchedEffect(currentAlly) {
        if (currentAlly != null) {
            repo.allyManager.setUnNew(currentAlly)
        } else {
            goto(OUT_ALLY)
        }
    }
    GameBackground(title = stringResource(Res.string.ally_properties)) {
        Box(
            modifier = Modifier.fillMaxSize().paint(
                painterResource(Res.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            ),
        )
        currentAlly?.let { ally ->
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AllyDetailTop(ally)
                AllyDetailBottom(ally)
            }
        }
    }
}

@Composable
fun AllyDetailBottom(ally: Ally) {
    Column(
        Modifier.fillMaxSize().background(B35).padding(horizontal = padding22),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val role = BattleManager.getMyRoleByAlly(ally)
        Spacer(Modifier.size(padding10))
        AllyPropertyLayout(role)
        Spacer(Modifier.size(padding4))
        AllySkillLayout(role, ally)
        Spacer(Modifier.weight(1f))
        Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
            AllyLevelUpView(ally = ally)
            AllyStarUpView(ally = ally)
        }
        Spacer(Modifier.height(padding26))
    }
}

@Composable
fun AllySkillLayout(role: Role, ally: Ally) {
    // 这个UI，英雄弹窗会用到，战斗弹窗也会用到，所以要区别处理下
    val skills = (if (ally.fixedSkills.first() == 0) emptyList() else ally.fixedSkills.map {
        repo.gameCore.getSkillById(
            it
        )
    }) + ally.extraSkills3.filter { it != 0 }.map {
        it * 100 + ally.star
    }.map { repo.gameCore.getSkillById(it) }
    val unlockedSkillSize =
        ally.fixedSkills.size + if (ally.star >= 10) ally.extraSkills3.size else if (ally.star >= 5) ally.extraSkills2.size else ally.extraSkills1.size
    StrokedText(text = stringResource(Res.string.skills), style = MaterialTheme.typography.h2)
    Spacer(Modifier.padding(vertical = padding8).height(padding4).width(padding360).background(W50))
    Row(
        Modifier.fillMaxWidth().padding(start = padding20),
        verticalAlignment = Alignment.CenterVertically
    ) {
        skills.forEachIndexed { index, skill ->
            SingleSkillView(
                skill = skill,
                showName = false,
                showStars = false,
                locked = index >= unlockedSkillSize,
                forceQuality = role.getAlly().quality
            ) {
                if (index >= unlockedSkillSize) {
                    if (skill.id / 100 in ally.extraSkills2) {
                        AppWrapper.getStringKmp(Res.string.unlock_skill_5).toast()
                    } else {
                        AppWrapper.getStringKmp(Res.string.unlock_skill_10).toast()
                    }
                }
                Dialogs.skillDetailDialog.value = it
            }
            Spacer(modifier = Modifier.size(padding8))
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AllyPropertyLayout(role: Role) {
    StrokedText(text = stringResource(Res.string.battle_prop), style = MaterialTheme.typography.h2)
    Spacer(Modifier.padding(vertical = padding8).height(padding4).width(padding360).background(W50))
    FlowRow(
        horizontalArrangement = Arrangement.spacedBy(padding80),
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = 2
    ) {
        role.getCurrentProperty().MainPropertyLine(
            textStyle = MaterialTheme.typography.h3,
            textColor = Color.White,
        )
    }
}

@Composable
fun AllyDetailTop(ally: Ally) {
    Box(Modifier.fillMaxWidth().height(padding380)) {
        Column(
            Modifier.align(Alignment.TopEnd).padding(padding12), horizontalAlignment = Alignment.End
        ) {
            CurrentAllyExpPoint()
            CurrentResourcesPoint(index = 0, showPlus = true)
        }
        Column(
            Modifier.align(Alignment.Center), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                Modifier.size(padding250, padding80).paint(
                    painterResource(Res.drawable.name_label),
                    contentScale = ContentScale.FillBounds
                ).padding(horizontal = padding4, vertical = padding2),
                contentAlignment = Alignment.Center,
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Row(modifier = Modifier.graphicsLayer {
                        translationX = -imageLargeFrame.toPx() / 2
                    }, verticalAlignment = Alignment.CenterVertically) {
                        if (ally.getGroupPic().isNotEmpty()) {
                            EffectButton(
                                modifier = Modifier.size(imageLarge), onClick = {
                                    ally.raceType2.toGroupTips().toast()
                                }) {
                                Image(
                                    modifier = Modifier.fillMaxSize(),
                                    painter = kmpPainterResource(ally.getGroupPic()),
                                    contentDescription = null
                                )
                            }
                        }
                        Spacer(Modifier.size(padding2))
                        EffectButton(onClick = {
                            ally.raceType.toRaceTips().toast()
                        }) {
                            Image(
                                painter = painterResource(
                                    ally.getTypeRes()
                                ), modifier = Modifier.size(imageMedium), contentDescription = null
                            )
                        }
                        Spacer(Modifier.size(padding2))
                        StrokedText(
                            text = ally.name,
                            style = MaterialTheme.typography.h1,
                            textAlign = TextAlign.Center
                        )
                    }
                    Spacer(Modifier.size(padding4))
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        StrokedText(
                            text = ally.quality.getQualityName(),
                            style = MaterialTheme.typography.h2,
                            color = ally.quality.toQualityColor()
                        )
                        Spacer(Modifier.size(padding12))
                        StrokedText(
                            text = "LV: ${ally.level}", style = MaterialTheme.typography.h2
                        )
                    }
                }
            }
            Box(Modifier.size(padding220)) {
                MovableImage(
                    modifier = Modifier.size(padding220).scale(ally.getScaleByQuality()),
                    imageResource = kmpDrawableResource(ally.pic),
                )
                if (ally.battlePosition >= 0) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .size(imageHugeLite),
                        painter = painterResource(Res.drawable.common_choose),
                        contentDescription = null
                    )
                }
            }
            Box(Modifier, contentAlignment = Alignment.Center) {
                CommonBar(
                    currentValue = ally.num,
                    maxValue = ally.getStarUpNum(),
                    modifier = Modifier.size(cardNumSmallWidth, cardNumSmallHeight),
                    fullRes = Res.drawable.bar_blue,
                    emptyRes = Res.drawable.bar_empty,
                    textColor = Color.White,
                    style = MaterialTheme.typography.h5
                )
                Stars(
                    Modifier.align(Alignment.CenterStart).size(imageMedium).graphicsLayer {
                        translationX = -padding8.toPx()
                    }, ally.star
                )
            }
            PowerLabel(
                Modifier.size(powerWidth, powerHeight), power = ally.getAllPower()
            )
        }
        SelectAllyArrows(Modifier.align(Alignment.Center))
    }
}


@Composable
fun SelectAllyArrows(modifier: Modifier) {
    Row(modifier, horizontalArrangement = Arrangement.SpaceEvenly) {
        EffectButton(clickGap = QUICK_GAP, onClick = {
            repo.allyManager.selectPrev()
        }) {
            Image(
                modifier = Modifier.size(imageHugeLite),
                painter = painterResource(Res.drawable.common_arrow_left),
                contentDescription = null
            )
        }
        Spacer(modifier = Modifier.weight(1f))
        EffectButton(clickGap = QUICK_GAP, onClick = {
            repo.allyManager.selectNext()
        }) {
            Image(
                modifier = Modifier.size(imageHugeLite),
                painter = painterResource(Res.drawable.common_arrow_right),
                contentDescription = null
            )
        }
    }
}
