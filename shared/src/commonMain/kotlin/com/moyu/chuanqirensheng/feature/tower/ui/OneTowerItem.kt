package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.role.createTowerEnemyRole
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.tower.getTypeName
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding140
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding54
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding640
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.MovableImage
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Award
import com.moyu.core.model.Tower
import com.moyu.core.model.getRaceTypeName
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.challenge
import shared.generated.resources.common_choose
import shared.generated.resources.tower_level
import shared.generated.resources.tower_stage_frame
import shared.generated.resources.tower_tips3
import shared.generated.resources.tower_tips4

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun OneTowerItem(tower: Tower, callback: () -> Unit) {
    Box(
        modifier = Modifier
            .height(padding640)
            .fillMaxWidth()
            .paint(
                painter = painterResource(Res.drawable.tower_stage_frame),
                contentScale = ContentScale.FillBounds,
            )
    ) {
        StrokedText(
            modifier = Modifier
                .align(Alignment.TopCenter).padding(top = padding19),
            text = stringResource(Res.string.tower_level, tower.id),
            style = MaterialTheme.typography.h1
        )
        val pool = repo.gameCore.getPoolById(tower.playPara2)
        val enemies = pool.pool.map {
            repo.gameCore.getAllyById(it)
        }.map {
            createTowerEnemyRole(it, tower)
        }
        Box(
            modifier = Modifier.align(Alignment.Center).graphicsLayer {
                translationY = padding12.toPx()
            },  // 根据需求增减
        ) {
            EffectButton(onClick = {
                Dialogs.roleDetailDialog.value = enemies.first()
            }) {
                MovableImage(Modifier.size(padding140),
                    imageResource = kmpDrawableResource(enemies.first().getAlly().pic),
                )
            }

        }
        AwardList(
            modifier = Modifier.align(Alignment.TopCenter).padding(top = padding72),
            award = Award(diamond = repo.gameCore.getTowerAwardKey(tower.layer)),
            param = defaultParam.copy(itemSize = ItemSize.Large)
        )
        Column(
            modifier = Modifier.align(Alignment.BottomCenter).padding(bottom = padding54),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                StrokedText(
                    text = tower.getTypeName(),
                    style = MaterialTheme.typography.h1,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.weight(1f)

                )
            }

            if (tower.type.first() == 2) {
                val haloPool = repo.gameCore.getSkillById(tower.playPara1.first())
                haloPool.apply {
                    SingleSkillView(
                        Modifier,
                        this,
                        itemSize = ItemSize.Medium,
                        textColor = Color.White
                    )
                }
            } else if (tower.type.first() == 3) {
                StrokedText(
                    text = stringResource(
                        Res.string.tower_tips3,
                        tower.playPara1.first().getRaceTypeName()
                    ),
                    style = MaterialTheme.typography.h2,
                    textAlign = TextAlign.Center
                )
            } else if (tower.type.first() == 4) {
                StrokedText(
                    text = stringResource(Res.string.tower_tips4, tower.playPara1.first()),
                    style = MaterialTheme.typography.h2,
                    textAlign = TextAlign.Center
                )
            }
            Box(contentAlignment = Alignment.Center) {
                GameButton(
                    text = stringResource(Res.string.challenge),
                    buttonSize = ButtonSize.Huge,
                    enabled = TowerManager.maxLevel.value + 1 == tower.id,
                    onClick = callback,
                )
                if (TowerManager.maxLevel.value >= tower.id) {
                    Image(
                        painter = painterResource(Res.drawable.common_choose),
                        modifier = Modifier
                            .size(imageLarge)
                            .align(Alignment.BottomEnd),
                        contentDescription = null
                    )
                }
                if (GuideManager.guideIndex.value == 37) {
                    GuideHand(
                        modifier = Modifier.align(Alignment.BottomCenter).height(padding80).graphicsLayer {
                            translationY = padding60.toPx()
                        },
                        handType = HandType.UP_HAND
                    )
                }
            }
        }
    }
}