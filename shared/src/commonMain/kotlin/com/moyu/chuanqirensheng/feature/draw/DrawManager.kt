package com.moyu.chuanqirensheng.feature.draw

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager.onDrawUnlockId
import com.moyu.chuanqirensheng.feature.quest.onTaskDrawAllyCard
import com.moyu.chuanqirensheng.feature.quest.onTaskDrawHeroCard
import com.moyu.chuanqirensheng.feature.sell.SellManager.isDrawing
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_ALLY_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_ALLY_COUPON_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_HERO_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_HERO_COUPON_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_FIRST_DRAW
import com.moyu.chuanqirensheng.sub.datastore.KEY_ONE_TURN_CONSUME_ALLY_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_ONE_TURN_CONSUME_HERO_COUPON
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.toAward
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlin.random.Random

const val MUST_ORANGE_DRAW = 70
const val FIRST_DRAW_ALLY_ID = 1006
const val NO_RED_DRAW = 40

object DrawManager {
    fun getAllyCouponAward(index: Int, forceGreenAndBlue: Boolean = false): Award {
        val drawItems = repo.gameCore.getDrawPool().filter { it.isDrawAlly() }
        // 计算所有符合条件抽奖项的总概率
        val totalRate = drawItems.sumOf { it.rate }
        // 生成一个随机数 [0, totalRate) 之间
        val randomValue = randomAlly(index).nextDouble(0.0, totalRate)
        // 遍历抽奖项，根据概率选择
        var cumulativeRate = 0.0
        drawItems.forEach { item ->
            cumulativeRate += item.rate
            if (randomValue <= cumulativeRate) {
                val result = repo.gameCore.getPoolById(item.pool).toAward(random = randomAlly(index))
                if (forceGreenAndBlue) {
                    if (result.outAllies.any { it.quality >= 3 }) {
                        if (isLite()) {
                            "原本${result.outAllies.first().id}，但是第一抽不能红黄，重新随机".toast()
                        }
                        var newResult: Award
                        do {
                            newResult = repo.gameCore.getPoolById(drawItems.first().pool).toAward()
                        } while (newResult.outAllies.any { it.quality >= 3 })
                        return newResult
                    } else {
                        return result
                    }
                } else if (getIntFlowByKey(KEY_CONSUME_ALLY_COUPON_ALL) < NO_RED_DRAW) {
                    // 15抽内不出红卡
                    if (result.outAllies.any { it.quality >= 4 }) {
                        if (isLite()) {
                            "原本${result.outAllies.first().id}，但是不能红卡，重新随机".toast()
                        }
                        return getAllyOrangeAward()
                    } else {
                        return result
                    }
                } else {
                    return result
                }
            }
        }
        return Award()
    }

    fun getAllyOrangeAward(): Award {
        val drawItems = repo.gameCore.getDrawPool().filter { it.isDrawAlly() }
        // 固定出黄色，不能出红色，所以是倒数第二个
        return repo.gameCore.getPoolById(drawItems.dropLast(1).last().pool).toAward(random = randomAlly())
    }

    fun getHeroCouponAward(index: Int): Award {
        val drawItems = repo.gameCore.getDrawPool().filter { it.isDrawEquip() }
        // 计算所有符合条件抽奖项的总概率
        val totalRate = drawItems.sumOf { it.rate }
        // 生成一个随机数 [0, totalRate) 之间
        val randomValue = randomHero(index).nextDouble(0.0, totalRate)
        // 遍历抽奖项，根据概率选择
        var cumulativeRate = 0.0
        drawItems.forEach { item ->
            cumulativeRate += item.rate
            if (randomValue <= cumulativeRate) {
                val result = repo.gameCore.getPoolById(item.pool).toAward(random = randomHero(index))
                if (getIntFlowByKey(KEY_CONSUME_HERO_COUPON_ALL) < NO_RED_DRAW) {
                    // 15抽内不出红卡
                    if (result.outEquips.any { it.quality >= 4 }) {
                        if (isLite()) {
                            "原本${result.outEquips.first().id}改掉了".toast()
                        }
                        return getHeroOrangeAward()
                    } else {
                        return result
                    }
                } else {
                    return result
                }
            }
        }
        return Award()
    }

    fun getHeroOrangeAward(): Award {
        val drawItems = repo.gameCore.getDrawPool().filter { it.isDrawEquip() }
        return repo.gameCore.getPoolById(drawItems.dropLast(1).last().pool).toAward(random = randomHero())
    }

    suspend fun buyAllyCoupon(repeat: Int = 1) {
        if (isDrawing) {
            return
        }
        isDrawing = true
        GameCore.instance.onBattleEffect(SoundEffect.Draw)
        var totalAward = Award()
        repeat(repeat) {
            var this10DrawHaveOrange = false
            val award = (1..10).map {
                // 确保橙卡
                val singleAward = if (getBooleanFlowByKey(KEY_FIRST_DRAW, true) && it == 10) {
                    Award(outAllies = listOf(repo.gameCore.getAlly(FIRST_DRAW_ALLY_ID, 1, 1)))
                } else if (needOrangeAllyThisTurn() && it == 10 && !this10DrawHaveOrange) {
                    getAllyOrangeAward()
                } else getAllyCouponAward(it, forceGreenAndBlue = getBooleanFlowByKey(KEY_FIRST_DRAW, true))
                this10DrawHaveOrange = if (!this10DrawHaveOrange) singleAward.outAllies.any { it.quality >= 3 } else true
                totalAward += singleAward
                Dialogs.drawResultDialog.value = totalAward
                // delay给main线程时间去更新UI
                delay(100)
                singleAward
            }.reduce { acc, award -> acc + award }
            if (this10DrawHaveOrange) {
                setAllyOrangeDraw(0)
            } else {
                setAllyOrangeDraw(getIntFlowByKey(KEY_ONE_TURN_CONSUME_ALLY_COUPON) + 10)
            }
            setBooleanValueByKey(KEY_FIRST_DRAW, false)
            increaseIntValueByKey(KEY_CONSUME_ALLY_COUPON, 1)
            increaseIntValueByKey(KEY_CONSUME_ALLY_COUPON_ALL, 1)
            AppWrapper.globalScope.async {
                if (AwardManager.couponAlly.value < 10) {
                    val lack = 10 - AwardManager.couponAlly.value
                    AwardManager.gainAward(
                        award.copy(
                            couponAlly = -AwardManager.couponAlly.value,
                            key = -lack * repo.gameCore.getAllyCouponRate()
                        )
                    )
                } else {
                    AwardManager.gainAward(award.copy(couponAlly = -10))
                }
                award.outAllies.map { it.id }.forEach {
                    onDrawUnlockId(it)
                }
                delay(600)
                if (award.outAllies.any { it.quality == 4 }) {
                    MusicManager.playSound(SoundEffect.DrawRed)
                } else if (award.outAllies.any { it.quality == 3 }) {
                    MusicManager.playSound(SoundEffect.DrawOrange)
                }
            }
        }
        onTaskDrawAllyCard(10 * repeat)
        isDrawing = false
    }

    suspend fun buyHeroCoupon(repeat: Int = 1) {
        if (isDrawing) {
            return
        }
        isDrawing = true
        GameCore.instance.onBattleEffect(SoundEffect.Draw)
        var totalAward = Award()
        repeat(repeat) {
            var this10DrawHaveOrange = false
            val award = (1..10).map {
                val singleAward = if (needOrangeHeroThisTurn() && it == 10 && !this10DrawHaveOrange) {
                    getHeroOrangeAward()
                } else getHeroCouponAward(it)
                this10DrawHaveOrange = if (!this10DrawHaveOrange) singleAward.outEquips.any { it.quality >= 3 } else true
                totalAward += singleAward
                Dialogs.drawResultDialog.value = totalAward
                // delay给main线程时间去更新UI
                delay(100)
                singleAward
            }.reduce { acc, award -> acc + award }
            if (this10DrawHaveOrange) {
                setHeroOrangeDraw(0)
            } else {
                setHeroOrangeDraw(getIntFlowByKey(KEY_ONE_TURN_CONSUME_HERO_COUPON) + 10)
            }
            increaseIntValueByKey(KEY_CONSUME_HERO_COUPON, 1)
            increaseIntValueByKey(KEY_CONSUME_HERO_COUPON_ALL, 1)
            AppWrapper.globalScope.async {
                if (AwardManager.couponHero.value < 10) {
                    val lack = 10 - AwardManager.couponHero.value
                    AwardManager.gainAward(
                        award.copy(
                            couponEquip = -AwardManager.couponHero.value,
                            key = -lack * repo.gameCore.getHeroCouponRate()
                        )
                    )
                } else {
                    AwardManager.gainAward(award.copy(couponEquip = -10))
                }
                award.outEquips.map { it.id }.forEach {
                    onDrawUnlockId(it)
                }
                delay(600)
                if (award.outEquips.any { it.quality == 4 }) {
                    MusicManager.playSound(SoundEffect.DrawRed)
                } else if (award.outEquips.any { it.quality == 3 }) {
                    MusicManager.playSound(SoundEffect.DrawOrange)
                }
            }
        }
        onTaskDrawHeroCard(10 * repeat)
        isDrawing = false
    }

    fun randomAlly(index: Int = 0): Random {
        return Random(
            (gameSdkDefaultProcessor().getObjectId()?: "empty").toCharArray().sumOf { it.code } + getIntFlowByKey(
                KEY_CONSUME_ALLY_COUPON_ALL
            ).toLong() * 951 + index * 63917 + LoginManager.instance.loginData.value.serverData.serverId)
    }

    fun randomHero(index: Int = 0): Random {
        return Random(
            (gameSdkDefaultProcessor().getObjectId()?: "empty").toCharArray().sumOf { it.code } + getIntFlowByKey(
                KEY_CONSUME_HERO_COUPON_ALL
            ).toLong()* 1209 + index * 17277 + LoginManager.instance.loginData.value.serverData.serverId)
    }

    fun getAllyLeftDraw(): Int {
        return MUST_ORANGE_DRAW - getIntFlowByKey(KEY_ONE_TURN_CONSUME_ALLY_COUPON)
    }

    fun getHeroLeftDraw(): Int {
        return MUST_ORANGE_DRAW - getIntFlowByKey(KEY_ONE_TURN_CONSUME_HERO_COUPON)
    }

    fun needOrangeAllyThisTurn(): Boolean {
        return getAllyLeftDraw() <= 10
    }

    fun needOrangeHeroThisTurn(): Boolean {
        return getHeroLeftDraw() <= 10
    }

    fun setAllyOrangeDraw(value: Int) {
        setIntValueByKey(KEY_ONE_TURN_CONSUME_ALLY_COUPON, value)
    }

    fun setHeroOrangeDraw(value: Int) {
        setIntValueByKey(KEY_ONE_TURN_CONSUME_HERO_COUPON, value)
    }
}
