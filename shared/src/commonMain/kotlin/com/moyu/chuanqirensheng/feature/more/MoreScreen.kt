package com.moyu.chuanqirensheng.feature.more

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.router.FEEDBACK_SCREEN
import com.moyu.chuanqirensheng.feature.router.MAILS_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.sub.saver.CloudSaverDefault
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.filterHeight
import com.moyu.chuanqirensheng.ui.theme.filterWidth
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.textFieldHeight
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.button.QUICK_GAP
import com.moyu.chuanqirensheng.widget.common.DecorateTextField
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.IconView
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.VersionTag
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.button_gray_long
import shared.generated.resources.button_green_long
import shared.generated.resources.common_big_frame
import shared.generated.resources.exchange
import shared.generated.resources.feed_back
import shared.generated.resources.mails
import shared.generated.resources.more
import shared.generated.resources.red_icon
import shared.generated.resources.server
import shared.generated.resources.setting
import shared.generated.resources.setting_icon
import shared.generated.resources.switch
import shared.generated.resources.switch_server
import shared.generated.resources.switch_server_tips

data class MoreItem(
    val name: String,
    val route: () -> Unit,
    val icon: String,
    val unlock: () -> Boolean = { true },
    val red: () -> Boolean = { false }
)

val moreItems = listOf(
    MoreItem(
        name = AppWrapper.getStringKmp(Res.string.setting),
        route = { Dialogs.settingDialog.value = true },
        icon = "setting_icon"
    ),
    MoreItem(
        name = AppWrapper.getStringKmp(Res.string.mails),
        route = { goto(MAILS_SCREEN) },
        icon = "mail_icon"
    ),
    MoreItem(
        name = AppWrapper.getStringKmp(Res.string.feed_back),
        route = { goto(FEEDBACK_SCREEN) },
        icon = "feedback_icon"
    ),
)

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun MoreScreen() {
    GameBackground(title = stringResource(Res.string.more)) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding10, horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(padding28))
            Spacer(modifier = Modifier.size(padding19))
            FlowRow(
                modifier = Modifier.padding(horizontal = padding4),
                maxItemsInEachRow = 3,
                verticalArrangement = Arrangement.spacedBy(padding16),
                horizontalArrangement = Arrangement.spacedBy(padding5),
                overflow = FlowRowOverflow.Visible,
            ) {
                moreItems.filter { it.unlock() }.forEach {
                    OneItem(modifier = Modifier.width(padding120), it)
                }
            }
            val text = remember {
                mutableStateOf("")
            }
            Spacer(modifier = Modifier.size(padding36))
            Column(
                modifier = Modifier
                    .align(Alignment.Start)
                    .padding(horizontal = padding26)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    DecorateTextField(
                        modifier = Modifier
                            .weight(1f)
                            .height(
                                textFieldHeight
                            ), text = text.value
                    ) {
                        text.value = it
                    }
                    Spacer(modifier = Modifier.size(padding10))
                    GameButton(
                        enabled = text.value.isNotEmpty(),
                        onClick = {
                            AwardManager.doNetAward(text.value.trim())
                        },
                        text = stringResource(Res.string.exchange),
                        textColor = Color.White,
                        buttonStyle = ButtonStyle.Green,
                        buttonSize = ButtonSize.Medium
                    )
                }
                Spacer(modifier = Modifier.size(padding10))
            }
            Spacer(modifier = Modifier.size(padding10))
        }
        val showServerSelector = remember {
            mutableStateOf(false)
        }

        Row(
            Modifier.align(Alignment.BottomCenter).fillMaxWidth().padding(padding20),
            verticalAlignment = Alignment.CenterVertically
        ) {
            VersionTag()
            Spacer(Modifier.weight(1f))
            if (LoginManager.instance.loginData.value.serverList.size > 1) {
                ServerView(showServerSelector)
            }
        }
        ServerSelectorLayout(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(padding20),
            show = showServerSelector,
            callback = {
                if (LoginManager.instance.getSavedServerId() != it) {
                    Dialogs.alertDialog.value = CommonAlert(
                        title = AppWrapper.getStringKmp(Res.string.switch_server),
                        content = AppWrapper.getStringKmp(Res.string.switch_server_tips),
                        confirmText = AppWrapper.getStringKmp(Res.string.switch),
                        onConfirm = {
                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                CloudSaverDefault.switchServerId(it)
                            }
                        }
                    )
                }
            }
        )
    }
}

@Composable
fun ServerView(showServerSelector: MutableState<Boolean>) {
    EffectButton(onClick = {
        showServerSelector.value = !showServerSelector.value
    }) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            StrokedText(
                modifier = Modifier,
                text = stringResource(Res.string.server) + (LoginManager.instance.getSavedServerId() + 1),
                style = MaterialTheme.typography.h2,
            )
            Image(
                modifier = Modifier.size(imageSmallPlus),
                painter = painterResource(Res.drawable.setting_icon),
                contentDescription = stringResource(Res.string.server),
            )
        }
    }
}


@Composable
fun ServerSelectorLayout(
    modifier: Modifier,
    show: MutableState<Boolean>,
    callback: ((Int) -> Unit)? = null
) {
    if (show.value) {
        Box(
            modifier = Modifier
            .fillMaxSize()
            .clickable {
                show.value = false
            }
            .background(B50))
    }
    AnimatedVisibility(
        modifier = modifier, visible = show.value
    ) {
        Column(modifier = Modifier.verticalScroll(rememberScrollState())) {
            LoginManager.instance.loginData.value.serverList.forEach { server ->
                val selected = server.serverId == LoginManager.instance.getSavedServerId()
                EffectButton(clickGap = QUICK_GAP, onClick = {
                    callback?.invoke(server.serverId)
                }) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.padding(bottom = padding2)
                    ) {
                        val res = if (selected) Res.drawable.button_green_long
                        else Res.drawable.button_gray_long
                        Image(
                            modifier = Modifier.size(filterWidth, filterHeight),
                            painter = painterResource(res),
                            contentScale = ContentScale.FillBounds,
                            contentDescription = null
                        )
                        StrokedText(
                            text = stringResource(Res.string.server) + (server.serverId + 1),
                            style = MaterialTheme.typography.h3,
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun OneItem(
    modifier: Modifier,
    moreItem: MoreItem,
    callback: () -> Unit = {}
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Box {
            IconView(
                itemSize = ItemSize.LargePlus,
                res = kmpDrawableResource(moreItem.icon),
                frame = null,
                clipShape = RoundedCornerShape(padding10),
                resZIndex = 99f
            ) {
                callback()
                moreItem.route()
            }
            if (moreItem.red()) {
                Image(
                    modifier = Modifier
                        .size(imageSmall)
                        .align(Alignment.TopEnd),
                    painter = kmpPainterResource(Res.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
        StrokedText(
            modifier = Modifier.graphicsLayer {
                translationY = padding4.toPx()
            },
            text = moreItem.name,
            style = MaterialTheme.typography.h2,
            textAlign = TextAlign.Center
        )
    }
}
