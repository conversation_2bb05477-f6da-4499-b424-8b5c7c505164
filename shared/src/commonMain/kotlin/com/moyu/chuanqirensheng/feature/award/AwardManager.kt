package com.moyu.chuanqirensheng.feature.award

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.toMutableStateList
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.Dialogs.accountLevelUpDialog
import com.moyu.chuanqirensheng.application.powerToast
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.battle.MAX_BATTLE_SIZE
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.quest.onTaskGetItem
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.sell.SELL_FOREVER
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.sell.SellManager.isDoubleGained
import com.moyu.chuanqirensheng.feature.sell.SellManager.setDoubleGained
import com.moyu.chuanqirensheng.feature.story.toReputationName
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkAlly
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.anticheat.GuardedB
import com.moyu.chuanqirensheng.sub.anticheat.GuardedL
import com.moyu.chuanqirensheng.sub.anticheat.GuardedList
import com.moyu.chuanqirensheng.sub.datastore.KEY_ACCOUNT_EXP
import com.moyu.chuanqirensheng.sub.datastore.KEY_AD_MONEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_AD_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_ALLY_EXP
import com.moyu.chuanqirensheng.sub.datastore.KEY_COUPON_ALLY
import com.moyu.chuanqirensheng.sub.datastore.KEY_COUPON_HERO
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIAMOND
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIAMOND_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC
import com.moyu.chuanqirensheng.sub.datastore.KEY_FIRST_CHARGE_DONE
import com.moyu.chuanqirensheng.sub.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.KEY_HOLIDAY_MONEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY_COST
import com.moyu.chuanqirensheng.sub.datastore.KEY_LOTTERY_MONEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_POWER
import com.moyu.chuanqirensheng.sub.datastore.KEY_POWER_INC_TIME
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_DIAMOND
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_DIAMOND_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_REAL_MONEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPUTATIONS
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPUTATION_MONEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_RESOURCES
import com.moyu.chuanqirensheng.sub.datastore.KEY_TALENT_POINT
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS1
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS1_BOUGHT
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS2
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS2_BOUGHT
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS3
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS3_BOUGHT
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS4
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.sub.saver.CloudSaverDefault
import com.moyu.chuanqirensheng.util.AdUtil.decodeText
import com.moyu.chuanqirensheng.util.AdUtil.encodeText
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_REPUTATION
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.MASTER_MAIN_ID
import com.moyu.core.model.Sell
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.perBiggerI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import shared.generated.resources.Res
import shared.generated.resources.code_already_used_by_you
import shared.generated.resources.code_error
import shared.generated.resources.code_error_tips2
import shared.generated.resources.code_not_found
import shared.generated.resources.confirm_login
import shared.generated.resources.reputation_level_up_tips
import shared.generated.resources.unlocked_tips
import kotlin.math.max
import kotlin.math.min

const val MAX_ACCOUNT_LEVEL = 500
const val INIT_ALLY_COUPON = 10

object AwardManager {
    val battlePass1Bought = GuardedB(KEY_WAR_PASS1_BOUGHT)
    val battlePass2Bought = GuardedB(KEY_WAR_PASS2_BOUGHT)
    val battlePass3Bought = GuardedB(KEY_WAR_PASS3_BOUGHT)

    val diamond = Guarded(KEY_DIAMOND)
    val diamondAll = Guarded(KEY_DIAMOND_ALL)

    val keyCost = Guarded(KEY_KEY_COST)
    val key = Guarded(KEY_KEY)
    val keyAll = Guarded(KEY_KEY_ALL)

    val couponAlly = Guarded(KEY_COUPON_ALLY, mutableIntStateOf(INIT_ALLY_COUPON))
    val couponHero = Guarded(KEY_COUPON_HERO)

    val electric = Guarded(KEY_ELECTRIC)

    val pvpDiamond = Guarded(KEY_PVP_DIAMOND)
    val pvpDiamondAll = Guarded(KEY_PVP_DIAMOND_ALL)

    val warPass1 = Guarded(KEY_WAR_PASS1)
    val warPass2 = Guarded(KEY_WAR_PASS2)
    val warPass3 = Guarded(KEY_WAR_PASS3)
    val warPass4 = Guarded(KEY_WAR_PASS4)

    val lotteryMoney = Guarded(KEY_LOTTERY_MONEY)
    val holidayMoney = Guarded(KEY_HOLIDAY_MONEY)

    val realMoney = Guarded(KEY_REAL_MONEY)
    val reputationMoney = Guarded(KEY_REPUTATION_MONEY)
    val reputations = GuardedList(KEY_REPUTATIONS, EMPTY_REPUTATION.toMutableStateList())
    val accountExp = Guarded(KEY_ACCOUNT_EXP)
    val allyExp = Guarded(KEY_ALLY_EXP)
    val talentPoint = Guarded(KEY_TALENT_POINT)

    val resources = GuardedList(KEY_RESOURCES, EMPTY_RESOURCES.toMutableStateList())
    val lastPowerIncTime = GuardedL(KEY_POWER_INC_TIME)
    val power = Guarded(KEY_POWER, mutableIntStateOf(repo.gameCore.getMaxPower()))
    val adMoney = Guarded(KEY_AD_MONEY)
    val adNum = Guarded(KEY_AD_NUM)

    fun init() {

    }

    fun getMasterAlly(): Ally {
        return repo.gameCore.getAllyPool(MASTER_MAIN_ID).first { it.level == getMasterLevel() }
    }

    /**
     * OpenChest,
     *     PropertyAward,
     *     ReputationAward,
     *     ResourcesAward,
     *     DiamondAward,
     *     MoneyAward,
     *     HealAward,
     *     AllyExpAward,
     *     EquipAward,
     *     AllyAward,
     *     Resources9Award,
     */
    suspend fun gainAward(award: Award) {
        withContext(Dispatchers.Main) {
            onTaskGetItem(award)
            BattleManager.addBattleAward(award)
            award.diamond.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.DiamondAward)
                gainDiamond(it)
            }
            award.pvpDiamond.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.DiamondAward)
                gainPvpDiamond(it)
            }
            award.pvpScore.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.ReputationAward)
                PvpManager.pvpScore.value += it
                val oldLevel = PvpManager.getRankLevel().id
                PvpManager.pvpTotalScore.value += it
                val newLevel = PvpManager.getRankLevel().id
                if (newLevel > oldLevel) {
                    Dialogs.pvpLevelUpDialog.value = true
                }
            }
            award.pvp2Score.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.ReputationAward)
                Pvp2Manager.pvpScore.value += it
            }
            award.talentPoint.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.ReputationAward)
                gainTalentPoint(it)
            }
            award.adMoney.takeIf { it > 0 }?.let {
                MusicManager.playSound(SoundEffect.MoneyAward)
                adMoney.value += it
            }
            if (award.resources.any { it != 0 }) {
                if (resources[7] > 0) {
                    MusicManager.playSound(SoundEffect.Resources8Award)
                } else {
                    MusicManager.playSound(SoundEffect.ResourcesAward)
                }
                gainResources(award.resources)
            }
            if (award.reputations.any { it != 0 }) {
                MusicManager.playSound(SoundEffect.ReputationAward)
                gainReputations(award.reputations)
            }
            award.key.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.DiamondAward)
                gainKey(it)
            }
            award.lotteryMoney.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.MoneyAward)
                lotteryMoney.value += it
            }
            award.couponAlly.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.AllyAward)
                couponAlly.value += it
            }
            award.couponEquip.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.EquipAward)
                couponHero.value += it
            }
            award.electric.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.MoneyAward)
                gainElectric(it)
            }
            award.reputationMoney.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.MoneyAward)
                reputationMoney.value += it
            }
            award.realMoney.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.MoneyAward)
                realMoney.value += it
            }
            award.holidayMoney.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.MoneyAward)
                holidayMoney.value += it
            }
            award.warPass1.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.AllyExpAward)
                warPass1.value += it
            }
            award.warPass2.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.AllyExpAward)
                warPass2.value += it
            }
            award.warPass3.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.AllyExpAward)
                warPass3.value += it
            }
            award.warPass4.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.AllyExpAward)
                warPass4.value += it
            }
            award.power.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.AllyExpAward)
                gainPower(it)
            }
            award.allyExp.takeIf { it != 0 }?.let {
                MusicManager.playSound(SoundEffect.AllyExpAward)
                gainAllyExp(it)
            }
            award.accountExp.takeIf { it != 0 }?.let { rawExp ->
                MusicManager.playSound(SoundEffect.AllyExpAward)
                gainAccountExp(rawExp)
            }
            award.allHeal.takeIf { it > 0 }?.let {
                MusicManager.playSound(SoundEffect.HealAward)
                BattleManager.healAllAllyInGame(it)
            }
            award.allies.forEach { ally ->
                MusicManager.playSound(SoundEffect.AllyAward)
                BattleManager.gainInGame(ally)
            }
            award.outEquips.forEach { equip ->
                MusicManager.playSound(SoundEffect.EquipAward)
                repo.equipManager.gain(equip.create().copy(new = true))
            }
            award.outAllies.forEach { ally ->
                checkAlly(ally)
                MusicManager.playSound(SoundEffect.AllyAward)
                repo.allyManager.gain(ally.create().copy(new = true))
                if (BattleManager.getGameAllies().none { it.mainId == ally.mainId }
                    && BattleManager.getGameAllies().size < MAX_BATTLE_SIZE) {
                    BattleManager.selectToGame(ally, BattleManager.availablePosition())
                }
            }
            award.skills.forEach { skill ->
                MusicManager.playSound(SoundEffect.PropertyAward)
                BattleManager.gainSkillInGame(skill.copy(new = false))
            }
            award.advProperty.takeIf { it.isNotEmpty() }?.let { property ->
                MusicManager.playSound(SoundEffect.PropertyAward)
                BattleManager.gainAdventureProp(property)
            }
            award.battleProperty.takeIf { it.isNotEmpty() }?.let { properties ->
                if (properties.any { it.isBad() }) {
                    MusicManager.playSound(SoundEffect.DoublePunish)
                } else {
                    MusicManager.playSound(SoundEffect.DoubleReward)
                }
                properties.forEach {
                    BattleManager.gainBattleProp(it)
                }
            }
            award.unlockList.forEach {
                UnlockManager.unlockCode(it)
            }
        }
    }

    fun gainAccountExp(gain: Int) {
        val oldLevel = getMasterLevel()
        val oldAlly = getMasterAlly()
        accountExp.value += gain
        val newLevel = getMasterLevel()
        if (newLevel > oldLevel) {
            accountLevelUpDialog.value = true
            oldAlly.getLevelUpPowerDiff().powerToast()
        }
    }

    fun doNetAward(text: String) {
        AppWrapper.globalScope.launch {
            // 确认是否是网络兑换码
            val netCode = decodeText(text)
            if (netCode?.startsWith("u_") == true) {
                val flow = getLongFlowByKey(text)
                if (flow != 0L) {
                    AppWrapper.getStringKmp(Res.string.code_already_used_by_you).toast()
                } else {
                    gameSdkDefaultProcessor().getObjectId()?.let {
                        encodeText(it)?.let { encodedUserId ->
                            val result =
                                RetrofitModel.getCodeAwards(
                                    it,
                                    netCode,
                                    encodedUserId,
                                    getVersionCode()
                                )
                            if (result.resultCode != 0) {
                                if (result.resultCode == 1 || result.resultCode == 2) {
                                    // todo 后续改成根据code支持多语言
                                    result.message.toast()
                                } else {
                                    AppWrapper.getStringKmp(Res.string.code_error_tips2).toast()
                                }
                            } else {
                                // 标记已领取
                                setLongValueByKey(text, 1)
                                if (result.unlockList.isNotEmpty()) {
                                    AppWrapper.getStringKmp(Res.string.unlocked_tips).toast()
                                } else if (result.message.isNotEmpty()) {
                                    result.message.toast()
                                }
                                if (!result.isEmpty()) {
                                    val sell = repo.gameCore.getSellPool()
                                        .firstOrNull { it.id == result.sellId }
                                    if (sell != null) {
                                        realGainItem(sell, result)
                                    } else {
                                        gainAward(result)
                                        Dialogs.awardDialog.value = result
                                    }
                                }
                            }
                        } ?: AppWrapper.getStringKmp(Res.string.code_error).toast()
                    } ?: AppWrapper.getStringKmp(Res.string.confirm_login).toast()
                }
            } else {
                AppWrapper.getStringKmp(Res.string.code_not_found).toast()
            }
        }
    }

    fun gainDiamond(gain: Int) {
        diamond.value += gain
        diamondAll.value += gain
        if (gain <= 0) {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainAllyExp(gain: Int) {
        allyExp.value += gain
        if (gain < 0) {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }


    fun gainPvpDiamond(gain: Int) {
        pvpDiamond.value += gain
        pvpDiamondAll.value += gain
        if (gain < 0) {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainRealMoney(gain: Int) {
        realMoney.value += gain
        if (gain < 0) {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainReputationMoney(gain: Int) {
        reputationMoney.value += gain
        if (gain < 0) {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainPower(gainedPower: Int) {
        val isPowerCurrentFull = power.value >= repo.gameCore.getMaxPower()
        power.value += gainedPower
        if (gainedPower <= 0) {
            if (isPowerCurrentFull && power.value < repo.gameCore.getMaxPower()) {
                // 消耗体力前体力已满，消耗体力后，体力不满，要更新计时器
                lastPowerIncTime.value = getCurrentTime()
            }
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)

        }
    }

    fun gainKey(gain: Int) {
        key.value += gain
        keyAll.value += gain
        if (gain <= 0) {
            keyCost.value += -gain
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainTalentPoint(gain: Int) {
        talentPoint.value += gain
        if (gain <= 0) {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainResources(gainedResources: List<Int>) {
        gainedResources.forEachIndexed { index, resource ->
            if (resource != 0) {
                resources[index] = resources[index] + resource
            }
            if (resource < 0) {
                GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
            }
        }
    }

    fun gainReputations(gainedReputations: List<Int>) {
        gainedReputations.forEachIndexed { index, reputation ->
            if (reputation != 0) {
                val oldLevel = ReputationManager.getReputationLevel(reputations[index])
                reputations[index] = reputations[index] + reputation
                val newLevel = ReputationManager.getReputationLevel(reputations[index])
                if (oldLevel != newLevel) {
                    ((index + 1).toReputationName() + AppWrapper.getStringKmp(Res.string.reputation_level_up_tips) + newLevel).toast()
                }
            }

            if (reputation <= 0) {
                if (reputation < 0) {
                    GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
                }
            }
        }
    }

    suspend fun gainElectric(gain: Int) {
        withContext(Dispatchers.Main) {
            val oldVipLevel = VipManager.getVipLevel()
            electric.value += gain
            SevenDayManager.gainElectric(gain)
            HolidayManager.gainElectric(gain)
            setBooleanValueByKey(KEY_FIRST_CHARGE_DONE, true)
            val newVipLevel = VipManager.getVipLevel()
            if (newVipLevel > oldVipLevel) {
                // 升级了VIP
                Dialogs.vipLevelUpDialog.value = true
            }
            // 付费后强制上传一次存档
            AppWrapper.globalScope.launch(Dispatchers.IO) {
                async {
                    delay(2000)
                    CloudSaverDefault.uploadCurrentSave()
                }
            }
        }
    }

    fun gainHolidayMoney(gain: Int) {
        holidayMoney.value += gain
    }

    fun isAffordable(award: Award, consume: Boolean = false): Boolean {
        if (award.diamond > 0 && award.diamond > diamond.value) {
            return false
        }
        if (award.pvpDiamond > 0 && award.pvpDiamond > pvpDiamond.value) {
            return false
        }
        if (award.key > 0 && award.key > key.value) {
            return false
        }
        if (award.talentPoint > 0 && award.talentPoint > talentPoint.value) {
            return false
        }
        award.resources.forEachIndexed { index, i ->
            if (i != 0) {
                if (resources[index] < i) {
                    return false
                }
            }
        }
        if (award.reputations.any { it != 0 } && !reputations.perBiggerI(award.reputations)) {
            return false
        }
        if (consume) {
            AppWrapper.globalScope.launch(Dispatchers.Main) {
                gainResources(award.resources.map { -it })
                gainDiamond(-award.diamond)
                gainKey(-award.key)
                gainTalentPoint(-award.talentPoint)
            }
        }
        return true
    }

    fun toReputationLevelData(): List<ReputationLevel> {
        return reputations.map {
            ReputationManager.getReputationLevelData(it)
        }
    }


    fun toReputationLevels(): List<Int> {
        return reputations.map {
            ReputationManager.getReputationLevel(it)
        }
    }

    fun getMasterLevel(): Int {
        return min(MAX_ACCOUNT_LEVEL, accountExp.value / 100 + 1)
    }


    /** 检查距离上次增体力的时间，恢复相应体力（最多到 maxPower），更新 lastPowerIncTime */
    fun recoverPowerIfNeeded(
        power: MutableState<Int>,
        lastPowerIncTime: MutableState<Long>,
        maxPower: Int,
        hourlyRegen: Int
    ) {
        val now = getCurrentTime()
        val passedMs = now - lastPowerIncTime.value
        if (passedMs < 0) {
            // 出现了时间倒退（可能玩家系统时间被改了），可做防作弊处理
            return
        }

        // 一小时的毫秒数
        val HOUR_IN_MILLIS = 3600000L

        // 看过去了多少整小时
        val passedHours = (passedMs / HOUR_IN_MILLIS).toInt()
        if (passedHours > 0) {
            // 可以加体力了
            val currentPower = power.value
            // 如果当前体力已超出上限则不需要截断，但只在恢复时保证不超过 maxPower
            if (currentPower < maxPower) {
                val newPower = (currentPower + passedHours * hourlyRegen).coerceAtMost(maxPower)
                power.value = newPower
            }
            // 更新 lastPowerIncTime，只增加了整数小时部分，余下的毫秒数留到下次计算
            lastPowerIncTime.value += passedHours * HOUR_IN_MILLIS
        }
    }

    fun getAllBattlePower(): Int {
        return repo.allyManager.getPower() + repo.equipManager.getPower() + getMasterAlly().levelPower + TalentManager.getTotalPower()
    }

    fun getAllVipRequire(): Int {
        return max(repo.allyManager.getVipRequire(), repo.equipManager.getVipRequire())
    }

    suspend fun realGainItem(it: Sell, realAward: Award) {
        reportManager().itemMoneyBought(it.id)
        val monthCardUnlockId = repo.gameCore.getSellPool().first { it.isMonthCard() }.showCondition
        if (it.id == monthCardUnlockId) {
            reportManager().unlockMonthCard()
        }
        if (it.isGift()) {
            setBooleanValueByKey(KEY_GIFT_AWARDED + it.id, true)
            setBooleanValueByKey(SELL_FOREVER + it.id, true)
            SellManager.openSellChest(it)
            GameCore.instance.onBattleEffect(SoundEffect.BuyGood)
        } else {
            val finalAward =
                if (it.isSellKeyForMoneyItem() && !isDoubleGained(it)) {
                    // 处理key首次购买双倍逻辑
                    setDoubleGained(it)
                    realAward.copy(key = realAward.key * 2)
                } else {
                    realAward
                }
            SellManager.openGoogleBillSell(it)
            gainAward(finalAward)
            Dialogs.awardDialog.value = finalAward
            if (it.isNewTaskPackage()) {
                SevenDayManager.markGoogleSellItem(it)
            } else if (it.isTower()) {
                TowerManager.markGoogleSellItem(it)
            } else if (it.isMonthCard()) {
                MonthCardManager.buyCard(
                    it.id,
                    it.storage,
                    getCurrentTime()
                )
            }
        }
    }
}