package com.moyu.chuanqirensheng.feature.ally.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.resource.PowerLabel
import com.moyu.chuanqirensheng.feature.role.ui.MainPropertyLine
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.text.getQualityName
import com.moyu.chuanqirensheng.text.getTypeRes
import com.moyu.chuanqirensheng.text.toGroupTips
import com.moyu.chuanqirensheng.text.toRaceTips
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding268
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.ui.theme.padding96
import com.moyu.chuanqirensheng.ui.theme.powerHeight
import com.moyu.chuanqirensheng.ui.theme.powerWidth
import com.moyu.chuanqirensheng.ui.theme.toQualityColor
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.MovableImage
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.Ally
import com.moyu.core.model.role.Role
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.battle_prop
import shared.generated.resources.name_label
import shared.generated.resources.skills
import shared.generated.resources.unlock_skill_10
import shared.generated.resources.unlock_skill_5

@Composable
fun AllyInfoLayout(modifier: Modifier = Modifier, ally: Ally) {
    Column(
        modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        val role = BattleManager.getMyRoleByAlly(ally, skipBattleProperty = true)
        AllyInfoDetailLayout(ally)
        Row(Modifier.fillMaxWidth()) {
            AllyInfoSkillLayout(role, ally)
            Spacer(modifier = Modifier.size(padding6))
            AllyInfoPropertyLayout(role)
        }
    }
}

@Composable
fun AllyInfoDetailLayout(ally: Ally) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            MovableImage(
                modifier = Modifier.size(padding90),
                imageResource = kmpDrawableResource(ally.pic),
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(padding4),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(Modifier.size(padding2))
                StrokedText(
                    text = ally.name, style = MaterialTheme.typography.h2, color = Color.White
                )
                Row(
                    Modifier.size(padding150, padding36).paint(
                        painterResource(Res.drawable.name_label),
                        contentScale = ContentScale.FillBounds
                    ).padding(horizontal = padding8, vertical = padding2),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(padding4)
                ) {
                    EffectButton(onClick = {
                        ally.raceType.toRaceTips().toast()
                    }) {
                        Image(
                            painter = painterResource(
                                ally.getTypeRes()
                            ), modifier = Modifier.size(imageMedium), contentDescription = null
                        )
                    }
                    if (ally.getGroupPic().isNotEmpty()) {
                        EffectButton(
                            modifier = Modifier.size(imageMedium), onClick = {
                                ally.raceType2.toGroupTips().toast()
                            }) {
                            Image(
                                modifier = Modifier.fillMaxSize(),
                                painter = kmpPainterResource(ally.getGroupPic()),
                                contentDescription = null
                            )
                        }
                    }
                    StrokedText(
                        text = ally.quality.getQualityName(),
                        style = MaterialTheme.typography.h3,
                        color = ally.quality.toQualityColor(),
                    )
                }
                PowerLabel(
                    Modifier.size(powerWidth, powerHeight),
                    ally.getAllPower()
                )
            }
        }
    }
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AllyInfoPropertyLayout(role: Role) {
    Column(Modifier.width(padding268), horizontalAlignment = Alignment.CenterHorizontally) {
        StrokedText(
            text = stringResource(Res.string.battle_prop),
            style = MaterialTheme.typography.h4
        )
        Spacer(Modifier.padding(vertical = padding5).height(padding2).fillMaxWidth().background(W50))
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(padding2),
            overflow = FlowRowOverflow.Visible,
            maxItemsInEachRow = 4
        ) {
            role.getCurrentProperty().MainPropertyLine(
                textStyle = MaterialTheme.typography.h5,
                textColor = Color.White,
                showName = false
            )
        }
    }
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AllyInfoSkillLayout(role: Role, ally: Ally) {
    // 这个UI，英雄弹窗会用到，战斗弹窗也会用到，所以要区别处理下
    val skills = (if (ally.fixedSkills.first() == 0) emptyList() else ally.fixedSkills.map {
        repo.gameCore.getSkillById(
            it
        )
    }) + ally.extraSkills3.filter { it != 0 }.map {
        it * 100 + ally.star
    }.map { repo.gameCore.getSkillById(it) }
    val unlockedSkillSize =
        ally.fixedSkills.size + if (ally.star >= 10) ally.extraSkills3.size else if (ally.star >= 5) ally.extraSkills2.size else ally.extraSkills1.size
    Column(Modifier.width(padding96), horizontalAlignment = Alignment.CenterHorizontally) {
        StrokedText(text = stringResource(Res.string.skills), style = MaterialTheme.typography.h4)
        Spacer(Modifier.padding(vertical = padding5).height(padding2).fillMaxWidth().background(W50))
        FlowRow {
            skills.forEachIndexed { index, skill ->
                SingleSkillView(
                    skill = skill,
                    showName = false,
                    showStars = false,
                    itemSize = ItemSize.Small,
                    locked = index >= unlockedSkillSize,
                    forceQuality = role.getAlly().quality
                ) {
                    if (index >= unlockedSkillSize) {
                        if (skill.id / 100 in ally.extraSkills2) {
                            AppWrapper.getStringKmp(Res.string.unlock_skill_5).toast()
                        } else {
                            AppWrapper.getStringKmp(Res.string.unlock_skill_10).toast()
                        }
                    }
                    Dialogs.skillDetailDialog.value = it
                }
                Spacer(modifier = Modifier.size(padding2))
            }
        }
    }
}