package com.moyu.chuanqirensheng.feature.setting

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.common.IconView
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.DrawableResource
import shared.generated.resources.Res
import shared.generated.resources.battle_info
import shared.generated.resources.battle_menu_info

val battleInfo = SettingItem(
    name = { AppWrapper.getStringKmp(Res.string.battle_info) },
    icon = { Res.drawable.battle_menu_info },
    action = { Dialogs.infoDialog.value = true },
)

val pause = SettingItem(
    name = { GameSpeedManager.getCurrentSpeed().description },
    icon = { kmpDrawableResource(GameSpeedManager.getCurrentSpeed().icon) },
    action = { GameSpeedManager.nextSpeed() },
)

val settingBattleItems = {
    if (isLite()) {
        if (!repo.gameMode.value.isNormalMode()) {
            listOf(
                battleInfo,
                pause
            )
        } else {
            listOf(battleInfo)
        }
    } else {
        if (!repo.gameMode.value.isNormalMode()) {
            listOf(
                pause
            )
        } else {
            listOf<SettingItem>()
        }
    }
}

data class SettingItem(
    val name: () -> String,
    val icon: () -> DrawableResource = { Res.drawable.battle_menu_info },
    val redIcon: () -> Boolean = { false },
    val upgradeIcon: () -> Boolean = { false },
    val action: () -> Unit,
)

@Composable
fun SettingColumn(modifier: Modifier, settings: List<SettingItem>) {
    Box(
        modifier = modifier
            .height(ItemSize.Large.frameSize)
            .animateContentSize(),
        contentAlignment = Alignment.Center
    ) {
        Row {
            settings.forEach {
                IconView(
                    res = it.icon(),
                    itemSize = ItemSize.Large,
                    frame = null,
                    resZIndex = 999f,
                ) {
                    it.action()
                }
                Spacer(Modifier.size(padding4))
            }
        }
    }
}