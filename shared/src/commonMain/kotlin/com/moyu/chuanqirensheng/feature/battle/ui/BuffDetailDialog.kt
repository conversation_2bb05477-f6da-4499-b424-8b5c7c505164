package com.moyu.chuanqirensheng.feature.battle.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Buff
import com.moyu.core.model.role.Role
import com.moyu.core.util.realValueToDotWithOneDigits
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.buff_effects
import shared.generated.resources.buff_layer
import shared.generated.resources.buff_name
import shared.generated.resources.buff_source
import shared.generated.resources.buff_time
import shared.generated.resources.turn

@Composable
fun BuffDetailDialog(switch: MutableState<Pair<Buff, Role>?>) {
    switch.value?.let { pair ->
        val buff = pair.first
        val role = pair.second
        PanelDialog(
            onDismissRequest = { switch.value = null }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = padding26)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.Start,
            ) {
                val realBuffs = if (buff.isCombined()) {
                    role.getBuffList().filter { it.combinedId == buff.combinedId }
                } else listOf(buff)
                Spacer(modifier = Modifier.size(padding40))
                realBuffs.forEach {
                    StrokedText(
                        text = stringResource(Res.string.buff_name) + it.name,
                        style = MaterialTheme.typography.h2,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    StrokedText(
                        text = stringResource(Res.string.buff_source) + (it.skill?.name ?: ""),
                        style = MaterialTheme.typography.h2,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    StrokedText(
                        text = stringResource(Res.string.buff_effects) + it.buffValue.realValueToDotWithOneDigits(),
                        style = MaterialTheme.typography.h2,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    StrokedText(
                        text = stringResource(Res.string.buff_layer) + it.getLayerString(),
                        style = MaterialTheme.typography.h2,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    StrokedText(
                        text = stringResource(Res.string.buff_time) + it.leftTurnString(role) + stringResource(
                            Res.string.turn),
                        style = MaterialTheme.typography.h2,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.size(padding10))
                }
            }
        }
    }
}
