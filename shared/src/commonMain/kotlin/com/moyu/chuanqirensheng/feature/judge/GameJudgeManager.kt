package com.moyu.chuanqirensheng.feature.judge

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager.currentTarget
import com.moyu.chuanqirensheng.feature.quest.onTaskKillEnemy
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_GAME
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.p_getTimeMillis
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.min

class GameJudgeManager : GameJudge {
    override val inBattle = mutableStateOf(false)
    override val gameOver = mutableStateOf(false)

    override fun onGameOver(ending: Ending?) {
        gameOver.value = true
        inBattle.value = false
        ContinueManager.clearSave()
        goto(LOGIN_SCREEN)
        playerMusicByScreen() // 音乐
    }

    override fun onBattleWin(allies: List<Role>, enemies: List<Role>) {
        updateAlliesAfterBattle(allies)
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
        AppWrapper.globalScope.launch(Dispatchers.Main) {
            onTaskKillEnemy(enemies)
            if (repo.gameMode.value.isPvp1Mode()) {
                currentTarget.value = RankData(p_getTimeMillis())
                goto(PVP_CHOOSE_ENEMY_SCREEN)
            } else if (repo.gameMode.value.isPvp2Mode()) {
                Pvp2Manager.currentTarget.value = RankData(p_getTimeMillis())
                goto(PVP2_CHOOSE_ENEMY_SCREEN)
            } else if (repo.gameMode.value.isTowerMode()) {
                goto(TOWER_SCREEN)
            } else {
                val event = EventManager.selectedEvent.value
                if (event?.playPara2?.firstOrNull() == 0.0) {
                    // 需要标记事件结束，否则对话无法正常进行
                    EventManager.getOrCreateHandler(EventManager.selectedEvent.value!!).eventResult.value = true
                    EventManager.getOrCreateHandler(EventManager.selectedEvent.value!!).eventFinished.value = true
                    // 战斗胜利
                    EventManager.doEventResult(EventManager.selectedEvent.value!!, true)
                } else {
                    repo.onBattleInfo(event?.startText ?: "", type = BattleInfoType.ExtraSkill, play = event?.play?: 1)
                    EventManager.getOrCreateHandler(EventManager.selectedEvent.value!!).doEventPostAwards.value = true
                }
            }
        }

        reportManager().battle(1, 0, BattleManager.getAge())
    }

    override fun onBattleLose(allies: List<Role>, enemies: List<Role>) {
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
        if (repo.gameMode.value.isPvp1Mode()) {
            currentTarget.value = RankData(p_getTimeMillis())
            goto(PVP_CHOOSE_ENEMY_SCREEN)
        } else if (repo.gameMode.value.isPvp2Mode()) {
            Pvp2Manager.currentTarget.value = RankData(p_getTimeMillis())
            goto(PVP2_CHOOSE_ENEMY_SCREEN)
        } else if (repo.gameMode.value.isTowerMode()) {
            goto(TOWER_SCREEN)
        } else {
            updateAlliesAfterBattle(allies)
            EventManager.doEventBattleResult(
                EventManager.selectedEvent.value, false,
            )
        }
        reportManager().battle(0, 0, BattleManager.getAge())
        increaseIntValueByKey(KEY_DIED_IN_GAME)
    }

    private fun updateAlliesAfterBattle(allies: List<Role>) {
        AppWrapper.globalScope.launch(Dispatchers.Main) {
            allies.forEach {
                BattleManager.updateAllyInGameById(
                    it,
                    hp = min(it.getOriginProperty().hp, it.getCurrentProperty().hp)
                )
            }
            BattleManager.checkAnyAllyDied()
        }
    }
}