package com.moyu.chuanqirensheng.feature.rank.ui

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.feature.router.RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_RANK
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.MainIcon
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.rank_icon
import shared.generated.resources.rank_title

@Composable
fun RankIcon(itemSize: ItemSize) {
    MainIcon(
        itemSize = itemSize,
        unlocks = listOf(repo.gameCore.getUnlockById(UNLOCK_RANK)),
        click = {
            goto(RANK_SCREEN)
        },
        red = { false },
        title = stringResource(Res.string.rank_title),
        icon = Res.drawable.rank_icon
    )
}
