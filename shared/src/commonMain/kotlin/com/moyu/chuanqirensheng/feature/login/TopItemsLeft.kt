package com.moyu.chuanqirensheng.feature.login

import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.getHeadBitmap
import com.moyu.chuanqirensheng.widget.common.UserImageView
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.not_login


@Composable
fun TopItemsLeft(modifier: Modifier = Modifier) {
    Column(modifier) {
        UserImageView(
            headRes = gameSdkDefaultProcessor().getAvatarUrl(),
            headBitmap = getHeadBitmap(),
            name = gameSdkDefaultProcessor().getUserName() ?: stringResource(Res.string.not_login)
        )
    }
}