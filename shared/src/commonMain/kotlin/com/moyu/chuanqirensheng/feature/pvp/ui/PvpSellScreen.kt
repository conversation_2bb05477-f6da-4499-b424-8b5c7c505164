package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.feature.sell.SELL_TYPE_PVP
import com.moyu.chuanqirensheng.feature.sell.ui.SellPage
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.widget.common.GameBackground
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.pvp_bg
import shared.generated.resources.pvp_shop

@Composable
fun PvpSellScreen() {
    GameBackground(
        title = stringResource(Res.string.pvp_shop),
        bgMask = B35,
        background = Res.drawable.pvp_bg
    ) {
        SellPage(listOf(SELL_TYPE_PVP), showPvpDiamond = true)
    }
}