package com.moyu.chuanqirensheng.feature.story


import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.feature.stage.MAX_AGE
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_STORY_SELECTION
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.core.model.Story

fun Int.toReputationName(): String {
    return repo.gameCore.getStoryPool().firstOrNull { it.id == this }?.name?: ""
}

fun Int.toReputationRes(): String {
    return repo.gameCore.getStoryPool().firstOrNull { it.id == this }?.pic?: ""
}

object StoryManager {
    val stories = mutableStateListOf<Story>()

    fun init() {
        // 初始化
        getListObject<Story>(KEY_STORY_SELECTION).let { taskList ->
            val tasks = taskList.mapNotNull { task ->
                repo.gameCore.getStoryPool().firstOrNull { it.id == task.id }
                    ?.copy(selected = task.selected)
            }
            stories.addAll(tasks)
        }
        // 首次直接全部加入
        if (stories.isEmpty()) {
            stories.addAll(repo.gameCore.getStoryPool())
        }
        // 精确检测是否有缺失的故事包
        repo.gameCore.getStoryPool().forEach { story->
            if (stories.none { it.id == story.id }) {
                stories.add(story)
            }
        }
        // 如果没有选中任何故事包，选中前3个
        if (stories.none { it.selected }) {
            stories[0] = stories[0].switchSelection()
            stories[1] = stories[1].switchSelection()
            stories[2] = stories[2].switchSelection()
        }
    }

    fun getUnlockedStoryIds(): List<Int> {
        return stories.filter {
            val lock = repo.gameCore.getUnlockById(it.unlockId)
            UnlockManager.getUnlockedFlow(lock)
        }.map { it.unlockId }
    }

    fun getMaxAge(): Int {
        return MAX_AGE
    }
}