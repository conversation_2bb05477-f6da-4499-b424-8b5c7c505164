package com.moyu.chuanqirensheng.feature.ally.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.battle.MAX_BATTLE_SIZE
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding51
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.getTextStyle
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.filter.CommonFilterView
import com.moyu.chuanqirensheng.widget.filter.CommonOrderView
import com.moyu.chuanqirensheng.widget.filter.FilterLayout
import com.moyu.chuanqirensheng.widget.filter.ItemFilter
import com.moyu.chuanqirensheng.widget.filter.OrderLayout
import com.moyu.chuanqirensheng.widget.filter.allyOrderList
import com.moyu.chuanqirensheng.widget.filter.pvpFilterList
import com.moyu.core.model.Ally
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.ally_name_frame
import shared.generated.resources.common_big_frame
import shared.generated.resources.common_choose
import shared.generated.resources.died
import shared.generated.resources.filter_frame
import shared.generated.resources.hurt
import shared.generated.resources.one_click_deselect_battle
import shared.generated.resources.one_click_select_battle
import shared.generated.resources.start_battle


@Composable
fun OtherAllyPage(
    data: SelectAllyData,
    showSelect: Boolean = false,
    currentSlotIndex: MutableIntState,
) {
    val showFilter = remember {
        mutableStateOf(false)
    }
    val filter = remember {
        mutableStateListOf<ItemFilter<Ally>>()
    }
    val showOrder = remember {
        mutableStateOf(false)
    }
    val order = remember {
        mutableStateOf(allyOrderList.first())
    }

    val rawList = BattleManager.getGameAllies().filter { data.filter(it) }
        .filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }.filter {
            !it.isHero()
        }

    val list = rawList.filter { !it.isHero() }.filter { ally ->
        filter.all { it.filter.invoke(ally) }
    }.sortedByDescending { order.value.order?.invoke(it) }

    val itemSize = ItemSize.LargePlus
    Box(Modifier.fillMaxSize()) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(Res.drawable.common_big_frame),
            contentScale = ContentScale.FillBounds,
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(padding8))
            OtherPrepareFilterRow(showFilter, showOrder, data)
            Spacer(modifier = Modifier.size(padding4))
            LazyVerticalGrid(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(-padding20),
                columns = GridCells.Fixed(4),
                content = {
                    items(list.size) { index ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            val ally = list[index]
                            Box {
                                if (ally.battlePosition >= 0) {
                                    Image(
                                        painter = painterResource(Res.drawable.common_choose),
                                        modifier = Modifier
                                            .size(imageMedium)
                                            .align(Alignment.BottomEnd)
                                            .zIndex(999f).graphicsLayer {
                                                translationY = -padding14.toPx()
                                            },
                                        contentDescription = null
                                    )
                                }
                                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                    SingleAllyView(
                                        ally = ally,
                                        showName = false,
                                        showHp = false,
                                        itemSize = itemSize,
                                        extraInfo = if (ally.isDead()) stringResource(Res.string.died)
                                        else if (ally.isHurt()) stringResource(Res.string.hurt) else ""
                                    ) {
                                        if (showSelect) {
                                            // 其他
                                            BattleManager.selectAllyToBattle(
                                                ally,
                                                currentSlotIndex.intValue,
                                            )
                                            // 刷新
                                            currentSlotIndex.intValue =
                                                (0..8).toList().firstOrNull {
                                                    BattleManager.getBattleAllies()[it] == null
                                                } ?: 0
                                        }
                                    }
                                    Box(modifier = Modifier.zIndex(-999f).graphicsLayer {
                                        translationY = -padding22.toPx()
                                    }, contentAlignment = Alignment.Center) {
                                        Image(
                                            modifier = Modifier.width(itemSize.itemSize),
                                            contentScale = ContentScale.FillWidth,
                                            painter = painterResource(Res.drawable.ally_name_frame),
                                            contentDescription = null
                                        )
                                        StrokedText(
                                            modifier = Modifier.graphicsLayer {
                                                translationY = padding4.toPx()
                                            },
                                            text = ally.name,
                                            style = itemSize.getTextStyle()
                                        )
                                    }
                                }
                            }
                        }
                    }
                })
            Spacer(Modifier.size(padding4))
        }
        OrderLayout(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(start = padding10, top = padding45),
            show = showOrder,
            filter = order,
            filterList = allyOrderList
        )
        FilterLayout(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(start = padding10, top = padding45),
            show = showFilter,
            filter = filter,
            filterList = pvpFilterList
        )
    }
}

@Composable
fun OtherPrepareFilterRow(
    showFilter: MutableState<Boolean>,
    showOrder: MutableState<Boolean>,
    data: SelectAllyData
) {
    Row(
        Modifier.fillMaxWidth().height(padding51).paint(
            painterResource(Res.drawable.filter_frame),
            contentScale = ContentScale.FillBounds
        ),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        CommonOrderView(
            Modifier.padding(start = padding10), showOrder
        )
        // 如果8个都已经上阵，或者所有兵种都已经上阵，则显示一键下阵
        val isDeSelect = BattleManager.getBattleAllies().size == MAX_BATTLE_SIZE || BattleManager.getBattleAllies().size == BattleManager.allyGameData.size + 1
        GameButton(
            buttonSize = ButtonSize.MediumMinus,
            enabled = !repo.inBattle.value,
            text = if (isDeSelect) stringResource(Res.string.one_click_deselect_battle) else stringResource(Res.string.one_click_select_battle)
        ) {
            if (!repo.inBattle.value) {
                if (isDeSelect) {
                    BattleManager.oneShotDeselect()
                } else {
                    BattleManager.oneShotSelect(data)
                }
            }
        }
        GameButton(
            text = stringResource(Res.string.start_battle),
            enabled = !repo.inBattle.value,
            buttonStyle = ButtonStyle.Blue,
            buttonSize = ButtonSize.MediumMinus
        ) {
            if (!repo.inBattle.value) {
                data.start()
            }
        }
        CommonFilterView(
            Modifier.padding(end = padding10), showFilter
        )
    }
}