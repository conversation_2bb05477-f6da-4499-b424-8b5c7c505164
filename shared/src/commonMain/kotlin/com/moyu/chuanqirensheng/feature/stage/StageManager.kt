package com.moyu.chuanqirensheng.feature.stage

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_PASS_MAX_STAGE_ID
import com.moyu.chuanqirensheng.sub.datastore.KEY_STAGE
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueIfBiggerByKey
import com.moyu.chuanqirensheng.sub.datastore.setObject
import com.moyu.core.model.Award
import com.moyu.core.model.toAward
import kotlin.math.min

const val MAX_AGE = 999

object StageManager {
    val currentStage = mutableStateOf(repo.gameCore.getDungeonPool().first())
    val maxStage = mutableStateOf(0)
    val needAutoSelectLastStage = mutableStateOf(true)

    fun init() {
        maxStage.value = getIntFlowByKey(KEY_PASS_MAX_STAGE_ID)
    }

    fun selectPrev() {
        val dungeons = repo.gameCore.getDungeonPool()
        val index = dungeons.indexOf(currentStage.value)
        if (index > 0) {
            currentStage.value = dungeons[index - 1]
            setObject(KEY_STAGE, currentStage.value)
        }
    }

    fun selectNext() {
        val dungeons = repo.gameCore.getDungeonPool()
        val index = dungeons.indexOf(currentStage.value)
        if (index < getShowMaxStage()) {
            currentStage.value = dungeons[index + 1]
            setObject(KEY_STAGE, currentStage.value)
        }
    }

    fun hasPrev(): Boolean {
        val dungeons = repo.gameCore.getDungeonPool()
        return dungeons.indexOf(currentStage.value) > 0
    }

    fun hasNext(): Boolean {
        return (currentStage.value.id <= getShowMaxStage()) && (currentStage.value.id < repo.gameCore.getDungeonPool().size)
    }

    fun isCurrentUnlocked(): Boolean {
        return currentStage.value.id <= maxStage.value + 1 && AwardManager.getMasterLevel() >= currentStage.value.condition
    }

    fun setMaxStage(stage: Int) {
        if (stage > maxStage.value) {
            maxStage.value = stage
            setIntValueIfBiggerByKey(KEY_PASS_MAX_STAGE_ID, stage)
            needAutoSelectLastStage.value = true
        }
    }

    fun getAwardByStage(stage: Int): Award {
        val poolId = repo.gameCore.getDungeonPool().first { it.id == stage }.reward
        return repo.gameCore.getPoolById(poolId).toAward()
    }

    fun getMaxAge(): Int {
        return if (repo.gameMode.value.isStage()) {
            currentStage.value.limit
        } else {
            MAX_AGE
        }
    }

    fun getShowMaxStage(): Int {
        return min(repo.gameCore.getDungeonPool().size, maxStage.value)
    }
}