package com.moyu.chuanqirensheng.feature.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.platform.billPayClientDataList
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.bill_order
import shared.generated.resources.icon_billing_order
import shared.generated.resources.red_icon

@Composable
fun ErrorOrderIcon() {
    val itemSize = ItemSize.MediumPlus
    if (billPayClientDataList().isNotEmpty()) {
        val pressing = remember {
            mutableStateOf(false)
        }
        val colorFilter = if (pressing.value) {
            ColorFilter.tint(
                B50, BlendMode.SrcAtop
            )
        } else {
            null
        }
        val color = if (pressing.value) W50 else Color.White
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            EffectButton(
                modifier = Modifier.width(itemSize.frameSize), pressing = pressing, onClick = {
                    Dialogs.errorOrderDialog.value = true
                }) {
                Image(
                    modifier = Modifier.height(itemSize.itemSize),
                    painter = painterResource(Res.drawable.icon_billing_order),
                    colorFilter = colorFilter,
                    contentDescription = null
                )
                if (true) {
                    Image(
                        modifier = Modifier.align(Alignment.TopEnd).padding(itemSize.itemSize / 40)
                            .size(imageTinyPlus),
                        painter = painterResource(Res.drawable.red_icon),
                        colorFilter = colorFilter,
                        contentDescription = null
                    )
                }
            }
            StrokedText(
                modifier = Modifier.width(itemSize.frameSize * 1.1f),
                text = AppWrapper.getStringKmp(Res.string.bill_order),
                maxLines = 1,
                color = color,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.h4
            )
        }
    }
}
