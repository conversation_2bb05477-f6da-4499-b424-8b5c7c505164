package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.draw.ui.AllyCouponItem
import com.moyu.chuanqirensheng.feature.draw.ui.HeroCouponItem
import com.moyu.chuanqirensheng.feature.resource.CurrentAllyCouponPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentHeroCouponPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding165
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.activity_frame
import shared.generated.resources.card_back_ally
import shared.generated.resources.card_back_hero
import shared.generated.resources.common_big_frame
import shared.generated.resources.draw_activity_top_label
import shared.generated.resources.draw_label1
import shared.generated.resources.red_icon


@Composable
fun SellDrawPage() {
    LaunchedEffect(Unit) {
        SellManager.init()
    }
    val selectedIndex = remember {
        mutableStateOf(0)
    }
    Column(
        Modifier.paint(
            painterResource(Res.drawable.common_big_frame),
            contentScale = ContentScale.FillBounds
        ),
    ) {
        Spacer(modifier = Modifier.size(padding8))
        Row(
            modifier = Modifier.padding(start = padding14)
        ) {
            CurrentKeyPoint(showPlus = true)
            CurrentAllyCouponPoint()
            CurrentHeroCouponPoint()
        }
        Spacer(Modifier.size(padding4))
        Box {
            Image(
                modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120).padding(padding2).clip(
                    RoundedCornerShape(padding10)
                ),
                painter = painterResource(Res.drawable.draw_activity_top_label),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
            Image(
                modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120),
                painter = painterResource(Res.drawable.activity_frame),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
            Column(
                Modifier.padding(start = padding40, top = padding30),
            ) {
                StrokedText(
                    modifier = Modifier.scale(1.2f),
                    text = stringResource(Res.string.draw_label1),
                    style = MaterialTheme.typography.h1
                )
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(
                    rememberScrollState()
                ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when (selectedIndex.value) {
                0 -> {
                    AllyCouponItem()
                }
                else -> {
                    HeroCouponItem()
                }
            }
            Row(
                Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                DrawCardBack(Modifier, 0, selectedIndex.value == 0) {
                    selectedIndex.value = 0
                }
                DrawCardBack(Modifier, 1, selectedIndex.value == 1) {
                    selectedIndex.value = 1
                }
            }
            Spacer(modifier = Modifier.weight(4f))
        }
    }
}

@Composable
fun DrawCardBack(
    modifier: Modifier = Modifier,
    index: Int,
    selected: Boolean,
    onClick: () -> Unit
) {
    // 根据是否选中，计算目标偏移量
    val offsetY by animateDpAsState(
        targetValue = if (selected) -padding28 else -padding10, label = ""
    )

    EffectButton(onClick = onClick,
        modifier = modifier.graphicsLayer {
            translationY = offsetY.toPx()
        }
    ) {
        Image(
            modifier = Modifier.size(padding120, padding165),
            painter = painterResource(
                when (index) {
                    0 -> Res.drawable.card_back_ally
                    else -> Res.drawable.card_back_hero
                }
            ),
            contentDescription = null
        )
        if (!selected && ((index == 0 && AwardManager.couponAlly.value >= 10)
            || (index == 1 && AwardManager.couponHero.value >= 10))) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopEnd).padding(top = padding16)
                    .size(imageSmall),
                painter = painterResource(Res.drawable.red_icon),
                contentDescription = null
            )
        }
    }
}

