package com.moyu.chuanqirensheng.feature.reputation.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.reputation.MAX_REPUTATION_LEVEL
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.router.gotoReputationShopById
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.reputationHugeItemWidth
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.CommonBar
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Story
import com.moyu.core.model.level.ReputationLevel
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.awards
import shared.generated.resources.bar_blue
import shared.generated.resources.bar_empty
import shared.generated.resources.common_big_frame
import shared.generated.resources.reputation_level
import shared.generated.resources.talent1_frame_light


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ReputationLevelScreen() {
    GameBackground(title = stringResource(Res.string.reputation_level)) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(Res.drawable.common_big_frame),
            contentScale = ContentScale.FillBounds,
            contentDescription = null
        )
        FlowRow(
            modifier = Modifier
                .fillMaxSize().padding(top = padding8)
                .verticalScroll(rememberScrollState()),
            maxItemsInEachRow = 2,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            repo.gameCore.getStoryPool().sortedBy { it.order }.forEach {
                val index = it.id - 1
                val reputationLevel = AwardManager.toReputationLevelData()[index]
                ReputationItem(index, reputationLevel) { item ->
                    gotoReputationShopById(item.id)
                }
            }
            Spacer(Modifier.size(reputationHugeItemWidth, padding22))
        }
    }
}

@Composable
fun ReputationItem(
    index: Int,
    reputationLevel: ReputationLevel,
    showAwardButton: Boolean = true,
    badgeClick: (Story) -> Unit
) {
    val item = repo.gameCore.getStoryPool()[index]
    val currentExp =
        AwardManager.reputations[index] - (reputationLevel.expTotal)
    Column(
        modifier = Modifier
            .width(reputationHugeItemWidth),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        ReputationBadge(
            Modifier.size(reputationHugeItemWidth),
            item,
            style = MaterialTheme.typography.h2,
        ) {
            badgeClick(item)
        }
        ReputationLevelView(
            Modifier.size(reputationHugeItemWidth, padding36),
            reputationLevel,
            currentExp
        )
        if (showAwardButton) {
            GameButton(
                text = stringResource(Res.string.awards),
                buttonSize = ButtonSize.Medium,
                enabled = ReputationManager.hasRed(index),
                buttonStyle = ButtonStyle.Blue,
                onClick = {
                    Dialogs.reputationRewardDialog.value = index
                })
        }
        Spacer(modifier = Modifier.size(padding10))
    }
}

@Composable
fun ReputationLevelView(modifier: Modifier, reputationLevel: ReputationLevel, currentExp: Int) {
    Box(modifier) {
        CommonBar(
            modifier = Modifier.fillMaxSize()
                .padding(horizontal = padding8, vertical = padding4),
            currentValue = currentExp,
            maxValue = reputationLevel.exp,
            emptyRes = Res.drawable.bar_empty,
            fullRes = Res.drawable.bar_blue,
            style = MaterialTheme.typography.h5,
            showNum = reputationLevel.level < MAX_REPUTATION_LEVEL,
        )
        Box(
            modifier = Modifier
                .size(padding36)
                .align(Alignment.CenterStart), contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier.fillMaxSize().scale(1.4f),
                painter = painterResource(Res.drawable.talent1_frame_light),
                contentDescription = null
            )
            StrokedText(text = "Lv${reputationLevel.level}", style = MaterialTheme.typography.h5, maxLines = 1)
        }
    }
}
