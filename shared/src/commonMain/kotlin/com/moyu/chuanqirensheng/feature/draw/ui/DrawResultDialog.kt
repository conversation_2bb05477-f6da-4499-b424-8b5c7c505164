package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.ally.ui.SingleAllyView
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.resource.AllyCouponPoint
import com.moyu.chuanqirensheng.feature.resource.HeroCouponPoint
import com.moyu.chuanqirensheng.feature.skill.ui.SingleEquipView
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.dialogBigHeight
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.TitleCloseButton
import com.moyu.chuanqirensheng.widget.dialog.EmptyDialog
import com.moyu.chuanqirensheng.widget.effect.FlippableBox
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.singleDrawGif
import com.moyu.core.AppWrapper
import com.moyu.core.model.Award
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.ally_coupon_not_enough
import shared.generated.resources.card_back_ally
import shared.generated.resources.card_back_hero
import shared.generated.resources.continue_draw
import shared.generated.resources.draw_100
import shared.generated.resources.hero_coupon_not_enough
import shared.generated.resources.left_draw_orange

@Composable
fun DrawResultDialog(show: MutableState<Award?>) {
    show.value?.let {
        val buttonVisible = remember {
            mutableStateOf(false)
        }
        val showSize = remember {
            mutableStateOf(10)
        }
        EmptyDialog(onDismissRequest = {
            if (buttonVisible.value) {
                show.value = null
            }
        }) {
            Column(Modifier.fillMaxWidth().height(dialogBigHeight).clickable {
                if (buttonVisible.value) {
                    show.value = null
                }
            }) {
                val scrollState = rememberLazyGridState()
                LaunchedEffect(it.outAllies.size + it.outEquips.size) {
                    try {
                        if (scrollState.layoutInfo.totalItemsCount > 0) {
                            scrollState.animateScrollToItem(scrollState.layoutInfo.totalItemsCount - 1)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace() // 这里你可以记录日志或处理异常
                    }
                }
                TitleCloseButton(
                    Modifier
                        .align(Alignment.End)
                ) {
                    show.value = null
                }
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    LazyVerticalGrid(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = padding28, horizontal = padding36)
                            .weight(1f),
                        columns = GridCells.Fixed(3),
                        verticalArrangement = Arrangement.spacedBy(padding28),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        state = scrollState,
                    ) {
                        items(it.outAllies.size + it.outEquips.size) { index ->
                            if (it.outAllies.isNotEmpty()) {
                                Box(
                                    Modifier.size(ItemSize.LargePlus.frameSize),
                                    contentAlignment = Alignment.Center
                                ) {
                                    FlippableBox(if (showSize.value > 10) 0 else 600, key = it.outAllies[index].id, front = {
                                        Image(
                                            modifier = Modifier.size(ItemSize.LargePlus.frameSize),
                                            painter = painterResource(
                                                resource = Res.drawable.card_back_ally
                                            ),
                                            contentDescription = null
                                        )
                                    }) {
                                        SingleAllyView(
                                            ally = it.outAllies[index].copy(peek = true),
                                            itemSize = ItemSize.LargePlus,
                                            showName = true,
                                            showRed = false,
                                            showEffect = true,
                                            textColor = Color.White
                                        )
                                    }
                                    GifView(
                                        modifier = Modifier.size(ItemSize.LargePlus.frameSize).scale(1.6f),
                                        enabled = true,
                                        gifCount = singleDrawGif.count,
                                        gifDrawable = singleDrawGif.gif,
                                        pace = 3
                                    )
                                }
                            } else {
                                Box(
                                    Modifier.size(ItemSize.LargePlus.frameSize),
                                    contentAlignment = Alignment.Center
                                ) {
                                    FlippableBox(if (showSize.value > 10) 0 else 600, key = it.outEquips[index].id, front = {
                                        Image(
                                            modifier = Modifier.size(ItemSize.LargePlus.frameSize),
                                            painter = painterResource(
                                                resource = Res.drawable.card_back_hero
                                            ),
                                            contentDescription = null
                                        )
                                    }) {
                                        SingleEquipView(
                                            equipment = it.outEquips[index].copy(peek = true),
                                            itemSize = ItemSize.LargePlus,
                                            showName = true,
                                            showRed = false,
                                            showEffect = true,
                                            textColor = Color.White
                                        )
                                    }
                                    GifView(
                                        modifier = Modifier.size(ItemSize.LargePlus.frameSize).scale(1.6f),                                        enabled = true,
                                        gifCount = singleDrawGif.count,
                                        gifDrawable = singleDrawGif.gif,
                                        pace = 3
                                    )
                                }
                            }
                        }
                    }
                }
                LaunchedEffect(it.outAllies.size + it.outEquips.size) {
                    delay(1000)
                    buttonVisible.value = (it.outAllies.size + it.outEquips.size) == showSize.value
                }
                AnimatedVisibility(visible = buttonVisible.value) {
                    // 底部按钮
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .padding(bottom = padding19),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            if (it.outAllies.isNotEmpty()) {
                                AllyCouponPoint(cost = 100)
                            } else {
                                HeroCouponPoint(cost = 100)
                            }
                            Spacer(modifier = Modifier.height(padding4))
                            GameButton(
                                text = stringResource(Res.string.draw_100),
                                buttonSize = ButtonSize.Big,
                                enabled = if (it.outAllies.isEmpty()) {
                                    (AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) >= 100
                                } else {
                                    (AwardManager.couponAlly.value + AwardManager.key.value / repo.gameCore.getAllyCouponRate()) >= 100
                                },
                                buttonStyle = ButtonStyle.Blue,
                                onClick = {
                                    if (it.outAllies.isEmpty()) {
                                        if ((AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) < 100) {
                                            GiftManager.onDrawHeroFailed()
                                            AppWrapper.getStringKmp(Res.string.hero_coupon_not_enough)
                                                .toast()
                                        } else {
                                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                                buttonVisible.value = false
                                                showSize.value = 100
                                                DrawManager.buyHeroCoupon(10)
                                            }
                                        }
                                    } else {
                                        if ((AwardManager.couponAlly.value + AwardManager.key.value / repo.gameCore.getAllyCouponRate()) < 100) {
                                            GiftManager.onDrawAllyFailed()
                                            AppWrapper.getStringKmp(Res.string.ally_coupon_not_enough)
                                                .toast()
                                        } else {
                                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                                buttonVisible.value = false
                                                showSize.value = 100
                                                DrawManager.buyAllyCoupon(10)
                                            }
                                        }
                                    }
                                })
                            if (it.outAllies.isNotEmpty()) {
                                StrokedText(
                                    text = stringResource(
                                        Res.string.left_draw_orange,
                                        DrawManager.getAllyLeftDraw()
                                    ),
                                    style = MaterialTheme.typography.h5,
                                    textAlign = TextAlign.Center
                                )
                            } else {
                                StrokedText(
                                    text = stringResource(
                                        Res.string.left_draw_orange,
                                        DrawManager.getHeroLeftDraw()
                                    ),
                                    style = MaterialTheme.typography.h5,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            if (it.outAllies.isNotEmpty()) {
                                AllyCouponPoint()
                            } else {
                                HeroCouponPoint()
                            }
                            Spacer(modifier = Modifier.height(padding4))
                            GameButton(
                                text = stringResource(Res.string.continue_draw),
                                buttonSize = ButtonSize.Big,
                                buttonStyle = ButtonStyle.Blue,
                                enabled = if (it.outAllies.isEmpty()) {
                                    (AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) >= 10
                                } else {
                                    (AwardManager.couponAlly.value + AwardManager.key.value / repo.gameCore.getAllyCouponRate()) >= 10
                                },
                                onClick = {
                                    if (it.outAllies.isEmpty()) {
                                        if ((AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) < 10) {
                                            GiftManager.onDrawHeroFailed()
                                            AppWrapper.getStringKmp(Res.string.hero_coupon_not_enough)
                                                .toast()
                                        } else {
                                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                                buttonVisible.value = false
                                                showSize.value = 10
                                                DrawManager.buyHeroCoupon()
                                            }
                                        }
                                    } else {
                                        if ((AwardManager.couponAlly.value + AwardManager.key.value / repo.gameCore.getAllyCouponRate()) < 10) {
                                            GiftManager.onDrawAllyFailed()
                                            AppWrapper.getStringKmp(Res.string.ally_coupon_not_enough)
                                                .toast()
                                        } else {
                                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                                buttonVisible.value = false
                                                showSize.value = 10
                                                DrawManager.buyAllyCoupon()
                                            }
                                        }
                                    }
                                })
                            if (it.outAllies.isNotEmpty()) {
                                StrokedText(
                                    text = stringResource(
                                        Res.string.left_draw_orange,
                                        DrawManager.getAllyLeftDraw()
                                    ),
                                    style = MaterialTheme.typography.h5,
                                    textAlign = TextAlign.Center
                                )
                            } else {
                                StrokedText(
                                    text = stringResource(
                                        Res.string.left_draw_orange,
                                        DrawManager.getHeroLeftDraw()
                                    ),
                                    style = MaterialTheme.typography.h5,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
