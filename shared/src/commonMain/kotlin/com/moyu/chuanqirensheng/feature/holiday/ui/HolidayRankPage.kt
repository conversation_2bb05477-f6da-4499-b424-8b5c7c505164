package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.api.getHolidayRanks
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.ui.SingleRecord
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.holiday_lottery_num
import shared.generated.resources.net_error_retry

val holidayRanks = mutableStateOf(emptyList<RankData>())
val holidaySolidRanks = mutableStateOf(emptyList<RankData>())


@Composable
fun HolidayRankPage() {
    LaunchedEffect(Unit) {
        try {
            delay(200)

            // 活动已经结束，拉取定榜
            getHolidayRanks(
                platformChannel()
            ).let {
                if (HolidayManager.canShowOnlyRank()) {
                    holidaySolidRanks.value = json.decodeFromString(
                        ListSerializer(RankData.serializer()), it.message
                    )
                } else {
                    holidayRanks.value = json.decodeFromString(
                        ListSerializer(RankData.serializer()), it.message
                    )
                }
            }
        } catch (e: Exception) {
            AppWrapper.getStringKmp(Res.string.net_error_retry).toast()
        }
    }
    val ranks = if (HolidayManager.canShowOnlyRank()) holidaySolidRanks else holidayRanks
    LazyColumn(
        horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
            .fillMaxSize(),
        content = {
            items(ranks.value.size) { index ->
                SingleRecord(
                    ranks.value[index], index + 1,
                    content = { rankData, _ ->
                        Column {
                            StrokedText(
                                text = stringResource(Res.string.holiday_lottery_num) + "：" + (rankData.holidayNum),
                                style = MaterialTheme.typography.h3
                            )
                        }
                    })
            }
        }
    )
}
