package com.moyu.chuanqirensheng.feature.sell

import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.lucky.AdManager
import com.moyu.chuanqirensheng.feature.quest.getMaxAge
import com.moyu.chuanqirensheng.feature.quest.onTaskBuy
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.platform.hasBilling
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkPoolCheck
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkSell
import com.moyu.chuanqirensheng.sub.datastore.KEY_DOUBLE_KEY1
import com.moyu.chuanqirensheng.sub.datastore.KEY_DOUBLE_KEY2
import com.moyu.chuanqirensheng.sub.datastore.KEY_DOUBLE_KEY3
import com.moyu.chuanqirensheng.sub.datastore.KEY_DOUBLE_KEY4
import com.moyu.chuanqirensheng.sub.datastore.KEY_DOUBLE_KEY5
import com.moyu.chuanqirensheng.sub.datastore.KEY_DOUBLE_KEY6
import com.moyu.chuanqirensheng.sub.datastore.KEY_SELL_ITEM1_REFRESHED
import com.moyu.chuanqirensheng.sub.datastore.KEY_SELL_ITEM2_REFRESHED
import com.moyu.chuanqirensheng.sub.datastore.KEY_SELL_ITEMS
import com.moyu.chuanqirensheng.sub.datastore.KEY_SELL_ITEMS_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setListObjectSync
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.Sell
import com.moyu.core.model.toAward
import com.moyu.core.model.toAwards
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import shared.generated.resources.Res
import shared.generated.resources.diamond_not_enough
import shared.generated.resources.error_tips
import shared.generated.resources.key_not_enough
import shared.generated.resources.pvp_diamond_not_enough
import shared.generated.resources.real_money_not_enough
import shared.generated.resources.sold_out
import shared.generated.resources.time_error_tips
import kotlin.math.min

const val SELL_FREE_INDEX = 1
const val SELL_KEY_INDEX = 2
const val SELL_SECRET_INDEX = 3
const val SELL_VIP_INDEX = 4
const val SELL_TYPE_PVP = 5

const val SELL_FOREVER = "s_)"

const val INFINITE_STORAGE = 6000

object SellManager {
    val items = mutableStateListOf<Sell>()
    val refresh1Count = mutableIntStateOf(0)
    val refresh2Count = mutableIntStateOf(0)
    var isDrawing = false

    val key1DoubleGained = mutableStateOf(false)
    val key2DoubleGained = mutableStateOf(false)
    val key3DoubleGained = mutableStateOf(false)
    val key4DoubleGained = mutableStateOf(false)
    val key5DoubleGained = mutableStateOf(false)
    val key6DoubleGained = mutableStateOf(false)

    fun init() {
        if (!isNetTimeValid()) {
            return
        }

        key1DoubleGained.value = getBooleanFlowByKey(KEY_DOUBLE_KEY1)
        key2DoubleGained.value = getBooleanFlowByKey(KEY_DOUBLE_KEY2)
        key3DoubleGained.value = getBooleanFlowByKey(KEY_DOUBLE_KEY3)
        key4DoubleGained.value = getBooleanFlowByKey(KEY_DOUBLE_KEY4)
        key5DoubleGained.value = getBooleanFlowByKey(KEY_DOUBLE_KEY5)
        key6DoubleGained.value = getBooleanFlowByKey(KEY_DOUBLE_KEY6)

        if (isSameDay(
                getLongFlowByKey(KEY_SELL_ITEMS_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 商店宝箱：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (items.isEmpty()) {
                getListObject<Sell>(KEY_SELL_ITEMS).let {
                    items.addAll(it.mapNotNull { sell ->
                        repo.gameCore.getSellPool().firstOrNull { it.id == sell.id }
                            ?.copy(
                                opened = sell.opened,
                                num = sell.num,
                                storage = sell.storage,
                                award = sell.award?.recreate()
                            )
                    })
                }
            }
            // todo 覆盖升级，gift只有首冲，其他都没有，需要添加
            if (items.count { it.isGift() } < repo.gameCore.getSellPool()
                    .filter { it.isGift() }.size) {
                items.removeAll { it.isGift() }
                items.addAll(repo.gameCore.getSellPool().filter { it.isGift() })
            }
            refresh1Count.intValue = getIntFlowByKey(KEY_SELL_ITEM1_REFRESHED)
            refresh2Count.intValue = getIntFlowByKey(KEY_SELL_ITEM2_REFRESHED)
        } else {
            items.clear()
            refresh1Count.intValue = 0
            refresh2Count.intValue = 0
            setIntValueByKey(KEY_SELL_ITEM1_REFRESHED, 0)
            setIntValueByKey(KEY_SELL_ITEM2_REFRESHED, 0)
        }
        // 首次升级，要手动刷新下所有商品
        if (items.isEmpty()) {
            refreshAll()
            setLongValueByKey(KEY_SELL_ITEMS_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        }
    }

    private fun refreshAll() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        val maxAge = min(
            StoryManager.getMaxAge(), getMaxAge()
        )
        val filteredChests = repo.gameCore.getSellPool().filter {
            maxAge >= it.condition.first() && maxAge <= it.condition[1]
        }
        val targetItems = mutableListOf<Sell>()
        repeat(5) { index ->
            val type = index + 1
            val raw = filteredChests.filter { it.type == type }
                .filter { it.storyUnlock == 0 || it.storyUnlock in StoryManager.getUnlockedStoryIds() }
            val shopInfo = repo.gameCore.getShopDataByIndex(index)
            val type1 =
                raw.filter { it.regularType == 2 }.filter { it.priceType == 0 }.shuffled(RANDOM)
                    .take(shopInfo[0]).map { it.copy() }
            val type2 =
                raw.filter { it.regularType == 2 }.filter { it.priceType == 3 }.shuffled(RANDOM)
                    .take(shopInfo[1]).map { it.copy() }
            val type3 =
                raw.filter { it.regularType == 2 }.filter { it.isKeyMoney() || it.isPvpMoney() }
                    .shuffled(RANDOM)
                    .take(shopInfo[2]).map { it.copy() }
            val type4 =
                raw.filter { it.regularType == 2 }.filter { it.priceType == 5 }.shuffled(RANDOM)
                    .take(shopInfo[3]).map { it.copy() }
            val fixed = raw.filter { it.regularType == 1 }
            targetItems.addAll(type1 + type2 + type3 + type4 + fixed)
        }
        targetItems.addAll(repo.gameCore.getSellPool().filter { it.isGift() })
        targetItems.addAll(repo.gameCore.getSellPool().filter { it.isMonthCard() })
        targetItems.addAll(repo.gameCore.getSellPool().filter { it.isVipItem() })

        // 注意，如果storageType是2，则表示永久限量，这里处理下
        getListObject<Sell>(KEY_SELL_ITEMS).mapNotNull { sell ->
            repo.gameCore.getSellPool().firstOrNull { sell.id == it.id }
                ?.copy(opened = sell.opened, num = sell.num, storage = sell.storage)
        }.filter { it.storageType == 2 }.forEach { sell ->
            targetItems.indexOfFirst { sell.id == it.id }.takeIf { it >= 0 }?.let {
                targetItems[it] = targetItems[it].copy(storage = sell.storage)
            }
        }
        // 全部刷新
        items.clear()
        items.addAll(targetItems.map {
            // 有很多随机的奖励，这里要提前换算出来
            if (it.award == null) {
                it.copy(award = it.toAward())
            } else it
        })
        setListObject(KEY_SELL_ITEMS, items)
    }

    fun refreshSellItemByType(type: Int) {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        when (type) {
            1 -> {
                refresh1Count.intValue += 1
            }

            else -> {
                refresh2Count.intValue += 1
            }
        }
        val maxAge = min(
            StoryManager.getMaxAge(), getMaxAge()
        )
        val filteredChests = repo.gameCore.getSellPool().filter {
            maxAge >= it.condition.first() && maxAge <= it.condition[1]
        }
        val targetItems = mutableListOf<Sell>()
        val raw = filteredChests.filter { it.type == type }.filter { it.canRefresh }
            .filter { it.storyUnlock == 0 || it.storyUnlock in StoryManager.getUnlockedStoryIds() }
        // 只刷新货币商品，免费和付费不刷
        val shopInfo = repo.gameCore.getShopDataByIndex(type - 1)
        targetItems.addAll(
            raw.filter { it.isKeyMoney() || it.isPvpMoney() }.shuffled(RANDOM).take(shopInfo[2])
                .map { it.copy() })

        // 注意，如果storageType是2，则表示永久限量，这里处理下，如果永久限量
        getListObject<Sell>(KEY_SELL_ITEMS).mapNotNull { sell ->
            repo.gameCore.getSellPool().firstOrNull { sell.id == it.id }
                ?.copy(opened = sell.opened, num = sell.num, storage = sell.storage)
        }.filter { it.storageType == 2 }.forEach { sell ->
            targetItems.indexOfFirst { sell.id == it.id }.takeIf { it >= 0 }?.let {
                targetItems[it] = targetItems[it].copy(storage = sell.storage)
            }
        }
        // 只刷新货币商品，免费和付费不刷
        items.removeAll { it.type == type && it.isKeyMoney() && it.canRefresh }
        items.addAll(targetItems.map {
            // 有很多随机的奖励，这里要提前换算出来
            if (it.award == null) {
                it.copy(award = it.toAward())
            } else it
        })
        setListObject(KEY_SELL_ITEMS, items)
    }

    // todo 保护
    var openingChest = false
    suspend fun openSellChest(sell: Sell) {
        if (!isNetTimeValid()) {
            // 防止作弊
            AppWrapper.getStringKmp(Res.string.time_error_tips).toast()
            return
        }
        if (openingChest) return
        openingChest = true
        checkSell(sell)
        checkPoolCheck()
        if (sell.isDiamondMoney() && AwardManager.diamond.value < sell.price) {
            GiftManager.onDiamondNotEnough()
            AppWrapper.getStringKmp(Res.string.diamond_not_enough).toast()
        } else if (sell.isKeyMoney() && AwardManager.key.value < sell.price) {
            GiftManager.onKeyNotEnough()
            AppWrapper.getStringKmp(Res.string.key_not_enough).toast()
        } else if (sell.isPvpMoney() && AwardManager.pvpDiamond.value < sell.price) {
            AppWrapper.getStringKmp(Res.string.pvp_diamond_not_enough).toast()
            GiftManager.onPvpDiamondNotEnough()
        } else if (sell.isAifadianTower() && AwardManager.realMoney.value < sell.price) {
            AppWrapper.getStringKmp(Res.string.real_money_not_enough).toast()
        } else if (items.first { it.id == sell.id }.storage <= 0) {
            AppWrapper.getStringKmp(Res.string.sold_out).toast()
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.BuyGood)
            items.indexOfFirst { it.id == sell.id }.takeIf { it >= 0 }?.let {
                if (!sell.isMonthCard()) { // 月卡的storage是用来表示购后可以领取的天数，不用-1
                    items[it] = items[it].copy(storage = items[it].storage - 1)
                }
                setListObject(KEY_SELL_ITEMS, items)
                when (sell.priceType) {
                    1 -> {
                        AwardManager.gainDiamond(-sell.price)
                    }

                    2 -> {
                        reportManager().itemKeyBought(sell.id)
                        AwardManager.gainKey(-sell.price)
                    }

                    4 -> {
                        AwardManager.gainPvpDiamond(-sell.price)
                    }
                }

                reportManager().onShopPurchase(
                    sellId = sell.id,
                    price = sell.price,
                    priceType = sell.priceType
                )
                setBooleanValueByKey(SELL_FOREVER + sell.id, true)
                if (sell.isRandom()) {
                    val award = sell.award ?: sell.toAwards().shuffled(RANDOM).first()
                    val realAward =
                        award + Award(adMoney = if (sell.isAd()) AdManager.getRandomAdMoney() else 0)
                    // 广告幸运值
                    AwardManager.gainAward(realAward)
                    onTaskBuy(sell)
                    Dialogs.awardDialog.value = realAward
                } else {
                    val award = if (sell.isSellKeyForMoneyItem() && !isDoubleGained(sell)) {
                        setDoubleGained(sell)
                        val tmp = sell.award ?: sell.toAward()
                        tmp.copy(key = tmp.key * 2)
                    } else {
                        sell.award ?: sell.toAward()
                    }
                    val realAward =
                        award + Award(adMoney = if (sell.isAd()) AdManager.getRandomAdMoney() else 0)
                    realAward.apply {
                        AwardManager.gainAward(this)
                        onTaskBuy(sell)
                        Dialogs.awardDialog.value = this
                    }
                }
            } ?: AppWrapper.getStringKmp(Res.string.error_tips).toast()
        }
        openingChest = false
    }

    suspend fun openGiftSell(sell: Sell) {
        reportManager().onShopPurchase(
            sellId = sell.id,
            price = sell.price,
            priceType = sell.priceType
        )
        val award = sell.toAward()
        award.apply {
            AwardManager.gainAward(this)
            onTaskBuy(sell)
            Dialogs.awardDialog.value = this
        }
    }

    suspend fun openGoogleBillSell(sell: Sell) {
        GameCore.instance.onBattleEffect(SoundEffect.BuyGood)
        items.indexOfFirst { it.id == sell.id }.takeIf { it >= 0 }?.let {
            if (!sell.isMonthCard()) { // 月卡的storage是用来表示购后可以领取的天数，不用-1
                items[it] = items[it].copy(storage = items[it].storage - 1)
            }
            setListObjectSync(KEY_SELL_ITEMS, items)
        }
        setBooleanValueByKey(SELL_FOREVER + sell.id, true)
    }

    fun getRedVip(): Boolean {
        return repo.gameCore.getVipPool().any {
            VipManager.getVipLevel() >= it.level && !VipManager.isThisLevelGained(it)
        }
    }

    fun hasDrawRed(): Boolean {
        return AwardManager.couponAlly.value >= 10 || AwardManager.couponHero.value >= 10
    }

    fun getRedFree(type: Int): Boolean {
        return if (type == 0) {
            // 抽卡
            hasDrawRed()
        } else if (type == SELL_VIP_INDEX) {
            val vipLevel = VipManager.getVipLevel()
            repo.gameCore.getVipPool().filter { it.level <= vipLevel }.any {
                !VipManager.isThisLevelGained(it)
            }
        } else {
            val itemRed = items.filter { it.type == type }.filter { it.isFreeGift() }
                .any { it.storage > 0 }
            if (type == SELL_FREE_INDEX) {
                // 可领取广告要算上
                AdManager.luckyList.any {
                    val available = AdManager.canGetAdMoney(it)
                    val gained = AdManager.alreadyGot(it)
                    available && !gained
                } || itemRed
            } else itemRed
        }
    }

    fun getAllRedFree(): Boolean {
        return items.filter { it.isFreeGift() }.any { it.storage > 0 } || hasDrawRed()
    }

    fun isDoubleGained(sell: Sell): Boolean {
        return when (sell.sellKeyForMoneyIndex()) {
            1 -> key1DoubleGained.value
            2 -> key2DoubleGained.value
            3 -> key3DoubleGained.value
            4 -> key4DoubleGained.value
            5 -> key5DoubleGained.value
            6 -> key6DoubleGained.value
            else -> false
        }
    }

    fun setDoubleGained(sell: Sell) {
        when (sell.sellKeyForMoneyIndex()) {
            1 -> {
                key1DoubleGained.value = true
                setBooleanValueByKey(KEY_DOUBLE_KEY1, true)
            }

            2 -> {
                key2DoubleGained.value = true
                setBooleanValueByKey(KEY_DOUBLE_KEY2, true)
            }

            3 -> {
                key3DoubleGained.value = true
                setBooleanValueByKey(KEY_DOUBLE_KEY3, true)
            }

            4 -> {
                key4DoubleGained.value = true
                setBooleanValueByKey(KEY_DOUBLE_KEY4, true)
            }

            5 -> {
                key5DoubleGained.value = true
                setBooleanValueByKey(KEY_DOUBLE_KEY5, true)
            }

            6 -> {
                key6DoubleGained.value = true
                setBooleanValueByKey(KEY_DOUBLE_KEY6, true)
            }
        }
    }
}

fun Sell.preCondition(): Boolean {
    return if (hasBilling()) {
        if (showCondition == 0) true else {
            getBooleanFlowByKey(SELL_FOREVER + showCondition)
        }
    } else true
}