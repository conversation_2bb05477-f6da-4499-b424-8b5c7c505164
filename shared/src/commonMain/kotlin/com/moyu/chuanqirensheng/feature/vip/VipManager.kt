package com.moyu.chuanqirensheng.feature.vip

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateMapOf
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkVip
import com.moyu.chuanqirensheng.sub.anticheat.GuardedB
import com.moyu.chuanqirensheng.sub.datastore.KEY_VIP_GAINED
import com.moyu.core.AppWrapper
import com.moyu.core.model.Vip
import com.moyu.core.model.toAward
import shared.generated.resources.Res
import shared.generated.resources.already_got
import shared.generated.resources.unlock_success

const val DAILY_QUEST_NUM_INC_TYPE = 31
const val REFRESH_SHOP_NUM_INC_TYPE = 32
const val SPEED_UP_TYPE = 33
const val REFRESH_SHOP_FREE_TYPE = 34
const val DAILY_QUEST_DOUBLE_AWARD_TYPE = 37
const val AD_AVOID_TYPE = 39
const val PVP_NUM_TYPE = 44

object VipManager {

    private val gainedMap = mutableStateMapOf<Int, MutableState<Boolean>>()

    fun init() {
        repo.gameCore.getVipPool().forEach {
            gainedMap[it.id] = GuardedB(KEY_VIP_GAINED + it.id)
        }
    }

    fun getVipLevel(): Int {
        return if (AwardManager.electric.value == 0) 0 else repo.gameCore.getVipPool()
            .lastOrNull { it.num <= AwardManager.electric.value }?.level ?: repo.gameCore.getVipPool().last().level
    }

    fun getVipLevelData(): Vip {
        return repo.gameCore.getVipPool().lastOrNull { it.num <= AwardManager.electric.value }?:repo.gameCore.getVipPool().first()
    }

    fun isSpeed10Unlocked(): Boolean {
        return repo.gameCore.getVipPool().first { it.effectType == SPEED_UP_TYPE && it.effectNum == 10 }.level <= getVipLevel()
    }

    fun getExtraDailyQuest(): Int {
        return repo.gameCore.getVipPool().filter { it.level <= getVipLevel() }
            .filter { it.effectType == DAILY_QUEST_NUM_INC_TYPE }.sumOf { it.effectNum }
    }

    fun getMaxDailyQuest(): Int {
        return repo.gameCore.getVipPool()
            .filter { it.effectType == DAILY_QUEST_NUM_INC_TYPE }.sumOf { it.effectNum }
    }

    fun getExtraPvpNum(): Int {
        return repo.gameCore.getVipPool().filter { it.level <= getVipLevel() }
            .filter { it.effectType == PVP_NUM_TYPE }.sumOf { it.effectNum }
    }

    fun isSkipAd(): Boolean {
        return repo.gameCore.getVipPool().first { it.effectType == AD_AVOID_TYPE }.level <= getVipLevel()
    }

    fun getRealShopRefreshLimit(): Int {
        return repo.gameCore.getDailyShopRefreshCount() + repo.gameCore.getVipPool()
            .filter { it.level <= getVipLevel() }
            .filter { it.effectType == REFRESH_SHOP_NUM_INC_TYPE }.sumOf { it.effectNum }
    }

    fun getShopRefreshCost(): Int {
        return if (getVipLevel() >= repo.gameCore.getVipPool()
                .first { it.effectType == REFRESH_SHOP_FREE_TYPE }.level
        ) 0 else repo.gameCore.getRefreshShopCost()
    }

    fun isDoubleQuestAward(): Boolean {
        return getVipLevel() >= (repo.gameCore.getVipPool()
            .firstOrNull { it.effectType == DAILY_QUEST_DOUBLE_AWARD_TYPE }?.level ?: 999)
    }

    fun isThisLevelGained(cheat: Vip): Boolean {
        return gainedMap[cheat.id]!!.value
    }

    suspend fun gain(vip: Vip) {
        val gained = gainedMap[vip.id]!!
        if (gained.value) {
            AppWrapper.getStringKmp(Res.string.already_got).toast()
            return
        }
        checkVip(vip)
        gained.value = true
        val award = vip.toAward()
        if (!award.isEmpty()) {
            Dialogs.awardDialog.value = award
            AwardManager.gainAward(award)
        } else {
            AppWrapper.getStringKmp(Res.string.unlock_success).toast()
        }
    }

    fun isCheat(): Boolean {
        return AwardManager.electric.value < AwardManager.getAllVipRequire()
    }
}