package com.moyu.chuanqirensheng.feature.rank

import kotlinx.serialization.Serializable

@Serializable
data class RankWrapper(
    val data: String = "", // 加密后的数据
)

@Serializable
data class RankData(
    val time: Long,
    val versionCode: Int = 0,
    val userName: String = "",
    val userId: String = "",
    val userPic: String = "",
    val battlePower: Int = 0,
    val endingNum: Int = 0, // 实际为游戏关卡
    val talentLevel: Int = 0, // 复用talent1
    val tcgValue: Int = 0, // 复用talent2
    val electric: Int = 0,
    val level: Int = 0,// 复用talent3
    val liked: Int = 0,
    val pvpScore: Int = 0,
    val pvpLastScore: Int = 0, // 复用，作为totalscore用
    val pvpData: PvpData = PvpData(),
    val platformChannel: String = "taptap",
    val holidayNum: Int = 0,
    val towerLevel: Int = 0,
    val serverId: Int = 0, // 服务器ID
)

@Serializable
data class PvpData(
    val allyIds: List<Int> = emptyList(),
    val talentIds: Map<Int, Int> = emptyMap(),
    val equipIds: List<Int> = emptyList(),
    val win: Int = 0,
    val lose: Int = 0,
    val tcgIds: List<Int> = emptyList() // 新增，用来支持图鉴提升的战斗属性
)
