package com.moyu.chuanqirensheng.feature.event.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.battle.ui.RoleHpWithAnim
import com.moyu.chuanqirensheng.platform.statusBarHeightInDp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding158
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.healGif
import com.moyu.core.GameCore
import com.moyu.core.logic.role.battleAllyList
import com.moyu.core.music.SoundEffect
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.in_game_ally_frame


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun MiniAllyView(modifier: Modifier) {
    Box(
        modifier = modifier
            .padding(top = statusBarHeightInDp()).size(padding66, padding158)
            .graphicsLayer {
                translationX = -padding8.toPx()
                translationY = -padding8.toPx()
            }.paint(
                painterResource(Res.drawable.in_game_ally_frame),
            ), contentAlignment = Alignment.Center
    ) {
        FlowRow(
            modifier = Modifier.padding(bottom = padding10),
            maxItemsInEachRow = 2, horizontalArrangement = Arrangement.spacedBy(
                padding2
            ), verticalArrangement = Arrangement.spacedBy(
                padding4
            )
        ) {
            repeat(8) { index ->
                repo.battleRoles[battleAllyList[index]]?.let {
                    Column(
                        modifier = Modifier.clickable {
                            GameCore.instance.onBattleEffect(SoundEffect.Click)
                            Dialogs.roleDetailDialog.value = it
                        }, horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Box {
                            Image(
                                modifier = Modifier.size(padding20, padding22),
                                painter = painterResource(
                                    kmpDrawableResource(
                                        it.getRace().getHeadIcon()
                                    )
                                ),
                                contentDescription = null
                            )
                            GifView(
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .size(padding20, padding22).scale(2f), BattleManager.healingStates.value, healGif.count, healGif.gif, pace = 2
                            ) {
                                BattleManager.healingStates.value = false
                            }
                        }
                        Box(
                            modifier = Modifier.size(
                                padding20, padding5
                            )
                        ) {
                            RoleHpWithAnim(it)
                        }
                    }
                } ?: Box(Modifier.size(padding20, padding22))
            }
        }
    }
}
