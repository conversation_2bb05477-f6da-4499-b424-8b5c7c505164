package com.moyu.chuanqirensheng.feature.lottery.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.lottery_icon
import shared.generated.resources.lottery_title2
import shared.generated.resources.tab_gift

@Composable
fun ExpensiveLotteryScreen() {
    val listTabItems = remember {
        mutableStateListOf(
            Res.drawable.lottery_icon,
            Res.drawable.tab_gift
        )
    }
    val pagerState = remember {
        mutableStateOf(0)
    }
    GameBackground(title = stringResource(Res.string.lottery_title2)) {
        Column(
            modifier = Modifier
                .fillMaxSize().paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(Modifier.weight(1f)) {
                when (pagerState.value) {
                    0 -> ExpensiveLotteryPage()
                    else -> ExpensiveGiftPage()
                }
            }
            NavigationTab(
                modifier = Modifier.padding(bottom = padding6),
                pageState = pagerState,
                titles = listTabItems
            )
        }
    }
}