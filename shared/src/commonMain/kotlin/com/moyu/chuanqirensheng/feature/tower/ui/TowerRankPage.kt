package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.TOWER_TYPE
import com.moyu.chuanqirensheng.feature.rank.ui.RankPage
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.music.SoundEffect
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.tower_icon
import shared.generated.resources.tower_mode_progress_tips
import shared.generated.resources.tower_tab3

val towerRanks = mutableStateOf(emptyList<RankData>())

@Composable
fun TowerRankScreen() {
    GameBackground(title = stringResource(Res.string.tower_tab3)) {
        LaunchedEffect(Unit) {
            towerRanks.value = emptyList()
        }
        RankPage(type = TOWER_TYPE, data = towerRanks) { rankData, rankIndex ->
            Row(modifier = Modifier.clickable {
                MusicManager.playSound(SoundEffect.Click)
                AppWrapper.getStringKmp(Res.string.tower_mode_progress_tips).toast()
            }, verticalAlignment = Alignment.CenterVertically) {
                Image(
                    modifier = Modifier.size(imageSmall),
                    painter = painterResource(Res.drawable.tower_icon),
                    contentDescription = null
                )
                StrokedText(
                    text = rankData.towerLevel.toString(),
                    style = MaterialTheme.typography.h3
                )
            }
        }
    }
}