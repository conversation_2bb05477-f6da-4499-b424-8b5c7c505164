package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.api.getRanks
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.pvp.MAX_PVP_NUM
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.rank.LAST_PVP_TYPE
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.router.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_SELL_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.sell.SELL_TYPE_PVP
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding54
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isBetween23_45And00_15
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.effect.ShadowImage
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.arena_time_tips
import shared.generated.resources.net_error_retry
import shared.generated.resources.pvp
import shared.generated.resources.pvp_bg
import shared.generated.resources.pvp_entrance
import shared.generated.resources.pvp_quest
import shared.generated.resources.pvp_quest_icon
import shared.generated.resources.pvp_rank
import shared.generated.resources.pvp_rank_icon
import shared.generated.resources.pvp_shop
import shared.generated.resources.pvp_shop_icon
import shared.generated.resources.pvp_top_frame
import shared.generated.resources.red_icon

@Composable
fun PvpEntryScreen() {
    LaunchedEffect(Unit) {
        // 进这个页面就需要刷一下排行榜，需要领取排名任务
        PvpManager.init()
        try {
            if (!isBetween23_45And00_15(getCurrentTime())) {
                if (lastPvpRanks.value.isEmpty()) {
                    delay(200)
                    getRanks(
                        platformChannel(),
                        LAST_PVP_TYPE
                    ).let {
                        lastPvpRanks.value =
                            json.decodeFromString(ListSerializer(RankData.serializer()), it.message)
                    }
                }
            }
        } catch (e: Exception) {
//            Timber.e(e)
            AppWrapper.getStringKmp(Res.string.net_error_retry).toast()
        }
    }
    GameBackground(
        title = stringResource(Res.string.pvp),
        bgMask = B35,
        background = Res.drawable.pvp_bg
    ) {
        PvpTopDataRow(Modifier
            .fillMaxWidth().paint(
                painterResource(Res.drawable.pvp_top_frame),
                contentScale = ContentScale.FillBounds
            ))
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding12, vertical = padding100),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.End)
                    .padding(end = padding28).graphicsLayer {
                        translationY = padding100.toPx()
                    },
                stringResource(Res.string.pvp_shop),
                Res.drawable.pvp_shop_icon,
                red = {
                    SellManager.getRedFree(SELL_TYPE_PVP)
                }
            ) {
                goto(PVP_SELL_SCREEN)
            }
            Box(Modifier
                .weight(1f)
                .align(Alignment.Start)
                .padding(start = padding10).graphicsLayer {
                    translationY = -padding40.toPx()
                }) {
                SingleMapItem(
                    Modifier,
                    stringResource(Res.string.pvp),
                    Res.drawable.pvp_entrance,
                    red = {
                        PvpManager.pkNumToday.value < MAX_PVP_NUM + VipManager.getExtraPvpNum()
                    }
                ) {
                    if (isBetween23_45And00_15(getCurrentTime())) {
                        AppWrapper.getStringKmp(Res.string.arena_time_tips).toast()
                    } else {
                        pvpRanks.value = emptyList()
                        goto(PVP_CHOOSE_ENEMY_SCREEN)
                    }
                }
                if (GuideManager.guideIndex.value == 33) {
                    GuideHand(
                        modifier = Modifier.align(Alignment.BottomCenter).height(padding80).graphicsLayer {
                            translationY = padding60.toPx()
                        },
                        handType = HandType.UP_HAND
                    )
                }
            }

            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.End)
                    .padding(end = padding16).graphicsLayer {
                        translationY = -padding50.toPx()
                    },
                stringResource(Res.string.pvp_quest), Res.drawable.pvp_quest_icon,
                red = {
                    QuestManager.pvpTasks.any {
                        QuestManager.getTaskDoneFlow(it)
                                && !it.opened
                    }
                }
            ) {
                if (isNetTimeValid()) {
                    if (isBetween23_45And00_15(getCurrentTime())) {
                        AppWrapper.getStringKmp(Res.string.arena_time_tips).toast()
                    } else {
                        goto(PVP_QUEST_SCREEN)
                    }
                }
            }
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.Start)
                    .padding(start = padding54).graphicsLayer {
                        translationY = -padding180.toPx()
                    },
                stringResource(Res.string.pvp_rank),
                Res.drawable.pvp_rank_icon,
                red = {
                    false
                }
            ) {
                if (isBetween23_45And00_15(getCurrentTime())) {
                    AppWrapper.getStringKmp(Res.string.arena_time_tips).toast()
                } else {
                    goto(PVP_RANK_SCREEN)
                }
            }
        }
    }
}

@Composable
fun SingleMapItem(modifier: Modifier, name: String, buildingRes: DrawableResource, red: ()-> Boolean, callback: () -> Unit) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Box(contentAlignment = Alignment.Center) {
            EffectButton(onClick = {
                callback()
            }) {
                Box(
                    Modifier
                        .width(padding120 + padding12)
                        .height(padding120 + padding22),
                    contentAlignment = Alignment.TopCenter
                ) {
                    ShadowImage(
                        modifier = Modifier.size(padding120),
                        imageResource = buildingRes,
                    )
                    if (red()) {
                        Image(
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .padding(padding2)
                                .size(imageSmall),
                            painter = painterResource(Res.drawable.red_icon),
                            contentDescription = null
                        )
                    }
                }
                Box(Modifier.align(Alignment.BottomCenter).graphicsLayer {
                    translationY = padding4.toPx()
                }, contentAlignment = Alignment.Center) {
                    StrokedText(
                        text = name, style = MaterialTheme.typography.h1
                    )
                }
            }
        }
    }
}