package com.moyu.chuanqirensheng.feature.talent.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE4
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.skill.getRealDescColorful
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager.talents
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding70
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.Talent
import com.moyu.core.music.SoundEffect
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.diamond_not_enough
import shared.generated.resources.learn_talent1
import shared.generated.resources.resource1_not_enough_tips
import shared.generated.resources.talent_learned_tips1

@Composable
fun Talent1DetailDialog(show: MutableState<Int?>) {
    show.value?.let { talentId ->
        val showTalent = repo.gameCore.getTalent1Pool()
            .first { it.id == talentId }
        val skill = repo.gameCore.getSkillById(showTalent.talentSkill)
        PanelDialog(onDismissRequest = {
            if (GuideManager.guideIndex.value == 21) {
                GuideManager.guideIndex.value = 22
                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE4)
                if (AwardManager.diamond.value >= showTalent.cost) {
                    AwardManager.gainDiamond(-showTalent.cost)
                    TalentManager.upgradeTalent(showTalent)
                }
                Dialogs.detailTalent1Dialog.value = null
                GuideManager.showGuide.value = false
            } else {
                show.value = null
            }
        }, contentBelow = {
            Column(Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
                Talent1StarUpView(talent = showTalent)
            }
        }) {
            Column(
                Modifier
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                StrokedText(
                    text = showTalent.name,
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding4))
                val skills =
                    repo.gameCore.getSkillPool().filter { it.mainId == skill.mainId }
                Spacer(modifier = Modifier.size(padding19))
                Column(
                    Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(horizontal = padding19)
                        .verticalScroll(
                            rememberScrollState()
                        )
                ) {
                    skills.filter { it.level >= showTalent.level }.take(1).forEach {
                        StrokedText(
                            text = it.getRealDescColorful(
                                MaterialTheme.typography.h2.toSpanStyle()
                            ),
                            style = MaterialTheme.typography.h2,
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.size(padding10))
                    }
                }
            }
        }
    }
}

@Composable
fun Talent1StarUpView(modifier: Modifier = Modifier, talent: Talent) {
    val award = Award(diamond = talent.cost, resources = EMPTY_RESOURCES.toMutableList().apply {
        this[0] = talent.cost2
    })
    val learnedLevel =
        talents[talent.mainId] ?: 0
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.size(padding4))
        Row(verticalAlignment = Alignment.CenterVertically) {
            if ((talents[talent.mainId]?: 0) < talent.level) {
                AwardList(
                    modifier = modifier,
                    award = award,
                    param = defaultParam.copy(
                        showName = false,
                        itemSize = ItemSize.Small,
                        checkAffordable = true,
                        showColumn = false,
                        noFrameForItem = true,
                        numInFrame = false,
                        textColor = Color.White,
                    ),
                )
            }
        }
        Box {
            GameButton(
                text = if (learnedLevel < talent.level) stringResource(Res.string.learn_talent1) else stringResource(
                    Res.string.talent_learned_tips1
                ),
                buttonStyle = ButtonStyle.Green,
                enabled = AwardManager.isAffordable(award) && learnedLevel < talent.level,
                buttonSize = ButtonSize.Big,
                mute = true,
                onClick = {
                    if (GuideManager.guideIndex.value == 21) {
                        GuideManager.guideIndex.value = 22
                        setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE4)
                        GuideManager.showGuide.value = false
                    }
                    if (learnedLevel >= talent.level) {
                        AppWrapper.getStringKmp(Res.string.talent_learned_tips1)
                            .toast()
                        GameCore.instance.onBattleEffect(SoundEffect.Click)
                    } else if (!AwardManager.isAffordable(award)) {
                        if (AwardManager.diamond.value < talent.cost) {
                            GiftManager.onDiamondNotEnough()
                            AppWrapper.getStringKmp(Res.string.diamond_not_enough)
                                .toast()
                        } else {
                            GiftManager.onResource1NotEnough()
                            AppWrapper.getStringKmp(Res.string.resource1_not_enough_tips).toast()
                        }
                        GameCore.instance.onBattleEffect(SoundEffect.Click)
                    } else {
                        AwardManager.gainDiamond(-talent.cost)
                        AwardManager.gainResources(EMPTY_RESOURCES.toMutableList().apply {
                            this[0] = -talent.cost2
                        })
                        TalentManager.upgradeTalent(talent)
                        Dialogs.detailTalent1Dialog.value = null
                        EndingManager.uploadRank()
                    }
                })
            if (GuideManager.guideIndex.value == 21) {
                GuideHand(
                    modifier = Modifier.align(Alignment.TopCenter)
                        .height(padding70).graphicsLayer {
                            translationY = -padding50.toPx()
                        }.scale(1.14f), handType = HandType.DOWN_HAND
                )
            }
        }
    }
}
