package com.moyu.chuanqirensheng.feature.talent.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE6
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE9
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.skill.getRealDescColorful
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding70
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.talent2Gif
import com.moyu.chuanqirensheng.widget.effect.talent3Gif
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.Talent
import com.moyu.core.model.mainIdToTalentType
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.toAward
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.building_star_up
import shared.generated.resources.common_arrow_down
import shared.generated.resources.current_level
import shared.generated.resources.learn_talent3
import shared.generated.resources.level_max
import shared.generated.resources.level_up
import shared.generated.resources.next_level
import shared.generated.resources.resource1_not_enough_tips
import shared.generated.resources.talent_base
import shared.generated.resources.talent_point_not_enough
import shared.generated.resources.talent_resource_not_enough
import shared.generated.resources.user_head_frame
import kotlin.math.max

@Composable
fun TalentDetailDialog(show: MutableState<Int?>) {
    show.value?.let { talentMainId ->
        val talentLevel = TalentManager.talents[talentMainId] ?: 0
        val showTalentLevel = max(1, talentLevel)
        val showTalent = if (talentMainId.mainIdToTalentType() == 2) {
            repo.gameCore.getTalent2Pool()
                .first { it.level == showTalentLevel && it.mainId == talentMainId }
        } else {
            repo.gameCore.getTalent3Pool()
                .first { it.level == showTalentLevel && it.mainId == talentMainId }
        }
        val skill = repo.gameCore.getSkillById(showTalent.talentSkill)
        val showSize = if (talentLevel == 0 || talentLevel == showTalent.levelLimit) {
            1
        } else {
            2
        }
        PanelDialog(onDismissRequest = {
            if (GuideManager.guideIndex.value == 29) {
                GuideManager.guideIndex.value = 30
                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE6)
                Dialogs.detailTalentDialog.value = null
                GuideManager.showGuide.value = false
            } else if (GuideManager.guideIndex.value == 43) {
                GuideManager.guideIndex.value = GUIDE_STAGE9
                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE9)
                Dialogs.detailTalentDialog.value = null
                GuideManager.showGuide.value = false
            } else {
                show.value = null
            }
        }, contentBelow = {
            Column(Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
                TalentStarUpView(skill = skill, talent = showTalent)
            }
        }) {
            Column(
                Modifier
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                StrokedText(
                    text = showTalent.name,
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding22))
                val skills =
                    repo.gameCore.getSkillPool().filter { it.mainId == skill.mainId }
                val gifShowed = remember {
                    mutableStateOf(0)
                }
                val oldLevel = remember(gifShowed.value) {
                    mutableStateOf(talentLevel)
                }
                if (showTalent.isType2()) {
                    Box(contentAlignment = Alignment.Center) {
                        if (oldLevel.value != showTalent.level) {
                            GifView(
                                modifier = Modifier
                                    .size(ItemSize.LargePlus.itemSize).scale(2f).graphicsLayer {
                                        translationY = padding20.toPx()
                                    },
                                enabled = true,
                                gifCount = talent2Gif.count,
                                gifDrawable = talent2Gif.gif,
                                pace = talent2Gif.pace,
                            ) {
                                gifShowed.value += 1
                            }
                        }
                        Image(
                            modifier = Modifier.align(Alignment.BottomCenter).width(ItemSize.LargePlus.frameSize)
                                .graphicsLayer {
                                    translationY = padding8.toPx()
                                }.scale(1.5f),
                            painter = painterResource(Res.drawable.talent_base),
                            contentDescription = null
                        )
                        Image(
                            modifier = Modifier.padding(start = padding8).size(ItemSize.LargePlus.frameSize)
                                .clip(RoundedCornerShape(ItemSize.LargePlus.itemSize / 10)),
                            painter = painterResource(kmpDrawableResource(showTalent.icon)),
                            contentDescription = null
                        )
                    }
                } else {
                    Box(contentAlignment = Alignment.Center) {
                        if (oldLevel.value != talentLevel) {
                            GifView(
                                modifier = Modifier
                                    .size(ItemSize.LargePlus.itemSize).scale(2.5f),
                                enabled = true,
                                gifCount = talent3Gif.count,
                                gifDrawable = talent3Gif.gif,
                                pace = talent3Gif.pace,
                            ) {
                                gifShowed.value += 1
                            }
                        }
                        SingleSkillView(
                            skill = skill,
                            itemSize = ItemSize.LargePlus,
                            frame = Res.drawable.user_head_frame,
                            showStars = false,
                            showName = false
                        ) { }
                    }
                }
                Spacer(modifier = Modifier.size(padding10))
                Column(
                    Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(horizontal = padding19)
                        .verticalScroll(
                            rememberScrollState()
                        )
                ) {
                    skills.filter { it.level >= showTalentLevel }.take(showSize).forEach {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            if (it.level == talentLevel + 1) {
                                StrokedText(
                                    text = stringResource(Res.string.next_level),
                                    style = MaterialTheme.typography.h3,
                                    color = Color.Blue
                                )
                                Image(
                                    modifier = Modifier.size(imageSmall),
                                    painter = painterResource(Res.drawable.common_arrow_down),
                                    contentDescription = null
                                )
                            } else if (it.level == talentLevel) {
                                StrokedText(
                                    text = stringResource(Res.string.current_level),
                                    style = MaterialTheme.typography.h3,
                                    color = Color.Green
                                )
                                Image(
                                    modifier = Modifier.size(imageSmall),
                                    painter = painterResource(Res.drawable.common_arrow_down),
                                    contentDescription = null
                                )
                            }
                        }
                        StrokedText(
                            text = "Lv.${it.level}:" + it.getRealDescColorful(
                                MaterialTheme.typography.h3.toSpanStyle()
                            ),
                            style = MaterialTheme.typography.h3,
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.size(padding10))
                    }
                }
            }
        }
    }
}

@Composable
fun TalentStarUpView(modifier: Modifier = Modifier, skill: Skill, talent: Talent) {
    val talentLevel = TalentManager.talents[skill.mainId] ?: 0
    val nextLevelTalent = repo.gameCore.getTalentPool(talent.mainId.mainIdToTalentType())
        .firstOrNull { it.mainId == talent.mainId && it.level == talentLevel + 1 }
    val award = nextLevelTalent?.let { nextTalent ->
        // 消耗看下一级的cost，不看本级的
        if (nextTalent.isType2()) {
            (if (nextTalent.costPool == 0) Award() else repo.gameCore.getPoolById(nextTalent.costPool)
                .toAward()) + Award(diamond = nextTalent.cost)
        } else {
            Award(talentPoint = nextTalent.cost, resources = EMPTY_RESOURCES.toMutableList().apply {
                this[0] = nextTalent.cost2
            })
        }
    } ?: Award()
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.size(padding4))
        val haveNextLevel = talent.level < talent.levelLimit
        Row(verticalAlignment = Alignment.CenterVertically) {
            AwardList(
                modifier = modifier,
                award = award,
                param = defaultParam.copy(
                    showName = false,
                    itemSize = ItemSize.Small,
                    checkAffordable = true,
                    showColumn = false,
                    noFrameForItem = true,
                    numInFrame = false,
                    textColor = Color.White,
                ),
            )
        }
        val (locked, toast) = TalentManager.getLockInfoByTalent(nextLevelTalent?: talent)
        Box {
            GameButton(
                text = if (talentLevel == 0) {
                    if (talent.isType2()) {
                        stringResource(Res.string.building_star_up)
                    } else {
                        stringResource(Res.string.learn_talent3)
                    }
                } else if (haveNextLevel) stringResource(
                    Res.string.level_up
                ) else stringResource(Res.string.level_max),
                buttonStyle = ButtonStyle.Green,
                enabled = talent.level != talent.levelLimit && AwardManager.isAffordable(award),
                buttonSize = ButtonSize.Big,
                locked = locked,
                mute = true,
                onClick = {
                    if (GuideManager.guideIndex.value == 29) {
                        GuideManager.guideIndex.value = 30
                        setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE6)
                        GuideManager.showGuide.value = false
                    } else if (GuideManager.guideIndex.value == 43) {
                        GuideManager.guideIndex.value = GUIDE_STAGE9
                        setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE9)
                        GuideManager.showGuide.value = false
                    }
                    if (locked) {
                        toast.toast()
                        GameCore.instance.onBattleEffect(SoundEffect.Click)
                    } else if (!haveNextLevel) {
                        AppWrapper.getStringKmp(Res.string.level_max).toast()
                        GameCore.instance.onBattleEffect(SoundEffect.Click)
                    } else if (!AwardManager.isAffordable(award)) {
                        if (talent.isType2()) {
                            GiftManager.onTalentResourceNotEnough(award)
                            AppWrapper.getStringKmp(Res.string.talent_resource_not_enough).toast()
                        } else {
                            if (AwardManager.talentPoint.value < talent.cost) {
                                GiftManager.onTalentPointNotEnough()
                                AppWrapper.getStringKmp(Res.string.talent_point_not_enough).toast()
                            } else {
                                GiftManager.onResource1NotEnough()
                                AppWrapper.getStringKmp(Res.string.resource1_not_enough_tips).toast()
                            }
                        }
                        GameCore.instance.onBattleEffect(SoundEffect.Click)
                    } else {
                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                            AwardManager.isAffordable(award, true)
                            Dialogs.detailTalentDialog.value =
                                TalentManager.upgradeTalent(talent).mainId
                        }
                    }
                })
            if (GuideManager.guideIndex.value == 29) {
                GuideHand(
                    modifier = Modifier.align(Alignment.TopCenter)
                        .height(padding70).graphicsLayer {
                            translationY = -padding50.toPx()
                        }.scale(1.14f), handType = HandType.DOWN_HAND
                )
            } else if (GuideManager.guideIndex.value == 43) {
                GuideHand(
                    modifier = Modifier.align(Alignment.TopCenter)
                        .height(padding70).graphicsLayer {
                            translationY = -padding50.toPx()
                        }.scale(1.14f), handType = HandType.DOWN_HAND
                )
            }
        }
    }
}
