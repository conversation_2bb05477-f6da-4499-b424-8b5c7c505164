package com.moyu.chuanqirensheng.feature.illustration.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.illustration.TcgManager
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.filter.CommonFilterView
import com.moyu.chuanqirensheng.widget.filter.FilterLayout
import com.moyu.chuanqirensheng.widget.filter.ItemFilter
import com.moyu.chuanqirensheng.widget.filter.tcgFilterList
import com.moyu.core.model.Tcg
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.tcg_title


@Composable
fun TcgScreen() {
    val tcgs = TcgManager.getShowTcgs().sortedBy {
        val isDone = TcgManager.isDone(it)
        val isGain = TcgManager.isGain(it.type, it.star)
        if (isDone && !isGain) 0 else if (isGain) 9999 else it.type
    }
    val showFilter = remember {
        mutableStateOf(false)
    }
    val filter = remember {
        mutableStateListOf<ItemFilter<Tcg>>(tcgFilterList.first())
    }
    val list = tcgs.filter { ally ->
        filter.all { it.filter.invoke(ally) }
    }
    GameBackground(title = stringResource(Res.string.tcg_title)) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                ),
        )
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Spacer(modifier = Modifier.size(padding8))
            CommonFilterView(
                Modifier.align(Alignment.End).padding(end = padding10), showFilter
            )
            Spacer(modifier = Modifier.size(padding4))
            LazyColumn(modifier = Modifier.fillMaxSize()) {
                items(list.size) { index ->
                    SingleTcg(list[index])
                    Spacer(modifier = Modifier.size(padding5))
                }
            }
        }
        FilterLayout(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(start = padding10, top = padding45),
            show = showFilter,
            filter = filter,
            filterList = tcgFilterList
        )
    }
}