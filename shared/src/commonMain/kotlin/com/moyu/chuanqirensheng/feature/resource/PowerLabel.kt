package com.moyu.chuanqirensheng.feature.resource

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.em
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.ui.theme.SkillLevel5Color
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.battle_power_icon
import shared.generated.resources.battle_power_tips
import shared.generated.resources.power_frame

@Composable
fun PowerLabel(modifier: Modifier, power: Int) {
    Row(
        modifier.paint(painter = painterResource(Res.drawable.power_frame)).clickable {
            GameCore.instance.onBattleEffect(SoundEffect.Click)
            AppWrapper.getStringKmp(Res.string.battle_power_tips).toast()
        },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Spacer(modifier = Modifier.size(padding6))
        Image(
            modifier = Modifier.size(imageSmallPlus),
            painter = painterResource(Res.drawable.battle_power_icon),
            contentDescription = null
        )
        Spacer(modifier = Modifier.size(padding3))
        StrokedText(
            text = power.toString(),
            style = MaterialTheme.typography.h2.copy(
                fontWeight = FontWeight.Thin,
                letterSpacing = (-0.05).em
            ),
            color = SkillLevel5Color
        )
    }
}