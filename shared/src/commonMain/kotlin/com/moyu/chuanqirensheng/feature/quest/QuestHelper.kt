package com.moyu.chuanqirensheng.feature.quest

import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueIfBiggerByKey
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.Sell
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.isMagic

fun getLoginDays(): Int {
    return getIntFlowByKey(KEY_GAME_LOGIN_DAY)
}

//  1=次数，2=天数
fun onTaskStartGameTime() {
    setIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.START_GAME.id + "_1", 1)
}

fun onTaskStartGameDay() {
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.START_GAME.id + "_2")
    increaseIntValueByKey(KEY_GAME_LOGIN_DAY)
}

fun getMaxAge(): Int {
    return getIntFlowByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id,
        0
    )
}

fun onTaskAge(age: Int) {
    setIntValueIfBiggerByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id, age)
    setIntValueIfBiggerByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id, age)

}

fun onTaskEnterEvent(event: Event) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.ENTER_EVENT.id + "_${event.play}")
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ENTER_EVENT.id + "_${event.play}")
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.ENTER_EVENT.id)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ENTER_EVENT.id)
}

fun onTaskKillEnemy(enemies: List<Role>) {
    // 1. 先累加总数，只需调用两次（常规 / 永久）
    val size = enemies.size
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.KILL.id, size)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.KILL.id, size)

    // 2. 根据 raceType 聚合
    val raceTypeCountMap = mutableMapOf<Int, Int>()
    // 3. 根据 raceType2 聚合（注意你加了 10）
    val raceType2CountMap = mutableMapOf<Int, Int>()

    enemies.forEach { enemy ->
        val raceType = enemy.getRace().raceType
        val raceType2 = enemy.getRace().raceType2 + 10

        raceTypeCountMap[raceType] = (raceTypeCountMap[raceType] ?: 0) + 1
        raceType2CountMap[raceType2] = (raceType2CountMap[raceType2] ?: 0) + 1
    }

    // 4. 统一对同 raceType 的数量一次性增加
    raceTypeCountMap.forEach { (type, count) ->
        val key = KEY_GAME_TASK_PROGRESS + QuestEvent.KILL.id + "_$type"
        increaseIntValueByKey(key, count)
        increaseIntValueByKey(FOREVER + key, count)
    }

    // 5. 统一对同 raceType2 的数量一次性增加
    raceType2CountMap.forEach { (type2, count) ->
        val key2 = KEY_GAME_TASK_PROGRESS + QuestEvent.KILL.id + "_$type2"
        increaseIntValueByKey(key2, count)
        increaseIntValueByKey(FOREVER + key2, count)
    }
}

fun onTaskDoneEvent(event: Event) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.DONE_EVENT.id + "_${event.play}")
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.DONE_EVENT.id + "_${event.play}")
}

fun onTaskGetItem(award: Award) {
    if (repo.inGame.value) {
        // todo 仅局内获得道具才计数
        if (award.skills.isNotEmpty()) {
            award.skills.forEach {
                if (it.isMagic()) {
                    increaseIntValueByKey(
                        KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_20",
                        award.skills.size
                    )
                    increaseIntValueByKey(
                        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_20",
                        award.skills.size
                    )
                }
            }
        }
        if (award.resources.any { it > 0 }) {
            award.resources.forEachIndexed { index, i ->
                if (i > 0) {
                    increaseIntValueByKey(
                        KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_5" + "${index + 1}",
                        i
                    )
                    increaseIntValueByKey(
                        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_5" + "${index + 1}",
                        i
                    )
                }
            }
        }
    }
}


fun onTaskEnding(event: Event, win: Boolean) {
}

fun onTaskSpecialDecision1(type: Int) {
}

fun onTaskSpecialDecision2(type: Int) {
}

fun onTaskSpecialDecision3(type: Int) {
}

fun onTaskSpecialDecision4(type: Int) {
}

fun onTaskSpecialDecision5(type: Int) {
}

fun onTaskSpecialDecision6(type: Int) {
}

fun onTaskBuy(sell: Sell) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.BUY.id + "_${sell.type}")
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.BUY.id + "_${sell.type}")
}

fun onTaskStarUp(ally: Ally, level: Int) {
}

fun onTaskDrawAllyCard(count: Int) {
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + QuestEvent.DRAW.id + "_2",
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.DRAW.id + "_2",
        count
    )
}


fun onTaskDrawHeroCard(count: Int) {
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + QuestEvent.DRAW.id + "_1",
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.DRAW.id + "_1",
        count
    )
}


fun onTaskDrawActivityCard(count: Int) {
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + QuestEvent.ACTIVITY_DRAW.id,
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ACTIVITY_DRAW.id,
        count
    )
}

fun onTaskTalentUp(talentType: Int, level: Int) {
    // talentType定义是1,3，以及2xx，然后task表的定义是31,32,33
    val realType = 30 + if (talentType > 200) 2 else talentType
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + QuestEvent.TALENT_UP.id + "_$realType",
        level
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.TALENT_UP.id + "_$realType",
        level
    )
}

fun onTaskPvpWin() {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.PVP_BATTLE.id + "_1")
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.PVP_BATTLE.id)
}

fun onTaskPvpLose() {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.PVP_BATTLE.id)
}

fun onTaskStartFight(enemies: List<Ally>) {
    // 1. 根据 writeType 聚合计数
    val countMap = mutableMapOf<Int, Int>()
    enemies.forEach {
        val writeType = 10 + it.raceType2
        countMap[writeType] = (countMap[writeType] ?: 0) + 1
    }

    // 2. 统一对每种 writeType 调用 increaseIntValueByKey（常规、永久）
    countMap.forEach { (writeType, count) ->
        val key = KEY_GAME_TASK_PROGRESS + QuestEvent.ARMY_TYPE_FIGHT.id + "_$writeType"
        increaseIntValueByKey(key, count)
        increaseIntValueByKey(FOREVER + key, count)
    }
}

fun onTaskWatchAd() {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.WATCH_AD.id)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.WATCH_AD.id)
}

fun onTaskAllyLevelUp(level: Int) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.ALLY_LEVEL_UP.id + "_2")
    if (level == 3) {
        increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ALLY_LEVEL_UP.id + "_3")
    }
    if (level == 10) {
        increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ALLY_LEVEL_UP.id + "_10")
    }
    if (level == 20) {
        increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ALLY_LEVEL_UP.id + "_20")
    }
}