package com.moyu.chuanqirensheng.feature.ending

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.api.postRankData
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.feature.event.adventureSkillTrigger
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.skill.PassStageEvent
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_ENDINGS
import com.moyu.chuanqirensheng.sub.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.AppWrapper
import com.moyu.core.model.Event
import com.moyu.core.model.mainIdToTalentType
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.ending
import shared.generated.resources.game_over_title
import shared.generated.resources.not_login

object EndingManager {
    val endings = mutableStateListOf<Ending>()

    fun init() {
        endings.addAll(
            getListObject(KEY_ENDINGS)
        )
    }

    suspend fun ending(ending: Ending?) {
        MusicManager.stopSound(SoundEffect.HorseWalk)
        MusicManager.stopSound(SoundEffect.ShipSail)
        ending?.let {
            Dialogs.endingDialog.value = ending
            repo.inGame.value = false
            playerMusicByScreen() // 音乐
        }
        adventureSkillTrigger(triggerSkill = PassStageEvent)
    }

    fun uploadRank() {
        // todo 放开lite包上传
        if (DebugManager.uploadRank || !isLite()) {
            AppWrapper.globalScope.launch(Dispatchers.IO) {
                try {
                    postRankData(
                        RankData(
                            time = getCurrentTime(),
                            versionCode = getVersionCode(),
                            userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else gameSdkDefaultProcessor().getUserName()
                                ?: AppWrapper.getStringKmp(Res.string.not_login),
                            userId = gameSdkDefaultProcessor().getObjectId() ?: "0",
                            userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "role_101" else gameSdkDefaultProcessor().getAvatarUrl() ?: "0",
                            tcgValue = TalentManager.talents.filter { it.key.mainIdToTalentType() == 2 }.values.sum(),
                            level = TalentManager.talents.filter { it.key.mainIdToTalentType() == 3 }.values.sum(),
                            battlePower = AwardManager.getAllBattlePower(),
                            pvpScore = PvpManager.pvpScore.value,
                            pvpLastScore = 0, // 废弃
                            pvpData = PvpManager.getPvpData(),
                            endingNum = StageManager.maxStage.value,
                            talentLevel = TalentManager.talents.filter { it.key.mainIdToTalentType() == 1 }.values.sum(),
                            electric = AwardManager.electric.value,
                            platformChannel = platformChannel(),
                            serverId = LoginManager.instance.getSavedServerId(),
                        )
                    )
                } catch (e: Exception) {
//                    Timber.e(e)
                }
            }
        }
    }

    fun saveEnding(
        role: Role, usedEvents: List<Event>, pass: Boolean
    ): Ending? {
        if (usedEvents.isEmpty()) {
            return null
        }
        endings.removeAll { it.uuid == role.extraInfo.allyUuid }
        val lastEvent = usedEvents.last()
        val endingRank = 0.0
        val dieReason = ""

        val endingAge =
            if (pass) BattleManager.adventureProps.value.age else BattleManager.adventureProps.value.age

        val endingTitle =
            if (BattleManager.getAge() >= StoryManager.getMaxAge() && pass) AppWrapper.getStringKmp(
                Res.string.ending
            ) else AppWrapper.getStringKmp(
                Res.string.game_over_title
            )
        val endingText = ""

        return Ending(
            uuid = role.extraInfo.allyUuid,
            ending = endingTitle,
            storyId = 0,
            pass = BattleManager.adventureProps.value.age >= StageManager.getMaxAge() && pass,
            pic = SkinManager.getGameMasterWithSkinDrawable(),
            dieReason = dieReason,
            rank = endingRank,
            lastEvent = lastEvent.name,
            difficult = 0,
            shareTitle = if (pass) lastEvent.winText else lastEvent.loseText,
            age = endingAge,
            kill = DetailProgressManager.detailProgressData.defeatEnemyRecord.size,
            endingText = endingText,
            stage = StageManager.currentStage.value.id,
        )
    }
}