package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.sell.INFINITE_STORAGE
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.imageHuge
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding170
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.padding54
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.ui.theme.shopItemWidth
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.chuanqirensheng.util.millisToMidnight
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.IconView
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.LabelSize
import com.moyu.chuanqirensheng.widget.common.TextLabel2
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.Sell
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.first_purchase_double
import shared.generated.resources.gain_award
import shared.generated.resources.month_card_day
import shared.generated.resources.month_card_immediate
import shared.generated.resources.month_card_tips
import shared.generated.resources.sell_frame
import shared.generated.resources.sell_label
import shared.generated.resources.sell_left_tips
import shared.generated.resources.sell_month_card_frame
import shared.generated.resources.sell_storage_tips
import shared.generated.resources.shop_discount
import shared.generated.resources.task_refresh_left_time
import shared.generated.resources.time_left
import shared.generated.resources.time_left_day

@Composable
fun SingleNormalChest(
    modifier: Modifier = Modifier, sell: Sell, content: @Composable BoxScope.() -> Unit
) {
    Box(
        modifier = modifier, contentAlignment = Alignment.Center
    ) {
        val award = if (sell.isSellKeyForMoneyItem()) {
            if (SellManager.isDoubleGained(sell)) {
                sell.award ?: sell.toAward()
            } else {
                val tmp = sell.award ?: sell.toAward()
                tmp.copy(extraKeyRate = 2)
            }
        } else {
            sell.award ?: sell.toAward()
        }
        if (sell.isRandom()) {
            Column(
                Modifier.width(shopItemWidth), horizontalAlignment = Alignment.CenterHorizontally
            ) {
                EffectButton {
                    IconView(
                        resZIndex = 99f,
                        res = kmpDrawableResource(sell.pic),
                    ) {
                        sell.name.toast()
                    }
                    if (sell.storage in 1..INFINITE_STORAGE) {
                        LeftNumView(modifier = Modifier.align(Alignment.Center), sell)
                    }
                }
                Spacer(modifier = Modifier.size(padding5))
                Box(contentAlignment = Alignment.Center) {
                    StrokedText(
                        modifier = Modifier.width(shopItemWidth).graphicsLayer {
                            translationY = -padding4.toPx()
                        },
                        text = sell.name,
                        style = MaterialTheme.typography.h3,
                        color = Color.White,
                        minLines = LanguageManager.getTextLines(),
                        maxLines = LanguageManager.getTextLines(),
                        textAlign = TextAlign.Center
                    )
                }
                Box {
                    content()
                }
            }
        } else if (sell.isPackageSingle()) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                EffectButton {
                    IconView(
                        resZIndex = 99f,
                        extraPadding = padding8,
                        res = kmpDrawableResource(sell.pic),
                    ) {
                        Dialogs.sellPoolDialog.value = award
                    }
                    if (sell.storage in 1..INFINITE_STORAGE) {
                        LeftNumView(modifier = Modifier.align(Alignment.Center), sell)
                    }
                }
                Spacer(modifier = Modifier.size(padding5))
                Box(contentAlignment = Alignment.Center) {
                    StrokedText(
                        modifier = Modifier.width(shopItemWidth).graphicsLayer {
                            translationY = -padding4.toPx()
                        },
                        text = sell.name,
                        style = MaterialTheme.typography.h3,
                        color = Color.White,
                        minLines = LanguageManager.getTextLines(),
                        maxLines = LanguageManager.getTextLines(),
                        textAlign = TextAlign.Center
                    )
                }
                Box {
                    content()
                }
            }
        } else if (sell.isPackageExp()) {
            Row(
                Modifier.fillMaxWidth().padding(horizontal = padding10).paint(
                    painter = painterResource(Res.drawable.sell_frame),
                    contentScale = ContentScale.FillWidth
                ).graphicsLayer {
                    translationY = -padding8.toPx()
                },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Box(Modifier.size(ItemSize.LargePlus.frameSize)) {
                    Image(
                        modifier = Modifier.fillMaxSize().padding(padding8),
                        contentScale = ContentScale.FillWidth,
                        painter = kmpPainterResource(sell.pic),
                        contentDescription = null
                    )
                    if (sell.storage in 1..INFINITE_STORAGE) {
                        StrokedText(
                            modifier = Modifier.align(Alignment.BottomCenter)
                                .graphicsLayer {
                                    translationY = padding8.toPx()
                                }.clip(RoundedCornerShape(20f)).background(
                                    B35
                                ).padding(horizontal = padding6, vertical = padding2),
                            text = stringResource(Res.string.sell_storage_tips, sell.storage),
                            style = MaterialTheme.typography.h3,
                            color = Color.White,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                Column(Modifier.graphicsLayer {
                    translationY = padding12.toPx()
                }, horizontalAlignment = Alignment.CenterHorizontally) {
                    TextLabel2(
                        text = sell.name,
                        labelSize = LabelSize.Large,
                    )
                    AwardList(
                        // todo 商店里不要显示电力，获得时候有就行
                        award = award.copy(electric = 0),
                        param = defaultParam.copy(itemSize = if (award.resources.count { it > 0 } >= 4) ItemSize.Medium else ItemSize.Large),
                        mainAxisAlignment = Arrangement.spacedBy(padding4),
                    )
                    Box {
                        content()
                    }
                }
            }
        } else if (sell.isMonthCard()) {
            val refresh = remember {
                mutableIntStateOf(0)
            }
            LaunchedEffect(refresh) {
                SellManager.init()
            }
            val leftUpdateTime = remember {
                mutableLongStateOf(0L)
            }
            val leftStorage = remember {
                mutableStateOf(MonthCardManager.getLeftDay(sell.id))
            }
            LaunchedEffect(refresh) {
                refreshNetTime()
                if (isNetTimeValid()) {
                    while (true) {
                        leftUpdateTime.longValue = millisToMidnight(getCurrentTime())
                        leftStorage.value = MonthCardManager.getLeftDay(sell.id)
                        if (leftUpdateTime.longValue <= 1000) {
                            delay(1000)
                            // 修改这个，上面的LauncherEffect会刷新任务
                            refresh.intValue += 1
                        }
                        delay(500)
                    }
                }
            }
            Box(
                modifier = Modifier.size(dialogWidth, padding170).shadow(
                    elevation = padding16, shape = RoundedCornerShape(padding8)
                ).clip(RoundedCornerShape(padding8)) // Clip only the background
                    .paint(
                        painterResource(Res.drawable.sell_month_card_frame),
                        contentScale = ContentScale.FillBounds
                    )
            ) {
                Row(
                    modifier = Modifier.size(dialogWidth, padding170), // Match size but no clipping
                    horizontalArrangement = Arrangement.SpaceAround
                ) {
                    Column(
                        Modifier.fillMaxHeight(), verticalArrangement = Arrangement.SpaceEvenly
                    ) {
                        StrokedText(
                            text = sell.name,
                            style = MaterialTheme.typography.h1,
                            modifier = Modifier.scale(1.2f)
                                .padding(start = padding6, top = padding8)
                        )
                        Row(
                            Modifier.size(padding180, padding26).paint(
                                painterResource(Res.drawable.sell_label),
                                contentScale = ContentScale.FillBounds
                            ).graphicsLayer {
                                translationX = -padding12.toPx()
                            },
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            StrokedText(
                                modifier = Modifier.graphicsLayer {
                                    translationX = padding10.toPx()
                                },
                                text = stringResource(Res.string.month_card_tips),
                                style = MaterialTheme.typography.h5
                            )
                        }
                        Row(modifier.animateContentSize()) {
                            if (!MonthCardManager.canCardGainDailyAward(sell.id)) {
                                Column {
                                    StrokedText(
                                        text = stringResource(Res.string.month_card_immediate),
                                        style = MaterialTheme.typography.h5
                                    )
                                    AwardList(
                                        Modifier,
                                        award = award.copy(electric = 0),
                                        param = defaultParam.copy(
                                            textColor = Color.White,
                                            itemSize = ItemSize.MediumPlus,
                                            minLine = 1,
                                            showName = false
                                        ),
                                        mainAxisAlignment = Arrangement.Center
                                    )
                                }
                                Spacer(Modifier.size(padding4))
                                Spacer(
                                    Modifier.size(padding2, padding60).clip(RoundedCornerShape(50f))
                                        .background(W50)
                                )
                                Spacer(Modifier.size(padding4))
                            }
                            Box(contentAlignment = Alignment.Center) {
                                Column {
                                    StrokedText(
                                        // 仅在买了月卡，并且没过期，并且今天已经领过了不能再领的时候，显示倒计时，其他情况都显示固定文案
                                        text = if (MonthCardManager.getItemById(sell.id)?.expired() != false || MonthCardManager.canCardGainDailyAward(
                                                sellId = sell.id
                                            )
                                        ) stringResource(Res.string.month_card_day) else stringResource(
                                            Res.string.task_refresh_left_time
                                        ) + leftUpdateTime.longValue.millisToHoursMinutesSeconds(),
                                        style = MaterialTheme.typography.h5
                                    )
                                    AwardList(
                                        award = repo.gameCore.getPoolById(sell.itemId2).toAward(),
                                        param = defaultParam.copy(
                                            textColor = Color.White,
                                            itemSize = ItemSize.MediumPlus,
                                            minLine = 1,
                                            showName = false
                                        ),
                                        mainAxisAlignment = Arrangement.Center
                                    )
                                }
                            }
                            if (MonthCardManager.canCardGainDailyAward(sell.id)) {
                                Spacer(Modifier.size(padding4))
                                GameButton(
                                    modifier = Modifier.graphicsLayer {
                                        translationY = padding19.toPx()
                                    },
                                    text = stringResource(Res.string.gain_award),
                                    buttonStyle = ButtonStyle.Green,
                                    buttonSize = ButtonSize.MediumMinus
                                ) {
                                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                                        MonthCardManager.gainDailyAward(sell)
                                    }
                                }
                            }
                        }
                    }
                    Column(
                        modifier = Modifier.fillMaxHeight(), // Allow overflow
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Spacer(Modifier.size(padding6))
                        Box(contentAlignment = Alignment.Center) {
                            Image(
                                modifier = Modifier.size(imageHuge)
                                    .scale(1.2f), // Ensure it renders above other elements
                                painter = painterResource(kmpDrawableResource(sell.pic)),
                                contentDescription = null
                            )

                            leftStorage.value?.let {
                                StrokedText(
                                    text = stringResource(Res.string.time_left) + it + stringResource(
                                        Res.string.time_left_day
                                    ),
                                    style = MaterialTheme.typography.h2,
                                    modifier = Modifier.clip(RoundedCornerShape(padding2))
                                        .background(B50)
                                        .padding(horizontal = padding3, vertical = padding1)
                                )
                            }
                        }
                        Box {
                            content()
                        }
                    }
                }
            }
        } else {
            Column(
                Modifier.width(shopItemWidth), horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(contentAlignment = Alignment.Center) {
                    AwardList(
                        // todo 商店里不要显示电力，获得时候有就行
                        award = award.copy(electric = 0),
                        param = defaultParam.copy(
                            textColor = Color.White,
                            itemSize = ItemSize.LargePlus,
                        ),
                        mainAxisAlignment = Arrangement.Center,
                    )
                    if (sell.storage in 1..INFINITE_STORAGE) {
                        LeftNumView(modifier = Modifier.align(Alignment.Center), sell)
                    }
                }
                Box {
                    content()
                }
            }
        }
        if (sell.desc2 != "0") {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.align(Alignment.TopCenter).graphicsLayer {
                    translationX =
                        if (sell.isMonthCard()) padding150.toPx() else if (sell.isPackageExp()) -padding90.toPx() else -padding34.toPx()
                    translationY =
                        if (sell.isPackageExp()) padding26.toPx() else -padding16.toPx()
                }.scale(if (sell.isPackageExp()) 1.5f else 1.25f)
            ) {
                Image(
                    painter = painterResource(Res.drawable.shop_discount),
                    contentDescription = null,
                    modifier = Modifier.size(padding54).scale(if (sell.isMonthCard()) 1.5f else 1f)
                )
                StrokedText(
                    text = sell.desc2,
                    style = MaterialTheme.typography.h5,
                    maxLines = 2,
                    textAlign = TextAlign.Center
                )
            }
        }
        if (sell.isSellKeyForMoneyItem() && !SellManager.isDoubleGained(sell)) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.align(Alignment.TopCenter).graphicsLayer {
                    translationY = -padding16.toPx()
                    translationX = -padding36.toPx()
                }) {
                Image(
                    painter = painterResource(Res.drawable.shop_discount),
                    contentDescription = null,
                    modifier = Modifier.size(padding66)
                )
                StrokedText(
                    modifier = Modifier.width(padding30),
                    text = stringResource(Res.string.first_purchase_double),
                    style = MaterialTheme.typography.h5,
                    maxLines = 2,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
fun LeftNumView(modifier: Modifier, sell: Sell) {
    StrokedText(
        modifier = modifier.clip(RoundedCornerShape(20f)).background(
            B35
        ).padding(horizontal = padding6, vertical = padding2),
        text = stringResource(Res.string.sell_left_tips, sell.storage),
        style = MaterialTheme.typography.h6,
        color = Color.White
    )
}