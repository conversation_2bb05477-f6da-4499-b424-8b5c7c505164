package com.moyu.chuanqirensheng.feature.reputation.ui

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.router.REPUTATION_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_REPUTATION
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.MainIcon
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.reputation_icon
import shared.generated.resources.reputation_title

@Composable
fun ReputationIcon(itemSize: ItemSize) {
    MainIcon(
        itemSize = itemSize,
        unlocks = listOf(repo.gameCore.getUnlockById(UNLOCK_REPUTATION)),
        click = {
            goto(REPUTATION_SCREEN)
        },
        red = {
            ReputationManager.hasRed()
        },
        title = stringResource(Res.string.reputation_title),
        icon = Res.drawable.reputation_icon
    )
}
