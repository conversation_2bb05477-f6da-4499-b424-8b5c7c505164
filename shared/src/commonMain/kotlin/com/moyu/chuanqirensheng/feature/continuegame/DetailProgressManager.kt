package com.moyu.chuanqirensheng.feature.continuegame

import com.moyu.chuanqirensheng.feature.event.adventureSkillTrigger
import com.moyu.chuanqirensheng.feature.skill.DefeatEnemy
import com.moyu.chuanqirensheng.feature.skill.GetAdventureSkillInGame
import com.moyu.chuanqirensheng.feature.skill.GetAllyCard
import com.moyu.chuanqirensheng.feature.skill.GetBattleSkillInGame
import com.moyu.chuanqirensheng.feature.skill.LoseAllyFromGame
import com.moyu.chuanqirensheng.feature.skill.isUseMoney
import com.moyu.chuanqirensheng.feature.skill.isUseResource
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.Pool
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure


object DetailProgressManager {
    var detailProgressData = DetailProgressData()

    fun onNewGame() {
        detailProgressData = DetailProgressData()
    }

    suspend fun addDefeatEnemies(enemies: List<Role>) {
        enemies.forEach {
            detailProgressData.defeatEnemyRecord.add(RecordItem.create(it.getRace().raceType))
            adventureSkillTrigger(DefeatEnemy.copy(mainId = it.getRace().raceType))
        }
    }

    suspend fun gainInGame(target: Skill) {
        if (target.isAdventure()) {
            detailProgressData.getAdventureCardRecord.add(RecordItem.create(target.elementType))
            adventureSkillTrigger(GetAdventureSkillInGame.copy(mainId = target.elementType))
        } else {
            detailProgressData.getBattleSkillCardRecord.add(RecordItem.create(target.elementType))
            adventureSkillTrigger(GetBattleSkillInGame.copy(mainId = target.elementType))
        }
    }

    suspend fun gainInGame(target: Ally) {
        detailProgressData.getAllyRecord.add(RecordItem.create(target.raceType, number = target.num))
        adventureSkillTrigger(GetAllyCard.copy(mainId = target.raceType))
    }

    suspend fun dropFromGame(target: Ally) {
        detailProgressData.lostAllyRecord.add(RecordItem.create(target.raceType))
        adventureSkillTrigger(LoseAllyFromGame.copy(mainId = target.raceType))
    }

    fun gainedMoreThanRepeatable(pool: Pool, startAge: Int): Boolean {
        return false
    }

    fun gainedMoreThanOneTime(pool: Pool, startAge: Int): Boolean {
        return false
    }

    fun consumedMoreThanRepeatable(skill: Skill?, pool: Pool): Boolean {
        if (pool.type.first() == 14) {
            return skill?.isUseResource(pool.num.first().toInt()) == true
        } else if (pool.type.first() == 13) {
            return skill?.isUseMoney(pool.num.first().toInt()) == true
        }
        return false // 未使用，暂时空
    }

    fun consumedMoreThanOneTime(award: Award): Boolean {
        return false // 未使用，暂时空
    }
}