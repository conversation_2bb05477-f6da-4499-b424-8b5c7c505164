package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import com.eygraber.uri.Uri
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.platform.billPrepay
import com.moyu.chuanqirensheng.platform.hasBilling
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.openGamePage
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.ad.AdHolder
import com.moyu.chuanqirensheng.sub.ad.KEY_BUY_AD_ITEM
import com.moyu.chuanqirensheng.sub.datastore.KEY_INIT_GAME_TIME
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.util.getCurrentDay
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.ButtonType
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.core.AppWrapper
import com.moyu.core.aiFaDian
import com.moyu.core.model.Sell
import com.moyu.core.model.toAward
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.cant_get_yet
import shared.generated.resources.confirm_buy
import shared.generated.resources.confirm_buy2
import shared.generated.resources.diamond_not_enough
import shared.generated.resources.go_and_get
import shared.generated.resources.key_not_enough
import shared.generated.resources.locked
import shared.generated.resources.lottery_run_out
import shared.generated.resources.pvp_diamond_not_enough
import shared.generated.resources.real_money_not_enough
import shared.generated.resources.real_money_not_enough_content
import shared.generated.resources.reputation_money_not_enough
import shared.generated.resources.sold_out


@Composable
fun OneSellItem(sell: Sell, scale: Float = 1f) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        SingleNormalChest(Modifier.scale(scale), sell) {
            SellButton(sell = sell)
        }
        Spacer(modifier = Modifier.size(padding16))
    }
}

@Composable
fun SellButton(
    sell: Sell,
    showRealMoney: Boolean = false
) {
    val unlocked = if (sell.isNewTaskPackage()) {
        // 如果是七日活动的商品，根据日期来解锁
        val day = getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME))
        if (DebugManager.unlockAll) true else {
            repo.gameCore.getDayRewardPool().filter { !it.isHoliday() }
                .firstOrNull { it.value == sell.id }?.let {
                    it.unlock <= day && it.disappear > day
                } == true
        }
    } else if (sell.unlock == 0) true else UnlockManager.getUnlockedFlow(
        repo.gameCore.getUnlockById(
            sell.unlock
        )
    )
    GameButton(
        text = getButtonText(sell, showRealMoney),
        buttonSize = ButtonSize.MediumMinus,
        buttonStyle = ButtonStyle.Blue,
        buttonType = getButtonType(sell, showRealMoney),
        locked = !unlocked,
        enabled = if (sell.isMonthCard()) {
            true
        } else (sell.storage > 0) && checkResourceEnough(sell, false),
        onClick = {
            AppWrapper.globalScope.launch(Dispatchers.Main) {
                if (!unlocked) {
                    // 如果是七日活动的商品，根据日期来解锁
                    if (sell.isNewTaskPackage()) {
                        AppWrapper.getStringKmp(Res.string.locked).toast()
                    } else {
                        repo.gameCore.getUnlockById(sell.unlock).desc.toast()
                    }
                } else {
                    handleSellClick(sell)
                }
            }
        }
    )
}

/**
 * 根据 Sell 计算按钮文案
 */
@Composable
private fun getButtonText(sell: Sell, showRealMoney: Boolean): String {
    // 1. If no stock, show "Sold Out".
    if (sell.storage <= 0) {
        return stringResource(Res.string.sold_out)
    }

    // 2. If not “AiFaDian”, just return numeric price.
    if (!sell.isAifadian()) {
        return sell.price.toString()
    }

    // 4. Otherwise (AiFaDian but not month card), show the price in one of three ways.
    return getPriceLabel(sell, showRealMoney)
}

/**
 * Helper function that decides which "price" string to return
 * based on hasGoogleService() or showRealMoney.
 */
private fun getPriceLabel(sell: Sell, showRealMoney: Boolean): String {
    return when {
        hasGoogleService() -> sell.priceDollar.toString()
        showRealMoney || hasBilling() -> sell.price.toString()
        else -> AppWrapper.getStringKmp(Res.string.go_and_get)
    }
}


/**
 * 根据 Sell 计算按钮类型
 */
private fun getButtonType(sell: Sell, showRealMoney: Boolean): ButtonType {
    return when {
        sell.storage <= 0 -> ButtonType.Normal
        sell.isAifadian() && showRealMoney -> ButtonType.RealMoney
        sell.isAifadian() -> ButtonType.AiFaDian
        sell.isAd() -> ButtonType.Ad
        sell.isKeyMoney() -> ButtonType.Key
        sell.isPvpMoney() -> ButtonType.Pvp
        sell.isReputationMoney() -> ButtonType.Reputation
        else -> ButtonType.Diamond
    }
}

/**
 * 点击按钮后的逻辑入口
 */
private suspend fun handleSellClick(sell: Sell) {
    // 1. 仓库是否还有库存
    if (sell.storage <= 0) {
        AppWrapper.getStringKmp(Res.string.sold_out).toast()
        return
    }
    // 2. 是否为节日限购但已过期
    if (sell.isHoliday() && !HolidayLotteryManager.canDoCheap()) {
        AppWrapper.getStringKmp(Res.string.lottery_run_out).toast()
        return
    }
    // 3. 检查玩家资源是否足够（钻石/钥匙/名望货币等）
    if (!checkResourceEnough(sell)) {
        return
    }

    // 4. 进入实际购买/观看广告/爱发电/确认弹窗的流程
    when {
        sell.isAd() -> buyByWatchingAd(sell)
        sell.isAifadian() -> buyAifadian(sell)
        else -> confirmBuy(sell)
    }
}

/**
 * 检查各种货币资源是否足够，如果不足则做相应提示或操作，返回值表示“是否足够”
 */
private fun checkResourceEnough(sell: Sell, toast: Boolean = true): Boolean {
    // 钻石
    if (sell.isDiamondMoney() && AwardManager.diamond.value < sell.price) {
        if (toast) {
            GiftManager.onDiamondNotEnough()
            AppWrapper.getStringKmp(Res.string.diamond_not_enough).toast()
        }
        return false
    }
    // PVP 钻石
    if (sell.isPvpMoney() && AwardManager.pvpDiamond.value < sell.price) {
        if (toast) {
            GiftManager.onPvpDiamondNotEnough()
            AppWrapper.getStringKmp(Res.string.pvp_diamond_not_enough).toast()
        }
        return false
    }
    // 钥匙
    if (sell.isKeyMoney() && AwardManager.key.value < sell.price) {
        if (toast) {
            GiftManager.onKeyNotEnough()
            AppWrapper.getStringKmp(Res.string.key_not_enough).toast()
        }
        return false
    }
    // 名望
    if (sell.isReputation() && AwardManager.reputationMoney.value < sell.price) {
        if (toast) {
            GiftManager.onReputationMoneyNotEnough()
            AppWrapper.getStringKmp(Res.string.reputation_money_not_enough).toast()
        }
        return false
    }
    // 月卡已经购买，可以点击
    if (sell.isMonthCard() && MonthCardManager.canCardGainDailyAward(sell.id)) {
        return true
    }
    // 爱发电爬塔且没谷歌服务并且真金不够
    if (sell.isAifadianTower() && !hasBilling() && AwardManager.realMoney.value < sell.price) {
        if (toast) {
            handleRealMoneyNotEnough()
        }
        return false
    }
    return true
}

/**
 * 针对“爱发电爬塔”时，真金不足的处理
 */
private fun handleRealMoneyNotEnough() {
    if (LoginManager.instance.canShowAifadian()) {
        Dialogs.alertDialog.value = CommonAlert(
            title = AppWrapper.getStringKmp(Res.string.real_money_not_enough),
            content = AppWrapper.getStringKmp(Res.string.real_money_not_enough_content),
            onConfirm = {
                val uri: Uri = Uri.parse(aiFaDian)
                openGamePage(uri)
            }
        )
    } else {
        AppWrapper.getStringKmp(Res.string.real_money_not_enough).toast()
    }
}

/**
 * 观看广告后再购买
 */
private fun buyByWatchingAd(sell: Sell) {
    AppWrapper.globalScope.launch(Dispatchers.Main) {
        AdHolder.playAd(KEY_BUY_AD_ITEM) {
            AppWrapper.globalScope.launch(Dispatchers.Main) {
                openSellPackage(sell)
            }
        }
    }
}

/**
 * 爱发电购买的逻辑
 */
private suspend fun buyAifadian(sell: Sell) {
    if (hasBilling()) {
        // 谷歌内购流程
        billPrepay(sell) {
            AppWrapper.globalScope.launch(Dispatchers.Main) {
                AwardManager.realGainItem(sell, sell.toAward())
            }
        }
    } else {
        // 非谷歌服务场景
        if (sell.isAifadianTower()) {
            // todo: 爬塔的爱发电 sell 特殊流程
            Dialogs.alertDialog.value = CommonAlert(
                title = AppWrapper.getStringKmp(Res.string.confirm_buy),
                content = AppWrapper.getStringKmp(Res.string.confirm_buy2) + sell.name + "?",
                onConfirm = {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        openSellPackage(sell)
                    }
                }
            )
        } else {
            // desc == "0" 代表不可获取，否则跳转爱发电链接
            if (sell.desc == "0") {
                AppWrapper.getStringKmp(Res.string.cant_get_yet).toast()
            } else {
                if (LoginManager.instance.canShowAifadian()) {
                    val uri: Uri = Uri.parse(sell.desc)
                    openGamePage(uri)
                } else {
                    AppWrapper.getStringKmp(Res.string.cant_get_yet).toast()
                }
            }
        }
    }
}

/**
 * 非广告、非爱发电的购买流程：先确认弹窗
 */
private suspend fun confirmBuy(sell: Sell) {
    openSellPackage(sell)
}

/**
 * 打开礼包/节日/爬塔等统一方法
 */
private suspend fun openSellPackage(sell: Sell) {
    MusicManager.playSound(SoundEffect.OpenChest)
    when {
        sell.isNewTaskPackage() -> SevenDayManager.openPackage(sell)
        sell.isHoliday() -> HolidayManager.openPackage(sell)
        sell.isTower() -> TowerManager.openPackage(sell)
        sell.isReputation() -> ReputationManager.openPackage(sell)
        sell.isMonthCard() -> MonthCardManager.openPackage(sell)
        else -> SellManager.openSellChest(sell)
    }
}
