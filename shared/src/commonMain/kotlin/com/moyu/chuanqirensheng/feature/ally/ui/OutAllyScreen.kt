package com.moyu.chuanqirensheng.feature.ally.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.router.DETAIL_ALLY
import com.moyu.chuanqirensheng.feature.router.isCurrentRoute
import com.moyu.chuanqirensheng.platform.statusBarHeightInDp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.padding93
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import shared.generated.resources.Res
import shared.generated.resources.icon_ally_tab
import shared.generated.resources.icon_arrange
import shared.generated.resources.out_ally_background


@Composable
fun OutAllyScreen() {
    LaunchedEffect(Unit) {
        // 每次进入固定是养成页面
        repo.allyManager.selectedTab.value = 0
    }
    GameBackground(
        showCloseIcon = false,
    ) {
        Image(
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
            painter = kmpPainterResource(Res.drawable.out_ally_background),
            contentDescription = null
        )
        Spacer(
            modifier = Modifier.fillMaxSize().background(B35),
        )
        val currentSlotIndex = remember {
            mutableIntStateOf(0)
        }
        DisposableEffect(Unit) {
            onDispose {
                if (!isCurrentRoute(DETAIL_ALLY)) {
                    // 现在兵种详情是一个完整的页面，这里要注意，跳详情页不要全部去掉红点
                    repo.allyManager.setAllUnNew()
                }
            }
        }
        val listTabItems = remember {
            mutableStateListOf(
                Res.drawable.icon_ally_tab,
                Res.drawable.icon_arrange,
            )
        }
        val pagerState = repo.allyManager.selectedTab
        Column(Modifier.padding(top = statusBarHeightInDp()), horizontalAlignment = Alignment.CenterHorizontally) {
            Column(
                modifier = Modifier.fillMaxWidth().weight(1f),
            ) {
                if (pagerState.value == 1) {
                    AllySlots(repo.allyManager.getOutAlliesPosition(), currentSlotIndex) {
                        repo.allyManager.selectAllyToBattle(it, -1)
                    }
                }
                OutAllyPage(pagerState.value == 1, currentSlotIndex)
            }
        }
        NavigationTab(
            modifier = Modifier.align(Alignment.BottomCenter).padding(bottom = padding93),
            pageState = pagerState,
            titles = listTabItems,
        )
    }
}