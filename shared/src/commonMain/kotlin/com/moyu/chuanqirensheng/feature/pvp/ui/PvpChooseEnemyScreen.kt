package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import coil3.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.api.getPvpByScore
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.pvp.MAX_PVP_NUM
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager.targetsFromServer
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.role.createPvpRole
import com.moyu.chuanqirensheng.feature.role.ui.SingleRoleView
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding54
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isBetween23_45And00_15
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.arena_time_tips
import shared.generated.resources.challenge
import shared.generated.resources.common_frame_long
import shared.generated.resources.duplicated_pk_target_tips
import shared.generated.resources.pvp2_battle_bg
import shared.generated.resources.pvp_choose_enemy
import shared.generated.resources.pvp_top_frame
import shared.generated.resources.refresh
import shared.generated.resources.today_pvp_num
import shared.generated.resources.user_head_frame

@Composable
fun PvpChooseEnemyScreen() {
    GameBackground(
        title = stringResource(Res.string.pvp_choose_enemy),
        bgMask = B35,
        background = Res.drawable.pvp2_battle_bg
    ) {
        LaunchedEffect(PvpManager.pvpScore.value) {
            try {
                delay(200)
                // 拉取pk对手列表
                getPvpByScore(
                    platformChannel(), PvpManager.pvpScore.value
                ).let {
                    targetsFromServer.value =
                        json.decodeFromString(ListSerializer(RankData.serializer()), it.message)
                            .filter { it.userId != gameSdkDefaultProcessor().getObjectId() }
                            .filter {
                                it.userId !in PvpManager.pkTargetList
                            }.filter {
                                it.pvpData.allyIds.isNotEmpty()
                            }
                    if (targetsFromServer.value.size < 30) {
                        targetsFromServer.value += (PvpManager.getMockPvpData().shuffled(RANDOM)
                            .filter {
                                it.userId !in PvpManager.pkTargetList
                            })
                    }
                    PvpManager.refreshTargets()
                }
            } catch (e: Exception) {
//                Timber.e(e)
//                AppWrapper.getStringKmp(Res.string.net_error_retry).toast()
            }
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = padding6)
        ) {
            PvpTopDataRow(
                Modifier
                    .fillMaxWidth().paint(
                        painterResource(Res.drawable.pvp_top_frame),
                        contentScale = ContentScale.FillBounds
                    )
            )
            Spacer(modifier = Modifier.size(padding12))
            PvpManager.currentTargets.value.forEach {
                SinglePvpRecord(it, PvpManager.pkTargetList) { deathRole ->
                    if (!PvpManager.pkTargetList.contains(deathRole.userId)) {
                        PvpManager.pk(deathRole)
                    } else {
                        AppWrapper.getStringKmp(Res.string.duplicated_pk_target_tips).toast()
                    }
                }
            }
            Spacer(modifier = Modifier.size(padding36))
            if (targetsFromServer.value.isNotEmpty()) {
                GameButton(
                    buttonSize = ButtonSize.Big,
                    buttonStyle = ButtonStyle.Blue,
                    text = stringResource(Res.string.refresh)
                ) {
                    PvpManager.refreshTargets()
                }
                Spacer(modifier = Modifier.size(padding3))
                StrokedText(
                    text = stringResource(
                        Res.string.today_pvp_num,
                        minOf(
                            PvpManager.pkNumToday.value,
                            MAX_PVP_NUM + VipManager.getExtraPvpNum()
                        ),
                        MAX_PVP_NUM + VipManager.getExtraPvpNum()
                    ),
                    style = MaterialTheme.typography.h3,
                    color = Color.White
                )
            }
        }
    }
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun SinglePvpRecord(deathRole: RankData, pkTargetList: List<String>, callback: (RankData) -> Unit = {}) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(padding120)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(Res.drawable.common_frame_long),
            contentDescription = null
        )
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding19),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center) {
                    Box(modifier = Modifier.size(padding54)) {
                        Image(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(padding4)
                                .clip(RoundedCornerShape(padding4)),
                            painter = if (deathRole.userPic.startsWith("http")) rememberAsyncImagePainter(
                                deathRole.userPic
                            ) else kmpPainterResource(deathRole.userPic),
                            contentDescription = null
                        )
                        Image(
                            modifier = Modifier.fillMaxSize(),
                            painter = painterResource(Res.drawable.user_head_frame),
                            contentDescription = null
                        )
                    }
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        val rank = PvpManager.getRankLevel(deathRole.pvpLastScore)
                        Image(
                            modifier = Modifier.size(imageSmallPlus),
                            painter = painterResource(kmpDrawableResource(rank.pic)),
                            contentDescription = null
                        )
                        Spacer(Modifier.size(padding2))
                        StrokedText(
                            modifier = Modifier.width(padding50),
                            text = rank.name,
                            style = MaterialTheme.typography.h3,
                            maxLines = 1,
                            overflow = TextOverflow.Visible
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding4))
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight().padding(vertical = padding6),
                    verticalArrangement = Arrangement.SpaceEvenly
                ) {
                    StrokedText(
                        text = deathRole.userName, style = MaterialTheme.typography.h2
                    )
                    val allies = deathRole.pvpData.allyIds.take(8)
                    FlowRow(
                        horizontalArrangement = Arrangement.spacedBy(padding2),
                        overflow = FlowRowOverflow.Visible,
                        maxItemsInEachRow = 4
                    ) {
                        repeat(allies.size) {
                            val role = createPvpRole(
                                repo.gameCore.getAllyById(allies[it]),
                                deathRole.pvpData.talentIds,
                                deathRole.pvpData.equipIds,
                                deathRole.pvpData.tcgIds,
                            )
                            SingleRoleView(
                                Modifier,
                                role = role,
                                itemSize = ItemSize.Small,
                                showName = false,
                                showHp = false
                            )
                        }
                    }
                }
                GameButton(
                    text = stringResource(Res.string.challenge),
                    enabled = !pkTargetList.contains(deathRole.userId)
                ) {
                    if (pkTargetList.contains(deathRole.userId)) {
                        AppWrapper.getStringKmp(Res.string.duplicated_pk_target_tips).toast()
                    } else if (isBetween23_45And00_15(getCurrentTime())) {
                        AppWrapper.getStringKmp(Res.string.arena_time_tips).toast()
                    } else {
                        callback(deathRole)
                    }
                }
            }
        }
    }
}
