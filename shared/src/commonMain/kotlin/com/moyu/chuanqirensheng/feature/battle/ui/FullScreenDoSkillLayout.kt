package com.moyu.chuanqirensheng.feature.battle.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.B85
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding170
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.ForeverGif
import com.moyu.chuanqirensheng.widget.effect.ShadowImage
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.Texture
import com.moyu.chuanqirensheng.widget.effect.magicFrame
import com.moyu.chuanqirensheng.widget.effect.sweepHighlight
import com.moyu.core.model.skill.Skill
import kotlinx.coroutines.delay
import shared.generated.resources.Res
import shared.generated.resources.role_101


@Composable
fun FullScreenDoSkillLayout(pic: String, skill: Skill, isLeft: Boolean) {
    val show = remember {
        mutableStateOf(false)
    }
    LaunchedEffect(skill) {
        show.value = true
        delay(GameSpeedManager.animDuration() * 6)
        show.value = false
    }

    AnimatedVisibility(
        modifier = Modifier.fillMaxSize().padding(top = padding60).graphicsLayer {
            translationY = padding16.toPx()
        },
        visible = show.value, // 这里可改成你的状态
        enter = slideInHorizontally(initialOffsetX = { if (isLeft) -it else it }) + fadeIn(),
        exit = slideOutHorizontally(targetOffsetX = { if (isLeft) it else -it }) + fadeOut()
    ) {
        Spacer(Modifier.fillMaxSize().magicFrame(                      // 只换这一行
            base = B50,
            borderColor = B85,
            texture = Texture.DIAGONAL_STRIPE   // 或 Texture.HORIZONTAL_NOISE
        ).sweepHighlight())
        Box(
            modifier = Modifier.size(padding150, padding260),
            contentAlignment = Alignment.Center
        ) {
            ForeverGif(
                modifier = Modifier.fillMaxSize().scale(1.5f).graphicsLayer {
                    rotationY = if (isLeft) 180f else 0f
                },
                gifCount = 16,
                gifDrawable = "skillshow_"
            )
            // 普通技能
            Column {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    SingleSkillView(
                        skill = skill,
                        itemSize = ItemSize.LargePlus,
                        showName = false
                    ) {}
                    Spacer(modifier = Modifier.size(padding4))
                    StrokedText(
                        text = skill.name,
                        color = Color.White,
                        style = MaterialTheme.typography.h1,
                        maxLines = 1,
                        overflow = TextOverflow.Visible,
                        softWrap = false,
                    )
                }
                ShadowImage(
                    modifier = Modifier.size(padding170).graphicsLayer {
                        rotationY = if (isLeft) 0f else 180f
                    },
                    imageResource = Res.drawable.role_101,
                )
            }
        }
    }
}