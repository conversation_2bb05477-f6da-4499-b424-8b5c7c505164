package com.moyu.chuanqirensheng.feature.battlepass


import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_BATTLE_PASS2
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.GuardedB
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.core.AppWrapper
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Quest
import com.moyu.core.model.toAward
import shared.generated.resources.Res
import shared.generated.resources.already_got

abstract class BaseBattlePassManager(
    private val keyPrefix: String,
    private val getBattlePassPool: () -> List<BattlePass>,
    private val getWarPassValue: () -> Int,
    private val checkWarPass: suspend (BattlePass) -> Unit,
    private val quests: SnapshotStateList<Quest>,
    val hasQuestCountDown: () -> Boolean,
    val isBattlePassBought: () -> Boolean
) {
    private val gainedMap = mutableStateMapOf<Int, MutableState<Boolean>>()

    fun init() {
        getBattlePassPool().forEach {
            gainedMap[it.id] = GuardedB(keyPrefix + it.id)
        }
    }

    fun getCurrentWarPass(): BattlePass? {
        return getBattlePassPool()
            .lastOrNull { it.expRealTotal <= getWarPassValue() }
    }

    suspend fun gain(cheat: BattlePass) {
        val gained = gainedMap[cheat.id]!!
        if (gained.value) {
            AppWrapper.getStringKmp(Res.string.already_got).toast()
            return
        }
        checkWarPass(cheat)
        gained.value = true
        setBooleanValueByKey(keyPrefix + cheat.id, true)
        val award = cheat.toAward()
        Dialogs.awardDialog.value = award
        AwardManager.gainAward(award)
    }

    fun isThisLevelGained(cheat: BattlePass): Boolean {
        return gainedMap[cheat.id]?.value ?: false
    }

    fun getCurrentLevelWarPass(): Int {
        val lastSeasonExpTotal = 0
        val total = getWarPassValue()
        val pre = getCurrentWarPass()?.expRealTotal ?: lastSeasonExpTotal
        return total - pre
    }

    fun hasRed(): Boolean {
        return hasRedItemToGain() || hasQuestRed()
    }

    fun hasQuestRed(): Boolean {
        return quests.any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        }
    }

    fun hasRedItemToGain(): Boolean {
        return getBattlePassPool().any {
            val unlocked = (getCurrentWarPass()?.id ?: 0) >= it.id
            if (unlocked && !isThisLevelGained(it)) {
                !(it.unlockType == 2 && !isBattlePassBought())
            } else {
                false
            }
        }
    }
}

// Constants
const val KEY_WAR_PASS1_UNLOCK_EVIDENCE = 10001
const val KEY_WAR_PASS2_UNLOCK_EVIDENCE = 10011
const val KEY_WAR_PASS3_UNLOCK_EVIDENCE = 10021

// Implementation for Pass 1
object BattlePass1Manager : BaseBattlePassManager(
    keyPrefix = "KEY_BATTLE_PASS1_GAINED",
    getBattlePassPool = { repo.gameCore.getBattlePass1Pool() },
    getWarPassValue = { AwardManager.warPass1.value },
    checkWarPass = { cheat -> com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkWarPass(cheat) },
    quests = QuestManager.warPass1Tasks,
    hasQuestCountDown = { true },
    isBattlePassBought = { AwardManager.battlePass1Bought.value }
)

// Implementation for Pass 2
object BattlePass2Manager : BaseBattlePassManager(
    keyPrefix = "KEY_BATTLE_PASS2_GAINED",
    getBattlePassPool = { repo.gameCore.getBattlePass2Pool() },
    getWarPassValue = { AwardManager.warPass2.value },
    checkWarPass = { cheat -> com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkWarPass2(cheat) },
    quests = QuestManager.warPass2Tasks,
    hasQuestCountDown = { false },
    isBattlePassBought = { AwardManager.battlePass2Bought.value }
)

// Implementation for Pass 3
object BattlePass3Manager : BaseBattlePassManager(
    keyPrefix = "KEY_BATTLE_PASS3_GAINED",
    getBattlePassPool = { repo.gameCore.getBattlePass3Pool() },
    getWarPassValue = { AwardManager.warPass3.value },
    checkWarPass = { cheat -> com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkWarPass3(cheat) },
    quests = QuestManager.warPass3Tasks,
    hasQuestCountDown = { false },
    isBattlePassBought = { AwardManager.battlePass3Bought.value }
)

// 你也可以做一个接口 IBattlePassManager，再让 Manager1/2/3 实现。
// 这里用最简单的方式，用 when 直接返回。
fun getBattlePassManager(passType: Int) = when(passType) {
    1 -> BattlePass1Manager
    2 -> BattlePass2Manager
    3 -> BattlePass3Manager
    else -> error("Unsupported passType: $passType")
}

fun unlockEvidenceKey(passType: Int): Int {
    // 你原本在 WarPass1UnlockDialog.kt/WarPass2UnlockDialog.kt 用 KEY_WAR_PASS1_UNLOCK_EVIDENCE / KEY_WAR_PASS2_UNLOCK_EVIDENCE
    // 若要支持3，需要你新增 KEY_WAR_PASS3_UNLOCK_EVIDENCE
    return when(passType) {
        1 -> KEY_WAR_PASS1_UNLOCK_EVIDENCE
        2 -> KEY_WAR_PASS2_UNLOCK_EVIDENCE
        3 -> KEY_WAR_PASS3_UNLOCK_EVIDENCE
        else -> error("Unsupported passType: $passType")
    }
}