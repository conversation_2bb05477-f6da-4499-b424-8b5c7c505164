package com.moyu.chuanqirensheng.feature.info

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.ally.ui.AllyInfoLayout
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.battle.ui.GameDamageDetailAlertDialog
import com.moyu.chuanqirensheng.text.getQualityFrame
import com.moyu.chuanqirensheng.ui.theme.B85
import com.moyu.chuanqirensheng.ui.theme.W30
import com.moyu.chuanqirensheng.ui.theme.W95
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding400
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.qualityColor1
import com.moyu.chuanqirensheng.ui.theme.qualityColor2
import com.moyu.chuanqirensheng.ui.theme.qualityColor3
import com.moyu.chuanqirensheng.ui.theme.qualityColor4
import com.moyu.chuanqirensheng.ui.theme.qualityColorPurple
import com.moyu.chuanqirensheng.ui.theme.qualityColorYellow
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.IconView
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.EffectBorderBox
import com.moyu.chuanqirensheng.widget.effect.EffectColors
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.Texture
import com.moyu.chuanqirensheng.widget.effect.magicFrame
import com.moyu.core.model.info.BattleInfo
import com.moyu.core.model.info.BattleInfoType
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.day
import shared.generated.resources.first_day_tips
import shared.generated.resources.search_icon

/**
 * 战斗信息面板
 */
@Composable
fun InfoLayout(modifier: Modifier = Modifier, info: SnapshotStateList<BattleInfo>) {
    val alertDialog = remember { mutableStateOf(false) }
    val battleInfoState = remember { mutableStateOf(BattleInfo("")) }
    Box(
        modifier = modifier
            .fillMaxSize(),
        contentAlignment = Alignment.TopStart
    ) {
        val listState = rememberLazyListState()
        // Remember a CoroutineScope to be able to launch
        LazyColumn(
            state = listState,
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(padding4), content = {
                items(info.size) { index ->
                    info.getOrNull(index)?.let {
                        when (it.type) {
                            BattleInfoType.Damage -> DamageInfoLayout(
                                it, alertDialog, battleInfoState
                            )
                            BattleInfoType.DayEvent -> {
                                Column {
                                    Spacer(Modifier.size(padding28))
                                    Row(
                                        modifier = Modifier.fillMaxWidth()
                                            .padding(horizontal = padding8),
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.Start
                                    ) {
                                        if (it.day > 0) {
                                            StrokedText(
                                                text = stringResource(Res.string.day, it.day),
                                                style = MaterialTheme.typography.h2,
                                                color = Color.White,
                                            )
                                            Spacer(Modifier.size(padding4))
                                            StrokedText(
                                                modifier = Modifier.clip(RoundedCornerShape(padding4)).background(
                                                    B85
                                                ).padding(horizontal = padding4, vertical = padding1),
                                                text = it.content,
                                                style = MaterialTheme.typography.h2,
                                                textAlign = TextAlign.Center
                                            )
                                        } else {
                                            StrokedText(
                                                text = stringResource(Res.string.first_day_tips),
                                                style = MaterialTheme.typography.h2,
                                                color = Color.White,
                                            )
                                        }
                                    }
                                }
                            }

                            else -> {
                                if (index == info.size - 1) {
                                    EffectBorderBox(
                                        rotationCount = 100,
                                        effectColor = EffectColors.normal
                                    ) {
                                        ExtraSkillInfoLayout(it)
                                    }
                                } else {
                                    ExtraSkillInfoLayout(it)
                                }
                            }
                        }
                    }
                }
            })
        LaunchedEffect(info.size) {
            if (info.isNotEmpty()) {
                listState.animateScrollToItem(index = info.size - 1)
            }
        }
    }
    GameDamageDetailAlertDialog(alertDialog, battleInfoState.value)
}

@Composable
fun DamageInfoLayout(
    info: BattleInfo,
    alertDialog: MutableState<Boolean>,
    battleInfoState: MutableState<BattleInfo>
) {
    EffectButton(onClick = {
        alertDialog.value = true
        battleInfoState.value = info
    }) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            StrokedText(
                text = info.content,
                style = MaterialTheme.typography.h3.copy(textDecoration = TextDecoration.Underline),
                color = Color.White
            )
        }
    }
}

@Composable
fun ExtraSkillInfoLayout(info: BattleInfo) {
    val backgroundColor = when (info.play) {
        1 -> qualityColor1
        2 -> qualityColor2
        3 -> qualityColor4
        4 -> qualityColor3
        5 -> qualityColorPurple
        else -> qualityColorYellow
    }
    val borderColor = backgroundColor.copy(
        red = backgroundColor.red * 0.8f,
        green = backgroundColor.green * 0.8f,
        blue = backgroundColor.blue * 0.8f
    )

    EffectButton(
        modifier = Modifier
            .fillMaxWidth()
            .defaultMinSize(padding400, padding100)
            .magicFrame(                      // 只换这一行
                base = backgroundColor,
                borderColor = borderColor,
                texture = Texture.CHECKER   // 或 Texture.HORIZONTAL_NOISE
            ),
        onClick = {
            Dialogs.skillDetailDialog.value = info.skill
        }
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = padding19, vertical = padding14)
                .animateContentSize(), // 平滑过渡高度
            verticalArrangement = Arrangement.Center
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                info.skill?.let {
                    IconView(
                        res = kmpDrawableResource(it.icon),
                        frame = 3.getQualityFrame(),
                        itemSize = ItemSize.Small,
                    ) {
                        Dialogs.skillDetailDialog.value = it
                    }
                    Spacer(modifier = Modifier.size(padding4))
                }
                StrokedText(
                    text = info.content,
                    style = MaterialTheme.typography.h3,
                    color = W95
                )
            }
            info.award?.let {
                val showDetails =
                    remember { mutableStateOf(it.outAllies.isNotEmpty()) }

                Spacer(modifier = Modifier.size(padding4))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    AwardList(
                        modifier = Modifier
                            .weight(1f)
                            .graphicsLayer {
                                translationY = -padding4.toPx()
                            },
                        it,
                        param = defaultParam.copy(
                            showColumn = false,
                            showPlus = true,
                            itemSize = ItemSize.Small,
                            showName = true,
                            showNum = true,
                            frameDrawable = null,
                            numInFrame = false,
                            textColor = W95,
                            maxLine = 1,
                            minLine = 1
                        )
                    )
                    IconView(
                        res = Res.drawable.search_icon,
                        frame = null,
                        itemSize = ItemSize.Large,
                    ) {
                        if (it.skills.isNotEmpty()) {
                            Dialogs.gameSkillDialog.value = true
                        } else if (it.battleProperty.isNotEmpty()) {
                            Dialogs.gamePropertyDialog.value = true
                        } else {
                            if (it.outAllies.isNotEmpty()) {
                                showDetails.value = !showDetails.value
                            } else {
                                Dialogs.gameAwardsDialog.value = true
                            }
                        }
                    }
                }

                // 详情 UI：使用 scaleOut 动画，模拟左上角缩小退出
                AnimatedVisibility(
                    visible = showDetails.value,
                    enter = expandVertically(expandFrom = Alignment.Top) + fadeIn(),
                    exit = shrinkVertically(shrinkTowards = Alignment.Top) + fadeOut()
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth().clip(RoundedCornerShape(padding12))
                            .background(W30)
                    ) {
                        it.outAllies.firstOrNull()?.let { ally ->
                            AllyInfoLayout(ally = ally)
                        }
                    }
                }
            }
        }
    }
}