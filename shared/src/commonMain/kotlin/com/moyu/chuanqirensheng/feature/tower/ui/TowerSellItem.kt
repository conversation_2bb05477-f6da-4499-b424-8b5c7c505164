package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.sell.ui.SellButton
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding130
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding54
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding76
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.Tower
import com.moyu.core.model.toAward
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.sell_frame
import shared.generated.resources.shop_discount
import shared.generated.resources.sold_out
import shared.generated.resources.tower_level
import shared.generated.resources.tower_not_done
import shared.generated.resources.tower_not_done_tips


@Composable
fun TowerSellItem(tower: Tower) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(padding130)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(Res.drawable.sell_frame),
            contentDescription = null
        )
        Row(
            Modifier
                .fillMaxSize()
                .padding(horizontal = padding19),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val sell = repo.gameCore.getSellPool().first { it.id == tower.reward }
            Box(Modifier.width(padding90)) {
                Image(
                    modifier = Modifier.size(padding90, padding76).graphicsLayer {
                        translationY = -padding8.toPx()
                    },
                    contentScale = ContentScale.FillHeight,
                    painter = painterResource(kmpDrawableResource(sell.pic)),
                    contentDescription = null
                )
                StrokedText(
                    modifier = Modifier.width(padding90)
                        .align(Alignment.BottomCenter)
                        .graphicsLayer {
                            translationY = padding28.toPx()
                        },
                    text = stringResource(Res.string.tower_level, tower.id),
                    style = MaterialTheme.typography.h3,
                    color = Color.White,
                    minLines = 2,
                    textAlign = TextAlign.Center
                )
                if (sell.desc2 != "0") {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .align(Alignment.TopStart).graphicsLayer {
                                translationY = -padding8.toPx()
                            }
                    ) {
                        Image(
                            painter = painterResource(Res.drawable.shop_discount),
                            contentDescription = null,
                            modifier = Modifier.size(padding54)
                        )
                        StrokedText(
                            text = sell.desc2,
                            style = MaterialTheme.typography.h5,
                            maxLines = 2,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
            Column(Modifier
                .weight(1f)
                .graphicsLayer {
                    translationY = padding6.toPx()
                }, horizontalAlignment = Alignment.CenterHorizontally) {
                AwardList(
                    // todo 商店里不要显示电力，获得时候有就行
                    award = sell.toAward().copy(electric = 0)
                )
            }
            if (tower.id > TowerManager.maxLevel.value) {
                GameButton(
                    buttonSize = ButtonSize.MediumMinus,
                    text = stringResource(Res.string.tower_not_done),
                    enabled = false
                ) {
                    AppWrapper.getStringKmp(Res.string.tower_not_done_tips).toast()
                }
            } else if (tower.id in TowerManager.gainedLevels) {
                GameButton(
                    buttonSize = ButtonSize.MediumMinus,
                    text = stringResource(Res.string.sold_out),
                    enabled = false
                ) {

                }
            } else {
                SellButton(sell)
            }
        }
    }
}