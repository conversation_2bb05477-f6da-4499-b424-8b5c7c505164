package com.moyu.chuanqirensheng.feature.serverrank.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.ui.SingleQuest
import com.moyu.chuanqirensheng.feature.serverrank.ServerRankManager
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.chuanqirensheng.util.timeLeft
import com.moyu.chuanqirensheng.util.toDayHourMinuteSecond
import com.moyu.chuanqirensheng.widget.common.GameLabel2
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Quest
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.time_left

@Composable
fun ServerRankQuestPage(type: Int) {
    val tasks = remember {
        mutableStateListOf<Quest>()
    }
    val refresh = remember {
        mutableStateOf(0)
    }
    val leftUpdateTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(Unit) {
        while (true) {
            leftUpdateTime.longValue = timeLeft(
                getCurrentTime(),
                LoginManager.instance.loginData.value.serverData.createTimeStamp,
                7
            )
            delay(500)
        }
    }
    LaunchedEffect(refresh.value.toString()) {
        if (!isNetTimeValid()) {
            refreshNetTime()
        }
        when (type) {
            1 -> {
                ServerRankManager.createServerRankTasks1()
            }

            2 -> {
                ServerRankManager.createServerRankTasks2()
            }

            else -> {
                ServerRankManager.createServerRankTasks3()
            }
        }

        // 完成的任务排前面，已领取的排最后
        ServerRankManager.getTasksByType(type).map {
            it.copy(done = QuestManager.getTaskDoneFlow(it))
        }.sortedBy { it.order }
            .sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }
            .apply {
                tasks.clear()
                tasks.addAll(this)
            }
    }
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer(modifier = Modifier.size(padding4))
        if (leftUpdateTime.longValue > 0) {
            GameLabel2(
                modifier = Modifier.size(padding120, padding30)) {
                StrokedText(
                    text = stringResource(Res.string.time_left) + leftUpdateTime.longValue.toDayHourMinuteSecond(),                            style = MaterialTheme.typography.h4
                )
            }
        }
        LazyColumn(
            modifier = Modifier
                .fillMaxSize(), content = {
                items(tasks.size) { index ->
                    Spacer(modifier = Modifier.size(padding5))
                    SingleQuest(tasks[index]) {
                        refresh.value += 1
                    }
                }
            })
    }
}