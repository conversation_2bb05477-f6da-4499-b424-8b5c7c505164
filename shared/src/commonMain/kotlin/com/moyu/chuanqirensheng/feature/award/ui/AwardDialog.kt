package com.moyu.chuanqirensheng.feature.award.ui

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.role.ui.DropBattlePropertyLine
import com.moyu.chuanqirensheng.feature.role.ui.DropPropertyLine
import com.moyu.chuanqirensheng.feature.story.toReputationName
import com.moyu.chuanqirensheng.feature.story.toReputationRes
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.text.getEquipQualityFrame
import com.moyu.chuanqirensheng.text.getQualityFrame
import com.moyu.chuanqirensheng.text.indexToResourceIcon
import com.moyu.chuanqirensheng.text.indexToResourceName
import com.moyu.chuanqirensheng.text.indexToResourceTips
import com.moyu.chuanqirensheng.text.toPrefix
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.MaskView
import com.moyu.chuanqirensheng.widget.common.getTextStyle
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.ForeverGif
import com.moyu.chuanqirensheng.widget.effect.GifData
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.awardGif
import com.moyu.chuanqirensheng.widget.effect.orangeItemGif
import com.moyu.chuanqirensheng.widget.effect.redItemGif
import com.moyu.core.AppWrapper
import com.moyu.core.logic.skill.quality
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_REPUTATION
import com.moyu.core.model.EMPTY_RESOURCES
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.account_exp_icon
import shared.generated.resources.account_exp_tips
import shared.generated.resources.account_exp_title
import shared.generated.resources.ad_money
import shared.generated.resources.ad_value_icon
import shared.generated.resources.ally_coupon
import shared.generated.resources.ally_coupon_tips
import shared.generated.resources.ally_exp
import shared.generated.resources.ally_exp_icon
import shared.generated.resources.ally_exp_tips
import shared.generated.resources.awards
import shared.generated.resources.common_charge
import shared.generated.resources.common_key
import shared.generated.resources.common_medal
import shared.generated.resources.confirm
import shared.generated.resources.coupon_ally_icon
import shared.generated.resources.diamond_tips
import shared.generated.resources.diamond_title
import shared.generated.resources.electric_tips
import shared.generated.resources.electric_title
import shared.generated.resources.frame_equip_quality_2
import shared.generated.resources.frame_equip_quality_3
import shared.generated.resources.heal_award
import shared.generated.resources.heal_icon
import shared.generated.resources.hero_coupon
import shared.generated.resources.hero_coupon_icon
import shared.generated.resources.hero_coupon_tips
import shared.generated.resources.holiday_money
import shared.generated.resources.holiday_money_tips
import shared.generated.resources.item_battlepass
import shared.generated.resources.item_battlepass2
import shared.generated.resources.item_battlepass3
import shared.generated.resources.item_battlepass4
import shared.generated.resources.key_tips
import shared.generated.resources.key_title
import shared.generated.resources.lottery_money
import shared.generated.resources.lottery_money_tips
import shared.generated.resources.power
import shared.generated.resources.power_icon
import shared.generated.resources.power_tips
import shared.generated.resources.pvp2_score
import shared.generated.resources.pvp2_score_icon
import shared.generated.resources.pvp2_score_tips
import shared.generated.resources.pvp_diamond
import shared.generated.resources.pvp_diamond_icon
import shared.generated.resources.pvp_diamond_tips
import shared.generated.resources.pvp_score
import shared.generated.resources.pvp_score_icon
import shared.generated.resources.pvp_score_tips
import shared.generated.resources.real_money_icon
import shared.generated.resources.real_money_tips
import shared.generated.resources.real_money_title
import shared.generated.resources.reputation
import shared.generated.resources.reputation_money
import shared.generated.resources.reputation_money_tips
import shared.generated.resources.reputation_tips
import shared.generated.resources.talent_point
import shared.generated.resources.talent_point_icon
import shared.generated.resources.talent_point_tips
import shared.generated.resources.war_pass2_exp
import shared.generated.resources.war_pass2_tips
import shared.generated.resources.war_pass3_exp
import shared.generated.resources.war_pass3_tips
import shared.generated.resources.war_pass4_exp
import shared.generated.resources.war_pass4_tips
import shared.generated.resources.war_pass_exp
import shared.generated.resources.war_pass_tips
import kotlin.math.roundToInt

@Composable
fun AwardDialog(show: MutableState<Award?>) {
    show.value?.let {
        val button = stringResource(Res.string.confirm)
        PanelDialog(onDismissRequest = {
            show.value = null
        }, contentBelow = {
            GameButton(
                text = button,
                buttonSize = ButtonSize.Big,
                buttonStyle = ButtonStyle.Blue,
                onClick = {
                    show.value = null
                })
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                StrokedText(
                    text = stringResource(Res.string.awards),
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding30))
                AwardList(
                    award = it,
                    param = defaultParam.copy(textColor = Color.White, itemSize = ItemSize.Large),
                    mainAxisAlignment = Arrangement.spacedBy(
                        padding10
                    )
                )
            }
            ForeverGif(
                Modifier.align(Alignment.TopCenter)
                    .width(dialogWidth), awardGif.gif, awardGif.count, needGap = true
            )
        }
    }
}

data class AwardUIParam(
    val showName: Boolean = true,
    val itemSize: ItemSize = ItemSize.Large,
    val frameDrawable: DrawableResource? = Res.drawable.frame_equip_quality_3,
    val noFrameForItem: Boolean = false,
    val showNum: Boolean = true,
    val textFrameDrawable: DrawableResource? = null,
    val textFrameDrawableYPadding: Dp = padding0,
    val numInFrame: Boolean = true,
    val frameZIndex: Float = 0f,
    val peek: Boolean = false,
    val textWidthScale: Float = 1f,
    val checkAffordable: Boolean = false,
    val showColumn: Boolean = true,
    val showPlus: Boolean = false,
    val textColor: Color = Color.White,
    val colorFilter: ColorFilter? = null,
    val showEffect: GifData? = null,
    val propertyMuteTeam: Boolean = false,
    val minLine: Int = LanguageManager.getTextLines(),
    val maxLine: Int = LanguageManager.getTextLines(),
    val softWrap: Boolean = LanguageManager.getSoftWrap(),
    val callback: (() -> Unit)? = null,
)

val defaultParam = AwardUIParam()

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AwardList(
    modifier: Modifier = Modifier,
    award: Award,
    mainAxisAlignment: Arrangement.Horizontal = Arrangement.Start,
    param: AwardUIParam = defaultParam,
    paddingHorizontalInDp: Dp = padding0,
    paddingVerticalInDp: Dp = padding0,
) {
    FlowRow(
        modifier = modifier.padding(
            horizontal = paddingHorizontalInDp,
            vertical = paddingVerticalInDp,
        ),
        horizontalArrangement = mainAxisAlignment,
        overflow = FlowRowOverflow.Visible,
    ) {
        award.outAllies.sortedByDescending { it.quality }.groupBy { it.id }.forEach {
            val ally = it.value.first()
            val size = it.value.sumOf { it.num }
            Box {
                SingleAwardItem(
                    name = ally.name,
                    drawable = kmpDrawableResource(ally.getHeadIcon()),
                    num = if (size > 1) "+$size" else "",
                    alignment = Alignment.TopCenter,
                    param = param.copy(
                        showEffect = if (ally.quality < 3) null else if (ally.quality == 3) orangeItemGif else redItemGif,
                        frameDrawable = ally.quality.getEquipQualityFrame(),
                        callback = {
                            param.callback?.invoke() ?: run {
                                Dialogs.allyDetailDialog.value = ally.copy(peek = true)
                            }
                        }),
                )
            }
        }
        award.outEquips.sortedByDescending { it.quality }
            .groupBy { it.id }
            .forEach { equipGroup ->
                val equip = equipGroup.value.first()
                val size = equipGroup.value.sumOf { it.num }
                Box {
                    SingleAwardItem(
                        name = equip.name,
                        drawable = kmpDrawableResource(equip.pic),
                        // If we have more than 1, show "+X"
                        num = if (size > 1) "+$size" else "",
                        contentScale = ContentScale.Fit,
                        param = param.copy(
                            showEffect = if (equip.quality < 3) null else if (equip.quality == 3) orangeItemGif else redItemGif,
                            frameDrawable = equip.quality.getEquipQualityFrame(),
                            callback = {
                                param.callback?.invoke() ?: run {
                                    Dialogs.equipDetailDialog.value = equip.copy(peek = true)
                                }
                            }
                        )
                    )
                }
            }
        award.couponAlly.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.ally_coupon),
                drawable = Res.drawable.coupon_ally_icon,
                num = "$it",
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.ally_coupon_tips).toast()
                    }),
            )
        }
        award.couponEquip.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.hero_coupon),
                drawable = Res.drawable.hero_coupon_icon,
                num = "$it",
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.hero_coupon_tips).toast()
                    }),
            )
        }
        award.realMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.real_money_title),
                drawable = Res.drawable.real_money_icon,
                num = "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(frameDrawable = Res.drawable.frame_equip_quality_3) {
                    AppWrapper.getStringKmp(Res.string.real_money_tips).toast()
                },
            )
        }
        award.key.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.key_title),
                drawable = Res.drawable.common_key,
                num = if (award.extraKeyRate > 0) "${it}x${award.extraKeyRate}" else "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.key_tips).toast()
                    }),
            ) {
                AwardManager.isAffordable(Award(key = it))
            }
        }
        award.diamond.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = if (param.showColumn) stringResource(Res.string.diamond_title) else "",
                num = if (param.showPlus) "+$it" else "$it",
                drawable = Res.drawable.common_medal,
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.diamond_tips).toast()
                    }),
            ) {
                AwardManager.isAffordable(Award(diamond = it))
            }
        }
        award.pvpDiamond.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.pvp_diamond),
                drawable = Res.drawable.pvp_diamond_icon,
                num = "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.pvp_diamond_tips).toast()
                    }),
            ) {
                AwardManager.isAffordable(Award(pvpDiamond = it))
            }
        }
        award.pvpScore.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.pvp_score),
                drawable = Res.drawable.pvp_score_icon,
                num = "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.pvp_score_tips).toast()
                    }),
            ) {
                AwardManager.isAffordable(Award(pvpDiamond = it))
            }
        }
        award.pvp2Score.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.pvp2_score),
                drawable = Res.drawable.pvp2_score_icon,
                num = "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.pvp2_score_tips).toast()
                    }),
            ) {
                AwardManager.isAffordable(Award(pvpDiamond = it))
            }
        }
        award.lotteryMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.lottery_money),
                drawable = Res.drawable.lottery_money,
                num = "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                    AppWrapper.getStringKmp(Res.string.lottery_money_tips).toast()
                }),
            )
        }
        award.holidayMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.holiday_money),
                drawable = Res.drawable.holiday_money,
                num = "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                    AppWrapper.getStringKmp(Res.string.holiday_money_tips).toast()
                }),
            )
        }
        award.warPass1.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.war_pass_exp),
                drawable = Res.drawable.item_battlepass,
                num = "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.war_pass_tips).toast()
                    }),
            )
        }
        award.warPass2.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.war_pass2_exp),
                drawable = Res.drawable.item_battlepass2,
                num = "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.war_pass2_tips).toast()
                    }),
            )
        }
        award.warPass3.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.war_pass3_exp),
                drawable = Res.drawable.item_battlepass3, // 请用你实际的图标
                num = "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.war_pass3_tips).toast()
                    }),
            )
        }
        award.warPass4.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.war_pass4_exp),
                drawable = Res.drawable.item_battlepass4, // 请用你实际的图标
                num = "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.war_pass4_tips).toast()
                    }),
            )
        }
        award.adMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                stringResource(Res.string.ad_money),

                Res.drawable.ad_value_icon,
                "$it",
                extraPadding = param.itemSize.frameSize / 10,
                param = param
            )
        }
        // Talent Point
        award.talentPoint.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.talent_point),
                drawable = Res.drawable.talent_point_icon,  // 请用你实际的图标
                extraPadding = param.itemSize.frameSize / 10,
                num = "$it",
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.talent_point_tips).toast()
                    }),
            ) {
                AwardManager.isAffordable(Award(talentPoint = it))
            }
        }
        award.power.takeIf { it != 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.power),
                drawable = Res.drawable.power_icon,  // 请用你实际的图标
                extraPadding = param.itemSize.frameSize / 10,
                num = "$it",
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.power_tips).toast()
                    }),
            )
        }
        // 声望代币
        award.reputationMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.reputation_money),
                drawable = Res.drawable.reputation_money, // 请用你实际的图标
                extraPadding = param.itemSize.frameSize / 10,
                num = "$it",
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.reputation_money_tips).toast()
                    }),
            )
        }
        // 声望数组
        award.reputations.takeIf { it.any { it != 0 } }?.let {
            it.forEachIndexed { index, i ->
                if (i != 0) {
                    SingleAwardItem(
                        drawable = kmpDrawableResource((index + 1).toReputationRes()),
                        extraPadding = param.itemSize.frameSize / 10,
                        name = if (param.showColumn) (index + 1).toReputationName() + AppWrapper.getStringKmp(
                            Res.string.reputation
                        ) else "",
                        num = if (param.showPlus) "+$i" else "$i",
                        param = param.copy(
                            frameDrawable = 2.getEquipQualityFrame(),
                            callback = param.callback ?: {
                                ((index + 1).toReputationName() + AppWrapper.getStringKmp(Res.string.reputation) + AppWrapper.getStringKmp(
                                    Res.string.reputation_tips
                                )).toast()
                            }),
                    ) {
                        AwardManager.isAffordable(
                            Award(
                                reputations = EMPTY_REPUTATION.toMutableList().apply {
                                    set(index, i)
                                })
                        )
                    }
                }
            }
        }
        award.advProperty.takeIf { it.isNotEmpty() }?.DropPropertyLine(param = param)
        award.battleProperty.takeIf { it.isNotEmpty() }?.forEach {
            it.property.DropBattlePropertyLine(param = param, prefix = it.toPrefix(muteTeam = param.propertyMuteTeam))
        }
        award.skills.forEach {
            Box {
                SingleAwardItem(
                    name = it.name,
                    drawable = kmpDrawableResource(it.icon),
                    num = "",
                    contentScale = ContentScale.Crop,
                    param = param.copy(
                        frameDrawable = it.quality().getQualityFrame(),
                        callback = {
                            param.callback?.invoke() ?: run {
                                Dialogs.skillDetailDialog.value = it.copy(peek = true)
                            }
                        }),
                )
            }
        }
        award.allies.forEach {
            Box {
                SingleAwardItem(
                    name = if (it.num == 1) it.name else it.name + "+${it.num}",
                    drawable = kmpDrawableResource(it.getHeadIcon()),
                    contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                    alignment = Alignment.TopCenter,
                    param = param.copy(
                        frameDrawable = it.quality.getEquipQualityFrame(),
                        callback = {
                            param.callback?.invoke() ?: run {
                                Dialogs.allyDetailDialog.value = it.copy(peek = true)
                            }
                        }),
                )
            }
        }
        award.unlockList.forEach {
            val unlock = repo.gameCore.getUnlockById(it)
            SingleAwardItem(
                name = unlock.name,
                drawable = kmpDrawableResource(unlock.icon),
                contentScale = ContentScale.Crop,
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = {
                        unlock.name.toast()
                    }),
            )
        }
        award.allyExp.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = if (param.showColumn) stringResource(Res.string.ally_exp) else "",
                drawable = Res.drawable.ally_exp_icon,  // 请用你实际的图标
                extraPadding = param.itemSize.frameSize / 10,
                num = if (param.showPlus) "+$it" else "$it",
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_2,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.ally_exp_tips).toast()
                    }),
            )
        }
        award.allHeal.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.heal_award),
                drawable = Res.drawable.heal_icon,
                num = "${it}%",
                param = param.copy(frameDrawable = Res.drawable.frame_equip_quality_2),
            )
        }
        award.accountExp.takeIf { it > 0 }?.let {
            SingleAwardItem(
                drawable = Res.drawable.account_exp_icon,
                extraPadding = param.itemSize.frameSize / 10,
                name = if (param.showColumn) stringResource(Res.string.account_exp_title) else "",
                num = if (param.showPlus) "+$it" else "$it",
                param = param.copy(
                    frameDrawable = 2.getEquipQualityFrame(),
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.account_exp_tips).toast()
                    }),
            )
        }
        award.resources.forEachIndexed { index, i ->
            if (i != 0) {
                SingleAwardItem(
                    // 横向时候，不显示资源名字，否则显示不下
                    name = if (param.showColumn) index.indexToResourceName() else "",
                    drawable = index.indexToResourceIcon(),
                    // 横向时候，显示加号
                    num = if (param.showPlus) "+$i" else "$i",
                    extraPadding = param.itemSize.frameSize / 10,
                    param = param.copy(frameDrawable = Res.drawable.frame_equip_quality_2)
                        .copy(callback = {
                            param.callback?.invoke() ?: run {
                                index.indexToResourceTips().toast()
                            }
                        }),
                    affordable = {
                        AwardManager.isAffordable(
                            Award(
                                resources = EMPTY_RESOURCES.toMutableList().apply {
                                    set(index, i)
                                })
                        )
                    }
                )
            }
        }
        award.electric.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(Res.string.electric_title),
                drawable = Res.drawable.common_charge,
                num = "$it",
                param = param.copy(
                    frameDrawable = Res.drawable.frame_equip_quality_3,
                    callback = param.callback ?: {
                        AppWrapper.getStringKmp(Res.string.electric_tips).toast()
                    }),
            )
        }
    }
}

@Composable
fun SingleAwardItem(
    name: String,
    drawable: DrawableResource?,
    num: String = "",
    extraPadding: Dp = padding0,
    contentScale: ContentScale = ContentScale.Fit,
    alignment: Alignment = Alignment.Center,
    param: AwardUIParam = defaultParam,
    affordable: () -> Boolean = { true },
) {
    if (param.showColumn) {
        Column(
            modifier = Modifier
                .width(param.itemSize.frameSize + padding2)
                .padding(vertical = padding4),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            InnerAwardItem(
                name = name,
                drawable = drawable,
                num = num,
                extraPadding = extraPadding,
                contentScale = contentScale,
                alignment = alignment,
                param = param,
                affordable = affordable
            )
        }
    } else {
        Row(
            modifier = Modifier.padding(horizontal = padding2),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            InnerAwardItem(
                name = name, // 横向显示不需要名字
                drawable = drawable,
                num = num,
                contentScale = contentScale,
                alignment = alignment,
                param = param,
                affordable = affordable
            )
        }
    }
}

@Composable
fun InnerAwardItem(
    name: String,
    drawable: DrawableResource?,
    num: String = "",
    extraPadding: Dp = padding0,
    contentScale: ContentScale = ContentScale.Fit,
    alignment: Alignment = Alignment.Center,
    param: AwardUIParam = defaultParam,
    affordable: () -> Boolean = { true },
) {
    EffectButton(onClick = {
        param.callback?.invoke() ?: run { name.toast() }
    }) {
        param.frameDrawable?.let {
            if (!param.noFrameForItem) {
                Image(
                    modifier = Modifier
                        .size(param.itemSize.frameSize)
                        .zIndex(param.frameZIndex),
                    contentScale = ContentScale.FillBounds,
                    painter = kmpPainterResource(it),
                    contentDescription = null
                )
            }
        }
        drawable?.let {
            Image(
                modifier = Modifier
                    .size(param.itemSize.itemSize).padding(extraPadding)
                    .clip(RoundedCornerShape(param.itemSize.itemSize / 6)),
                alignment = alignment,
                colorFilter = param.colorFilter,
                contentScale = contentScale,
                painter = kmpPainterResource(drawable),
                contentDescription = name
            )
        }
        if (num.isNotEmpty() && param.numInFrame) {
            if (param.showNum) {
                MaskView(
                    modifier = Modifier.align(Alignment.BottomCenter)
                        .padding(horizontal = param.itemSize.itemSize / 14)
                        .padding(bottom = param.itemSize.itemSize / 18),
                    text = num,
                    itemSize = param.itemSize
                )
            }
        }
        param.showEffect?.let {
            val infiniteTransition = rememberInfiniteTransition(label = "")
            val index = infiniteTransition.animateFloat(
                initialValue = 1f,
                targetValue = it.count.toFloat(),
                animationSpec = infiniteRepeatable(
                    animation = tween(2200, easing = LinearEasing),
                    repeatMode = RepeatMode.Restart,
                ),
                label = ""
            )
            if (index.value.roundToInt() <= it.count) { // 做一个间歇的效果
                Image(
                    modifier = Modifier
                        .size(param.itemSize.frameSize)
                        .scale(1.6f).graphicsLayer {
                            translationX = -padding2.toPx()
                            translationY = -padding2.toPx()
                        },
                    painter = kmpPainterResource("${it.gif}${index.value.roundToInt()}"),
                    contentDescription = null
                )
            }
        }
    }
    Spacer(modifier = Modifier.height(padding2))
    Box(Modifier.graphicsLayer {
        translationY = param.textFrameDrawableYPadding.toPx()
    }, contentAlignment = Alignment.Center) {
        param.textFrameDrawable?.let {
            Image(
                modifier = Modifier
                    .fillMaxWidth()
                    .scale(1.8f),
                painter = painterResource(it),
                contentScale = ContentScale.FillWidth,
                contentDescription = null
            )
        }
        val textModifier = if (param.textWidthScale > 1) {
            Modifier.width(param.itemSize.frameSize * param.textWidthScale)
        } else {
            Modifier
        }
        if (param.showName) {
            StrokedText(
                modifier = textModifier,
                text = if (param.numInFrame) name else "$name$num",
                style = param.itemSize.getTextStyle(),
                maxLines = param.maxLine,
                minLines = param.minLine,
                softWrap = param.softWrap,
                overflow = TextOverflow.Visible,
                color = param.textColor,
                textAlign = TextAlign.Center,
            )
        } else {
            if (!param.numInFrame) {
                if (param.showNum) {
                    StrokedText(
                        modifier = textModifier,
                        text = num,
                        style = param.itemSize.getTextStyle(),
                        maxLines = param.maxLine,
                        minLines = param.minLine,
                        softWrap = param.softWrap,
                        color = if (param.checkAffordable && !affordable()) DARK_RED else param.textColor,
                        overflow = TextOverflow.Visible,
                        textAlign = TextAlign.Center,
                    )
                }
            }
        }
    }
}