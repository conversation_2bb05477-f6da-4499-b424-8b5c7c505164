package com.moyu.chuanqirensheng.feature.ally.ui

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.repeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.toMutableStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.feature.equip.ui.AllyFilterRow
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GUIDE_UPGRADE_ALLY_MAIN_ID
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.router.DETAIL_ALLY
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.padding84
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.getTextStyle
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.filter.FilterLayout
import com.moyu.chuanqirensheng.widget.filter.OrderLayout
import com.moyu.chuanqirensheng.widget.filter.allyFilterList
import com.moyu.chuanqirensheng.widget.filter.allyOrderList
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.ally_name_frame
import shared.generated.resources.common_big_frame
import shared.generated.resources.common_choose
import shared.generated.resources.died
import shared.generated.resources.hero_starup
import shared.generated.resources.hurt


@Composable
fun OutAllyPage(showSelect: Boolean = false, currentSlotIndex: MutableIntState) {
    val showFilter = remember {
        mutableStateOf(false)
    }
    val filter = remember {
        repo.allyManager.filterIndexes.map { allyFilterList[it] }.toMutableStateList()
    }
    val showOrder = remember {
        mutableStateOf(false)
    }
    val order = remember {
        mutableStateOf(allyOrderList[repo.allyManager.orderIndex.value])
    }
    val list = repo.allyManager.data.filter { !it.isHero() }.filter { ally ->
        filter.all { it.filter.invoke(ally) }
    }.sortedByDescending { it.getAllPower() }.sortedByDescending { order.value.order?.invoke(it) }.sortedByDescending { if (it.battlePosition >= 0) 9999 else 0 }
    val itemSize = ItemSize.LargePlus
    Box(Modifier.fillMaxSize().padding(bottom = padding84)) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(Res.drawable.common_big_frame),
            contentScale = ContentScale.FillBounds,
            contentDescription = null
        )
        Column(
            modifier = Modifier.fillMaxSize().padding(horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(padding8))
            AllyFilterRow(showFilter, showOrder, showSelect)
            Spacer(modifier = Modifier.size(padding4))
            LazyVerticalGrid(
                modifier = Modifier.weight(1f).fillMaxWidth().padding(bottom = padding26),
                verticalArrangement = Arrangement.spacedBy(-padding20),
                columns = GridCells.Fixed(4),
                content = {
                    items(list.size) { index ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            val ally = list[index]
                            Box {
                                if (ally.battlePosition >= 0) {
                                    Image(
                                        painter = painterResource(Res.drawable.common_choose),
                                        modifier = Modifier.size(imageMedium)
                                            .align(Alignment.BottomEnd).zIndex(999f).graphicsLayer {
                                                translationY = -padding14.toPx()
                                            },
                                        contentDescription = null
                                    )
                                }
                                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                    Box(contentAlignment = Alignment.Center) {
                                        SingleAllyView(
                                            ally = ally,
                                            showName = false,
                                            showLevel = true,
                                            showRed = ally.new || (ally.battlePosition >= 0 && repo.allyManager.canLevelUp(ally)),
                                            showHp = false,
                                            itemSize = itemSize,
                                            extraInfo = if (ally.isDead()) stringResource(Res.string.died)
                                            else if (ally.isHurt()) stringResource(Res.string.hurt) else "",
                                        ) {
                                            if (showSelect) {
                                                // 其他
                                                repo.allyManager.selectAllyToBattle(
                                                    ally,
                                                    currentSlotIndex.intValue,
                                                )
                                                // 刷新
                                                currentSlotIndex.intValue =
                                                    (0..7).toList().firstOrNull {
                                                        repo.allyManager.getOutAlliesPosition()[it] == null
                                                    } ?: 0
                                            } else {
                                                repo.allyManager.currentShowAllyMainId.value = it.mainId
                                                goto(DETAIL_ALLY)
                                            }
                                        }
                                        if (GuideManager.guideIndex.value == 10 && ally.mainId == GUIDE_UPGRADE_ALLY_MAIN_ID) {
                                            GuideHand(
                                                modifier = Modifier.align(Alignment.BottomCenter)
                                                    .height(padding80).graphicsLayer {
                                                        translationY = padding30.toPx()
                                                    }, handType = HandType.UP_HAND
                                            )
                                        }
                                    }
                                    Box(modifier = Modifier.zIndex(-999f).graphicsLayer {
                                        translationY = -padding22.toPx()
                                    }, contentAlignment = Alignment.Center) {
                                        Image(
                                            modifier = Modifier.width(itemSize.itemSize),
                                            contentScale = ContentScale.FillWidth,
                                            painter = painterResource(Res.drawable.ally_name_frame),
                                            contentDescription = null
                                        )
                                        StrokedText(
                                            modifier = Modifier.graphicsLayer {
                                                translationY = padding8.toPx()
                                            }, text = ally.name, style = itemSize.getTextStyle(),
                                            textAlign = TextAlign.Center,
                                        )
                                    }
                                }
                                if (ally.battlePosition >= 0 && repo.allyManager.canStarUp(ally)) {
                                    UpgradeAnimView(
                                        Modifier.align(Alignment.BottomStart)
                                            .padding(bottom = itemSize.itemSize / 3)
                                            .height(itemSize.itemSize / 2)
                                    )
                                }
                            }
                        }
                    }
                })
            Spacer(Modifier.size(padding4))
        }
        OrderLayout(
            modifier = Modifier.align(Alignment.TopStart)
                .padding(start = padding10, top = padding45, bottom = padding50),
            show = showOrder,
            filter = order,
            filterList = allyOrderList
        ) {
            repo.allyManager.updateOrder(order)
        }
        FilterLayout(
            modifier = Modifier.align(Alignment.TopEnd).padding(start = padding10, top = padding45, bottom = padding50),
            show = showFilter,
            filter = filter,
            filterList = allyFilterList
        ) {
            repo.allyManager.updateFilter(filter)
        }
    }
}

@Composable
fun UpgradeAnimView(modifier: Modifier) {
    // 创建一个动画状态，用于处理跳动效果
    val animatedOffsetY = remember { Animatable(0f) }

    // 当组件进入或手指类型改变时，触发跳动动画
    LaunchedEffect(Unit) {
        animatedOffsetY.animateTo(
            targetValue = -20f, // 设置动画的目标值
            animationSpec = repeatable( // 使用repeatable重复动画
                iterations = 999, // 设置动画无限重复
                animation = tween(
                    durationMillis = 500, // 设置动画持续时间
                    easing = FastOutSlowInEasing // 设置动画的缓动效果
                ), repeatMode = RepeatMode.Reverse // 设置动画反向重复
            )
        )
    }
    Image(
        modifier = modifier.graphicsLayer {
                translationY = animatedOffsetY.value
            },
        contentScale = ContentScale.FillHeight,
        painter = painterResource(Res.drawable.hero_starup),
        contentDescription = null
    )
}