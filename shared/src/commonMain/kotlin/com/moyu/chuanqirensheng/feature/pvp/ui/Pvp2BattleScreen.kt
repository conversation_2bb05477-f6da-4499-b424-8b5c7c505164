package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.ally.ui.OtherAllyPage
import com.moyu.chuanqirensheng.feature.ally.ui.OtherAllySlots
import com.moyu.chuanqirensheng.feature.ally.ui.SelectAllyData
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.battle.BattleManager.isMaster
import com.moyu.chuanqirensheng.feature.battle.ui.BattleFieldLayout
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager.filter
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK2_ALLY_IDS
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.core.AppWrapper
import com.moyu.core.logic.role.ALLY_ROW1_FIRST
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Ally
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.master_need_tips
import shared.generated.resources.no_role_to_battle_tips
import shared.generated.resources.only_one_master_tips
import shared.generated.resources.pvp2
import shared.generated.resources.pvp2_battle_bg
import shared.generated.resources.pvp2_rule_desc
import shared.generated.resources.pvp_ally_error
import shared.generated.resources.pvp_error_ally

@Composable
fun Pvp2BattleScreen() {
    LaunchedEffect(Unit) {
        // 将所有角色加入到局内
        BattleManager.selectAllToGame()
        Pvp2Manager.lastPvpAllyIds.forEachIndexed { index, savedId ->
            BattleManager.allyGameData.firstOrNull { it.id == savedId }?.let {
                val position = index + ALLY_ROW1_FIRST
                if (!it.isMaster()) { // 主角默认已经在阵，无需再上阵
                    BattleManager.selectAllyToBattle(it, position)
                }
            }
        }
    }
    GameBackground(
        title = stringResource(Res.string.pvp2),
        bgMask = B35,
        background = Res.drawable.pvp2_battle_bg
    ) {
        val currentSlotIndex = remember {
            mutableIntStateOf(0)
        }
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val filter = { it: Ally -> Pvp2Manager.getCurrentArena().filter(it) }
            val start = {
                if (BattleManager.getBattleAllies().values.isEmpty()) {
                    Dialogs.alertDialog.value =
                        CommonAlert(
                            content = AppWrapper.getStringKmp(Res.string.no_role_to_battle_tips),
                            onConfirm = {
                                repo.battle.value.terminate()
                                repo.inBattle.value = false
                                if (repo.gameMode.value.isPvp2Mode()) {
                                    Pvp2Manager.pkFailed(
                                        emptyList(),
                                        repo.battleRoles.values.mapNotNull { it })
                                } else {
                                    Pvp2Manager.pkFailed(
                                        emptyList(),
                                        repo.battleRoles.values.mapNotNull { it })
                                }
                            })
                    true
                } else if (BattleManager.getBattleAllies().values.none { it.isMaster() }) {
                    AppWrapper.getStringKmp(Res.string.master_need_tips).toast()
                    false
                } else if (BattleManager.getBattleAllies().values.count { it.isMaster() } > 1) {
                    AppWrapper.getStringKmp(Res.string.only_one_master_tips).toast()
                    false
                } else if (positionListEnemy.none { repo.battleRoles[it]?.isOver() == false }) {
                    AppWrapper.getStringKmp(Res.string.pvp_ally_error).toast()
                    false
                } else if (BattleManager.getBattleAllies().values.any {
                        !it.isHero() && !filter(it)
                    }) {
                    (AppWrapper.getStringKmp(Res.string.pvp2_rule_desc) + Pvp2Manager.getCurrentArena().desc).toast()
                    false
                } else {
                    val battleAllies = BattleManager.getPvpBattleRoles()
                    Pvp2Manager.lastPvpAllyIds.clear()

                    val map = battleAllies.toList().sortedBy { it.first }.toMap()
                    Pvp2Manager.lastPvpAllyIds.addAll(map.values.map { it.getAlly().id })
                    setListObject(KEY_PK2_ALLY_IDS, Pvp2Manager.lastPvpAllyIds)
                    repo.setCurrentAllies(battleAllies)
                    if (repo.isCurrentEnemyEmptyOrDead()) {
                        AppWrapper.getStringKmp(Res.string.pvp_error_ally).toast()
                    } else {
                        repo.startBattle()
                    }
                    true
                }
            }
            if (repo.inBattle.value) {
                Box(modifier = Modifier.graphicsLayer {
                    // todo 简单处理pvp战斗场景的位置
                    translationY = -padding36.toPx()
                }) {
                    BattleFieldLayout(repo.battleRoles)
                }
            } else {
                OtherAllySlots(BattleManager.getBattleAllies(), currentSlotIndex) {
                    if (!it.isMaster()) {
                        BattleManager.selectAllyToBattle(it, -1)
                    }
                }
            }
            OtherAllyPage(
                data = SelectAllyData(filter = filter) {
                    start()
                }, showSelect = !repo.inBattle.value, currentSlotIndex
            )
        }
    }
}