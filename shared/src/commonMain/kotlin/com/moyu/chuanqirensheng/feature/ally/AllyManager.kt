package com.moyu.chuanqirensheng.feature.ally

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.toMutableStateList
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.powerToast
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.quest.onTaskAllyLevelUp
import com.moyu.chuanqirensheng.feature.quest.onTaskStarUp
import com.moyu.chuanqirensheng.logic.basic.BasicItemHolder
import com.moyu.chuanqirensheng.logic.basic.ItemHolder
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_ALLIES
import com.moyu.chuanqirensheng.sub.datastore.KEY_ALLY_FILTER
import com.moyu.chuanqirensheng.sub.datastore.KEY_ALLY_ORDER
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.widget.filter.ItemFilter
import com.moyu.chuanqirensheng.widget.filter.ItemOrder
import com.moyu.chuanqirensheng.widget.filter.allyFilterList
import com.moyu.chuanqirensheng.widget.filter.allyOrderList
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.logic.role.HERO_POSITION
import com.moyu.core.logic.role.positionList
import com.moyu.core.logic.role.positionOrderedListAllies
import com.moyu.core.model.Ally
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.MAX_LEVEL
import com.moyu.core.model.MAX_STAR
import com.moyu.core.music.SoundEffect
import shared.generated.resources.Res
import shared.generated.resources.ally_level_up_account_tips
import shared.generated.resources.ally_level_up_exp_tips
import shared.generated.resources.already_max_star_tips
import shared.generated.resources.card_not_enough
import shared.generated.resources.resource1_not_enough_tips
import kotlin.math.max

class AllyManager(
    private val holder: ItemHolder<Ally> = BasicItemHolder(
        saveKey = KEY_ALLIES,
        elementSerializer = Ally.serializer(),
        data = mutableStateListOf(),
        sameGroup = { item1, item2 -> item1.mainId == item2.mainId },
        increase = { current, add ->
            current.copy(
                num = current.num + add.num,
                new = add.new,
                id = if (add.star > current.star) add.id else current.id,
                star = max(current.star, add.star)
            )
        })
) : ItemHolder<Ally> by holder {

    val currentShowAllyMainId = mutableStateOf(-1)
    val orderIndex = mutableStateOf(0)
    val filterIndexes = mutableStateListOf<Int>()
    val selectedTab = mutableStateOf(0)

    override suspend fun init() {
        holder.init()
        orderIndex.value = getIntFlowByKey(KEY_ALLY_ORDER)
//        filterIndexes.addAll(getListObject<Int>(KEY_ALLY_FILTER))
    }

    override fun gain(value: Ally) {
        // todo 注意，如果是首次获得，数量要-1，本体扣除1
        data.indexOfFirst { sameGroup(value, it) }.takeIf { it >= 0 }?.let {
            data[it] = increase(data[it], value)
        } ?: run {
            data.add(value.copy(num = value.num - 1))
            value.getAllPower().powerToast()
        }
        save()
    }

    fun selectToGame(target: Ally) {
        GameCore.instance.onBattleEffect(SoundEffect.EquipItem)
        // 新增逻辑
        if (target.isHero() && !target.selected) {
            data.filter { it.isHero() && it.selected }.forEach { deselectAlly ->
                data.indexOfFirst { it.id == deselectAlly.id }.takeIf { it != -1 }?.let {
                    data[it] = data[it].switchSelect()
                }
            }
        }
        data.indexOfFirst { it.id == target.id }.takeIf { it != -1 }?.let {
            data[it] = data[it].switchSelect()
            save()
        }
    }

    fun starUp(ally: Ally): Ally? {
        if (AwardManager.resources[0] < ally.getStarUpRes()) {
            GiftManager.onResource1NotEnough()
            AppWrapper.getStringKmp(Res.string.resource1_not_enough_tips).toast()
            Dialogs.moneyTransferDialog.value = true
        } else if (ally.getStarUpNum() == 0) {
            AppWrapper.getStringKmp(Res.string.already_max_star_tips).toast()
        } else if (ally.getStarUpNum() > ally.num) {
            AppWrapper.getStringKmp(Res.string.card_not_enough).toast()
        } else if (ally.star >= MAX_STAR) {
            AppWrapper.getStringKmp(Res.string.already_max_star_tips).toast()
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.StarUpItem)
            AwardManager.gainResources(EMPTY_RESOURCES.toMutableList().apply {
                this[0] = -ally.getStarUpRes()
            })
            data.indexOfFirst { it.mainId == ally.mainId }.takeIf { it != -1 }?.let {
                onTaskStarUp(data[it], data[it].star + 1)
                ally.getStarUpPowerDiff().powerToast()
                data[it] = data[it].copy(num = data[it].num - ally.getStarUpNum()).starUp()
                save()
                return data[it]
            }
        }
        return null
    }

    fun levelUp(tempAlly: Ally): Ally? {
        val ally = data.first { it.mainId == tempAlly.mainId }
        if (ally.level >= MAX_LEVEL) {
            AppWrapper.getStringKmp(Res.string.already_max_star_tips).toast()
        } else if (AwardManager.allyExp.value < ally.getLevelUpNum()) {
            GiftManager.onAllyExpNotEnough()
            AppWrapper.getStringKmp(Res.string.ally_level_up_exp_tips).toast()
        } else if (ally.level >= AwardManager.getMasterLevel()) {
            AppWrapper.getStringKmp(Res.string.ally_level_up_account_tips).toast()
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.CardLevelUp)
            AwardManager.gainAllyExp(-ally.getLevelUpNum())
            ally.getLevelUpPowerDiff().powerToast()

            data.indexOfFirst { it.mainId == ally.mainId }.takeIf { it != -1 }?.let {
                onTaskAllyLevelUp(ally.level + 1)
                ally.getLevelUpPowerDiff().powerToast()
                data[it] = data[it].levelUp()
                save()
                return data[it]
            }
        }
        return null
    }

    fun getOutAlliesPosition(): Map<Int, Ally> {
        val mutableMap = mutableMapOf<Int, Ally>()
        data.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = it
        }
        mutableMap[HERO_POSITION] = repo.getMasterAllyFromPool()
        return mutableMap
    }

    fun getOutBattleAllies(): List<Ally> {
        return data.sortedByDescending { it.battlePosition }
    }

    fun oneShotDeselect() {
        getOutAlliesPosition().forEach {
            selectAllyToBattle(it.value, -1)
        }
        selectAllyToBattle(repo.getMasterAllyFromPool(), HERO_POSITION)
    }

    fun oneShotSelect() {
        oneShotDeselect()
        val battleAllies = getOutAlliesPosition()
        positionOrderedListAllies.forEach { targetPosition ->
            if (targetPosition !in battleAllies.keys) {
                getOutBattleAllies().asSequence().filter { !it.isHero() }
                    .filter { it.battlePosition == -1 }
                    .sortedByDescending { it.getAllPower() }
                    .firstOrNull { !it.isDead() }
                    ?.let {
                        selectAllyToBattle(it, targetPosition)
                    }
            }
        }
    }

    fun selectAllyToBattle(ally: Ally, position: Int) {
        if (isAllyInBattle(ally)) {
            data.indexOfItemInGame(ally.id) {
                data[it] = data[it].switchSelectToBattle(-1)
            }
        } else if (position !in positionList && position != -1) {
            return
        } else if (data.any { it.battlePosition == position }) {
            data.indexOfItemInGame(data.first { it.battlePosition == position }.id) {
                data[it] = data[it].switchSelectToBattle(-1)
            }
            data.indexOfItemInGame(ally.id) {
                data[it] = data[it].switchSelectToBattle(position)
            }
        } else {
            data.indexOfItemInGame(ally.id) {
                data[it] = data[it].switchSelectToBattle(position)
            }
        }
        save()
    }

    fun isAllyInBattle(ally: Ally): Boolean {
        return data.any { it.id == ally.id && it.battlePosition >= 0 }
    }

    fun List<Ally>.indexOfItemInGame(uuid: Int, callback: (Int) -> Unit) {
        indexOfFirst { uuid == it.id }.takeIf { it != -1 }?.let { index ->
            callback(index)
        }
    }

    fun getPower(): Int {
        return data.sumOf { it.getAllPower() }
    }

    fun updateFilter(filter: List<ItemFilter<Ally>>) {
        // 养成页的筛选，需要记录到Manager
        repo.allyManager.filterIndexes.clear()
        allyFilterList.forEachIndexed { index, itemFilter ->
            if (filter.contains(itemFilter)) {
                repo.allyManager.filterIndexes.add(index)
            }
            setListObject(KEY_ALLY_FILTER, repo.allyManager.filterIndexes)
        }
    }

    fun updateOrder(order: MutableState<ItemOrder<Ally, Int>>) {
        // 养成页的排序，需要记录到Manager
        repo.allyManager.orderIndex.value = allyOrderList.indexOf(order.value)
        setIntValueByKey(KEY_ALLY_ORDER, repo.allyManager.orderIndex.value)
    }

    fun selectPrev() {
        val filter = repo.allyManager.filterIndexes.map { allyFilterList[it] }
            .toMutableStateList()
        val order = allyOrderList[repo.allyManager.orderIndex.value]
        val orderFilteredList = repo.allyManager.data.filter { !it.isHero() }.filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }.sortedByDescending { it.getAllPower() }.sortedByDescending { order.order?.invoke(it) }.sortedByDescending { if (it.battlePosition >= 0) 9999 else 0 }
        val index = orderFilteredList.indexOfFirst { it.mainId == currentShowAllyMainId.value }
        if (index > 0) {
            currentShowAllyMainId.value = orderFilteredList[index - 1].mainId
        } else {
            currentShowAllyMainId.value = orderFilteredList.lastOrNull()?.mainId ?: -1
        }
    }

    fun selectNext() {
        val filter = repo.allyManager.filterIndexes.map { allyFilterList[it] }
            .toMutableStateList()
        val order = allyOrderList[repo.allyManager.orderIndex.value]
        val orderFilteredList = repo.allyManager.data.filter { !it.isHero() }.filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }.sortedByDescending { it.getAllPower() }.sortedByDescending { order.order?.invoke(it) }.sortedByDescending { if (it.battlePosition >= 0) 9999 else 0 }
        val index = orderFilteredList.indexOfFirst { it.mainId == currentShowAllyMainId.value }
        if (index < orderFilteredList.size - 1) {
            currentShowAllyMainId.value = orderFilteredList[index + 1].mainId
        } else {
            currentShowAllyMainId.value = orderFilteredList.firstOrNull()?.mainId ?: -1
        }
    }

    fun canStarUp(ally: Ally): Boolean {
        val maxStar = ally.star >= MAX_STAR
        val allyNumOk = ally.num >= ally.getStarUpNum()
        val resource1Ok = AwardManager.resources[0] >= ally.getStarUpRes()
        return allyNumOk && !maxStar && resource1Ok
    }

    fun canLevelUp(ally: Ally): Boolean {
        val maxLevel = ally.level >= MAX_LEVEL
        val accountLevelOk = AwardManager.getMasterLevel() > ally.level
        val allyExpOk = AwardManager.allyExp.value >= ally.getLevelUpNum()
        return accountLevelOk && !maxLevel && allyExpOk
    }

    fun canInBattleAllyStarUp(): Boolean {
        return data.filter { it.battlePosition >= 0 }.any { canStarUp(it) }
    }

    fun canInBattleAllyLevelUp(): Boolean {
        return data.filter { it.battlePosition >= 0 }.any { canLevelUp(it) }
    }

    fun getVipRequire(): Int {
        return data.maxOfOrNull { it.vipValue }?: 0
    }
}
