package com.moyu.chuanqirensheng.feature.continuegame

import com.moyu.chuanqirensheng.feature.battle.BattleManager
import kotlinx.serialization.Serializable

@Serializable
data class RecordItem(
    val type: Int,
    val addAge: Int,
    val number: Int = 1
) {
    companion object {
        fun create(type: Int) = RecordItem(type, BattleManager.getAge())
        fun create(type: Int, number: Int) = RecordItem(type, BattleManager.getAge(), number)
    }
}

@Serializable
data class DetailProgressData(
    val defeatEnemyRecord: MutableList<RecordItem> = mutableListOf(), // raceType

    val lostAllyRecord: MutableList<RecordItem> = mutableListOf(), // raceType
    val getAllyRecord: MutableList<RecordItem> = mutableListOf(), // raceType

    val getAdventureCardRecord: MutableList<RecordItem> = mutableListOf(), // elementTyp
    val dropAdventureCardRecord: MutableList<RecordItem> = mutableListOf(), // elementTyp

    val getBattleSkillCardRecord: MutableList<RecordItem> = mutableListOf(), // elementTyp
    val dropBattleSkillCardRecord: MutableList<RecordItem> = mutableListOf(), // elementTyp
)