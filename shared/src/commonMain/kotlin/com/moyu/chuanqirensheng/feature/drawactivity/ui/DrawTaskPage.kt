package com.moyu.chuanqirensheng.feature.drawactivity.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.drawactivity.DrawActivityManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.ui.SingleQuest
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.chuanqirensheng.widget.common.SearchView
import com.moyu.core.model.Quest
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame

@Composable
fun DrawTaskPage() {
    val tasks = remember {
        mutableStateListOf<Quest>()
    }
    val refresh = remember {
        mutableStateOf(0)
    }
    val search = remember {
        mutableStateOf("")
    }
    LaunchedEffect(refresh.value.toString() + search.value) {
        if (!isNetTimeValid()) {
            refreshNetTime()
        }
        DrawActivityManager.init()
        // 完成的任务排前面，已领取的排最后
        DrawActivityManager.drawTasks.map {
            it.copy(done = QuestManager.getTaskDoneFlow(it))
        }.sortedBy { it.order }.sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }.apply {
            tasks.clear()
            if (search.value.isNotEmpty()) {
                tasks.addAll(this.filter { it.name.contains(search.value) })
            } else {
                tasks.addAll(this)
            }
        }
    }
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer(modifier = Modifier.size(padding4))
        if (isLite()) {
            SearchView(search)
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(padding4))
            tasks.forEach {
                SingleQuest(it) {
                    refresh.value += 1
                }
                Spacer(modifier = Modifier.size(padding5))
            }
        }
    }
}