package com.moyu.chuanqirensheng.feature.equip.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.skill.ui.SingleEquipView
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.text.getEquipTypeTips
import com.moyu.chuanqirensheng.text.toEquipSlotRes
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.CardSize
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.equipGif
import com.moyu.core.model.Equipment
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_lock
import shared.generated.resources.locked

val equipTypes = listOf(1, 2, 3, 4, 7, 8, 5, 9, 6)

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun EquipSlots() {
    val itemSize = ItemSize.Large
    val equips = repo.equipManager.data.filter { it.equipped }
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        FlowRow(
            modifier = Modifier.fillMaxWidth().height(CardSize.Medium.height)
                .padding(horizontal = padding16),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalArrangement = Arrangement.SpaceEvenly,
            overflow = FlowRowOverflow.Visible,
            maxItemsInEachRow = 2
        ) {
            equipTypes.take(6).forEach { type ->
                OneEquipSlot(itemSize, equips, type)
            }
        }
        Spacer(Modifier.size(padding6))
        Row(
            horizontalArrangement = Arrangement.spacedBy(padding6),
        ) {
            equipTypes.drop(6).forEach { type ->
                OneEquipSlot(itemSize, equips, type)
            }
        }
    }
}

@Composable
fun OneEquipSlot(itemSize: ItemSize, equips: List<Equipment>, type: Int) {
    val unlock = UnlockManager.getEquipUnlockByIndex(type)
    val unlocked = UnlockManager.getUnlockedFlow(unlock)
    Box(
        modifier = Modifier.width(itemSize.frameSize), contentAlignment = Alignment.Center
    ) {
        val gif = remember {
            mutableStateOf(false)
        }
        equips.firstOrNull { it.type == type }?.takeIf { unlocked }?.let { skill ->
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Box(contentAlignment = Alignment.Center) {
                    SingleEquipView(
                        equipment = skill,
                        itemSize = itemSize,
                        showName = false,
                    )
                    GifView(
                        modifier = Modifier.size(itemSize.itemSize).scale(1.5f),
                        gif.value,
                        equipGif.count,
                        equipGif.gif
                    )
                }
            }
        } ?: run {
            gif.value = true
            EffectButton(onClick = {
                if (!unlocked) {
                    unlock.desc.toast()
                } else {
                    (type).getEquipTypeTips().toast()
                }
            }) {
                Image(
                    modifier = Modifier.size(itemSize.frameSize),
                    painter = painterResource((type).toEquipSlotRes()),
                    contentDescription = null,
                )
                if (!unlocked) {
                    Image(
                        modifier = Modifier.size(imageLarge),
                        painter = painterResource(Res.drawable.common_lock),
                        contentDescription = stringResource(Res.string.locked),
                    )
                }
            }
        }
    }
}
