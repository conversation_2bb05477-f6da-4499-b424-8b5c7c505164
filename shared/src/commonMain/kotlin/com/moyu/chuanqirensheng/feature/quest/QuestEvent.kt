package com.moyu.chuanqirensheng.feature.quest


enum class QuestEvent(val id: Int) {
    START_GAME(1),
    GAME_PROGRESS(2),
    ENTER_EVENT(3),
    KILL(4),
    DONE_EVENT(5),
    GAIN_ITEM(7),
    <PERSON>AV<PERSON>_TYPE_ITEM(10),
    <PERSON><PERSON><PERSON>_STAR_ITEM(11),
    BUY(16),
    TALENT_UP(17),
    PVP_BATTLE(19),
    PVP_RANK(20),
    DRAW(21),
    COST(22), // 通过AwardManager的keyCost实现
    CHARGE(23), // 和7日活动的充值绑定，通过SevenDayManager实现
    HOLIDAY_RANK(24),
    HOLIDAY_LOTTERY(25),
    HOLIDAY_CHARGE(26),
    PVP2_RANK(27),
    TOWER(28),
    ARMY_TYPE_FIGHT(29),
    WATCH_AD(30),
    PASS_STAGE(31),
    ALLY_LEVEL_UP(32),
    ACTIVITY_DRAW(33),
    SERVER_RANK_1(34),
    SERVER_RANK_2(35),
    SERVER_RANK_3(36),
}
