package com.moyu.chuanqirensheng.feature.unlock.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.bigButtonWidth
import com.moyu.chuanqirensheng.ui.theme.dialogSmallHeight
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.dialog.EmptyDialog
import com.moyu.chuanqirensheng.widget.effect.ForeverGif
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.playerlevelUpGif
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.sell_label
import shared.generated.resources.unlock_title


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun UnlockStatusDialog(show: MutableState<Pair<List<Boolean>, List<Boolean>>?>) {
    show.value?.takeIf {
        val (unlockStatus, unlockStatus2) = it
        var anyUnlocked = false
        unlockStatus.forEachIndexed { index, b ->
            if (!b && unlockStatus2[index]) {
                anyUnlocked = true
            }
        }
        anyUnlocked
    }?.let {
        val (unlockStatus, unlockStatus2) = it
        EmptyDialog(onDismissRequest = {
            var updated = false
            unlockStatus.forEachIndexed { index, b ->
                if (!updated && !b && unlockStatus2[index]) {
                    show.value = Pair(unlockStatus.toMutableList().let {
                        it[index] = true
                        it
                    }, unlockStatus2)
                    updated = true
                }
            }
            if (!updated) {
                show.value = null
            }
        }) {
            ForeverGif(
                modifier = Modifier.size(bigButtonWidth),
                gifCount = playerlevelUpGif.count,
                gifDrawable = playerlevelUpGif.gif,
                scale = 2.0f,
                needGap = true,
            )
            Column(
                Modifier.fillMaxWidth().height(dialogSmallHeight),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier.size(padding260, padding48),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(Res.drawable.sell_label),
                        contentDescription = null
                    )
                    StrokedText(
                        text = stringResource(Res.string.unlock_title),
                        style = MaterialTheme.typography.h1,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )
                }
                Spacer(Modifier.size(padding48))
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    var showIconFound = false
                    unlockStatus.forEachIndexed { index, b ->
                        if (!b && unlockStatus2[index] && !showIconFound) {
                            val unlock = repo.gameCore.getUnlockPool().filter { it.canShowDialog() }[index]
                            LaunchedEffect(unlock.id) {
                                reportManager().unlockItem(unlock.id)
                            }
                            Image(
                                painter = painterResource(kmpDrawableResource(unlock.icon)),
                                modifier = Modifier.size(
                                    padding150
                                ),
                                contentDescription = null
                            )
                            StrokedText(
                                text = unlock.name,
                                style = MaterialTheme.typography.h1,
                                color = Color.White,
                                textAlign = TextAlign.Center
                            )
                            Spacer(Modifier.size(padding22))
                            StrokedText(
                                modifier = Modifier.width(padding200),
                                text = unlock.dialog,
                                style = MaterialTheme.typography.h2,
                                color = Color.White,
                                textAlign = TextAlign.Center
                            )
                            showIconFound = true
                        }
                    }
                }
            }
        }
    }
}