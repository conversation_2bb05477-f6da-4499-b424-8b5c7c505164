package com.moyu.chuanqirensheng.feature.skill.ui

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.text.getEquipQualityFrame
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.Stars
import com.moyu.chuanqirensheng.widget.common.getTextStyle
import com.moyu.chuanqirensheng.widget.common.getTextStyleSmall
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.orangeItemGif
import com.moyu.chuanqirensheng.widget.effect.redItemGif
import com.moyu.core.model.Equipment
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.red_icon
import kotlin.math.roundToInt


@Composable
fun SingleEquipView(
    equipment: Equipment,
    showName: Boolean = true,
    extraInfo: String = "",
    textColor: Color = Color.White,
    extraFrame: DrawableResource? = null,
    frameZIndex: Float = 0f,
    showRed: Boolean = true,
    colorFilter: ColorFilter? = null,
    showEffect: Boolean = false,
    itemSize: ItemSize = ItemSize.LargePlus,
    selectCallBack: (Equipment) -> Unit = { Dialogs.equipDetailDialog.value = it }
) {
    EffectButton(modifier = Modifier, onClick = {
        selectCallBack(equipment)
    }) {
        Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
            Box(contentAlignment = Alignment.Center) {
                Image(
                    modifier = Modifier.size(itemSize.frameSize).zIndex(frameZIndex),
                    painter = kmpPainterResource(extraFrame?: equipment.quality.getEquipQualityFrame()),
                    contentDescription = null,
                    colorFilter = colorFilter,
                )
                Image(
                    modifier = Modifier
                        .size(itemSize.itemSize)
                        .clip(RoundedCornerShape(itemSize.itemSize / 5)),
                    painter = kmpPainterResource(equipment.pic),
                    colorFilter = colorFilter,
                    contentDescription = null
                )
                if (showEffect && ((equipment.quality >= 3))) {
                    val infiniteTransition = rememberInfiniteTransition(label = "")
                    val gifData = if (equipment.quality == 3) orangeItemGif else redItemGif
                    val index = infiniteTransition.animateFloat(
                        initialValue = 1f,
                        targetValue = gifData.count.toFloat(),
                        animationSpec = infiniteRepeatable(
                            animation = tween(2200, easing = LinearEasing),
                            repeatMode = RepeatMode.Restart,
                        ),
                        label = ""
                    )
                    if (index.value.roundToInt() <= gifData.count) { // 做一个间歇的效果
                        Image(
                            modifier = Modifier
                                .size(itemSize.frameSize)
                                .scale(1.62f).graphicsLayer {
                                    translationX = -padding2.toPx()
                                    translationY = -padding2.toPx()
                                },
                            painter = kmpPainterResource("${gifData.gif}${index.value.roundToInt()}"),
                            contentDescription = null
                        )
                    }
                }
                if (extraInfo.isNotEmpty()) {
                    StrokedText(text = extraInfo, style = itemSize.getTextStyle(), color = Color.White)
                }
                Stars(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(itemSize.itemSize / 10).size(itemSize.itemSize / 3),
                    equipment.star,
                    style = itemSize.getTextStyleSmall()
                )
                if (showRed && equipment.new) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .size(imageTinyPlus),
                        painter = painterResource(Res.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
            if (showName) {
                Spacer(modifier = Modifier.size(padding2))
                StrokedText(
                    modifier = Modifier.width(itemSize.frameSize * 1.2f),
                    text = equipment.name,
                    style = itemSize.getTextStyle(),
                    maxLines = LanguageManager.getTextLines(),
                    minLines = LanguageManager.getTextLines(),
                    textAlign = TextAlign.Center,
                    color = textColor,
                )
            }
        }
    }
}