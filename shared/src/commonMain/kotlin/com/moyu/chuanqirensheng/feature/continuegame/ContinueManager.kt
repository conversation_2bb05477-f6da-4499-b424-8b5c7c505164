package com.moyu.chuanqirensheng.feature.continuegame

import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.router.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_RECORD
import com.moyu.chuanqirensheng.sub.datastore.KEY_RECORD_EVENT
import com.moyu.chuanqirensheng.sub.datastore.getObjectFromStore2
import com.moyu.chuanqirensheng.sub.datastore.setObjectToStore2
import com.moyu.core.AppWrapper
import com.moyu.core.model.Event
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 注意，局内的未完成游戏，独立一个存档，和云存档隔离
 */
object ContinueManager {
    fun haveSaver(): Boolean {
        return getObjectFromStore2<ContinueData>(KEY_RECORD) != null
    }

    fun recreateGame() {
        getObjectFromStore2<ContinueData>(KEY_RECORD)?.let { recordData ->
            BattleManager.you.value = recordData.you

            EventManager.resetEventRecorder(
                recordData.usedEvents.map { it.create() },
                recordData.winEvents.map { it.create() },
                recordData.loseEvents.map { it.create() }
            )

            EventManager.selectionEvents.clear()
            EventManager.selectionEvents.addAll(recordData.selectionEvents.map {
                it.create().createUUID()
            })

            DetailProgressManager.detailProgressData = recordData.records

            BattleManager.skillGameData.clear()
            BattleManager.skillGameData.addAll(recordData.skillGameData.map {
                it.create()
                    .copy(life = it.life, extraInfo = it.extraInfo)
            })

            BattleManager.allyGameData.clear()
            BattleManager.allyGameData.addAll(recordData.allyGameData
                .map { it.create() })

            BattleManager.adventureProps.value = recordData.adventureProps
            BattleManager.battleProp.value = recordData.battleProp
            BattleManager.propertyAwards.clear()
            BattleManager.propertyAwards.addAll(recordData.propertyAwards)

            BattleManager.battleRaceProps.clear()
            BattleManager.battleRaceProps.addAll(recordData.battleRaceProps)

            BattleManager.battleSkillPropMap.clear()
            BattleManager.battleSkillPropMap.putAll(recordData.battleSkillPropMap)

            BattleManager.masterSkills.clear()
            BattleManager.masterSkills.addAll(recordData.troopSkills.map { it.create() })

            BattleManager.currentBgMusic.value = MusicManager.getRandomDungeonMusic()
            BattleManager.battleAward.value = recordData.award.recreate()

            StageManager.currentStage.value = repo.gameCore.getDungeonPool().first { it.id == recordData.stageId }
            getObjectFromStore2<Event>(KEY_RECORD_EVENT)?.createOrNull()?.let { event ->
                goto(EVENT_SELECT_SCREEN)
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    EventManager.selectEvent(event)
                    EventManager.doEventResult(
                        event, EventManager.getOrCreateHandler(event).skipWin
                    )
                }
            } ?: kotlin.run {
                goto(EVENT_SELECT_SCREEN)
            }
        }
    }

    fun selectEvent(event: Event) {
        setObjectToStore2(KEY_RECORD_EVENT, event)
    }

    fun clearSave() {
        setObjectToStore2(KEY_RECORD_EVENT, Event(-1))
        setObjectToStore2(KEY_RECORD, null)
    }

    fun onSelections(events: List<Event>) {
        setObjectToStore2(KEY_RECORD_EVENT, Event(-1))
        setObjectToStore2(
            KEY_RECORD, ContinueData(
                you = BattleManager.you.value,
                usedEvents = EventManager.getUsedEvents(),
                winEvents = EventManager.getSucceededEvents(),
                loseEvents = EventManager.getFailedEvents(),
                selectionEvents = events,
                records = DetailProgressManager.detailProgressData,
                skillGameData = BattleManager.skillGameData,
                allyGameData = BattleManager.allyGameData,
                propertyAwards = BattleManager.propertyAwards,
                adventureProps = BattleManager.adventureProps.value,
                battleProp = BattleManager.battleProp.value,
                battleRaceProps = BattleManager.battleRaceProps,
                battleSkillPropMap = BattleManager.battleSkillPropMap,
                troopSkills = BattleManager.masterSkills,
                award = BattleManager.battleAward.value,
                initialProperty = BattleManager.initialProperty.value,
                stageId = StageManager.currentStage.value.id
            )
        )
    }
}