package com.moyu.chuanqirensheng.feature.rank

import kotlinx.serialization.Serializable

@Serializable
data class LikedData(
    val userId1: String = "",
    val userId2: String = "",
    val userId3: String = "",
    val userId4: String = "",
    val userId5: String = "",
    val serverId: Int = 0,
) {
    fun isNotEmpty(): <PERSON><PERSON><PERSON> {
        return userId1.isNotEmpty()
    }

    fun addLiked(userId: String): LikedData {
        return if (userId1.isEmpty()) {
            copy(userId1 = userId)
        } else if (userId2.isEmpty()) {
            copy(userId2 = userId)
        } else if (userId3.isEmpty()) {
            copy(userId3 = userId)
        } else if (userId4.isEmpty()) {
            copy(userId4 = userId)
        } else if (userId5.isEmpty()) {
            copy(userId5 = userId)
        } else {
            error("LikedData is full!")
        }
    }

    fun getExtraNum(userId: String): Int {
        var count = 0
        if (userId1 == userId) {
            count++
        }
        if (userId2 == userId) {
            count++
        }
        if (userId3 == userId) {
            count++
        }
        if (userId4 == userId) {
            count++
        }
        if (userId5 == userId) {
            count++
        }
        return count
    }
}