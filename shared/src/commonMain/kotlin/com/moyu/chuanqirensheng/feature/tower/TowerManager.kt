package com.moyu.chuanqirensheng.feature.tower

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.api.postTowerRankData
import com.moyu.chuanqirensheng.application.Dialogs.awardDialog
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gamemode.GameMode
import com.moyu.chuanqirensheng.feature.gamemode.MODE_TOWER
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.router.TOWER_BATTLER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.sell.SELL_FOREVER
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TOWER
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.hasBilling
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAINED_TOWER_LEVELS
import com.moyu.chuanqirensheng.sub.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.sub.datastore.KEY_MAX_TOWER_LEVEL
import com.moyu.chuanqirensheng.sub.datastore.KEY_TOWER_ALLY_IDS
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.AppWrapper
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.Sell
import com.moyu.core.model.Tower
import com.moyu.core.model.role.Role
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.not_login
import shared.generated.resources.tower_fight_tips
import shared.generated.resources.tower_type1
import shared.generated.resources.tower_type2
import shared.generated.resources.tower_type3
import shared.generated.resources.tower_type4

fun Tower.getTypeName(): String {
    return when (type.first()) {
        1 -> AppWrapper.getStringKmp(Res.string.tower_type1)
        2 -> AppWrapper.getStringKmp(Res.string.tower_type2)
        3 -> AppWrapper.getStringKmp(Res.string.tower_type3)
        else -> AppWrapper.getStringKmp(Res.string.tower_type4)
    }
}

object TowerManager {
    val targetLevel = mutableStateOf(repo.gameCore.getTowerPool().first())
    val maxLevel = Guarded(KEY_MAX_TOWER_LEVEL)
    val gainedLevels = mutableStateListOf<Int>()
    val lastTowerAllyIds = mutableListOf<Int>()

    fun init() {
        if (maxLevel.value > repo.gameCore.getTowerPool().size) {
            maxLevel.value = repo.gameCore.getTowerPool().size
        }
        gainedLevels.clear()
        gainedLevels.addAll(getListObject(KEY_GAINED_TOWER_LEVELS))

        lastTowerAllyIds.clear()
        lastTowerAllyIds.addAll(getListObject(KEY_TOWER_ALLY_IDS))
    }

    fun hasRed(): Boolean {
        // 获取当前等级可以解锁的所有奖励关卡
        val rewardLevels = (5..maxLevel.value).filter { it % 5 == 0 }.filter { towerId ->
            val tower = repo.gameCore.getTowerPool().first { it.id == towerId }
            val sell = repo.gameCore.getSellPool().first { it.id == tower.reward }
            sell.isFreeGift()
        }

        // 检查这些奖励关卡中是否有未领取的奖励
        return rewardLevels.any { !gainedLevels.contains(it) }
    }


    fun failed(allies: List<Role>, enemies: List<Role>) {
        repo.inBattle.value = false
    }

    fun win(allies: List<Role>, enemies: List<Role>) {
        if (targetLevel.value.id > maxLevel.value) {
            maxLevel.value = targetLevel.value.id
            val award = Award(diamond = repo.gameCore.getTowerAwardKey(targetLevel.value.layer))
            awardDialog.value = award
            AppWrapper.globalScope.launch(Dispatchers.Main) {
                AwardManager.gainAward(award)
            }
            uploadTowerRank()
        }
        repo.inBattle.value = false
    }

    fun fight(tower: Tower) {

        if (tower.id > maxLevel.value + 1) {
            AppWrapper.getStringKmp(Res.string.tower_fight_tips).toast()
            return
        } else if (tower.id <= maxLevel.value) {
            return
        }
        repo.gameMode.value = GameMode(MODE_TOWER)
        targetLevel.value = tower
        goto(TOWER_BATTLER_SCREEN)
    }

    /**
     * "战斗类型
     *  1=0
     *  2=诅咒ID
     *  3=限定种族，1~4，不限定英雄
     *  4=限时回合数"
     */
    fun getTowerFilter(tower: Tower): (Ally) -> Boolean {
        return if (tower.type.first() == 3) {
            {
                it.raceType == tower.playPara1.first()
            }
        } else {
            {
                true
            }
        }
    }

    fun uploadTowerRank() {
        // todo 放开lite包上传
        if ((DebugManager.uploadRank || !isLite())) {
            AppWrapper.globalScope.launch(Dispatchers.IO) {
                try {
                    postTowerRankData(
                        RankData(
                            time = getCurrentTime(),
                            versionCode = getVersionCode(),
                            userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else gameSdkDefaultProcessor().getUserName()
                                ?: AppWrapper.getStringKmp(Res.string.not_login),
                            userId = gameSdkDefaultProcessor().getObjectId() ?: "0",
                            userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "role_101" else gameSdkDefaultProcessor().getAvatarUrl() ?: "0",
                            platformChannel = platformChannel(),
                            towerLevel = maxLevel.value,
                            serverId = LoginManager.instance.getSavedServerId(),
                        )
                    )
                } catch (e: Exception) {
//                    Timber.e(e)
                }
            }
        }
    }

    suspend fun openPackage(sell: Sell) {
        if (sell.isKeyMoney()) {
            AwardManager.gainKey(-sell.price)
        } else if (sell.isAifadianTower()) {
            AwardManager.gainRealMoney(-sell.price)
        }

        val tower = repo.gameCore.getTowerPool().first { it.reward == sell.id }
        if (gainedLevels.contains(tower.id)) {
            return
        }
        markGoogleSellItem(sell)

        reportManager().onShopPurchase(
            sellId = sell.id,
            price = sell.price,
            priceType = sell.priceType
        )

        // 这些商品都是付费商品，但是在taptap，用了英雄币，已经获得了特权值奖励，这里要去掉特权
        val realAward = sell.toAward().let {
            if (hasBilling()) {
                it
            } else {
                it.copy(electric = 0)
            }
        }
        AwardManager.gainAward(realAward)
        awardDialog.value = realAward
        setBooleanValueByKey(SELL_FOREVER + sell.id, true)
    }

    fun markGoogleSellItem(sell: Sell) {
        val tower = repo.gameCore.getTowerPool().first { it.reward == sell.id }
        gainedLevels.add(tower.id)
        setListObject(KEY_GAINED_TOWER_LEVELS, gainedLevels)
    }

    fun unlocked(): Boolean {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_TOWER)
        return UnlockManager.getUnlockedFlow(unlock)
    }
}