package com.moyu.chuanqirensheng.feature.more

import com.moyu.chuanqirensheng.feature.award.AwardFromServer
import kotlinx.serialization.Serializable

@Serializable
data class EmailData(
    val id : Int = 0,
    val title: Map<String, String> = emptyMap(), // 邮件标题
    val content: Map<String, String> = emptyMap(), // 邮件内容
    val link: String = "", // 邮件跳转链接
    val type: Int = 0, // 1 公共，无过期时间 2 公共，有过期时间 3 单个人，无过期时间
    val awardCode: String = "", //
    val award: AwardFromServer? = null, // 奖励显示用
    val createTime: Long = 0, // 创建时间
)