package com.moyu.chuanqirensheng.feature.award.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.game_awards


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun GameAwardsDialog(show: MutableState<Boolean>) {
    show.value.takeIf { show.value }?.let {
        PanelDialog(onDismissRequest = {
            show.value = false
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding4).verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                StrokedText(
                    text = stringResource(Res.string.game_awards),
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding19))
                AwardList(
                    award = BattleManager.battleAward.value.copy(
                        battleProperty = emptyList(),
                        allHeal = 0
                    ),
                    param = defaultParam.copy(textColor = Color.White),
                    mainAxisAlignment = Arrangement.spacedBy(
                        padding10
                    )
                )
            }
        }
    }
}
