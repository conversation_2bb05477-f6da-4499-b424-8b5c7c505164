package com.moyu.chuanqirensheng.feature.event.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.debug.EventDebugButton
import com.moyu.chuanqirensheng.feature.battle.ui.FullScreenDoSkillLayout
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.event.canShowPropertyPanel
import com.moyu.chuanqirensheng.feature.event.handler.PlayHandler
import com.moyu.chuanqirensheng.feature.event.isBattle
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE1
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.info.InfoLayout
import com.moyu.chuanqirensheng.feature.setting.SettingManager
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.feature.stage.ui.ParallaxScrollingBackground
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.platform.statusBarHeightInDp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding420
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.button.HUGE_GAP
import com.moyu.chuanqirensheng.widget.common.GameLabel2
import com.moyu.chuanqirensheng.widget.common.IconView
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.day_progress
import shared.generated.resources.event_frame
import shared.generated.resources.icon_setting
import shared.generated.resources.next_day

@Composable
fun EventSelectScreen() {
    GameInfoView(
        Modifier.fillMaxSize()
    )
    Box(Modifier.fillMaxSize()) {
        EventDebugButton(Modifier.align(Alignment.BottomCenter))
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun GameInfoView(modifier: Modifier) {
    val handler = remember {
        mutableStateOf<PlayHandler?>(null)
    }
    val scrolling = remember {
        mutableStateOf(true)
    }
    Box(modifier = modifier, contentAlignment = Alignment.TopCenter) {
        Box(Modifier.fillMaxSize().padding(top = padding420, bottom = padding16)) {
            Image(
                modifier = Modifier.fillMaxWidth().scale(1.2f),
                painter = painterResource(Res.drawable.event_frame),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                alignment = Alignment.BottomCenter
            )
            Column(
                modifier = Modifier.fillMaxSize().padding(horizontal = padding10)
                    .padding(bottom = padding10), horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 事件记录区
                InfoLayout(Modifier.weight(1f).fillMaxWidth(), repo.lifeInfo)
                // 底部按钮区
                if (handler.value?.eventFinished?.value == false) {
                    // 选中的事件未结束，展示事件的Button
                    LaunchedEffect(Unit) {
                        scrolling.value = false
                    }
                    if (handler.value?.doEventPostAwards?.value == true) {
                        handler.value?.PostAwardButtons(event = EventManager.selectedEvent.value!!)
                    } else {
                        handler.value?.HandlerButtons(event = EventManager.selectedEvent.value!!)
                    }
                } else {
                    // 选中的事件已结束，展示下一天的Button
                    LaunchedEffect(Unit) {
                        scrolling.value = true
                    }
                    LaunchedEffect(EventManager.eventRecorder.usedEvents.size) {
                        if (SettingManager.autoSelect.value && Dialogs.endingDialog.value == null && EventManager.eventRecorder.usedEvents.size < StageManager.currentStage.value.limit) {
                            delay(1000)
                            if (GuideManager.guideIndex.value == 0) {
                                GuideManager.guideIndex.value = 1
                                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                            } else if (GuideManager.guideIndex.value == 1) {
                                GuideManager.guideIndex.value = 2
                                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                            }
                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                EventManager.generateNextEvent()?.let { event ->
                                    handler.value = EventManager.getOrCreateHandler(event)
                                    MusicManager.stopSound(SoundEffect.HorseWalk)
                                    MusicManager.stopSound(SoundEffect.ShipSail)
                                    EventManager.selectEvent(event)
                                }
                            }
                        }
                    }
                    Box(contentAlignment = Alignment.Center) {
                        GameButton(
                            buttonSize = ButtonSize.Huge,
                            clickGap = HUGE_GAP,
                            mute = true,
                            text = stringResource(Res.string.next_day)
                        ) {
                            if (GuideManager.guideIndex.value == 0) {
                                GuideManager.guideIndex.value = 1
                                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                            } else if (GuideManager.guideIndex.value == 1) {
                                GuideManager.guideIndex.value = 2
                                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                            }
                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                EventManager.generateNextEvent()?.let {
                                    event ->
                                    handler.value = EventManager.getOrCreateHandler(event)
                                    MusicManager.stopSound(SoundEffect.HorseWalk)
                                    MusicManager.stopSound(SoundEffect.ShipSail)
                                    EventManager.selectEvent(event)
                                }
                            }
                        }
                        if (GuideManager.guideIndex.value in listOf(0, 1)) {
                            GuideHand(
                                modifier = Modifier.align(Alignment.BottomCenter).height(padding80)
                                    .graphicsLayer {
                                        translationY = -padding90.toPx()
                                    }, handType = HandType.DOWN_HAND
                            )
                        }
                    }
                }
            }
            if (DebugManager.allEvent) {
                // 事件调试UI
                EventSelectPage(Modifier.height(padding200)) {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        EventManager.selectEvent(it)
                        handler.value = EventManager.getOrCreateHandler(it)
                    }
                }
            }
            repo.fullScreenSkillEffect.value?.let {
                FullScreenDoSkillLayout(it.first, it.second, it.third)
            }
        }
        // 事件内容
        Box(
            modifier = Modifier.fillMaxWidth().height(padding420),
            contentAlignment = Alignment.CenterStart
        ) {
            // 滚动背景图
            ParallaxScrollingBackground(
                foregroundPainter = painterResource(kmpDrawableResource(StageManager.currentStage.value.levelImg4)),
                backgroundPainter = painterResource(kmpDrawableResource(StageManager.currentStage.value.levelImg6)),
                modifier = Modifier.fillMaxSize(),
                isScrolling = scrolling
            )
            // 事件pic
            EventPicView(Modifier.align(Alignment.BottomEnd), handler)
            // 中间属性UI
            if (EventManager.selectedEvent.value?.canShowPropertyPanel() == true || handler.value?.eventFinished?.value != false) {
                PropertyView(Modifier.align(Alignment.BottomCenter))
            }
            // 左上角mini阵容UI
            if (EventManager.selectedEvent.value?.canShowPropertyPanel() == true || handler.value?.eventFinished?.value != false) {
                MiniAllyView(Modifier.align(Alignment.TopStart).padding(start = padding12))
            }
            val showEventLayout =
                handler.value?.eventFinished?.value == false && handler.value?.hasLayout?.value == true
            if (showEventLayout) {
                // 选中事件的Layout，比如战斗，奖励选择等
                EventManager.selectedEvent.value?.let {
                    handler.value?.Layout(it)
                }
            }
            WalkingMasterView(showEventLayout, handler)
            // 奖品动画效果
            EventManager.selectedEvent.value?.let {
                handler.value?.getEventAward()?.let {
                    if (handler.value?.eventFinished?.value == true) {
                        MultiAwardsAnimation(
                            modifier = Modifier.align(Alignment.BottomEnd)
                                .padding(end = padding60, bottom = padding100).size(padding150),
                            awards = it.toMultipleDisplayList()
                        )
                    }
                }
            }
            // 右上角设置按钮区
            Row(
                Modifier.align(Alignment.TopEnd)
                    .padding(top = statusBarHeightInDp() + padding4, end = padding4),
                horizontalArrangement = Arrangement.spacedBy(padding4),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AnimatedVisibility(EventManager.selectedEvent.value?.isBattle() == true) {
                    IconView(
                        res = kmpDrawableResource(GameSpeedManager.getCurrentSpeed().icon),
                        itemSize = ItemSize.Large,
                        frame = null,
                        resZIndex = 999f,
                    ) {
                        GameSpeedManager.nextSpeed()
                    }
                }
                IconView(
                    res = Res.drawable.icon_setting,
                    itemSize = ItemSize.Large,
                    frame = null,
                    resZIndex = 999f,
                ) {
                    Dialogs.settingDialog.value = true
                }
            }
            // 中间日期区
            GameLabel2(
                modifier = Modifier.align(Alignment.TopCenter)
                    .padding(top = statusBarHeightInDp() + padding19).size(padding120, padding30)
            ) {
                StrokedText(
                    text = stringResource(Res.string.day_progress) + ": ${EventManager.getShowDay()}/${StageManager.currentStage.value.limit}",
                    style = MaterialTheme.typography.h3
                )
            }
        }
    }
}