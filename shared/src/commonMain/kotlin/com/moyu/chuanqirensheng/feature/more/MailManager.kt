package com.moyu.chuanqirensheng.feature.more

import com.moyu.chuanqirensheng.api.getEmails
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.sub.datastore.KEY_READ_MAIL_IDS
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.util.AESUtil
import com.moyu.chuanqirensheng.util.AdUtil.encodeText
import com.moyu.core.AppWrapper
import kotlinx.serialization.builtins.ListSerializer
import shared.generated.resources.Res
import shared.generated.resources.network_error

object MailManager {
    val readMessageIds = mutableListOf<Int>()

    fun init() {
        readMessageIds.addAll(getListObject(KEY_READ_MAIL_IDS))
    }

    fun setRead(id: Int) {
        if (!readMessageIds.contains(id)) {
            readMessageIds.add(id)
            setListObject(KEY_READ_MAIL_IDS, readMessageIds)
        }
    }

    fun isGained(mail: EmailData): Boolean {
        return getLongFlowByKey(mail.awardCode) == 1L
    }

    suspend fun getMails(): List<EmailData> {
        return gameSdkDefaultProcessor().getObjectId()?.let { userId ->
            encodeText(userId)?.let { encodedUserId ->
                try {
                    val awardString = getEmails(encodedUserId, LoginManager.instance.getSavedServerId()).message
                    val codeAwardJson = AESUtil.decrypt(awardString, userId)
                    codeAwardJson?.let {
                        json.decodeFromString(ListSerializer(EmailData.serializer()), it)
                    }?: emptyList()
                } catch (e: Exception) {
                    AppWrapper.getStringKmp(Res.string.network_error).toast()
                    emptyList()
                }
            }
        }?: emptyList()
    }
}