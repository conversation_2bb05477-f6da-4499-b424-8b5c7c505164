package com.moyu.chuanqirensheng.feature.event

import com.moyu.chuanqirensheng.feature.event.handler.HALO2_BATTLE
import com.moyu.chuanqirensheng.feature.event.handler.HALO_BATTLE
import com.moyu.chuanqirensheng.feature.event.handler.LOTTERY_PLAY_EVENT
import com.moyu.chuanqirensheng.feature.event.handler.SIEGE_BATTLE
import com.moyu.core.model.Event

val battleEventIds = listOf(HALO_BATTLE, HALO2_BATTLE, SIEGE_BATTLE)
val keyBattleEventIds = listOf(SIEGE_BATTLE)
const val EVENT_SIZE = 1

fun Event.isBattle() = play in battleEventIds
fun Event.isKeyBattle() = play in keyBattleEventIds
fun Event.isNormalBattle() = play == HALO_BATTLE
fun Event.canShowPropertyPanel() = play !in battleEventIds
