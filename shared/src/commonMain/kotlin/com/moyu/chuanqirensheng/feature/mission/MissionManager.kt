package com.moyu.chuanqirensheng.feature.mission

import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_NEW_QUEST
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo


object MissionManager {

    fun init() {

    }

    fun hasRed(): Boolean {
        return QuestManager.newTasks.any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        }
    }

    fun getInitPageIndex(): Int {
        repeat(7) { index ->
            val questList = QuestManager.getNewQuestsByIndex(index)
            val missionsAllDone = questList.all { it.done } && questList.isNotEmpty()
            if (missionsAllDone) {
                return index
            }
        }
        return 9999

    }

    fun unlocked(): Boolean {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_NEW_QUEST)
        return UnlockManager.getUnlockedFlow(unlock)
    }
}