package com.moyu.chuanqirensheng.feature.activities.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.drawactivity.DrawActivityManager
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.mission.MissionManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.resource.InfoIcon
import com.moyu.chuanqirensheng.feature.router.DRAW_ACTIVITY_SCREEN
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN1
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN2
import com.moyu.chuanqirensheng.feature.router.NEW_TASK_SCREEN
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN1
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN2
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN3
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN4
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.router.gotoServerRankScreenByIndex
import com.moyu.chuanqirensheng.feature.serverrank.ServerRankManager
import com.moyu.chuanqirensheng.feature.serverrank.ServerRankManager.canShowServerRankEntrance
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_DRAW_ACTIVITY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_LOTTERY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_NEW_QUEST
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_SEVEN_DAY
import com.moyu.chuanqirensheng.platform.hasBilling
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.ui.theme.B10
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.activities
import shared.generated.resources.activities_top_label
import shared.generated.resources.activity_frame
import shared.generated.resources.activity_frame_with_label
import shared.generated.resources.common_big_frame
import shared.generated.resources.draw_activity
import shared.generated.resources.enter
import shared.generated.resources.lottery_title1
import shared.generated.resources.lottery_title2
import shared.generated.resources.new_quest
import shared.generated.resources.new_task_tab2
import shared.generated.resources.new_task_tab3
import shared.generated.resources.new_task_tab4
import shared.generated.resources.new_task_tab5
import shared.generated.resources.new_task_tab5_taptap
import shared.generated.resources.red_icon
import shared.generated.resources.server_rank_activity1
import shared.generated.resources.server_rank_activity2
import shared.generated.resources.server_rank_activity3
import shared.generated.resources.server_rank_tips1
import shared.generated.resources.server_rank_tips2
import shared.generated.resources.server_rank_tips3

data class ActivityItem(
    val name: () -> String,
    val route: () -> Unit,
    val show: () -> Boolean = { true },
    val subText: @Composable BoxScope.() -> Unit = {},
    val frame: String = "shop_label",
    val lockedToast: String = "",
    val unlock: () -> Boolean = { true },
    val red: () -> Boolean = { false }
)

val activityItems = listOf(
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.new_task_tab2) },
        route = { goto(SEVEN_DAY_SCREEN1) },
        frame = "collect_activity_frame",
        show = {
            SevenDayManager.show()
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_SEVEN_DAY).desc,
        unlock = {
            SevenDayManager.unlocked()
        },
        red = {
            SevenDayManager.hasRed1()
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.new_task_tab3) },
        route = { goto(SEVEN_DAY_SCREEN2) },
        frame = "sell_activity_frame",
        show = {
            SevenDayManager.show()
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_SEVEN_DAY).desc,
        unlock = {
            SevenDayManager.unlocked()
        },
        red = {
            SevenDayManager.hasRed2()
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.new_task_tab4) },
        route = { goto(SEVEN_DAY_SCREEN3) },
        frame = "cost_activity_frame",
        show = {
            SevenDayManager.show()
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_SEVEN_DAY).desc,
        unlock = {
            SevenDayManager.unlocked()
        },
        red = {
            SevenDayManager.hasRed3()
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(if (hasBilling()) Res.string.new_task_tab5 else Res.string.new_task_tab5_taptap) },
        route = { goto(SEVEN_DAY_SCREEN4) },
        frame = "charge_activity_frame",
        show = {
            SevenDayManager.show()
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_SEVEN_DAY).desc,
        unlock = {
            SevenDayManager.unlocked()
        },
        red = {
            SevenDayManager.hasRed4()
        }
    ),
    ActivityItem(
        name = {
            if (LotteryManager.showCheap()) AppWrapper.getStringKmp(Res.string.lottery_title1) else AppWrapper.getStringKmp(
                Res.string.lottery_title2
            )
        },
        route = { if (LotteryManager.showCheap()) goto(LOTTERY_SCREEN1) else goto(LOTTERY_SCREEN2) },
        frame = "lottery_frame",
        unlock = {
            LotteryManager.unlocked()
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_LOTTERY).desc,
        red = {
            LotteryManager.hasRed()
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.new_quest) },
        route = { goto(NEW_TASK_SCREEN) },
        frame = "new_task_frame",
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_NEW_QUEST).desc,
        unlock = {
            MissionManager.unlocked()
        },
        red = {
            MissionManager.hasRed()
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.draw_activity) },
        route = { goto(DRAW_ACTIVITY_SCREEN) },
        frame = "draw_activity_label",
        show = {
            DrawActivityManager.show()
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_DRAW_ACTIVITY).desc,
        unlock = {
            DrawActivityManager.unlocked()
        },
        red = {
            DrawActivityManager.hasRed()
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.server_rank_activity1) },
        route = { gotoServerRankScreenByIndex(1) },
        frame = "rank_activity1",
        show = {
            ServerRankManager.canShowServerRankEntrance()
        },
        unlock = {
            ServerRankManager.unlocked()
        },
        red = {
            ServerRankManager.hasRed1()
        },
        subText = {
            InfoIcon {
                Dialogs.alertDialog.value = CommonAlert(
                    title = AppWrapper.getStringKmp(Res.string.server_rank_activity1),
                    content = AppWrapper.getStringKmp(Res.string.server_rank_tips1),
                    onlyConfirm = true
                )
            }
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.server_rank_activity2) },
        route = { gotoServerRankScreenByIndex(2) },
        frame = "rank_activity2",
        show = {
            canShowServerRankEntrance()
        },
        unlock = {
            ServerRankManager.unlocked()
        },
        red = {
            ServerRankManager.hasRed2()
        },
        subText = {
            InfoIcon {
                Dialogs.alertDialog.value = CommonAlert(
                    title = AppWrapper.getStringKmp(Res.string.server_rank_activity2),
                    content = AppWrapper.getStringKmp(Res.string.server_rank_tips2),
                    onlyConfirm = true
                )
            }
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.server_rank_activity3) },
        route = { gotoServerRankScreenByIndex(3) },
        frame = "rank_activity3",
        show = {
            ServerRankManager.canShowServerRankEntrance()
        },
        unlock = {
            ServerRankManager.unlocked()
        },
        red = {
            ServerRankManager.hasRed3()
        },
        subText = {
            InfoIcon {
                Dialogs.alertDialog.value = CommonAlert(
                    title = AppWrapper.getStringKmp(Res.string.server_rank_activity3),
                    content = AppWrapper.getStringKmp(Res.string.server_rank_tips3),
                    onlyConfirm = true
                )
            }
        }
    ),
)

@Composable
fun ActivitiesScreen() {
    LaunchedEffect(Unit) {
        LotteryManager.refresh()
        SevenDayManager.init()
        if (canShowServerRankEntrance()) {
            ServerRankManager.refreshServerRankList(1, true)
            ServerRankManager.refreshServerRankList(2, true)
            ServerRankManager.refreshServerRankList(3, true)
        }
    }
    GameBackground(title = stringResource(Res.string.activities), topContent = {
        Image(
            modifier = Modifier
                .fillMaxWidth().scale(1.25f),
            painter = painterResource(Res.drawable.activities_top_label),
            contentScale = ContentScale.FillWidth,
            contentDescription = null
        )
    }) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(padding10),
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding10, horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            // todo 每个活动都需要显示首次红点，用activityItems的frame来做key
            activityItems.forEach {
                ActivityItem(
                    moreItem = it.copy(
                        red = {
                            if (!getBooleanFlowByKey(it.frame, false)) true else it.red()
                        },
                        route = {
                            if (!getBooleanFlowByKey(it.frame, false)) {
                                setBooleanValueByKey(it.frame, true)
                            }
                            it.route()
                        }
                    ),
                    frame = Res.drawable.activity_frame_with_label
                )
            }
        }
    }
}


@Composable
fun ActivityItem(
    modifier: Modifier = Modifier,
    moreItem: ActivityItem,
    frame: DrawableResource = Res.drawable.activity_frame,
    callback: () -> Unit = {}
) {
    if (moreItem.unlock()) {
        if (moreItem.show()) {
            Box(
                modifier.fillMaxWidth().height(padding120).shadow(
                    elevation = padding16, // 控制阴影的高度和扩散程度
                    shape = RoundedCornerShape(padding4) // 与素材的圆角保持一致
                )
                    .clip(RoundedCornerShape(padding4))
            ) {
                Image(
                    modifier = Modifier.fillMaxSize().padding(padding2).clip(
                        RoundedCornerShape(padding10)
                    ),
                    painter = painterResource(kmpDrawableResource(moreItem.frame)),
                    colorFilter = ColorFilter.tint(
                        color = B10,
                        BlendMode.SrcAtop
                    ),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = painterResource(frame),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )

                StrokedText(
                    text = moreItem.name(),
                    style = MaterialTheme.typography.h1,
                    modifier = Modifier.align(Alignment.TopStart)
                        .padding(top = padding26, start = padding26)
                )

                Box(
                    modifier = Modifier.align(Alignment.BottomStart)
                        .padding(bottom = padding26, start = padding26), content = moreItem.subText
                )

                Box(
                    modifier = Modifier.align(Alignment.BottomEnd)
                        .padding(end = padding16, bottom = padding16),
                ) {
                    GameButton(
                        text = stringResource(Res.string.enter),
                    ) {
                        callback()
                        if (moreItem.unlock()) {
                            moreItem.route()
                        } else {
                            moreItem.lockedToast.toast()
                        }
                    }
                    if (moreItem.red()) {
                        Image(
                            modifier = Modifier
                                .size(imageSmall)
                                .align(Alignment.TopEnd),
                            painter = kmpPainterResource(Res.drawable.red_icon),
                            contentDescription = null
                        )
                    }
                }
            }
        }
    }
}
