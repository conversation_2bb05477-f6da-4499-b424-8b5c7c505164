package com.moyu.chuanqirensheng.feature.speed

import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.core.AppWrapper
import com.moyu.core.model.GameSpeed
import shared.generated.resources.Res
import shared.generated.resources.normal_speed
import shared.generated.resources.triple_speed
import shared.generated.resources.x10_speed

/**
 * 当前内置的游戏速度
 */
val gameSpeeds = listOf(
    Speed(0, 1f, AppWrapper.getStringKmp(Res.string.normal_speed), "battle_menu_play"),
    Speed(1, 0.35f, AppWrapper.getStringKmp(Res.string.triple_speed), "battle_menu_speed3"),
    Speed(2, if (isLite()) 0.01f else 0.15f, AppWrapper.getStringKmp(Res.string.x10_speed), "battle_menu_speed10"),
)

/**
 * 游戏速度控制接口，任意动画都会从这里获取参数
 */
interface GameSpeedController: GameSpeed {
    fun getCurrentSpeed(): Speed
    fun nextSpeed()
    fun setSpeed(speed: Int)
    fun getSpeeds(): List<Speed>
    fun isStop(): Bo<PERSON>an
}