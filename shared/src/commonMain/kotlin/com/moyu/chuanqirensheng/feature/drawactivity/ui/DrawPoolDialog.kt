package com.moyu.chuanqirensheng.feature.drawactivity.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.drawactivity.DrawActivityManager
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.pool_title


@Composable
fun DrawPoolDialog(show: MutableState<Boolean>) {
    if (show.value) {
        val award = DrawActivityManager.getAwardPool()
        PanelDialog(onDismissRequest = { show.value = false }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                StrokedText(
                    text = stringResource(Res.string.pool_title),
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding10))
                AwardList(award = award)
            }
        }
    }
}