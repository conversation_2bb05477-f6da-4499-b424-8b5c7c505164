package com.moyu.chuanqirensheng.feature.pvp

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.api.postPvpRankData
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gamemode.GameMode
import com.moyu.chuanqirensheng.feature.gamemode.MODE_PVP
import com.moyu.chuanqirensheng.feature.illustration.TcgManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.onTaskPvpLose
import com.moyu.chuanqirensheng.feature.quest.onTaskPvpWin
import com.moyu.chuanqirensheng.feature.rank.PvpData
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.role.createPvpRole
import com.moyu.chuanqirensheng.feature.router.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.router.gotoSellWithTabIndex
import com.moyu.chuanqirensheng.feature.sell.SELL_TYPE_PVP
import com.moyu.chuanqirensheng.feature.sell.SELL_VIP_INDEX
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.getVersionCode
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_PVP
import com.moyu.chuanqirensheng.sub.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_ALLY_IDS
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_TARGET
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_LOSE
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_LOSE_TODAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_SCORE
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_TOTAL_SCORE
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_WIN
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_WIN_TODAY
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.logic.role.ENEMY_HERO_POSITION
import com.moyu.core.logic.role.battleEnemyNoMasterList
import com.moyu.core.model.Award
import com.moyu.core.model.MASTER_MAIN_ID
import com.moyu.core.model.PvpRank
import com.moyu.core.model.allyIdToMainId
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextInteger
import com.moyu.core.util.p_getTimeMillis
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import shared.generated.resources.Res
import shared.generated.resources.not_login
import shared.generated.resources.pvp_ally_error
import shared.generated.resources.pvp_error_ally
import shared.generated.resources.pvp_mock
import shared.generated.resources.pvp_num_limit
import shared.generated.resources.pvp_num_limit2
import shared.generated.resources.pvp_upgrade

const val MAX_PVP_NUM = 10
const val MAX_ALL_PVP_NUM = 20
const val INIT_PVP_SCORE = 1000

object PvpManager {
    val pvpScore = Guarded(KEY_PVP_SCORE, mutableStateOf(INIT_PVP_SCORE))
    val pvpTotalScore = Guarded(KEY_PVP_TOTAL_SCORE, mutableStateOf(0))
    val lastPvpAllyIds = mutableListOf<Int>()
    val pkNumToday = Guarded(KEY_PK_NUM)
    val pkWin = Guarded(KEY_PVP_WIN)
    val pkLose = Guarded(KEY_PVP_LOSE)
    val pkWinToday = Guarded(KEY_PVP_WIN_TODAY)
    val pkLoseToday = Guarded(KEY_PVP_LOSE_TODAY)
    val pkTargetList = mutableStateListOf<String>()
    val currentTarget = mutableStateOf(RankData(p_getTimeMillis()))
    val targetsFromServer = mutableStateOf(emptyList<RankData>())
    val currentTargets = mutableStateOf(emptyList<RankData>())

    suspend fun init() {
        initPkNum()
        lastPvpAllyIds.clear()
        lastPvpAllyIds.addAll(getListObject(KEY_PK_ALLY_IDS))
    }

    fun initPkNum() {
        if (!isNetTimeValid()) {
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_PK_UPDATE_TIME_IN_MILLIS),
                getCurrentTime()
            )
        ) {
            if (pkTargetList.isEmpty()) {
                pkTargetList.addAll(getListObject(KEY_PK_TARGET))
            }
        } else {
            pvpScore.value = INIT_PVP_SCORE
            pkNumToday.value = 0
            pkWinToday.value = 0
            pkLoseToday.value = 0
            pkTargetList.clear()
            setLongValueByKey(KEY_PK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_PK_TARGET, emptyList<String>())
        }
    }

    fun pk(rankData: RankData) {
        if (!DebugManager.unlockAll) {
            if (pkWinToday.value + pkLoseToday.value >= MAX_PVP_NUM + VipManager.getExtraPvpNum()
                || pkNumToday.value >= MAX_PVP_NUM + VipManager.getExtraPvpNum()
            ) {
                if (MAX_PVP_NUM + VipManager.getExtraPvpNum() < MAX_ALL_PVP_NUM) {
                    Dialogs.alertDialog.value = CommonAlert(
                        content = AppWrapper.getStringKmp(Res.string.pvp_num_limit2),
                        onConfirm = {
                            gotoSellWithTabIndex(SELL_VIP_INDEX)
                        }
                    )
                } else {
                    AppWrapper.getStringKmp(Res.string.pvp_num_limit).toast()
                }
                return
            }
        }
        if (rankData.pvpData.allyIds.isEmpty()) {
            AppWrapper.getStringKmp(Res.string.pvp_ally_error).toast()
            return
        }
        val allies = rankData.pvpData.allyIds.take(8)
        allies.forEach { ally ->
            if (repo.gameCore.getAllyPool(ally.allyIdToMainId()).isEmpty()) {
                AppWrapper.getStringKmp(Res.string.pvp_upgrade).toast()
                return
            }
        }
        if (currentTarget.value.userId.isNotEmpty()) {
            // 已经选择了对手，正在pk中
            return
        }
        currentTarget.value = rankData
        repo.gameMode.value = GameMode(MODE_PVP)
        pkNumToday.value += 1
        val roleHashMap = mutableMapOf<Int, Role?>()

        battleEnemyNoMasterList.forEachIndexed { index, position ->
            allies.filter { it.allyIdToMainId() != MASTER_MAIN_ID }.getOrNull(index)?.let {
                roleHashMap[position] =
                    createPvpRole(
                        repo.gameCore.getAllyById(it),
                        rankData.pvpData.talentIds,
                        rankData.pvpData.equipIds,
                        rankData.pvpData.tcgIds
                    )
            }
        }
        roleHashMap[ENEMY_HERO_POSITION] = createPvpRole(
            repo.gameCore.getAllyById(allies.first { it.allyIdToMainId() == MASTER_MAIN_ID }),
            rankData.pvpData.talentIds,
            rankData.pvpData.equipIds,
            rankData.pvpData.tcgIds
        )

        repo.battleRoles.clear()
        repo.battleRoles.putAll(roleHashMap)
        goto(PVP_BATTLE_SCREEN)
    }

    fun pkFailed(allies: List<Role>, enemies: List<Role>) {
        // 需要根据时间确认是否刷新pk数据
        initPkNum()

        pkLose.value += 1
        pkLoseToday.value += 1
        val index = currentTargets.value.sortedByDescending { it.pvpScore }
            .indexOfFirst { it.userId == currentTarget.value.userId }
        val pvpData = if (index == -1 || index >= repo.gameCore.getPvpPool(MODE_PVP).size) {
            repo.gameCore.getPvpPool(MODE_PVP).last()
        } else {
            repo.gameCore.getPvpPool(MODE_PVP)[index]
        }
        if (lessThanMax()) {
            AppWrapper.globalScope.launch {
                Dialogs.awardDialog.value =
                    Award(pvpDiamond = pvpData.loseToken, pvpScore = pvpData.losePoint).apply {
                        AwardManager.gainAward(this)
                    }
            }

            reportManager().pk(0, pvpScore.value)
        }
        onTaskPvpLose()
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
        goto(PVP_CHOOSE_ENEMY_SCREEN)
        currentTarget.value = RankData(p_getTimeMillis())
        increaseIntValueByKey(KEY_DIED_IN_PVP)
    }

    private fun lessThanMax(): Boolean {
        return (pkLoseToday.value + pkWinToday.value <= MAX_PVP_NUM + VipManager.getExtraPvpNum()) &&
                pkNumToday.value <= MAX_PVP_NUM + VipManager.getExtraPvpNum()
    }

    fun pkWined(allies: List<Role>, enemies: List<Role>) {
        // 需要根据时间确认是否刷新pk数据
        initPkNum()

        pkWin.value += 1
        pkWinToday.value += 1
        if (pkTargetList.contains(currentTarget.value.userId)) {
            AppWrapper.getStringKmp(Res.string.pvp_error_ally).toast()
            repo.inBattle.value = false
        } else {
            pkTargetList.add(currentTarget.value.userId)
            val index = currentTargets.value.sortedByDescending { it.pvpScore }
                .indexOfFirst { it.userId == currentTarget.value.userId }

            val pvpData = if (index == -1 || index >= repo.gameCore.getPvpPool(MODE_PVP).size) {
                repo.gameCore.getPvpPool(MODE_PVP).last()
            } else {
                repo.gameCore.getPvpPool(MODE_PVP)[index]
            }
            if (lessThanMax()) {
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    setListObject(KEY_PK_TARGET, pkTargetList)
                    Dialogs.awardDialog.value =
                        Award(pvpDiamond = pvpData.winToken, pvpScore = pvpData.winPoint).apply {
                            AwardManager.gainAward(this)
                        }
                    uploadPvpRank()
                    reportManager().pk(1, pvpScore.value)
                }
            }
            goto(PVP_CHOOSE_ENEMY_SCREEN)
            currentTarget.value = RankData(p_getTimeMillis())
            onTaskPvpWin()
            repo.inBattle.value = false
            GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
        }
    }

    fun getMockPvpData(): List<RankData> {
        val allyPool = repo.gameCore.getAllyPool()
        val allyHeroes = allyPool.filter { it.isHero() && it.level <= 50 }
            .shuffled(RANDOM)
        val allies = allyPool
            .filter { !it.isHero() && it.level == 1 }
        return List(20) { index ->
            val id = UUID.generateUUID().toString().take(5)
            RankData(
                time = getCurrentTime(),
                userId = id,
                userPic = gameSdkDefaultProcessor().getAvatarUrl() ?: "",
                userName = AppWrapper.getStringKmp(Res.string.pvp_mock) + id,
                pvpScore = RANDOM.nextInteger(
                    (pvpScore.value),
                    (pvpScore.value * 1.1f).toInt()
                ),
                pvpData = PvpData(
                    talentIds = TalentManager.getPvpTalents(),
                    allyIds = listOf(allyHeroes[index].id) + allies.shuffled().take(7).map { it.id })
            )
        } + List(30) { index ->
            val id = UUID.generateUUID().toString().take(5)
            RankData(
                time = getCurrentTime(),
                userId = id,
                userPic = gameSdkDefaultProcessor().getAvatarUrl() ?: "",
                userName = AppWrapper.getStringKmp(Res.string.pvp_mock) + id,
                pvpScore = RANDOM.nextInteger(
                    (pvpScore.value * 0.9f).toInt(),
                    (pvpScore.value),
                ),
                pvpData = PvpData(
                    talentIds = TalentManager.getPvpTalents(),
                    allyIds = listOf(allyHeroes[index].id) + allies.shuffled().take(7).map { it.id })
            )
        }
    }

    fun getPvpData(): PvpData {
        return PvpData(
            talentIds = TalentManager.getPvpTalents(),
            allyIds = lastPvpAllyIds.ifEmpty {
                repo.gameCore.getAllyPool().filter { it.star == 0 && it.quality == 3 }
                    .shuffled(RANDOM).take(6).map { it.id }
            },
            equipIds = repo.equipManager.data.filter { it.equipped }.map { it.id },
            win = pkWinToday.value,
            lose = pkLoseToday.value,
            tcgIds = TcgManager.doneTcgs.map { it.id }
        )
    }

    fun refreshTargets() {
        val listAbove = targetsFromServer.value.filter { it.pvpScore > pvpScore.value }
            .filter { it.userId !in pkTargetList }.shuffled()
        val listBelow = targetsFromServer.value.filter { it.pvpScore <= pvpScore.value }
            .filter { it.userId !in pkTargetList }.shuffled()
        if (listAbove.size < 2) {
            currentTargets.value =
                (listAbove + listBelow.take(5 - listAbove.size)).sortedByDescending { it.pvpScore }
        } else if (listBelow.size < 3) {
            currentTargets.value =
                (listAbove.take(5 - listBelow.size) + listBelow).sortedByDescending { it.pvpScore }
        } else {
            currentTargets.value =
                (listAbove.take(2) + listBelow.take(3)).sortedByDescending { it.pvpScore }
        }
    }

    fun uploadPvpRank() {
        if (!DebugManager.debug || DebugManager.uploadRank) {
            AppWrapper.globalScope.launch(Dispatchers.IO) {
                try {
                    postPvpRankData(
                        RankData(
                            time = getCurrentTime(),
                            versionCode = getVersionCode(),
                            userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else gameSdkDefaultProcessor().getUserName()
                                ?: AppWrapper.getStringKmp(Res.string.not_login),
                            userId = gameSdkDefaultProcessor().getObjectId() ?: "0",
                            userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "role_101" else gameSdkDefaultProcessor().getAvatarUrl() ?: "0",
                            pvpScore = pvpScore.value,
                            pvpLastScore = pvpTotalScore.value, // 废弃
                            pvpData = getPvpData(),
                            platformChannel = platformChannel(),
                            serverId = LoginManager.instance.getSavedServerId(),
                        )
                    )
                } catch (e: Exception) {
                    e.toString().toast()
                }
            }
        }
    }

    fun hasRedSell(): Boolean {
        return SellManager.getRedFree(SELL_TYPE_PVP)
    }

    fun hasRedPk(): Boolean {
        return pkNumToday.value < MAX_PVP_NUM + VipManager.getExtraPvpNum()
    }

    fun hasRedTask(): Boolean {
        return QuestManager.pvpTasks.any {
            QuestManager.getTaskDoneFlow(it)
                    && !it.opened
        }
    }

    fun hasRedAll(): Boolean {
        return hasRedSell() || hasRedPk() || hasRedTask()
    }

    fun unlocked(): Boolean {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_PVP)
        return UnlockManager.getUnlockedFlow(unlock)
    }

    fun getRankLevel(score: Int = pvpTotalScore.value): PvpRank {
        val pool = repo.gameCore.getPvpRankPool()
        return pool.firstOrNull { it.winPoint > score }?: pool.first()
    }
}