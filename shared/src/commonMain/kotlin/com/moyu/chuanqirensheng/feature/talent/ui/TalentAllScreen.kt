package com.moyu.chuanqirensheng.feature.talent.ui


import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.activities.ui.ActivityItem
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.router.TALENT1_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT2_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT3_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TALENT1
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TALENT2
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TALENT3
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.platform.statusBarHeightInDp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.mainIdToTalentType
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.talent_title1
import shared.generated.resources.talent_title2
import shared.generated.resources.talent_title3
import shared.generated.resources.talent_total_level1
import shared.generated.resources.talent_total_level2
import shared.generated.resources.talent_total_level3

val battlePassItems = listOf(
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.talent_title1) },
        route = {
            goto(TALENT1_SCREEN)
        },
        frame = "talent_label1",
        subText = {
            StrokedText(
                text = AppWrapper.getStringKmp(Res.string.talent_total_level1) + TalentManager.talents.entries.filter { it.key.mainIdToTalentType() == 1 }
                    .sumOf { it.value },
                style = MaterialTheme.typography.h2,
            )
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_TALENT1).desc,
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_TALENT1)
            UnlockManager.getUnlockedFlow(unlock)
        },
        red = {
            TalentManager.hasRed1()
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.talent_title2) },
        route = {
            goto(TALENT2_SCREEN)
        },
        frame = "talent_label2",
        subText = {
            StrokedText(
                text = AppWrapper.getStringKmp(Res.string.talent_total_level2) + TalentManager.talents.entries.filter { it.key.mainIdToTalentType() == 2 }
                    .sumOf { it.value },
                style = MaterialTheme.typography.h2,
            )
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_TALENT2).desc,
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_TALENT2)
            UnlockManager.getUnlockedFlow(unlock)
        },
        red = {
            TalentManager.hasRed2()
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.talent_title3) },
        route = {
            goto(TALENT3_SCREEN)
        },
        frame = "talent_label3",
        subText = {
            StrokedText(
                text = AppWrapper.getStringKmp(Res.string.talent_total_level3) + TalentManager.talents.entries.filter { it.key.mainIdToTalentType() == 3 }
                    .sumOf { it.value },
                style = MaterialTheme.typography.h2,
            )
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_TALENT3).desc,
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_TALENT3)
            UnlockManager.getUnlockedFlow(unlock)
        },
        red = {
            TalentManager.hasRed3()
        }
    ),
)

@Composable
fun TalentAllScreen() {
    GameBackground(showCloseIcon = false) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(padding10),
            modifier = Modifier.fillMaxSize().padding(top = statusBarHeightInDp()).paint(
                painterResource(Res.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            ).padding(vertical = padding10, horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            battlePassItems.forEachIndexed { index, it ->
                Box {
                    ActivityItem(
                        moreItem = it,
                    )
                    if (GuideManager.guideIndex.value == 19 && index == 0) {
                        GuideHand(
                            modifier = Modifier.align(Alignment.BottomEnd).padding(end = padding120).height(
                                padding60
                            ),
                            handType = HandType.RIGHT_HAND
                        )
                    } else if (GuideManager.guideIndex.value == 27 && index == 1) {
                        GuideHand(
                            modifier = Modifier.align(Alignment.BottomEnd).padding(end = padding120).height(
                                padding60
                            ),
                            handType = HandType.RIGHT_HAND
                        )
                    } else if (GuideManager.guideIndex.value == 41 && index == 2) {
                        GuideHand(
                            modifier = Modifier.align(Alignment.BottomEnd).padding(end = padding120).height(
                                padding60
                            ),
                            handType = HandType.RIGHT_HAND
                        )
                    }
                }
            }
        }
    }
}