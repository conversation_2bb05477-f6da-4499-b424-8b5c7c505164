package com.moyu.chuanqirensheng.feature.ally.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.resource.AllyExpPoint
import com.moyu.chuanqirensheng.feature.resource.PowerLabel
import com.moyu.chuanqirensheng.feature.resource.ResourcesPoint
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.text.getQualityName
import com.moyu.chuanqirensheng.text.getTypeRes
import com.moyu.chuanqirensheng.text.toGroupTips
import com.moyu.chuanqirensheng.text.toRaceTips
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.bigButtonWidth
import com.moyu.chuanqirensheng.ui.theme.dialogMediumHeight
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding145
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding70
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.powerHeight
import com.moyu.chuanqirensheng.ui.theme.powerWidth
import com.moyu.chuanqirensheng.ui.theme.toQualityColor
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.button.QUICK_GAP
import com.moyu.chuanqirensheng.widget.dialog.NewPanelDialog
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.MovableImage
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.levelUpGif
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Ally
import com.moyu.core.model.MAX_LEVEL
import com.moyu.core.model.MAX_STAR
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.already_max_star_tips
import shared.generated.resources.card_not_enough
import shared.generated.resources.level_max
import shared.generated.resources.level_up
import shared.generated.resources.name_label
import shared.generated.resources.red_icon
import shared.generated.resources.resource1_not_enough_tips
import shared.generated.resources.star_max
import shared.generated.resources.star_up


@Composable
fun AllyDetailDialog(show: MutableState<Ally?>) {
    show.value?.let { ally ->
        NewPanelDialog(
            onDismissRequest = { show.value = null }, dialogHeightDp = dialogMediumHeight
        ) {
            AllyDialogLayout(ally = ally)
        }
    }
}

@Composable
fun AllyDialogLayout(modifier: Modifier = Modifier, ally: Ally) {
    Column(
        modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        StrokedText(
            text = ally.name, style = MaterialTheme.typography.h1, color = Color.White
        )
        val role = BattleManager.getMyRoleByAlly(ally)
        Spacer(modifier = Modifier.size(padding10))
        AllyDetailLayout(ally, role)
        Spacer(modifier = Modifier.size(padding12))
        AllyPropertyLayout(role)
    }
}

@Composable
fun AllyDetailLayout(ally: Ally, role: Role) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            MovableImage(
                modifier = Modifier.size(padding145).scale(ally.getScaleByQuality()),
                imageResource = kmpDrawableResource(ally.pic),
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(padding12),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Row(
                    Modifier.size(padding150, padding36).paint(
                        painterResource(Res.drawable.name_label),
                        contentScale = ContentScale.FillBounds
                    ).padding(horizontal = padding8, vertical = padding2),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(padding4)
                ) {
                    EffectButton(onClick = {
                        ally.raceType.toRaceTips().toast()
                    }) {
                        Image(
                            painter = painterResource(
                                ally.getTypeRes()
                            ), modifier = Modifier.size(imageMedium), contentDescription = null
                        )
                    }
                    if (ally.getGroupPic().isNotEmpty()) {
                        EffectButton(
                            modifier = Modifier.size(imageMedium), onClick = {
                                ally.raceType2.toGroupTips().toast()
                            }) {
                            Image(
                                modifier = Modifier.fillMaxSize(),
                                painter = kmpPainterResource(ally.getGroupPic()),
                                contentDescription = null
                            )
                        }
                    }
                    StrokedText(
                        text = ally.quality.getQualityName(),
                        style = MaterialTheme.typography.h3,
                        color = ally.quality.toQualityColor(),
                    )
                }
                PowerLabel(
                    Modifier.size(powerWidth, powerHeight).graphicsLayer {
                        translationY = padding10.toPx()
                    },
                    ally.getAllPower()
                )
            }
        }
        Spacer(modifier = Modifier.size(padding12))
        AllySkillLayout(role, ally)
    }
}

@Composable
fun AllyStarUpView(ally: Ally) {
    Box(contentAlignment = Alignment.Center) {
        val maxStar = ally.star >= MAX_STAR
        val buttonText =
            if (maxStar) AppWrapper.getStringKmp(Res.string.star_max) else AppWrapper.getStringKmp(
                Res.string.star_up
            )
        val allyNumOk = ally.num >= ally.getStarUpNum()
        val resource1Ok = AwardManager.resources[0] >= ally.getStarUpRes()
        val enabled = allyNumOk && !maxStar && resource1Ok
        GameButton(
            text = "",
            buttonStyle = ButtonStyle.Green,
            buttonSize = ButtonSize.Big,
            enabled = enabled,
            onClick = {
                if (enabled) {
                    Dialogs.allyStarUpDialog.value = ally
                } else {
                    if (!allyNumOk) {
                        AppWrapper.getStringKmp(Res.string.card_not_enough).toast()
                    } else if (maxStar) {
                        AppWrapper.getStringKmp(Res.string.already_max_star_tips).toast()
                    } else if (!resource1Ok) {
                        AppWrapper.getStringKmp(Res.string.resource1_not_enough_tips).toast()
                        GiftManager.onResource1NotEnough()
                        Dialogs.moneyTransferDialog.value = true
                    }
                }
            })
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceAround
        ) {
            if (!maxStar) {
                ResourcesPoint(0, ally.getStarUpRes())
            }
            StrokedText(text = buttonText, style = MaterialTheme.typography.h1)
        }
        if (enabled) {
            UpgradeAnimView(
                Modifier.align(Alignment.BottomEnd).height(bigButtonHeight / 1.5f)
            )
        }
        if (GuideManager.guideIndex.value == 12) {
            GuideHand(
                modifier = Modifier.align(Alignment.TopCenter)
                    .height(padding70).graphicsLayer {
                        translationY = -padding50.toPx()
                    }.scale(1.14f), handType = HandType.DOWN_HAND
            )
        }
    }
}

@Composable
fun AllyLevelUpView(ally: Ally) {
    Box(contentAlignment = Alignment.Center) {
        val maxLevel = ally.level >= MAX_LEVEL
        val buttonText =
            if (maxLevel) AppWrapper.getStringKmp(Res.string.level_max) else AppWrapper.getStringKmp(
                Res.string.level_up
            )
        val accountLevelOk = AwardManager.getMasterLevel() > ally.level
        val allyExpOk = AwardManager.allyExp.value >= ally.getLevelUpNum()
        val enabled = accountLevelOk && !maxLevel && allyExpOk

        val gifShowed = remember {
            mutableStateOf(false)
        }
        if (gifShowed.value) {
            GifView(
                modifier = Modifier.size(bigButtonWidth, bigButtonHeight).scale(4f),
                enabled = true,
                gifCount = levelUpGif.count,
                gifDrawable = levelUpGif.gif,
                pace = levelUpGif.pace,
            ) {
                gifShowed.value = false
            }
        }

        GameButton(text = "",
            buttonStyle = ButtonStyle.Blue,
            buttonSize = ButtonSize.Big,
            enabled = enabled,
            longPress = true,
            mute = true,
            clickGap = QUICK_GAP,
            onClick = {
                repo.allyManager.levelUp(ally)?.let {
                    repo.allyManager.currentShowAllyMainId.value = it.mainId
                    if (it.level != MAX_LEVEL) {
                        gifShowed.value = true
                    }
                }?: run {
                    GameCore.instance.onBattleEffect(SoundEffect.Click)
                }
            })
        if (enabled) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(imageSmall),
                painter = painterResource(Res.drawable.red_icon),
                contentDescription = null
            )
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceAround
        ) {
            if (!maxLevel) {
                AllyExpPoint(ally.getLevelUpNum())
            }
            StrokedText(text = buttonText, style = MaterialTheme.typography.h1)
        }

        if (GuideManager.guideIndex.value == 11) {
            GuideHand(
                modifier = Modifier.align(Alignment.TopCenter)
                    .height(padding70).graphicsLayer {
                        translationY = -padding50.toPx()
                    }.scale(1.14f), handType = HandType.DOWN_HAND
            )
        }
    }
}