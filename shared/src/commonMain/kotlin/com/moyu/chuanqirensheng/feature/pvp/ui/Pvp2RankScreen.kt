package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.rank.LAST_PVP2_TYPE
import com.moyu.chuanqirensheng.feature.rank.PVP2_TYPE
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.ui.RankPage
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.music.SoundEffect
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.icon_rank_yesterday
import shared.generated.resources.last_pvp_rank_tips
import shared.generated.resources.pvp2_bg
import shared.generated.resources.pvp2_score_icon
import shared.generated.resources.pvp2_score_tips
import shared.generated.resources.pvp_rank1
import shared.generated.resources.pvp_rank2
import shared.generated.resources.rank_pk
import shared.generated.resources.rank_today

val pvp2Ranks = mutableStateOf(emptyList<RankData>())
val lastPvp2Ranks = mutableStateOf(emptyList<RankData>())

@Composable
fun Pvp2RankScreen() {
    LaunchedEffect(Unit) {
        pvp2Ranks.value = emptyList()
        lastPvp2Ranks.value = emptyList()
    }
    val pagerState = remember {
        mutableStateOf(0)
    }
    val listTabItems = remember {
        mutableStateListOf(
            Res.drawable.rank_today,
            Res.drawable.icon_rank_yesterday,
        )
    }
    GameBackground(
        title = if (pagerState.value == 0) stringResource(Res.string.pvp_rank1) else stringResource(
            Res.string.pvp_rank2
        ),
        bgMask = B35, background = Res.drawable.pvp2_bg
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Row(Modifier.fillMaxWidth()) {
                StrokedText(
                    modifier = Modifier
                        .padding(start = padding12),
                    text = stringResource(Res.string.last_pvp_rank_tips),
                    style = MaterialTheme.typography.h3
                )
            }
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
            ) {
                when (pagerState.value) {
                    0 -> RankPage(type = PVP2_TYPE, data = pvp2Ranks) { rankData, rankIndex ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Row(modifier = Modifier.clickable {
                                MusicManager.playSound(SoundEffect.Click)
                                AppWrapper.getStringKmp(Res.string.pvp2_score_tips).toast()
                            }, verticalAlignment = Alignment.CenterVertically) {
                                Image(
                                    modifier = Modifier.size(imageSmall),
                                    painter = painterResource(Res.drawable.pvp2_score_icon),
                                    contentDescription = null
                                )
                                StrokedText(
                                    text = rankData.pvpScore.toString(),
                                    style = MaterialTheme.typography.h4,
                                    textAlign = TextAlign.Center
                                )
                            }
                            Spacer(modifier = Modifier.size(padding12))
                            StrokedText(
                                text = stringResource(
                                    Res.string.rank_pk,
                                    rankData.pvpData.win,
                                    rankData.pvpData.lose
                                ),
                                style = MaterialTheme.typography.h5
                            )
                        }
                    }

                    else -> {
                        RankPage(
                            type = LAST_PVP2_TYPE,
                            data = lastPvp2Ranks
                        ) { rankData, rankIndex ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Row(modifier = Modifier.clickable {
                                    MusicManager.playSound(SoundEffect.Click)
                                    AppWrapper.getStringKmp(Res.string.pvp2_score_tips).toast()
                                }, verticalAlignment = Alignment.CenterVertically) {
                                    Image(
                                        modifier = Modifier.size(imageSmall),
                                        painter = painterResource(Res.drawable.pvp2_score_icon),
                                        contentDescription = null
                                    )
                                    StrokedText(
                                        text = rankData.pvpScore.toString(),
                                        style = MaterialTheme.typography.h4,
                                        textAlign = TextAlign.Center
                                    )
                                }
                                Spacer(modifier = Modifier.size(padding12))
                                StrokedText(
                                    text = stringResource(
                                        Res.string.rank_pk,
                                        rankData.pvpData.win,
                                        rankData.pvpData.lose
                                    ),
                                    style = MaterialTheme.typography.h5
                                )
                            }
                        }
                    }
                }
            }
            NavigationTab(
                modifier = Modifier.padding(bottom = padding6),
                pageState = pagerState,
                titles = listTabItems
            )

        }
    }
}