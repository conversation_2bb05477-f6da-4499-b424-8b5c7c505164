package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.battlepass.getBattlePassManager
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import com.moyu.core.AppWrapper
import shared.generated.resources.Res
import shared.generated.resources.battle_pass
import shared.generated.resources.battle_pass1
import shared.generated.resources.battle_pass2
import shared.generated.resources.battle_pass3
import shared.generated.resources.icon_quest


@Composable
fun BattlePassScreen(passType: Int) {
    // Tab 标题
    val listTabItems = remember {
        mutableStateListOf(
            when(passType) {
                1 -> Res.drawable.battle_pass
                2 -> Res.drawable.battle_pass2
                else -> Res.drawable.battle_pass3
            },
            Res.drawable.icon_quest
        )
    }
    val pagerState = remember {
        mutableStateOf(0)
    }
    GameBackground(
        title = when(passType) {
            1 -> AppWrapper.kmpStringResource(Res.string.battle_pass1)
            2 -> AppWrapper.kmpStringResource(Res.string.battle_pass2)
            3 -> AppWrapper.kmpStringResource(Res.string.battle_pass3) // 需要你在 strings 里加
            else -> "Battle Pass"
        }
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            // 中间分页
            Box(
                modifier = Modifier.fillMaxWidth().weight(1f)
            ) {
                when (pagerState.value) {
                    0 -> BattlePassPage(passType)
                    1 -> {
                        // 跳转到任务页面
                        // 原先你有 WarPassQuestScreen / WarPass2QuestScreen / WarPass3QuestScreen
                        // 也可以做一个统一 WarPassQuestScreen(passType)
                        // 这里演示单独写：
                        BattlePassQuestScreen(passType)
                    }
                }
            }
            // 底部 tab
            NavigationTab(
                modifier = Modifier.padding(bottom = padding6),
                pageState = pagerState,
                titles = listTabItems,
                redIcons = List(listTabItems.size) { index ->
                    if (index == 0) {
                        // hasRed()：有没有可领取的奖励
                        getBattlePassManager(passType).hasRedItemToGain()
                    } else {
                        getBattlePassManager(passType).hasQuestRed()
                    }
                }
            )
        }
    }
}
