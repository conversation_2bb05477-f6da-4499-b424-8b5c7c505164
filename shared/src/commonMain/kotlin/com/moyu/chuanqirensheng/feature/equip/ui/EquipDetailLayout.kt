package com.moyu.chuanqirensheng.feature.equip.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.role.ui.MainPropertyLine
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding165
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.CardSize
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.chuanqirensheng.widget.effect.ForeverGif
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.property.Property
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.card_label
import shared.generated.resources.card_quality_3
import shared.generated.resources.master_property_icon
import shared.generated.resources.properties


@Composable
fun EquipDetailLayout() {
    Box(
        modifier = Modifier.fillMaxWidth()
    ) {
        EquipSlots()
        EquipCard(Modifier.align(Alignment.TopCenter))
        if (GuideManager.guideIndex.value == 24) {
            GuideHand(
                modifier = Modifier.align(Alignment.BottomStart).padding(start = padding110)
                    .height(padding80), handType = HandType.DOWN_HAND
            )
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun EquipCard(modifier: Modifier) {
    Box(modifier) {
        Image(
            modifier = Modifier.size(CardSize.Medium.width, CardSize.Medium.height),
            painter = painterResource(Res.drawable.card_quality_3),
            contentDescription = null
        )
        ForeverGif(
            Modifier.size(CardSize.Medium.width, CardSize.Medium.height),
            gifCount = 30,
            gifDrawable = "master_move_",
            needGap = false,
            pace = 26,
            scale = 1f
        )
        Box(
            Modifier.align(Alignment.BottomCenter).padding(bottom = padding36).width(padding165),
            contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.FillWidth,
                painter = painterResource(Res.drawable.card_label),
                contentDescription = null
            )
            StrokedText(
                style = MaterialTheme.typography.h2,
                maxLines = 1,
                text = gameSdkDefaultProcessor().getUserName() ?: "",
                overflow = TextOverflow.Ellipsis,
            )
        }

        EffectButton(Modifier.align(Alignment.TopEnd).padding(padding22), onClick = {
            Dialogs.alertDialog.value = CommonAlert(
                title = AppWrapper.getStringKmp(Res.string.properties),
                onlyConfirm = true,
                extraContent = {
                    FlowRow(
                        modifier = Modifier.fillMaxWidth().padding(horizontal = padding20),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalArrangement = Arrangement.spacedBy(padding10),
                        overflow = FlowRowOverflow.Visible,
                        maxItemsInEachRow = 2
                    ) {
                        BattleManager.getGameMasterOutOfGame().getCurrentProperty()
                            .MainPropertyLine(
                                originProperty = Property(),
                                textStyle = MaterialTheme.typography.h2,
                                showBoost = false,
                                textColor = Color.White
                            )
                    }
                })
        }) {
            Image(
                modifier = Modifier.size(imageSmallPlus),
                painter = painterResource(Res.drawable.master_property_icon),
                contentDescription = null
            )
        }
    }
}
