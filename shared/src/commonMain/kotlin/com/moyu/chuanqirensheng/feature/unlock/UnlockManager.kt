package com.moyu.chuanqirensheng.feature.unlock

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.award.AwardManager.toReputationLevelData
import com.moyu.chuanqirensheng.feature.battle.MAX_BATTLE_SIZE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS1_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS3_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.quest.getLoginDays
import com.moyu.chuanqirensheng.feature.quest.getMaxAge
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_ALLY_EXP_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_ALLY_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_HERO_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIAMOND_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_GAME
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_PVP
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_ALLY_FAILED
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_HERO_FAILED
import com.moyu.chuanqirensheng.sub.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_DIAMOND_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPUTATION_MONEY_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_RESOURCE1_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_TALENT_POINT_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_TALENT_RESOURCE_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_UNLOCK_DRAW_ID_PREFIX
import com.moyu.chuanqirensheng.sub.datastore.KEY_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKeySync
import com.moyu.core.model.Unlock
import com.moyu.core.model.mainIdToTalentType


const val MENU_MAIN = 1
const val UNLOCK_MENU_HERO = 2
const val UNLOCK_MENU_ALLY = 4
const val UNLOCK_MENU_TASK = 6
const val UNLOCK_MENU_SELL = 5
const val UNLOCK_SIGN = 7
const val UNLOCK_BATTLE_PASS1 = 8
const val UNLOCK_NEW_QUEST = 10
const val UNLOCK_PVP = 15
const val UNLOCK_BATTLE_PASS2 = 16
const val UNLOCK_SEVEN_DAY = 17
const val UNLOCK_LOTTERY = 18
const val UNLOCK_DRAW_ACTIVITY = 19
const val UNLOCK_PVP2 = 20
const val UNLOCK_TOWER = 21
const val UNLOCK_REPUTATION = 26
const val UNLOCK_TALENT1 = 3
const val UNLOCK_TALENT2 = 22
const val UNLOCK_TALENT3 = 23
const val UNLOCK_BATTLE_PASS3 = 24
const val UNLOCK_RANK = 27
const val UNLOCK_TCG = 39
const val UNLOCK_AUTO_STAGE = 40


const val EQUIP_SLOT_UNLOCK_ID = 100

object UnlockManager {
    val drawUnlockIds = repo.gameCore.getUnlockPool().filter { it.conditionType.first() == UnlockId.DRAW_ID.id }.map { it.conditionNum.first() }
    val unlockStatus = mutableListOf<Boolean>()
    fun init() {
        // null
    }

    fun getUnlockedFlow(unlock: Unlock): Boolean {
        if (DebugManager.unlockAll || unlock.initialLock == 0) return true
        val result = unlock.conditionType.mapIndexed { index, conditionType ->
            when (conditionType) {
                UnlockId.Age.id ->
                    getMaxAge() >= unlock.conditionNum[index]
                UnlockId.LoginDay.id -> {
                    getLoginDays() >= unlock.conditionNum[index]
                }
                UnlockId.Talent.id -> {
                    val talentLevels = TalentManager.talents.values.sum()
                    talentLevels >= unlock.conditionNum[index]
                }
                UnlockId.Code.id ->
                    // 根据本地记录的是否已解锁来判定
                    getBooleanFlowByKey(
                        KEY_UNLOCK_EVIDENCE + unlock.conditionNum[index]
                    )
                UnlockId.VipLevel.id ->
                    // 根据本地记录的是否已解锁来判定
                    VipManager.getVipLevel() >= unlock.conditionNum[index]
                UnlockId.GiftId.id -> {
                    getBooleanFlowByKey(KEY_GIFT_AWARDED + unlock.conditionNum[index])
                }
                UnlockId.Charge.id -> {
                    // 玩家是否充值过（参数1=是，0=否）
                    if (unlock.conditionNum[index] == 0) {
                        AwardManager.electric.value <= 0
                    } else {
                        AwardManager.electric.value > 0
                    }
                }
                UnlockId.Die.id -> {
                    getIntFlowByKey(KEY_DIED_IN_GAME) >= unlock.conditionNum[index]
                }
                UnlockId.Ad.id -> {
                    AwardManager.adNum.value > 0
                }
                UnlockId.NoMoney.id -> {
                    //  11=使用货币提示不足（参数1=非付费货币 2=付费货币）
                    if (unlock.conditionNum[index] == 2) {
                        getIntFlowByKey(KEY_KEY_NOT_ENOUGH) > 0
                    } else if (unlock.conditionNum[index] == 3) {
                        getIntFlowByKey(KEY_DIAMOND_NOT_ENOUGH) > 0
                    } else if (unlock.conditionNum[index] == 1) {
                        getIntFlowByKey(KEY_RESOURCE1_NOT_ENOUGH) > 0
                    } else if (unlock.conditionNum[index] == 4) {
                        getIntFlowByKey(KEY_TALENT_POINT_NOT_ENOUGH) > 0
                    } else if (unlock.conditionNum[index] == 5) {
                        getIntFlowByKey(KEY_ALLY_EXP_NOT_ENOUGH) > 0
                    } else if (unlock.conditionNum[index] == 6) {
                        getIntFlowByKey(KEY_REPUTATION_MONEY_NOT_ENOUGH) > 0
                    } else if (unlock.conditionNum[index] == 7) {
                        getIntFlowByKey(KEY_PVP_DIAMOND_NOT_ENOUGH) > 0
                    } else {
                        false
                    }
                }
                UnlockId.PvpDie.id -> {
                    //  12=玩家竞技场战斗失败（参数N=连续失败N次）
                    getIntFlowByKey(KEY_DIED_IN_PVP) >= unlock.conditionNum[index]
                }
                UnlockId.ElectricMoreOrEqual.id -> {
                    AwardManager.electric.value >= unlock.conditionNum[index]
                }
                UnlockId.ElectricLess.id -> {
                    AwardManager.electric.value < unlock.conditionNum[index]
                }
                UnlockId.Coupon.id -> {
                    if (unlock.conditionNum[index] == 1) {
                        getIntFlowByKey(KEY_CONSUME_ALLY_COUPON) >= 10
                    } else {
                        getIntFlowByKey(KEY_CONSUME_HERO_COUPON) >= 10
                    }
                }
                UnlockId.PASS_STAGE.id -> {
                    StageManager.maxStage.value >= unlock.conditionNum[index]
                }
                UnlockId.DRAW_FAILED.id -> {
                    if (unlock.conditionNum[index] == 1) {
                        // 抽兵种失败
                        getIntFlowByKey(KEY_DRAW_ALLY_FAILED) >= unlock.conditionNum[index]
                    } else {
                        // 抽宝物失败
                        getIntFlowByKey(KEY_DRAW_HERO_FAILED) >= unlock.conditionNum[index]
                    }
                }
                UnlockId.TALENT_RESOURCE_FAILED.id -> {
                    getIntFlowByKey(KEY_TALENT_RESOURCE_NOT_ENOUGH) >= 1
                }
                UnlockId.MASTER_LEVEL.id -> {
                    AwardManager.getMasterLevel() >= unlock.conditionNum[index]
                }
                UnlockId.DRAW_ID.id -> {
                    getBooleanFlowByKey(KEY_UNLOCK_DRAW_ID_PREFIX + unlock.conditionNum[index])
                }
                UnlockId.AnyReputation.id -> {
                    val reputationLevel = unlock.conditionNum[index]
                    toReputationLevelData().any { it.level >= reputationLevel  }
                }
                in UnlockId.Reputation1.id..UnlockId.Reputation11.id -> {
                    // 11阵营声望等级
                    val reputationIndex = conditionType - UnlockId.Reputation1.id
                    val reputationLevel = unlock.conditionNum[index]
                    toReputationLevelData()[reputationIndex].level >= reputationLevel
                }
                UnlockId.Tower.id -> {
                    TowerManager.maxLevel.value >= unlock.conditionNum[index]
                }
                UnlockId.PvpLevel.id -> {
                    PvpManager.getRankLevel().id >= unlock.conditionNum[index]
                }
                UnlockId.Talent1.id -> {
                    TalentManager.talents.filter { it.key.mainIdToTalentType() == 1 }.values.sum() >= unlock.conditionNum[index]
                }
                UnlockId.Talent2.id -> {
                    TalentManager.talents.filter { it.key.mainIdToTalentType() == 2 }.values.sum() >= unlock.conditionNum[index]
                }
                UnlockId.Talent3.id -> {
                    TalentManager.talents.filter { it.key.mainIdToTalentType() == 3 }.values.sum() >= unlock.conditionNum[index]
                }
                else -> false
            }
        }
        return result.all { it }
    }

    suspend fun unlockCode(id: Int) {
        if (id == KEY_WAR_PASS1_UNLOCK_EVIDENCE) {
            AwardManager.battlePass1Bought.value = true
        } else if (id == KEY_WAR_PASS2_UNLOCK_EVIDENCE) {
            AwardManager.battlePass2Bought.value = true
        } else if (id == KEY_WAR_PASS3_UNLOCK_EVIDENCE) {
            AwardManager.battlePass3Bought.value = true
        }
        setBooleanValueByKeySync(KEY_UNLOCK_EVIDENCE + id, true)
    }

    fun getInitAllyNum(): Int {
        return MAX_BATTLE_SIZE
    }

    fun getEquipUnlockByIndex(index: Int): Unlock {
        return repo.gameCore.getUnlockById(EQUIP_SLOT_UNLOCK_ID + index)
    }

    fun checkIfShowUnlockDialog() {
        if (unlockStatus.isEmpty()) {
            unlockStatus.addAll(repo.gameCore.getUnlockPool().filter { it.canShowDialog() }.map { getUnlockedFlow(it) })
        } else {
            val newUnlockStatus = repo.gameCore.getUnlockPool().filter { it.canShowDialog() }.map { getUnlockedFlow(it) }
            if (newUnlockStatus != unlockStatus) {
                Dialogs.unlockStatusDialog.value = Pair(unlockStatus.toList(), newUnlockStatus)
                unlockStatus.clear()
                unlockStatus.addAll(newUnlockStatus)
            }
        }
    }
}