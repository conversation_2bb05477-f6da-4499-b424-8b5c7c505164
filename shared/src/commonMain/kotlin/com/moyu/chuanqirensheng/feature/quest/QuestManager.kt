package com.moyu.chuanqirensheng.feature.quest

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.drawactivity.DrawActivityManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.holiday.ui.holidaySolidRanks
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.ui.lastPvp2Ranks
import com.moyu.chuanqirensheng.feature.pvp.ui.lastPvpRanks
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.serverrank.ServerRankManager
import com.moyu.chuanqirensheng.feature.serverrank.ui.serverSolidRanks1
import com.moyu.chuanqirensheng.feature.serverrank.ui.serverSolidRanks2
import com.moyu.chuanqirensheng.feature.serverrank.ui.serverSolidRanks3
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkQuest
import com.moyu.chuanqirensheng.sub.datastore.KEY_CHARGE_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_COLLECT_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_COST_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_DRAW_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_PVP2_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_PVP2_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_PVP_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WARPASS2_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WARPASS3_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_HOLIDAY_GAME_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPUTATION_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_SERVER_RANK_GAME_TASK1
import com.moyu.chuanqirensheng.sub.datastore.KEY_SERVER_RANK_GAME_TASK2
import com.moyu.chuanqirensheng.sub.datastore.KEY_SERVER_RANK_GAME_TASK3
import com.moyu.chuanqirensheng.sub.datastore.KEY_TASK_DIALOG_SHOWED
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS2_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS3_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS_TASK
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.mapData
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.model.Award
import com.moyu.core.model.Quest
import com.moyu.core.util.RANDOM
import com.moyu.core.util.subListOrEmpty
import kotlin.math.max
import kotlin.math.min

const val FOREVER = "k_)"

object QuestManager {
    val dailyTasks = mutableStateListOf<Quest>()
    val pvpTasks = mutableStateListOf<Quest>()
    val pvp2Tasks = mutableStateListOf<Quest>()
    val warPass1Tasks = mutableStateListOf<Quest>()
    val warPass2Tasks = mutableStateListOf<Quest>()
    val warPass3Tasks = mutableStateListOf<Quest>()
    val newTasks = mutableStateListOf<Quest>()

    fun init() {
        createTasks()
        createPvpTasks()
        createPvp2Tasks()
        createWarPassTasks()
        createWarPass2Tasks()
        createWarPass3Tasks()
        createNewTasks()
        // 任务中,启动游戏可以直接标记已完成
        onTaskStartGameTime()
    }

    fun createWarPassTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (warPass1Tasks.isEmpty()) {
            getListObject<Quest>(KEY_WAR_PASS_TASK).let { taskList ->
                val tasks = taskList.map { task ->
                    repo.gameCore.getGameTaskById(task.id)
                        .copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                warPass1Tasks.addAll(tasks)
            }
        }
        if (!isSameDay(
                getLongFlowByKey(KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS),
                getCurrentTime()
            )
        ) {
            // 通行证任务，每天+4个
            val filteredTasks = repo.gameCore.getGameTaskPool()
            warPass1Tasks.clear()
            warPass1Tasks.addAll(filteredTasks.filter { it.isWarPass1Task() }
                .take(repo.gameCore.getWarPassQuestCount()))
            setLongValueByKey(KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        }
        setListObject(KEY_WAR_PASS_TASK, warPass1Tasks)
    }

    fun createWarPass2Tasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (warPass2Tasks.isEmpty()) {
            getListObject<Quest>(KEY_WAR_PASS2_TASK).let { taskList ->
                val tasks = taskList.map { task ->
                    repo.gameCore.getGameTaskById(task.id)
                        .copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                warPass2Tasks.addAll(tasks)
            }
        }
        if (warPass2Tasks.isEmpty()) {
            val filteredTasks = repo.gameCore.getGameTaskPool()
            warPass2Tasks.clear()
            warPass2Tasks.addAll(filteredTasks.filter { it.isWarPass2Task() })
            setLongValueByKey(KEY_GAME_WARPASS2_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_WAR_PASS2_TASK, warPass2Tasks)
        }
    }

    fun createWarPass3Tasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        // 1) 如果 warPass3Tasks 还没加载过，就尝试从本地存储读取
        if (warPass3Tasks.isEmpty()) {
            getListObject<Quest>(KEY_WAR_PASS3_TASK).let { taskList ->
                val tasks = taskList.map { task ->
                    // 拿到全量 pool 的任务，根据id找，并保留 opened/done
                    repo.gameCore.getGameTaskById(task.id)
                        .copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                warPass3Tasks.addAll(tasks)
            }
        }

        // 2) 如果还是空，则说明还没有生成过 -> 生成
        if (warPass3Tasks.isEmpty()) {
            val filteredTasks = repo.gameCore.getGameTaskPool()
            // 假设你给 warPass3 定义了 taskType == 8 的任务
            warPass3Tasks.clear()
            warPass3Tasks.addAll(filteredTasks.filter { it.isWarPass3Task() })

            // 更新刷新时间戳
            setLongValueByKey(KEY_GAME_WARPASS3_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            // 保存到本地
            setListObject(KEY_WAR_PASS3_TASK, warPass3Tasks)
        }
    }


    fun createNewTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (newTasks.isEmpty()) {
            getListObject<Quest>(KEY_NEW_TASK).let { taskList ->
                val tasks = taskList.map { task ->
                    repo.gameCore.getGameTaskById(task.id)
                        .copy(done = task.done, opened = task.opened, needRemoveCount = task.needRemoveCount)
                }
                newTasks.addAll(tasks)
            }
        }
        val canGenerateNewTasks = newTasks.isEmpty() || newTasks.all { getTaskDoneFlow(it) }
        if (canGenerateNewTasks && !isSameDay(
                getLongFlowByKey(KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            ) || DebugManager.questDone
        ) {
            // 新手任务，每天+4个
            val filteredTasks = repo.gameCore.getGameTaskPool()
            val showedMaxId = newTasks.maxOfOrNull { it.id } ?: 0
            newTasks.addAll(filteredTasks.filter { it.isNewTask() }.filter {
                it.id > showedMaxId
            }.sortedBy { it.id }.take(if (DebugManager.questDone) 999 else repo.gameCore.getNewQuestCount()).map { task ->
                // 要用永久计数，但是又要移除之前的计数
                val needRemove = if (task.isMaxRecordQuest()) {
                    0
                } else {
                    val postFix = task.subType.map {
                        if (it == 0) "" else "_$it"
                    }.reduce { acc, s -> acc + s }
                    getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + task.type + postFix, 0)
                }
                task.copy(needRemoveCount = needRemove)
            })
            // 特殊逻辑，只有刷新了，才更新，什么时候完成任务，进行刷新，什么时候才更新这个时间戳
            setLongValueByKey(KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        } else {
            // 如果没有刷新，也要更新一下时间戳，保证当天完成第一章任务，无法直接刷出第二章，要等第二天才能刷出来
            setLongValueByKey(KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        }
        setListObject(KEY_NEW_TASK, newTasks)
    }

    fun createTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (dailyTasks.isEmpty()) {
                try {
                    getListObject<Quest>(KEY_GAME_TASK).let {
                        dailyTasks.addAll(it.map { task ->
                            repo.gameCore.getGameTaskById(task.id)
                                .copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
//                    Timber.e(e)
                    // 修改了task格式
                    clearDayTasks()
                }
            }
        } else {
            // 任务：如果已经过了一天，清理任务记录
            clearDayTasks()
            onTaskStartGameDay()
        }

        // 强制设置最少1天签到
        if (getIntFlowByKey(KEY_GAME_LOGIN_DAY) == 0) {
            setIntValueByKey(KEY_GAME_LOGIN_DAY, 1)
        }
        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (dailyTasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getMaxAge()
            )
            dailyTasks.addAll(filteredTasks.filter { it.isDailyTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM)
                .take(VipManager.getMaxDailyQuest() + repo.gameCore.getDailyQuestCount())
            )

            setLongValueByKey(KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_TASK, dailyTasks)
        }
    }

    fun createPvpTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (pvpTasks.isEmpty()) {
                try {
                    getListObject<Quest>(KEY_GAME_PVP_TASK).let {
                        pvpTasks.addAll(it.map { task ->
                            repo.gameCore.getGameTaskById(task.id)
                                .copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
//                    Timber.e(e)
                    // 修改了task格式
                    clearDayTasks()
                }
            }
        } else {
            pvpTasks.clear()
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (pvpTasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getMaxAge()
            )
            pvpTasks.addAll(filteredTasks.filter { it.isPvpTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM))

            setLongValueByKey(KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_PVP_TASK, pvpTasks)
        }
    }

    fun createPvp2Tasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_PVP2_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (pvp2Tasks.isEmpty()) {
                try {
                    getListObject<Quest>(KEY_GAME_PVP2_TASK).let {
                        pvp2Tasks.addAll(it.map { task ->
                            repo.gameCore.getGameTaskById(task.id)
                                .copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
//                    Timber.e(e)
                }
            }
        } else {
            pvp2Tasks.clear()
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (pvp2Tasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getMaxAge()
            )
            pvp2Tasks.addAll(filteredTasks.filter { it.isPvp2Task() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM))

            setLongValueByKey(KEY_GAME_PVP2_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_PVP2_TASK, pvp2Tasks)
        }
    }

    fun getTaskDoneFlow(task: Quest): Boolean {
        val taskNum = task.num
        when (task.type) {
            QuestEvent.HAVE_TYPE_ITEM.id -> {
                return if (task.subType.first() in 10..13) {
                    if (task.subType.first() == 10) {
                        repo.equipManager.data.size >= taskNum
                    } else {
                        val quality = task.subType.first() - 10
                        repo.equipManager.data.filter { it.quality >= quality }.size >= taskNum
                    }
                } else {
                    if (task.subType.first() == 20) {
                        repo.allyManager.data.filter { !it.isHero() }.size >= taskNum
                    } else {
                        val quality = task.subType.first() - 20
                        repo.allyManager.data.filter { !it.isHero() && it.quality >= quality }.size >= taskNum
                    }
                }
            }

            QuestEvent.HAVE_STAR_ITEM.id -> {
                return if (task.subType.first() in 100..199) {
                    val star = task.subType.first() % 100
                    repo.equipManager.data.filter { it.star >= star }.size >= taskNum
                } else {
                    val star = task.subType.first() % 100
                    repo.allyManager.data.filter { !it.isHero() && it.star >= star }.size >= taskNum
                }
            }

            QuestEvent.PVP_BATTLE.id -> {
                return if (task.subType.first() == 0) {
                    PvpManager.pkWinToday.value + PvpManager.pkLoseToday.value >= taskNum
                } else {
                    PvpManager.pkWinToday.value >= taskNum
                }
            }

            QuestEvent.HOLIDAY_LOTTERY.id -> {
                return HolidayLotteryManager.isLotteryTaskDone(task.num)
            }

            QuestEvent.HOLIDAY_CHARGE.id -> {
                return HolidayManager.isChargeTaskDone(task.num)
            }

            QuestEvent.PVP_RANK.id -> {
                val realRank = lastPvpRanks.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    2 -> {
                        realRank.getOrNull(1)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    3 -> {
                        realRank.getOrNull(2)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    4 -> {
                        realRank.getOrNull(3)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    5 -> {
                        realRank.getOrNull(4)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    6 -> {
                        realRank.getOrNull(5)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    7 -> {
                        realRank.getOrNull(6)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    8 -> {
                        realRank.getOrNull(7)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    9 -> {
                        realRank.getOrNull(8)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    10 -> {
                        realRank.getOrNull(9)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    11 -> {
                        // 11-20名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 20)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                    }
                    12 -> {
                        // 21-50名
                        val userIds = if (realRank.size > 20) {
                            realRank.subList(20, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                    }
                    13 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 50) {
                            realRank.subList(50, min(realRank.size, 100)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                    }
                    else -> {
                        false
                    }
                }
            }

            QuestEvent.PVP2_RANK.id -> {
                val realRank = lastPvp2Ranks.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == gameSdkDefaultProcessor().getObjectId()
                    }
                    2 -> {
                        // 2-5名
                        val userIds = if (realRank.size > 1) {
                            realRank.subList(1, min(realRank.size, 5)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                    }
                    3 -> {
                        // 6-10名
                        val userIds = if (realRank.size > 5) {
                            realRank.subList(5, min(realRank.size, 10)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                    }
                    4 -> {
                        // 11-50名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 20)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                    }
                    5 -> {
                        // 21-50名
                        val userIds = if (realRank.size > 20) {
                            realRank.subList(20, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                    }
                    6 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 50) {
                            realRank.subList(50, min(realRank.size, 100)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                    }
                    else -> {
                        false
                    }
                }
            }

            /**
             *  24：1=第1名；2=第2名；3=第3名；4=4-5名；5=6-10名；6=11-50名；7=51-100名
             */
            QuestEvent.HOLIDAY_RANK.id -> {
                val realRank = holidaySolidRanks.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10)
                            .lastOrNull()?.holidayNum ?: 9999)
                                && realRank.size >= 3
                    }

                    2 -> {
                        realRank.getOrNull(1)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10)
                            .lastOrNull()?.holidayNum ?: 9999)
                                && realRank.size >= 3
                    }

                    3 -> {
                        realRank.getOrNull(2)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10)
                            .lastOrNull()?.holidayNum ?: 9999)
                                && realRank.size >= 3
                    }

                    4 -> {
                        (realRank.getOrNull(3)?.userId == gameSdkDefaultProcessor().getObjectId()
                                || realRank.getOrNull(4)?.userId == gameSdkDefaultProcessor().getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10)
                            .lastOrNull()?.holidayNum ?: 9999)
                    }

                    5 -> {
                        // 6-10名
                        val userIds = if (realRank.size > 5) {
                            realRank.subList(5, min(realRank.size, 10)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10)
                            .lastOrNull()?.holidayNum ?: 9999)
                    }

                    6 -> {
                        // 11-50名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.lastOrNull()?.holidayNum
                            ?: 9999)
                    }

                    7 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 50) {
                            realRank.subList(50, min(realRank.size, 100)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.lastOrNull()?.holidayNum
                            ?: 9999)
                    }

                    else -> {
                        false
                    }
                }
            }

            QuestEvent.SERVER_RANK_1.id -> {
                val ensureValue = 0
                val realRank = serverSolidRanks1.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && AwardManager.getAllBattlePower() >= (realRank.take(10)
                            .lastOrNull()?.battlePower ?: ensureValue)
                    }

                    2 -> {
                        realRank.getOrNull(1)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && AwardManager.getAllBattlePower() >= (realRank.take(10)
                            .lastOrNull()?.battlePower ?: ensureValue)
                    }

                    3 -> {
                        realRank.getOrNull(2)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && AwardManager.getAllBattlePower() >= (realRank.take(10)
                            .lastOrNull()?.battlePower ?: ensureValue)
                                && realRank.size >= 3
                    }

                    4 -> {
                        (realRank.getOrNull(3)?.userId == gameSdkDefaultProcessor().getObjectId()
                                || realRank.getOrNull(4)?.userId == gameSdkDefaultProcessor().getObjectId())
                                && AwardManager.getAllBattlePower() >= (realRank.take(10)
                            .lastOrNull()?.battlePower ?: ensureValue)
                    }

                    5 -> {
                        // 6-10名
                        val userIds = if (realRank.size > 5) {
                            realRank.subList(5, min(realRank.size, 10)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && AwardManager.getAllBattlePower() >= (realRank.take(10)
                            .lastOrNull()?.battlePower ?: ensureValue)
                    }

                    6 -> {
                        // 11-20名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 20)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && AwardManager.getAllBattlePower() >= (realRank.lastOrNull()?.battlePower
                            ?: ensureValue)
                    }

                    7 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 20) {
                            realRank.subList(20, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && AwardManager.getAllBattlePower() >= (realRank.lastOrNull()?.battlePower
                            ?: ensureValue)
                    }

                    else -> {
                        false
                    }
                }
            }

            QuestEvent.SERVER_RANK_2.id -> {
                val ensureValue = 0
                val realRank = serverSolidRanks2.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && TalentManager.getAllTalentLevel(1) >= (realRank.take(10)
                            .lastOrNull()?.talentLevel ?: ensureValue)
                    }

                    2 -> {
                        realRank.getOrNull(1)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && TalentManager.getAllTalentLevel(1) >= (realRank.take(10)
                            .lastOrNull()?.talentLevel ?: ensureValue)
                    }

                    3 -> {
                        realRank.getOrNull(2)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && TalentManager.getAllTalentLevel(1) >= (realRank.take(10)
                            .lastOrNull()?.talentLevel ?: ensureValue)
                                && realRank.size >= 3
                    }

                    4 -> {
                        (realRank.getOrNull(3)?.userId == gameSdkDefaultProcessor().getObjectId()
                                || realRank.getOrNull(4)?.userId == gameSdkDefaultProcessor().getObjectId())
                                && TalentManager.getAllTalentLevel(1) >= (realRank.take(10)
                            .lastOrNull()?.talentLevel ?: ensureValue)
                    }

                    5 -> {
                        // 6-10名
                        val userIds = if (realRank.size > 5) {
                            realRank.subList(5, min(realRank.size, 10)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && TalentManager.getAllTalentLevel(1) >= (realRank.take(10)
                            .lastOrNull()?.talentLevel ?: ensureValue)
                    }

                    6 -> {
                        // 11-20名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 20)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && TalentManager.getAllTalentLevel(1) >= (realRank.lastOrNull()?.talentLevel
                            ?: ensureValue)
                    }

                    7 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 20) {
                            realRank.subList(20, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && TalentManager.getAllTalentLevel(1) >= (realRank.lastOrNull()?.talentLevel
                            ?: ensureValue)
                    }

                    else -> {
                        false
                    }
                }
            }

            QuestEvent.SERVER_RANK_3.id -> {
                val ensureValue = 0
                val realRank = serverSolidRanks3.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && StageManager.maxStage.value >= (realRank.take(10)
                            .lastOrNull()?.endingNum ?: ensureValue)
                    }

                    2 -> {
                        realRank.getOrNull(1)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && StageManager.maxStage.value >= (realRank.take(10)
                            .lastOrNull()?.endingNum ?: ensureValue)
                    }

                    3 -> {
                        realRank.getOrNull(2)?.userId == gameSdkDefaultProcessor().getObjectId()
                                && StageManager.maxStage.value >= (realRank.take(10)
                            .lastOrNull()?.endingNum ?: ensureValue)
                                && realRank.size >= 3
                    }

                    4 -> {
                        (realRank.getOrNull(3)?.userId == gameSdkDefaultProcessor().getObjectId()
                                || realRank.getOrNull(4)?.userId == gameSdkDefaultProcessor().getObjectId())
                                && StageManager.maxStage.value >= (realRank.take(10)
                            .lastOrNull()?.endingNum ?: ensureValue)
                    }

                    5 -> {
                        // 6-10名
                        val userIds = if (realRank.size > 5) {
                            realRank.subList(5, min(realRank.size, 10)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && StageManager.maxStage.value >= (realRank.take(10)
                            .lastOrNull()?.endingNum ?: ensureValue)
                    }

                    6 -> {
                        // 11-20名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 20)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && StageManager.maxStage.value >= (realRank.lastOrNull()?.endingNum
                            ?: ensureValue)
                    }

                    7 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 20) {
                            realRank.subList(20, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(gameSdkDefaultProcessor().getObjectId())
                                && StageManager.maxStage.value >= (realRank.lastOrNull()?.endingNum
                            ?: ensureValue)
                    }

                    else -> {
                        false
                    }
                }
            }

            QuestEvent.COST.id -> {
                return AwardManager.keyCost.value >= task.num
            }

            QuestEvent.CHARGE.id -> {
                return SevenDayManager.isChargeTaskDone(task.num)
            }

            QuestEvent.TOWER.id -> {
                return TowerManager.maxLevel.value >= task.num
            }

            QuestEvent.PASS_STAGE.id -> {
                return StageManager.maxStage.value >= task.num
            }

            else -> {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                val preFix = if (task.isForever()) FOREVER else ""
                return getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + task.type + postFix) - task.needRemoveCount >= taskNum
            }
        }
    }

    fun getTaskProgressFlow(task: Quest): String {
        val taskNum = task.num
        when (task.type) {

            QuestEvent.HAVE_TYPE_ITEM.id -> {
                return  if (task.subType.first() in 10..13) {
                    if (task.subType.first() == 10) {
                        repo.equipManager.data.size.toString() + "/" + taskNum
                    } else {
                        val quality = task.subType.first() - 10
                        repo.equipManager.data.filter { it.quality >= quality }.size.toString() + "/" + taskNum
                    }
                } else {
                    if (task.subType.first() == 20) {
                        repo.allyManager.data.filter { !it.isHero() }.size.toString() + "/" + taskNum
                    } else {
                        val type = task.subType.first() - 20
                        repo.allyManager.data.filter { !it.isHero() && it.quality >= type }.size.toString() + "/" + taskNum
                    }
                }
            }

            QuestEvent.HAVE_STAR_ITEM.id -> {
                return if (task.subType.first() in 100..199) {
                    val star = task.subType.first() % 100
                    repo.equipManager.data.filter { it.star >= star }.size.toString() + "/" + taskNum
                } else {
                    val star = task.subType.first() % 100
                    repo.allyManager.data.filter { !it.isHero() && it.star >= star }.size.toString() + "/" + taskNum
                }
            }

            QuestEvent.PVP_BATTLE.id -> {
                return if (task.subType.first() == 0) {
                    (PvpManager.pkWinToday.value + PvpManager.pkLoseToday.value).toString() + "/" + taskNum
                } else {
                    (PvpManager.pkWinToday.value).toString() + "/" + taskNum
                }
            }

            QuestEvent.PVP_RANK.id -> {
                return ""
            }

            QuestEvent.PVP2_RANK.id -> {
                return ""
            }

            QuestEvent.HOLIDAY_RANK.id -> {
                return ""
            }

            QuestEvent.SERVER_RANK_1.id -> {
                return ""
            }

            QuestEvent.SERVER_RANK_2.id -> {
                return ""
            }

            QuestEvent.SERVER_RANK_3.id -> {
                return ""
            }

            QuestEvent.COST.id -> {
                return AwardManager.keyCost.value.toString() + "/" + task.num
            }

            QuestEvent.CHARGE.id -> {
                return SevenDayManager.isChargeTaskDoneString(task.num)
            }

            QuestEvent.TOWER.id -> {
                return TowerManager.maxLevel.value.toString() + "/" + task.num
            }

            QuestEvent.PASS_STAGE.id -> {
                return StageManager.maxStage.value.toString() + "/" + taskNum
            }

            else -> {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                val preFix = if (task.isForever()) FOREVER else ""
                return getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + task.type + postFix).let {
                    "${it - task.needRemoveCount}/${taskNum}"
                }
            }
        }
    }

    suspend fun questReward(quest: Quest, award: Award) {
        checkQuest(quest)
        if (quest.isDailyTask()) {
            val index = dailyTasks.indexOfFirst { it.id == quest.id }
            if (!dailyTasks[index].opened) {
                dailyTasks[index] = dailyTasks[index].copy(opened = true)
                setListObject(KEY_GAME_TASK, dailyTasks)
                val realAward = if (VipManager.isDoubleQuestAward()) {
                    award + award
                } else award
                AwardManager.gainAward(realAward)
                Dialogs.awardDialog.value = realAward
            }
        } else if (quest.isNewTask()) {
            val index = newTasks.indexOfFirst { it.id == quest.id }
            if (!newTasks[index].opened) {
                newTasks[index] = newTasks[index].copy(opened = true)
                setListObject(KEY_NEW_TASK, newTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isWarPass1Task()) {
            val index = warPass1Tasks.indexOfFirst { it.id == quest.id }
            if (!warPass1Tasks[index].opened) {
                warPass1Tasks[index] = warPass1Tasks[index].copy(opened = true)
                setListObject(KEY_WAR_PASS_TASK, warPass1Tasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isWarPass2Task()) {
            val index = warPass2Tasks.indexOfFirst { it.id == quest.id }
            if (!warPass2Tasks[index].opened) {
                warPass2Tasks[index] = warPass2Tasks[index].copy(opened = true)
                setListObject(KEY_WAR_PASS2_TASK, warPass2Tasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isWarPass3Task()) {
            val index = warPass3Tasks.indexOfFirst { it.id == quest.id }
            if (!warPass3Tasks[index].opened) {
                warPass3Tasks[index] = warPass3Tasks[index].copy(opened = true)
                setListObject(KEY_WAR_PASS3_TASK, warPass3Tasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isPvpTask()) {
            val index = pvpTasks.indexOfFirst { it.id == quest.id }
            if (!pvpTasks[index].opened) {
                pvpTasks[index] = pvpTasks[index].copy(opened = true)
                setListObject(KEY_GAME_PVP_TASK, pvpTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isPvp2Task()) {
            val index = pvp2Tasks.indexOfFirst { it.id == quest.id }
            if (!pvp2Tasks[index].opened) {
                pvp2Tasks[index] = pvp2Tasks[index].copy(opened = true)
                setListObject(KEY_GAME_PVP2_TASK, pvp2Tasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isCollectTask()) {
            val index = SevenDayManager.collectTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.collectTasks[index].opened) {
                SevenDayManager.collectTasks[index] = SevenDayManager.collectTasks[index].copy(opened = true)
                setListObject(KEY_COLLECT_TASK, SevenDayManager.collectTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isCostTask()) {
            val index = SevenDayManager.costTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.costTasks[index].opened) {
                SevenDayManager.costTasks[index] = SevenDayManager.costTasks[index].copy(opened = true)
                setListObject(KEY_COST_TASK, SevenDayManager.costTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isServerTask1()) {
            val index = ServerRankManager.serverRankTasks1.indexOfFirst { it.id == quest.id }
            if (!ServerRankManager.serverRankTasks1[index].opened) {
                ServerRankManager.serverRankTasks1[index] = ServerRankManager.serverRankTasks1[index].copy(opened = true)
                setListObject(KEY_SERVER_RANK_GAME_TASK1, ServerRankManager.serverRankTasks1)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isServerTask2()) {
            val index = ServerRankManager.serverRankTasks2.indexOfFirst { it.id == quest.id }
            if (!ServerRankManager.serverRankTasks2[index].opened) {
                ServerRankManager.serverRankTasks2[index] = ServerRankManager.serverRankTasks2[index].copy(opened = true)
                setListObject(KEY_SERVER_RANK_GAME_TASK2, ServerRankManager.serverRankTasks2)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isServerTask3()) {
            val index = ServerRankManager.serverRankTasks3.indexOfFirst { it.id == quest.id }
            if (!ServerRankManager.serverRankTasks3[index].opened) {
                ServerRankManager.serverRankTasks3[index] = ServerRankManager.serverRankTasks3[index].copy(opened = true)
                setListObject(KEY_SERVER_RANK_GAME_TASK3, ServerRankManager.serverRankTasks3)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isHolidayTask()) {
            val index = HolidayManager.holidayTasks.indexOfFirst { it.id == quest.id }
            if (!HolidayManager.holidayTasks[index].opened) {
                HolidayManager.holidayTasks[index] = HolidayManager.holidayTasks[index].copy(opened = true)
                setListObject(KEY_HOLIDAY_GAME_TASK, HolidayManager.holidayTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isChargeTask()) {
            val index = SevenDayManager.chargeTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.chargeTasks[index].opened) {
                SevenDayManager.chargeTasks[index] = SevenDayManager.chargeTasks[index].copy(opened = true)
                setListObject(KEY_CHARGE_TASK, SevenDayManager.chargeTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isDrawActivityTask()) {
            val index = DrawActivityManager.drawTasks.indexOfFirst { it.id == quest.id }
            if (!DrawActivityManager.drawTasks[index].opened) {
                DrawActivityManager.drawTasks[index] = DrawActivityManager.drawTasks[index].copy(opened = true)
                setListObject(KEY_GAME_DRAW_TASK, DrawActivityManager.drawTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isReputationTask()) {
            val index = ReputationManager.collectTasks.indexOfFirst { it.id == quest.id }
            if (!ReputationManager.collectTasks[index].opened) {
                ReputationManager.collectTasks[index] = ReputationManager.collectTasks[index].copy(opened = true)
                setListObject(KEY_REPUTATION_TASK, ReputationManager.collectTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else {
            error("未知任务类型${quest.taskType}")
        }
    }

    private fun clearDayTasks() {
        dailyTasks.clear()
        mapData.keys.filter {
            it.startsWith(
                KEY_GAME_TASK_PROGRESS
            )
        }.forEach {
            setIntValueByKey(it, 0)
        }
        // todo 每日清理数据放这里
        GiftManager.onRefreshNumTheNextDay()
    }

    fun isTaskDialogShowed(task: Quest): Boolean {
        return getBooleanFlowByKey(KEY_TASK_DIALOG_SHOWED + "_" + task.id)
    }

    fun showTaskDialog(task: Quest) {
        setBooleanValueByKey(KEY_TASK_DIALOG_SHOWED + "_" + task.id, true)
    }

    fun getNewQuestByPageIndex(tasks: List<Quest>, page: Int): List<Quest> {
        return tasks.subListOrEmpty(
            (page) * repo.gameCore.getNewQuestCount(),
            (page + 1) * repo.gameCore.getNewQuestCount()
        )
    }

    fun getNewQuestPageCount(tasks: List<Quest>): Int {
        val pageMax = repo.gameCore.getGameTaskPool()
            .filter { it.isNewTask() }.size / repo.gameCore.getNewQuestCount()
        if (DebugManager.questDone) return pageMax
        (0.until(pageMax)).forEach { page ->
            val subTask = getNewQuestByPageIndex(tasks, page)
            if (subTask.any { !it.done }) {
                // 当前页面有任务没完成，那就显示这一页
                return min(pageMax, page + 1)
            }
            if (subTask.isEmpty()) {
                // 当前页面没有任务，说明还没有开始解锁，那就显示上一页
                return min(pageMax, page)
            }
        }
        return pageMax
    }

    fun getInitPageIndex(tasks: SnapshotStateList<Quest>): Int {
        val pageMax = repo.gameCore.getGameTaskPool()
            .filter { it.isNewTask() }.size / repo.gameCore.getNewQuestCount()
        (0.until(pageMax)).forEach { page ->
            val subTask = getNewQuestByPageIndex(tasks, page)
            if (subTask.any { !it.opened }) {
                // 当前页面有任务没领完，那就显示这一页
                return min(pageMax, page)
            }
            if (subTask.isEmpty()) {
                // 当前页面没有任务，说明还没有开始解锁，那就显示上一页
                return max(0, page - 1)
            }
        }
        return 0
    }

    fun canAwardNewQuestByPageIndex(tasks: List<Quest>, pageIndex: Int): Boolean {
        val subTask = getNewQuestByPageIndex(tasks, pageIndex)
        return subTask.isNotEmpty() && subTask.all { it.done }
    }

    fun getNewQuestsByIndex(index: Int): List<Quest> {
        createNewTasks()
        // 完成的任务排前面，已领取的排最后
        return getNewQuestByPageIndex(newTasks, index).map {
            it.copy(done = getTaskDoneFlow(it))
        }.sortedBy { it.order }
    }
}