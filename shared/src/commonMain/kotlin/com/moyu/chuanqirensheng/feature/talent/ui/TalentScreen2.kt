package com.moyu.chuanqirensheng.feature.talent.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.reputation.ui.ReputationLevelView
import com.moyu.chuanqirensheng.feature.resource.CurrentResourcesPointAll
import com.moyu.chuanqirensheng.feature.story.toReputationName
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager.buildTalent2UIModel
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding400
import com.moyu.chuanqirensheng.ui.theme.padding44
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.composeDp
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_arrow_left
import shared.generated.resources.common_arrow_right
import shared.generated.resources.common_lock
import shared.generated.resources.hero_starup
import shared.generated.resources.next_page
import shared.generated.resources.prev_page
import shared.generated.resources.reputation_level
import shared.generated.resources.talent_base

val talent1Positions = listOf(
    Triple(41.composeDp(), 580.composeDp(), 1f),
    Triple(266.composeDp(), 584.composeDp(), 1f),
    Triple(151.composeDp(), 554.composeDp(), 0.8f),
    Triple(11.composeDp(), 474.composeDp(), 0.75f),
    Triple(295.composeDp(), 463.composeDp(), 0.90f),
    Triple(39.composeDp(), 364.composeDp(), 0.84f),
    Triple(181.composeDp(), 401.composeDp(), 0.88f),
    Triple(25.composeDp(), 255.composeDp(), 0.73f),
    Triple(299.composeDp(), 324.composeDp(), 0.81f),
    Triple(147.composeDp(), 287.composeDp(), 0.78f),
    Triple(267.composeDp(), 222.composeDp(), 0.76f),
    Triple(41.composeDp(), 154.composeDp(), 0.75f),
    Triple(151.composeDp(), 174.composeDp(), 0.74f),
    Triple(275.composeDp(), 101.composeDp(), 0.73f),
)

/* ── talent 2 ──────────────────────────────────────────────── */
val talent2Positions = listOf(
    Triple( 50.composeDp(), 576.composeDp(), 1.00f),
    Triple(260.composeDp(), 590.composeDp(), 1.00f),
    Triple(160.composeDp(), 530.composeDp(), 0.8f),
    Triple( 20.composeDp(), 466.composeDp(), 0.75f),
    Triple(290.composeDp(), 470.composeDp(), 0.91f),
    Triple( 30.composeDp(), 370.composeDp(), 0.85f),
    Triple(190.composeDp(), 396.composeDp(), 0.87f),
    Triple( 30.composeDp(), 263.composeDp(), 0.74f),
    Triple(305.composeDp(), 318.composeDp(), 0.82f),
    Triple(140.composeDp(), 294.composeDp(), 0.79f),
    Triple(275.composeDp(), 230.composeDp(), 0.77f),
    Triple( 50.composeDp(), 148.composeDp(), 0.74f),
    Triple(142.composeDp(), 180.composeDp(), 0.75f),
    Triple(268.composeDp(), 108.composeDp(), 0.74f),
)

/* ── talent 3 ──────────────────────────────────────────────── */
val talent3Positions = listOf(
    Triple(54.composeDp(), 575.composeDp(), 1.00f),
    Triple( 279.composeDp(), 590.composeDp(), 1.00f),
    Triple(170.composeDp(), 548.composeDp(), 0.8f),
    Triple(310.composeDp(), 470.composeDp(), 0.75f),
    Triple( 30.composeDp(), 460.composeDp(), 0.91f),
    Triple(285.composeDp(), 372.composeDp(), 0.86f),
    Triple(145.composeDp(), 395.composeDp(), 0.88f),
    Triple(300.composeDp(), 260.composeDp(), 0.74f),
    Triple( 24.composeDp(), 330.composeDp(), 0.82f),
    Triple(175.composeDp(), 280.composeDp(), 0.78f),
    Triple( 50.composeDp(), 230.composeDp(), 0.77f),
    Triple(285.composeDp(), 40.composeDp(), 0.72f),
    Triple(258.composeDp(), 140.composeDp(), 0.75f),
    Triple( 40.composeDp(),  97.composeDp(), 0.74f),
)

/* ── talent 4 ──────────────────────────────────────────────── */
val talent4Positions = listOf(
    Triple( 45.composeDp(), 570.composeDp(), 1.00f),
    Triple(260.composeDp(), 595.composeDp(), 1.00f),
    Triple(160.composeDp(), 530.composeDp(), 0.8f),
    Triple( 15.composeDp(), 470.composeDp(), 0.75f),
    Triple(290.composeDp(), 455.composeDp(), 0.90f),
    Triple( 50.composeDp(), 358.composeDp(), 0.85f),
    Triple(175.composeDp(), 410.composeDp(), 0.87f),
    Triple( 35.composeDp(), 260.composeDp(), 0.74f),
    Triple(310.composeDp(), 318.composeDp(), 0.82f),
    Triple(150.composeDp(), 295.composeDp(), 0.79f),
    Triple(275.composeDp(), 210.composeDp(), 0.76f),
    Triple( 38.composeDp(), 160.composeDp(), 0.75f),
    Triple(155.composeDp(), 180.composeDp(), 0.75f),
    Triple(270.composeDp(), 105.composeDp(), 0.74f),
)

/* ── talent 5 ──────────────────────────────────────────────── */
val talent5Positions = listOf(
    Triple( 52.composeDp(), 578.composeDp(), 1.00f),
    Triple(272.composeDp(), 582.composeDp(), 1.00f),
    Triple(145.composeDp(), 518.composeDp(), 0.8f),
    Triple( 20.composeDp(), 466.composeDp(), 0.75f),
    Triple(288.composeDp(), 472.composeDp(), 0.8f),
    Triple( 48.composeDp(), 372.composeDp(), 0.85f),
    Triple(185.composeDp(), 390.composeDp(), 0.87f),
    Triple( 28.composeDp(), 268.composeDp(), 0.74f),
    Triple(292.composeDp(), 315.composeDp(), 0.81f),
    Triple(140.composeDp(), 300.composeDp(), 0.79f),
    Triple(275.composeDp(), 215.composeDp(), 0.77f),
    Triple( 25.composeDp(), 150.composeDp(), 0.75f),
    Triple(148.composeDp(), 85.composeDp(), 0.74f),
    Triple(280.composeDp(), 113.composeDp(), 0.74f),
)

/* ── talent 6 ──────────────────────────────────────────────── */
val talent6Positions = listOf(
    Triple( 35.composeDp(), 590.composeDp(), 0.9f),
    Triple(255.composeDp(), 588.composeDp(), 0.92f),
    Triple(150.composeDp(), 532.composeDp(), 0.8f),
    Triple(  8.composeDp(), 482.composeDp(), 0.8f),
    Triple(300.composeDp(), 470.composeDp(), 0.8f),
    Triple( 32.composeDp(), 380.composeDp(), 0.86f),
    Triple(170.composeDp(), 405.composeDp(), 0.88f),
    Triple( 18.composeDp(), 250.composeDp(), 0.73f),
    Triple(310.composeDp(), 332.composeDp(), 0.82f),
    Triple(155.composeDp(), 280.composeDp(), 0.78f),
    Triple(272.composeDp(), 235.composeDp(), 0.77f),
    Triple( 45.composeDp(), 145.composeDp(), 0.75f),
    Triple(160.composeDp(), 170.composeDp(), 0.75f),
    Triple(268.composeDp(),  98.composeDp(), 0.74f),
)

/* ── talent 7 ──────────────────────────────────────────────── */
val talent7Positions = listOf(
    Triple( 60.composeDp(), 585.composeDp(), 1.00f),
    Triple(250.composeDp(), 578.composeDp(), 1.00f),
    Triple(160.composeDp(), 520.composeDp(), 0.80f),
    Triple( 30.composeDp(), 470.composeDp(), 0.75f),
    Triple(280.composeDp(), 460.composeDp(), 0.90f),
    Triple( 42.composeDp(), 368.composeDp(), 0.85f),
    Triple(190.composeDp(), 400.composeDp(), 0.88f),
    Triple( 32.composeDp(), 260.composeDp(), 0.74f),
    Triple(285.composeDp(), 320.composeDp(), 0.81f),
    Triple(135.composeDp(), 290.composeDp(), 0.78f),
    Triple(252.composeDp(), 220.composeDp(), 0.76f),
    Triple( 48.composeDp(), 150.composeDp(), 0.75f),
    Triple(140.composeDp(), 180.composeDp(), 0.75f),
    Triple(262.composeDp(), 110.composeDp(), 0.74f),
)

/* ── talent 8 ──────────────────────────────────────────────── */
val talent8Positions = listOf(
    Triple( 38.composeDp(), 570.composeDp(), 1.00f),
    Triple(278.composeDp(), 592.composeDp(), 1.00f),
    Triple(160.composeDp(), 546.composeDp(), 0.83f),
    Triple( 16.composeDp(), 460.composeDp(), 0.80f),
    Triple(290.composeDp(), 459.composeDp(), 0.80f),
    Triple( 45.composeDp(), 355.composeDp(), 0.84f),
    Triple(175.composeDp(), 398.composeDp(), 0.87f),
    Triple(148.composeDp(), 290.composeDp(), 0.78f),
    Triple(300.composeDp(), 330.composeDp(), 0.82f),
    Triple( 20.composeDp(), 248.composeDp(), 0.73f),
    Triple(270.composeDp(), 228.composeDp(), 0.77f),
    Triple(150.composeDp(), 170.composeDp(), 0.74f),
    Triple( 40.composeDp(), 120.composeDp(), 0.72f),
    Triple(275.composeDp(), 108.composeDp(), 0.74f),
)

/* ── talent 9 ──────────────────────────────────────────────── */
val talent9Positions = listOf(
    Triple( 55.composeDp(), 588.composeDp(), 1.00f),
    Triple(270.composeDp(), 580.composeDp(), 1.00f),
    Triple(155.composeDp(), 540.composeDp(), 0.80f),
    Triple( 14.composeDp(), 470.composeDp(), 0.80f),
    Triple(285.composeDp(), 475.composeDp(), 0.8f),
    Triple( 48.composeDp(), 370.composeDp(), 0.85f),
    Triple(180.composeDp(), 410.composeDp(), 0.88f),
    Triple( 30.composeDp(), 260.composeDp(), 0.74f),
    Triple(295.composeDp(), 315.composeDp(), 0.81f),
    Triple(145.composeDp(), 295.composeDp(), 0.79f),
    Triple(264.composeDp(), 218.composeDp(), 0.76f),
    Triple( 32.composeDp(), 155.composeDp(), 0.75f),
    Triple(158.composeDp(), 76.composeDp(), 0.64f),
    Triple(268.composeDp(), 105.composeDp(), 0.74f),
)

/* ── talent 10 ─────────────────────────────────────────────── */
val talent10Positions = listOf(
    Triple( 22.composeDp(), 512.composeDp(), 1.00f),
    Triple(232.composeDp(), 540.composeDp(), 1.00f),
    Triple(148.composeDp(), 445.composeDp(), 0.74f),
    Triple( 18.composeDp(), 428.composeDp(), 0.80f),
    Triple(268.composeDp(), 418.composeDp(), 0.76f),
    Triple( 36.composeDp(), 318.composeDp(), 0.85f),
    Triple(187.composeDp(), 352.composeDp(), 0.88f),
    Triple( 23.composeDp(), 208.composeDp(), 0.74f),
    Triple(305.composeDp(), 278.composeDp(), 0.82f),
    Triple(143.composeDp(), 235.composeDp(), 0.78f),
    Triple(270.composeDp(), 180.composeDp(), 0.77f),
    Triple( 69.composeDp(), 635.composeDp(), 0.75f),
    Triple(150.composeDp(), 125.composeDp(), 0.75f),
    Triple(280.composeDp(), 80.composeDp(), 0.74f),
)

/* ── talent 11 ─────────────────────────────────────────────── */
val talent11Positions = listOf(
    Triple( 50.composeDp(), 576.composeDp(), 1.00f),
    Triple(255.composeDp(), 586.composeDp(), 1.00f),
    Triple(152.composeDp(), 529.composeDp(), 0.8f),
    Triple( 12.composeDp(), 472.composeDp(), 0.80f),
    Triple(292.composeDp(), 461.composeDp(), 0.90f),
    Triple( 41.composeDp(), 360.composeDp(), 0.85f),
    Triple(170.composeDp(), 395.composeDp(), 0.87f),
    Triple( 27.composeDp(), 252.composeDp(), 0.73f),
    Triple(298.composeDp(), 320.composeDp(), 0.81f),
    Triple(138.composeDp(), 292.composeDp(), 0.78f),
    Triple(258.composeDp(), 225.composeDp(), 0.77f),
    Triple( 46.composeDp(), 150.composeDp(), 0.75f),
    Triple(155.composeDp(), 172.composeDp(), 0.75f),
    Triple(270.composeDp(), 106.composeDp(), 0.74f),
    Triple(10.composeDp(), 76.composeDp(), 0.7f),
)


val talentPositionsList = listOf(
    talent1Positions,
    talent2Positions,
    talent3Positions,
    talent4Positions,
    talent5Positions,
    talent6Positions,
    talent7Positions,
    talent8Positions,
    talent9Positions,
    talent10Positions,
    talent11Positions
)

@Composable
fun TalentScreen2() {
    val pagerState = remember {
        mutableStateOf(0)
    }
    val scope = rememberCoroutineScope()
    // 先计算剩余10个声望，哪些大于等级1，要显示，得到的是0-9的index
    val talentGroupsExceptFirst =
        AwardManager.reputations.drop(1).mapIndexed { index, reputation ->
            Pair(index, reputation)
        }.filter { ReputationManager.getReputationLevel(it.second) >= 1 }.map { it.first }

    // 换算到实际的声望Index
    val realTalentIndex = if (pagerState.value == 0) 0 else talentGroupsExceptFirst[pagerState.value - 1] + 1
    GameBackground(
        background = kmpDrawableResource("environment_${realTalentIndex + 1}"),
        bgMask = B35,
        title =  (realTalentIndex + 1).toReputationName(),
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Spacer(Modifier.size(padding10))
            CurrentResourcesPointAll(Modifier.padding(horizontal = padding12))
            Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                TalentPage(pagerState.value + 1)
                if (pagerState.value > 0) {
                    EffectButton(
                        modifier = Modifier.align(Alignment.BottomStart)
                            .padding(bottom = padding2), onClick = {
                            pagerState.value -= 1
                        }) {
                        Image(
                            modifier = Modifier.size(imageHugeLite),
                            painter = painterResource(Res.drawable.common_arrow_left),
                            contentDescription = stringResource(
                                Res.string.prev_page
                            )
                        )
                    }
                }
                if (pagerState.value < TalentManager.getUnlockedPageSize() - 1) {
                    EffectButton(
                        modifier = Modifier.align(Alignment.BottomEnd).padding(bottom = padding2),
                        onClick = {
                            scope.launch {
                                pagerState.value += 1
                            }
                        }) {
                        Image(
                            modifier = Modifier.size(imageHugeLite),
                            painter = painterResource(Res.drawable.common_arrow_right),
                            contentDescription = stringResource(
                                Res.string.next_page
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * page 从1开始，最多到11
 * 第一页城堡固定显示，后续只有声望等级大于1的才显示
 */
@Composable
fun TalentPage(page: Int) {
    Box(
        modifier = Modifier.fillMaxHeight().width(padding400),
    ) {
        // 先计算剩余10个声望，哪些大于等级1，要显示，得到的是0-9的index
        val talentGroupsExceptFirst =
            AwardManager.reputations.drop(1).mapIndexed { index, reputation ->
                Pair(index, reputation)
            }.filter { ReputationManager.getReputationLevel(it.second) >= 1 }.map { it.first }

        // 换算到实际的声望Index
        val realTalentIndex = if (page == 1) 0 else talentGroupsExceptFirst[page - 2] + 1
        val reputationLevel = AwardManager.toReputationLevelData()[realTalentIndex]
        val currentExp = AwardManager.reputations[realTalentIndex] - (reputationLevel.expTotal)
        Column(
            Modifier.align(Alignment.TopCenter).padding(top = padding10),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            StrokedText(
                text = (realTalentIndex + 1).toReputationName() + stringResource(Res.string.reputation_level),
                style = MaterialTheme.typography.h2,
                textAlign = TextAlign.Center,
            )
            Spacer(Modifier.size(padding2))
            ReputationLevelView(
                Modifier.size(padding200, padding36),
                reputationLevel,
                currentExp
            )
        }

        // 换算到实际的声望type
        val realTalentGroup = if (page == 1) page + 200 else talentGroupsExceptFirst[page - 2] + 202
        val talentTypes =
            repo.gameCore.getTalent2Pool().filter { it.level == 1 && it.type == realTalentGroup }
        talentTypes.forEachIndexed { index, talentType ->
            Box(
                modifier = Modifier.padding(
                    start = talentPositionsList[realTalentIndex][index].first,
                    top = talentPositionsList[realTalentIndex][index].second - padding40
                ).scale(talentPositionsList[realTalentIndex][index].third)
            ) {
                SingleTalentTypeView(
                    talentMainId = talentType.mainId,
                    talentLevel = TalentManager.talents[talentType.mainId] ?: 0
                )
                if (GuideManager.guideIndex.value == 28 && index == 0) {
                    GuideHand(
                        modifier = Modifier.align(Alignment.BottomCenter).height(
                            padding80
                        ).graphicsLayer {
                            translationY = padding45.toPx()
                        },
                        handType = HandType.UP_HAND
                    )
                }
            }
        }
    }
}

@Composable
fun SingleTalentTypeView(
    talentMainId: Int,
    talentLevel: Int,
    itemSize: ItemSize = ItemSize.LargePlus,
) {
    // 只要总等级变了，就要重新计算uiModel，可能前一个等级升级后，当前这个就解锁了
    val uiModel = remember(TalentManager.talents.values.sum(), talentMainId, talentLevel) {
        buildTalent2UIModel(talentMainId, talentLevel)
    }

    // Box 的透明度根据 locked2 决定
    Box(
        modifier = Modifier.alpha(if (uiModel.locked2) 0f else 1f),
        contentAlignment = Alignment.Center
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            // 第一块按钮
            EffectButton(
                onClick = {
                    if (!uiModel.locked2) {
                        // 展示详情对话框
                        Dialogs.detailTalentDialog.value = talentMainId
                    }
                }
            ) {
                Image(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .width(itemSize.frameSize)
                        .graphicsLayer {
                            translationY = padding8.toPx()
                        }
                        .scale(1.5f),
                    painter = painterResource(Res.drawable.talent_base),
                    contentDescription = null
                )
                Image(
                    modifier = Modifier
                        .padding(start = padding8)
                        .size(itemSize.frameSize)
                        .clip(RoundedCornerShape(itemSize.itemSize / 10)),
                    painter = painterResource(kmpDrawableResource(uiModel.iconRes)),
                    contentDescription = null
                )
                if (uiModel.locked) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .height(padding44),
                        contentScale = ContentScale.FillHeight,
                        painter = painterResource(Res.drawable.common_lock),
                        contentDescription = null
                    )
                }
            }

            // 第二块按钮：显示名称与星级提升等
            EffectButton(
                onClick = { Dialogs.detailTalentDialog.value = talentMainId }
            ) {
                StrokedText(
                    modifier = Modifier.width(itemSize.frameSize * 1.5f),
                    text = uiModel.displayName,
                    style = MaterialTheme.typography.h1,
                    textAlign = TextAlign.Center,
                    maxLines = LanguageManager.getTextLines()
                )
                if (uiModel.nextLevelAvailable) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.CenterStart)
                            .height(padding44)
                            .graphicsLayer {
                                translationX = -padding36.toPx()
                            },
                        contentScale = ContentScale.FillHeight,
                        painter = painterResource(Res.drawable.hero_starup),
                        contentDescription = null
                    )
                }
            }
        }
    }
}