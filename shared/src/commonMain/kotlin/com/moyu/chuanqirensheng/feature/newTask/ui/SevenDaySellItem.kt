package com.moyu.chuanqirensheng.feature.newTask.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.sell.INFINITE_STORAGE
import com.moyu.chuanqirensheng.feature.sell.ui.LeftNumView
import com.moyu.chuanqirensheng.feature.sell.ui.SellButton
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.util.toDayHourMinuteSecond
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Sell
import com.moyu.core.model.toAward
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.sell_frame
import shared.generated.resources.shop_discount
import shared.generated.resources.time_left


@Composable
fun SevenDaySellItem(sell: Sell, leftUpdateTime: ()-> Long) {
    val currentTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(Unit) {
        if (isNetTimeValid()) {
            while (true) {
                currentTime.longValue = getCurrentTime()
                delay(500)
            }
        }
    }
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(padding110)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(Res.drawable.sell_frame),
            contentDescription = null
        )
        Row(
            Modifier.fillMaxSize().padding(horizontal = padding19),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Box(Modifier.width(padding80)) {
                Image(
                    modifier = Modifier.fillMaxWidth().height(padding60).graphicsLayer {
                        translationY = -padding6.toPx()
                    },
                    contentScale = ContentScale.FillHeight,
                    painter = kmpPainterResource(sell.pic),
                    contentDescription = null
                )
                StrokedText(
                    modifier = Modifier.fillMaxWidth()
                        .align(Alignment.BottomCenter)
                        .graphicsLayer {
                            translationY = padding20.toPx()
                        },
                    text = sell.name,
                    style = MaterialTheme.typography.h3,
                    color = Color.White,
                    minLines = 2,
                    textAlign = TextAlign.Center
                )
                if (sell.storage in 1..INFINITE_STORAGE) {
                    LeftNumView(modifier = Modifier.align(Alignment.Center), sell)
                }
                if (sell.desc2 != "0") {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.align(Alignment.TopStart).graphicsLayer {
                            translationY = -padding30.toPx()
                            translationX = -padding26.toPx()
                        }
                    ) {
                        Image(
                            painter = painterResource(Res.drawable.shop_discount),
                            contentDescription = null,
                            modifier = Modifier.size(padding66)
                        )
                        StrokedText(
                            text = sell.desc2,
                            style = MaterialTheme.typography.h4,
                            maxLines = 2
                        )
                    }
                }
            }
            Column(Modifier.weight(1f), horizontalAlignment = Alignment.CenterHorizontally) {
                if (sell.storage < 999) {
                    StrokedText(
                        text = stringResource(
                            Res.string.time_left
                        ) + leftUpdateTime().toDayHourMinuteSecond(),
                        style = MaterialTheme.typography.h3,
                        color = Color.White,
                        modifier = Modifier.graphicsLayer {
                            translationY = padding2.toPx()
                        }
                    )
                }
                Row(Modifier.horizontalScroll(rememberScrollState())) {
                    AwardList(
                        // todo 商店里不要显示电力，获得时候有就行
                        award = sell.toAward().copy(electric = 0),
                        param = defaultParam.copy(itemSize = ItemSize.MediumPlus)
                    )
                }

            }
            SellButton(sell = sell)
        }
    }
}