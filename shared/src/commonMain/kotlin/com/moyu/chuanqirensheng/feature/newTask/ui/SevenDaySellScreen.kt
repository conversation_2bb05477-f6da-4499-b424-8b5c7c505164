package com.moyu.chuanqirensheng.feature.newTask.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.resource.CurrentDiamondPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_INIT_GAME_TIME
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getCurrentDay
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.timeLeft
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.day_1
import shared.generated.resources.day_2
import shared.generated.resources.day_3
import shared.generated.resources.day_4
import shared.generated.resources.day_5
import shared.generated.resources.day_6
import shared.generated.resources.day_7
import shared.generated.resources.new_task_tab3


@Composable
fun SevenDayPackageScreen() {
    val currentTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(Unit) {
        SellManager.init()
        SevenDayManager.createPackages()
    }
    LaunchedEffect(Unit) {
        if (isNetTimeValid()) {
            while (true) {
                currentTime.longValue = getCurrentTime()
                delay(500)
            }
        }
    }
    val listTabItems = remember {
        mutableStateListOf(
            Res.drawable.day_1,
            Res.drawable.day_2,
            Res.drawable.day_3,
            Res.drawable.day_4,
            Res.drawable.day_5,
            Res.drawable.day_6,
            Res.drawable.day_7,
        )
    }
    val pagerState = remember {
        val day = getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME)) - 1
        if (DebugManager.unlockAll) mutableStateOf(6) else mutableStateOf(day.coerceIn(0, 6))
    }
    GameBackground(title = stringResource(Res.string.new_task_tab3)) {
        Column(
            modifier = Modifier
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val day = pagerState.value + 1
            val shopChests = SevenDayManager.packages.filter { sell ->
                repo.gameCore.getDayRewardPool().filter { !it.isHoliday() }
                    .firstOrNull { it.value == sell.id }?.let {
                        it.unlock == day
                    } == true
            }.sortedBy { it.priority }.sortedBy { if (it.storage <= 0)  9999 else 0 }
            Spacer(modifier = Modifier.size(padding12))
            Row(
                Modifier
                    .align(Alignment.End)
                    .padding(end = padding6)
            ) {
                CurrentDiamondPoint(showPlus = true)
                CurrentKeyPoint(showPlus = true)
            }
            Spacer(modifier = Modifier.size(padding4))
            Column(
                modifier = Modifier.fillMaxWidth().weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(padding4))
                // 临时
                LazyColumn(
                    modifier = Modifier.fillMaxWidth().weight(1f).padding(horizontal = padding6), content = {
                        items(shopChests.size) { index ->
                            val sell = shopChests[index]
                            repo.gameCore.getDayRewardPool().filter { !it.isHoliday() }
                                .firstOrNull { it.value == sell.id }?.let { rewardItem ->
                                    val leftUpdateTime = timeLeft(
                                        currentTime.value, getLongFlowByKey(
                                            KEY_INIT_GAME_TIME
                                        ), rewardItem.getKeepDays()
                                    )
                                    if (leftUpdateTime > 0) {
                                        SevenDaySellItem(sell = sell) {
                                            leftUpdateTime
                                        }
                                    }
                                }
                            Spacer(modifier = Modifier.size(padding5))
                        }
                    })
            }
            NavigationTab(
                modifier = Modifier.padding(bottom = padding6),
                pageState = pagerState,
                titles = listTabItems,
                locks = if (DebugManager.unlockAll) List(7) { false } else List(getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME)).coerceIn(1, 7)) {
                    false
                } + List(7 - getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME))) {
                    true
                },
            )
        }
    }
}