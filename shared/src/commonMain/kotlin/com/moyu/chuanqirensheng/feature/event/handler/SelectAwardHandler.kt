package com.moyu.chuanqirensheng.feature.event.handler

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.Dialogs.chooseAllyDialog
import com.moyu.chuanqirensheng.application.Dialogs.chooseEquipDialog
import com.moyu.chuanqirensheng.application.Dialogs.chooseSkillDialog
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE1
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.setting.SettingManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.hugeButtonWidth
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.toAward
import com.moyu.core.model.toAwards
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.next_day

const val SELECT_AWARD_EVENT = 2

class SelectAwardHandler(
    override val skipWin: Boolean = true,
    override val hasLayout: MutableState<Boolean> = mutableStateOf(false),
    override val playId: Int = -1
) : PlayHandler() {

    private val awards = mutableStateListOf<Award>()

    @Composable
    override fun Layout(event: Event) {

    }

    override suspend fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
        awards.clear()
        var gainProps = event.playPara2.map { it.toInt() }.map {
            repo.gameCore.getPoolById(it).toAward()
        }
        awards.addAll(gainProps)
        // 全屏弹窗的情况下，要等1.5秒，让玩家看清楚选择的事件再弹窗
        if (awards.first().skills.isNotEmpty()) {
            // 全屏技能三选一
            delay(1500)
            chooseSkillDialog.value =
                repo.gameCore.getPoolById(event.playPara2.first().toInt()).toAwards(forceTotalNum = 3)
            awards.clear()
            awards.addAll(chooseSkillDialog.value)
        } else if (awards.first().outEquips.isNotEmpty()) {
            // 全屏宝物二选一
            delay(1500)
            chooseEquipDialog.value =
                repo.gameCore.getPoolById(event.playPara2.first().toInt()).toAwards(forceTotalNum = 2)
            awards.clear()
            awards.addAll(chooseEquipDialog.value)
        } else if (awards.first().outAllies.isNotEmpty()) {
            // 全屏兵种二选一
            delay(1500)
            chooseAllyDialog.value =
                repo.gameCore.getPoolById(event.playPara2.first().toInt()).toAwards(forceTotalNum = 2)
            awards.clear()
            awards.addAll(chooseAllyDialog.value)
        } else {
            // 去重
            while (awards[0] == awards[1]) {
                awards.clear()
                var gainProps = event.playPara2.map { it.toInt() }.map {
                    repo.gameCore.getPoolById(it).toAward()
                }
                awards.addAll(gainProps)
            }
        }
    }

    @Composable
    override fun HandlerButtons(event: Event) {
        if (awards.first().skills.isNotEmpty()) {
            // 全屏技能三选一
            GameButton(
                buttonSize = ButtonSize.Huge,
                mute = true,
                text = stringResource(Res.string.next_day)
            ) {

            }
        } else if (awards.first().outEquips.isNotEmpty()) {
            // 全屏宝物二选一
            GameButton(
                buttonSize = ButtonSize.Huge,
                mute = true,
                text = stringResource(Res.string.next_day)
            ) {

            }
        } else if (awards.first().outAllies.isNotEmpty()) {
            // 全屏兵种二选一
            GameButton(
                buttonSize = ButtonSize.Huge,
                mute = true,
                text = stringResource(Res.string.next_day)
            ) {

            }
        } else {
            LaunchedEffect(Unit) {
                if (SettingManager.autoSelect.value) {
                    delay(1000)
                    if (GuideManager.guideIndex.value == 2) {
                        GuideManager.guideIndex.value = 3
                        setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                    }
                    if (!eventFinished.value) {
                        eventFinished.value = true
                        val index = if (RANDOM.nextBoolean()) 1 else 0
                        EventManager.getOrCreateHandler(event).setEventAward(awards[index], index)
                        EventManager.doEventResult(event, true)
                    }
                }
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                Box(contentAlignment = Alignment.Center) {
                    GameButton(
                        text = "",
                        buttonSize = ButtonSize.Huge,
                        onClick = {
                            if (GuideManager.guideIndex.value == 2) {
                                GuideManager.guideIndex.value = 3
                                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                            }
                            if (!eventFinished.value) {
                                eventFinished.value = true
                                EventManager.getOrCreateHandler(event).setEventAward(awards[0], 0)
                                EventManager.doEventResult(event, true)
                            }
                        }
                    )
                    Column(
                        modifier = Modifier.width(hugeButtonWidth),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        StrokedText(text = event.option1, style = MaterialTheme.typography.h1)
                        Spacer(Modifier.size(padding2))
                        AwardList(
                            Modifier,
                            awards[0],
                            param = defaultParam.copy(
                                showColumn = false,
                                showPlus = true,
                                itemSize = ItemSize.Small,
                                showName = true,
                                showNum = true,
                                noFrameForItem = true,
                                maxLine = 1,
                                minLine = 1,
                                numInFrame = false,
                                callback = {
                                    if (GuideManager.guideIndex.value == 2) {
                                        GuideManager.guideIndex.value = 3
                                        setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                                    }
                                    if (!eventFinished.value) {
                                        eventFinished.value = true
                                        EventManager.getOrCreateHandler(event)
                                            .setEventAward(awards[0], 0)
                                        EventManager.doEventResult(event, true)
                                    }
                                }
                            )
                        )
                    }
                    if (GuideManager.guideIndex.value == 2) {
                        GuideHand(
                            modifier = Modifier.align(Alignment.BottomCenter).height(padding80)
                                .graphicsLayer {
                                    translationY = -padding90.toPx()
                                },
                            handType = HandType.DOWN_HAND
                        )
                    }
                }
                Box(contentAlignment = Alignment.Center) {
                    GameButton(
                        text = "",
                        buttonSize = ButtonSize.Huge,
                        onClick = {
                            if (!eventFinished.value) {
                                eventFinished.value = true
                                if (GuideManager.guideIndex.value == 2) {
                                    // 引导过程强制选第一个
                                    EventManager.getOrCreateHandler(event)
                                        .setEventAward(awards[0], 0)
                                } else {
                                    EventManager.getOrCreateHandler(event)
                                        .setEventAward(awards[1], 1)
                                }
                                EventManager.doEventResult(event, true)
                            }
                            if (GuideManager.guideIndex.value == 2) {
                                GuideManager.guideIndex.value = 3
                                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                            }
                        }
                    )
                    Column(
                        modifier = Modifier.width(hugeButtonWidth),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        StrokedText(text = event.option2, style = MaterialTheme.typography.h1)
                        Spacer(Modifier.size(padding2))
                        AwardList(
                            Modifier,
                            awards[1],
                            param = defaultParam.copy(
                                showColumn = false,
                                showPlus = true,
                                itemSize = ItemSize.Small,
                                showName = true,
                                showNum = true,
                                maxLine = 1,
                                minLine = 1,
                                noFrameForItem = true,
                                numInFrame = false,
                                callback = {
                                    if (!eventFinished.value) {
                                        eventFinished.value = true
                                        if (GuideManager.guideIndex.value == 2) {
                                            // 引导过程强制选第一个
                                            EventManager.getOrCreateHandler(event)
                                                .setEventAward(awards[0], 0)
                                        } else {
                                            EventManager.getOrCreateHandler(event)
                                                .setEventAward(awards[1], 1)
                                        }
                                        EventManager.doEventResult(event, true)
                                    }
                                    if (GuideManager.guideIndex.value == 2) {
                                        GuideManager.guideIndex.value = 3
                                        setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                                    }
                                }
                            )
                        )
                    }
                }
            }
        }
    }

    @Composable
    override fun PostAwardButtons(event: Event) {
        // do nothing
    }

    override fun getAwards(): List<Award> {
        return awards
    }
}