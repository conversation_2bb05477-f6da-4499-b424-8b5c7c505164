package com.moyu.chuanqirensheng.feature.event.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.util.immersionBarHeightInDp
import com.moyu.chuanqirensheng.widget.common.SearchView
import com.moyu.chuanqirensheng.widget.filter.CommonFilterView
import com.moyu.chuanqirensheng.widget.filter.FilterLayout
import com.moyu.chuanqirensheng.widget.filter.ItemFilter
import com.moyu.chuanqirensheng.widget.filter.eventFilterList
import com.moyu.core.model.Event

@Composable
fun EventSelectPage(modifier: Modifier, onSelectEvent: (Event) -> Unit) {
    val events = EventManager.getNextEvents()
    val showFilter = remember {
        mutableStateOf(false)
    }
    val filter = remember {
        mutableStateListOf<ItemFilter<Event>>()
    }

    Box(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding16)
        ) {
            val search = remember {
                mutableStateOf("")
            }
            val list = events.filter {
                if (search.value.isNotEmpty()) {
                    it.name.contains(search.value) || it.id.toString().contains(search.value)
                } else true
            }.filter { ally ->
                filter.all { it.filter.invoke(ally) }
            }
            Row(
                Modifier.fillMaxWidth().padding(top = immersionBarHeightInDp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Spacer(modifier = Modifier.size(padding4))
                SearchView(search)
                CommonFilterView(
                    Modifier.padding(end = padding10), showFilter
                )
            }
            LazyRow(modifier = Modifier.fillMaxSize(),
                horizontalArrangement = Arrangement.spacedBy(padding60),
                content = {
                    items(list.size) {
                        Column {
                            SingleEventCard(event = list[it], onSelectEvent)
                            Spacer(modifier = Modifier.size(padding19))
                        }
                    }
                })
        }
        FilterLayout(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = padding45, end = padding10),
            show = showFilter,
            filter = filter,
            filterList = eventFilterList
        )
    }
}