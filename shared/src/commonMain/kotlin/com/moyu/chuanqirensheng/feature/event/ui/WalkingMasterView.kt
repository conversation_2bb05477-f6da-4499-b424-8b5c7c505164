package com.moyu.chuanqirensheng.feature.event.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.event.handler.PlayHandler
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.DarkGreen
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding420
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.ui.theme.padding82
import com.moyu.chuanqirensheng.ui.theme.padding84
import com.moyu.chuanqirensheng.widget.common.HpBar
import com.moyu.chuanqirensheng.widget.effect.ForeverGif
import com.moyu.chuanqirensheng.widget.effect.ForeverShadowGif
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.healGif
import com.moyu.core.GameCore
import com.moyu.core.logic.role.ALLY_ROW2_SECOND
import com.moyu.core.music.SoundEffect


@Composable
fun WalkingMasterView(showEventLayout: Boolean, handler: MutableState<PlayHandler?>) {
    AnimatedVisibility(
        visible = !showEventLayout,
        enter = fadeIn() + slideInHorizontally(initialOffsetX = { fullWidth -> -fullWidth } // 从左边滑入
        ),
        exit = fadeOut() + slideOutHorizontally(targetOffsetX = { fullWidth -> -fullWidth } // 向左边滑出
        )) {
        Box(
            modifier = Modifier.fillMaxWidth().height(padding420),
            contentAlignment = Alignment.CenterStart
        ) {
            if (StageManager.currentStage.value.isShip()) {
                ForeverGif(
                    Modifier.align(Alignment.BottomStart)
                        .padding(start = padding40, bottom = padding82).size(padding84)
                        .clickable {
                            GameCore.instance.onBattleEffect(SoundEffect.Click)
                            Dialogs.roleDetailDialog.value =
                                repo.battleRoles[ALLY_ROW2_SECOND]
                        },
                    gifCount = 54,
                    gifDrawable = "master_ship_",
                    needGap = false,
                    pace = 38
                )
            } else {
                if (handler.value?.eventFinished?.value == false) {
                    // 正在处理事件，不展示跑图
                    ForeverShadowGif(
                        Modifier.align(Alignment.BottomStart)
                            .padding(start = padding40, bottom = padding82)
                            .size(padding84)
                            .clickable {
                                GameCore.instance.onBattleEffect(SoundEffect.Click)
                                Dialogs.roleDetailDialog.value =
                                    repo.battleRoles[ALLY_ROW2_SECOND]
                            },
                        gifCount = 28,
                        gifDrawable = "master_idle_",
                        needGap = false,
                        repeatMode = RepeatMode.Reverse,
                        pace = 38,
                        scale = 1.33f
                    )
                } else {
                    ForeverShadowGif(
                        Modifier.align(Alignment.BottomStart)
                            .padding(start = padding40, bottom = padding82)
                            .size(padding84)
                            .clickable {
                                GameCore.instance.onBattleEffect(SoundEffect.Click)
                                Dialogs.roleDetailDialog.value =
                                    repo.battleRoles[ALLY_ROW2_SECOND]
                            },
                        gifCount = 36,
                        gifDrawable = "master_move_",
                        needGap = false,
                        pace = 26
                    )
                }
            }
            Box(
                modifier = Modifier.align(Alignment.BottomStart)
                    .padding(start = padding50, bottom = padding180).size(
                        padding72, padding14
                    ), contentAlignment = Alignment.Center
            ) {
                repo.battleRoles[ALLY_ROW2_SECOND]?.let {
                    HpBar(
                        currentHp = it.getCurrentProperty().hp,
                        maxHp = it.getOriginProperty().hp,
                        totalShield = 0,
                    )
                    StrokedText(
                        text = it.getCurrentProperty().hp.toString(),
                        style = MaterialTheme.typography.body1,
                        color = if (it.getOriginProperty().hp > BattleManager.initialProperty.value.hp) DarkGreen else Color.White
                    )
                }
            }
            GifView(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = padding40, bottom = padding82)
                    .size(padding84).scale(2f), BattleManager.healingStates.value, healGif.count, healGif.gif, pace = 2
            ) {
                BattleManager.healingStates.value = false
            }
        }
    }
}
