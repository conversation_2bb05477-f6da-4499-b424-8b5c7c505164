package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass1Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass3Manager
import com.moyu.chuanqirensheng.feature.router.BATTLE_PASS_ALL_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_BATTLE_PASS1
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_BATTLE_PASS2
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_BATTLE_PASS3
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.MainIcon
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.battle_pass1
import shared.generated.resources.battle_pass_icon

@Composable
fun BattlePassIcon(itemSize: ItemSize) {
    MainIcon(
        itemSize = itemSize,
        unlocks = listOf(
            repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS1),
            repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS2),
            repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS3)
        ),
        click = {
            goto(BATTLE_PASS_ALL_SCREEN)
        },
        red = {
            (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS1)) && BattlePass1Manager.hasRed())
                || (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS2)) && BattlePass2Manager.hasRed())
                || (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS3)) && BattlePass3Manager.hasRed())
        },
        title = stringResource(Res.string.battle_pass1),
        icon = Res.drawable.battle_pass_icon,
    )
}
