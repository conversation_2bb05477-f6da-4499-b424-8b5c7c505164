package com.moyu.chuanqirensheng.feature.event.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.ally.ui.AllyInfoLayout
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.setting.SettingManager
import com.moyu.chuanqirensheng.ui.theme.dialogHeight
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding240
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.toQualityColor
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.dialog.EmptyDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.Texture
import com.moyu.chuanqirensheng.widget.effect.magicFrame
import com.moyu.chuanqirensheng.widget.effect.sweepHighlight
import com.moyu.core.model.Award
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.choose_ally_title
import shared.generated.resources.sell_label

@Composable
fun ChooseAllyDialog(show: MutableState<List<Award>>) {
    if (show.value.isNotEmpty()) {
        EmptyDialog(showTips = false) {
            Column(
                Modifier.fillMaxWidth().height(dialogHeight),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier.size(padding260, padding48),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(Res.drawable.sell_label),
                        contentDescription = null
                    )
                    StrokedText(
                        text = stringResource(Res.string.choose_ally_title),
                        style = MaterialTheme.typography.h1,
                        color = Color.White,
                    )
                }
                Spacer(Modifier.size(padding16))
                LaunchedEffect(Unit) {
                    if (SettingManager.autoSelect.value) {
                        delay(1000)
                        EventManager.selectedEvent.value?.let {
                            val handler = EventManager.getOrCreateHandler(it)
                            if (!handler.eventFinished.value) {
                                handler.eventFinished.value = true
                                EventManager.getOrCreateHandler(it)
                                    .setEventAward(Award(outAllies = listOf(show.value.map { it.outAllies.first() }
                                        .first())), 0)
                                EventManager.doEventResult(it, true)
                                show.value = emptyList()
                            }
                        }
                    }
                }
                show.value.map { it.outAllies.first() }.forEachIndexed { index, skill ->
                    val click = {
                        EventManager.selectedEvent.value?.let {
                            val handler = EventManager.getOrCreateHandler(it)
                            if (!handler.eventFinished.value) {
                                handler.eventFinished.value = true
                                show.value = emptyList()
                                EventManager.getOrCreateHandler(it)
                                    .setEventAward(Award(outAllies = listOf(skill)), index)
                                EventManager.doEventResult(it, true)
                            }
                        }
                    }
                    val backgroundColor = skill.quality.toQualityColor()
                    val borderColor = backgroundColor.copy(
                        red = backgroundColor.red * 0.8f,
                        green = backgroundColor.green * 0.8f,
                        blue = backgroundColor.blue * 0.8f
                    )

                    EffectButton(
                        modifier = Modifier
                            .fillMaxWidth().height(padding240)
                            .magicFrame(                      // 只换这一行
                                base = backgroundColor,
                                borderColor = borderColor,
                                texture = Texture.HORIZONTAL_NOISE   // 或 Texture.HORIZONTAL_NOISE
                            ).sweepHighlight(),
                        onClick = {
                            click()
                        }
                    ) {
                        AllyInfoLayout(Modifier, skill)
                    }
                    Spacer(Modifier.size(padding10))
                }
            }
        }
    }
}
