package com.moyu.chuanqirensheng.feature.continuegame

import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.PropertyAward
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS
import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import kotlinx.serialization.Serializable


@Serializable
data class ContinueData(
    val you: Role = Role(),
    val usedEvents: List<Event> = emptyList(),
    val winEvents: List<Event> = emptyList(),
    val loseEvents: List<Event> = emptyList(),
    val award: Award = Award(),
    val selectionEvents: List<Event> = emptyList(),
    val records: DetailProgressData = DetailProgressData(),
    val skillGameData: List<Skill> = emptyList(),
    val allyGameData: List<Ally> = emptyList(),
    val adventureProps: AdventureProps = EMPTY_ADV_PROPS,
    val propertyAwards: List<PropertyAward> = emptyList(),
    val battleProp: Property = EMPTY_PROPERTY,
    val battleSkillPropMap: Map<Int, Property> = emptyMap(),
    val troopSkills: List<Skill> = emptyList(),
    val initialProperty: Property = Property(),
    val stageId: Int = 1,
    val battleRaceProps: List<Property> = mutableListOf(
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
    ),
)