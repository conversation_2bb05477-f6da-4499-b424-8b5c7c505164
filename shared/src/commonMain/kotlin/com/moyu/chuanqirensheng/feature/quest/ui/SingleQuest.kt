package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.serverrank.ServerRankManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.Quest
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.already_got
import shared.generated.resources.award_got_toast
import shared.generated.resources.common_frame_long
import shared.generated.resources.double_gain
import shared.generated.resources.gain_award
import shared.generated.resources.quest_not_done_toast


@Composable
fun SingleQuest(quest: Quest, refresh: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(padding120)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(Res.drawable.common_frame_long),
            contentDescription = null
        )
        Column(modifier = Modifier.padding(horizontal = padding19, vertical = padding10)) {
            val postFix = QuestManager.getTaskProgressFlow(task = quest).let {
                if (it.isNotEmpty()) {
                    "（$it）"
                } else {
                    ""
                }
            }
            Spacer(Modifier.size(padding6))
            StrokedText(
                text = quest.desc + postFix,
                style = if (quest.desc.length >= 12) MaterialTheme.typography.h5 else MaterialTheme.typography.h4
            )
            Row(
                modifier = Modifier.fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Spacer(Modifier.size(padding10))
                val award = quest.toAward()
                AwardList(
                    award = award,
                    param = defaultParam.copy(
                        numInFrame = true,
                        showName = false,
                        itemSize = ItemSize.Medium
                    ),
                    paddingVerticalInDp = padding0,
                    paddingHorizontalInDp = padding0
                )
                Spacer(modifier = Modifier.weight(1f))
                val questCompleted =
                    QuestManager.getTaskDoneFlow(quest)
                GameButton(text =
                if (quest.opened) stringResource(Res.string.already_got)
                else if (VipManager.isDoubleQuestAward() && quest.isDailyTask()) stringResource(
                    Res.string.double_gain
                ) else stringResource(Res.string.gain_award),
                    enabled = questCompleted && !quest.opened,
                    buttonStyle = ButtonStyle.Green,
                    buttonSize = ButtonSize.Medium,
                    onClick = {
                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                            if (!questCompleted) {
                                AppWrapper.getStringKmp(Res.string.quest_not_done_toast).toast()
                            } else if (quest.opened) {
                                AppWrapper.getStringKmp(Res.string.award_got_toast).toast()
                            } else {
                                QuestManager.questReward(quest, award)
                                refresh()
                            }
                        }
                    })
            }
        }
    }
}
