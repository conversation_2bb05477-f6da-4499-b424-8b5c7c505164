package com.moyu.chuanqirensheng.feature.illustration.ui


import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.feature.illustration.TcgManager
import com.moyu.chuanqirensheng.feature.router.TCG_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TCG
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.MainIcon
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.icon_tcg
import shared.generated.resources.tcg_title

@Composable
fun TcgIcon(itemSize: ItemSize) {
    MainIcon(
        itemSize = itemSize,
        unlocks = listOf(repo.gameCore.getUnlockById(UNLOCK_TCG)),
        click = {
            goto(TCG_SCREEN)
        },
        red = { TcgManager.hasRed() },
        title = stringResource(Res.string.tcg_title),
        icon = Res.drawable.icon_tcg
    )
}
