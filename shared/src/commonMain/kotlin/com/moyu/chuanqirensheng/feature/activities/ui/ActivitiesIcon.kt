package com.moyu.chuanqirensheng.feature.activities.ui

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.feature.drawactivity.DrawActivityManager
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.mission.MissionManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.router.ACTIVITIES_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_DRAW_ACTIVITY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_LOTTERY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_NEW_QUEST
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_SEVEN_DAY
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.MainIcon
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.activities
import shared.generated.resources.seven_day_icon

@Composable
fun ActivitiesIcon(itemSize: ItemSize) {
    MainIcon(
        itemSize = itemSize,
        unlocks = listOf(
            repo.gameCore.getUnlockById(UNLOCK_SEVEN_DAY),
            repo.gameCore.getUnlockById(UNLOCK_LOTTERY),
            repo.gameCore.getUnlockById(UNLOCK_NEW_QUEST),
            repo.gameCore.getUnlockById(UNLOCK_DRAW_ACTIVITY)
        ),
        click = {
            goto(ACTIVITIES_SCREEN)
        },
        red = {
            activityItems.any { it.show() && it.unlock() && !getBooleanFlowByKey(it.frame) } ||
            (SevenDayManager.hasRed() && SevenDayManager.unlocked() && SevenDayManager.show())
                || (LotteryManager.hasRed() && LotteryManager.unlocked())
                || (MissionManager.hasRed() && MissionManager.unlocked())
                || (DrawActivityManager.hasRed() && DrawActivityManager.unlocked())
        },
        title = stringResource(Res.string.activities),
        icon = Res.drawable.seven_day_icon,
    )
}
