package com.moyu.chuanqirensheng.feature.talent

import androidx.compose.runtime.mutableStateMapOf
import com.moyu.chuanqirensheng.application.powerToast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.quest.onTaskTalentUp
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.story.toReputationName
import com.moyu.chuanqirensheng.feature.talent.ui.MAX_TALENT3_LEVEL
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TALENT1
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TALENT2
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TALENT3
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkTalent
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_TALENT_LEVEL
import com.moyu.chuanqirensheng.sub.datastore.KEY_TALENT_POWER
import com.moyu.chuanqirensheng.sub.datastore.getMapObject
import com.moyu.chuanqirensheng.sub.datastore.setMapObject
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.Talent
import com.moyu.core.model.mainIdToTalentType
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.toAward
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.builtins.serializer
import org.jetbrains.compose.resources.getString
import shared.generated.resources.Res
import shared.generated.resources.talent_locked_tips1
import shared.generated.resources.talent_locked_tips2
import shared.generated.resources.talent_locked_tips3
import kotlin.math.max

/**
 * 封装对当前天赋展示所需的所有信息
 */
data class Talent2UIModel(
    val showTalent: Talent,          // 当前显示/使用的 Talent
    val iconRes: String,            // Icon 用于 painterResource(kmpDrawableResource(...))
    val displayName: String,
    val locked: Boolean,
    val locked2: Boolean,
    val nextLevelAvailable: Boolean, // 下一级是否可升
    val costAward: Award,            // 升级所需消耗
)

/* 表示某一个在天赋界面上的「格位」
* 比如 row=1, column=1 下有若干同 mainId 不同 level 的天赋
*/
data class TalentType3SlotModel(
    val showTalent: Talent,
    val skill: Skill,
    val talentLevel: Int,          // 当前已激活的等级
    val locked: Boolean,           // 当前天赋是否锁定
    val nextTalent: Talent?,       // 天赋树中下一个位置的天赋（竖线连下去）
    val nextLocked: Boolean,       // 下一个位置的天赋是否锁定
    val nextLevelTalent: Talent?,  // 同一个 mainId、下一级别的 Talent
    val award: Award,              // 升下一级需要消耗的资源
    val maxLevel: Int              // talentType3 的总级数上限
)

object TalentManager {
    val talents = mutableStateMapOf<Int, Int>()
    val totalTalent = Guarded(KEY_TALENT_POWER)

    fun init() {
        talents.clear()
        talents.putAll(getMapObject(KEY_TALENT_LEVEL, Int.serializer(), Int.serializer()))
    }

    fun hasRed(): Boolean {
        return (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_TALENT1)) && hasRed1())
                || (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_TALENT2)) && hasRed2())
                || (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_TALENT3)) && hasRed3())

    }

    fun hasRed2(): Boolean {
        return repo.gameCore.getStoryPool().filter {
            // 先取出来可以显示的阵营天赋，第一个城堡默认解锁，其他要声望1级解锁
            it.id == 1 || ReputationManager.getReputationLevel(AwardManager.reputations[it.id - 1]) >= 1
        }.any { story ->
            val realTalentGroup = story.id + 200
            val talentTypes =
                repo.gameCore.getTalent2Pool().filter { it.level == 1 && it.type == realTalentGroup }
            talentTypes.any { talentType ->
                val uiModel = buildTalent2UIModel(
                    talentMainId = talentType.mainId,
                    talentLevel = talents[talentType.mainId] ?: 0
                )
                uiModel.nextLevelAvailable
            }
        }
    }

    fun hasRed3(): Boolean {
        val allTalents = repo.gameCore.getTalent3Pool().filter { it.type == 3 }
        return allTalents.filter { (talents[it.mainId] ?: 0) < it.level }.any { talent ->
            val award = Award(talentPoint = talent.cost)
            val locked = getLockInfoByTalent(talent).first
            AwardManager.isAffordable(award) && !locked
        }
    }

    fun hasRed1(): Boolean {
        val talents = repo.gameCore.getTalent1Pool()
            .filter { it.type == 1 }
            .sortedBy { it.conditionNum }
        talents.firstOrNull { (TalentManager.talents[it.mainId] ?: 0) < it.level }?.let { talent ->
            val award = Award(diamond = talent.cost)
            return AwardManager.isAffordable(award)
        }
        return false
    }

    fun upgradeTalent(talent: Talent): Talent {
        checkTalent(talent = talent)
        val talentLevel = talents[talent.mainId] ?: 0
        if (talent.isType2()) {
            GameCore.instance.onBattleEffect(SoundEffect.UpgradeTalent2)
        } else if (talent.isType3()) {
            GameCore.instance.onBattleEffect(SoundEffect.UpgradeTalent3)
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.UpgradeTalent1)
        }
        talents[talent.mainId] = talentLevel + 1
        save()
        val upgradedTalent = repo.gameCore.getTalentPool(talent.mainId.mainIdToTalentType())
            .first { it.mainId == talent.mainId && it.level == talentLevel + 1 }
        totalTalent.value += upgradedTalent.power
        onTaskTalentUp(talent.type, talentLevel + 1)
        upgradedTalent.power.powerToast()
        return upgradedTalent
    }

    fun save() {
        setMapObject(KEY_TALENT_LEVEL, talents, Int.serializer(), Int.serializer())
    }

    fun getLockInfoByTalent(scroll: Talent): Pair<Boolean, String> {
        if (DebugManager.unlockTalents) {
            return Pair(false, "")
        }
        return when {
            scroll.conditionType == 0 -> Pair(false, "")
            scroll.conditionType in 1..11 -> {
                val reputationType = scroll.conditionType
                val reputationLevel = scroll.conditionNum
                val reputation = AwardManager.toReputationLevelData()[reputationType - 1]
                Pair(
                    reputation.level < reputationLevel,
                    reputationType.toReputationName() + runBlocking {
                        getString(Res.string.talent_locked_tips3, reputationLevel)
                    }
                )
            }

            scroll.conditionType in 30000..39999 -> {
                val level = scroll.conditionType - 30000
                val talentMainId = scroll.conditionNum
                val name = repo.gameCore.getTalentPool(talentMainId.mainIdToTalentType()).first { it.mainId == talentMainId }.name
                Pair(
                    (talents[talentMainId] ?: 0) < level,
                    "Lv${level}" + name + AppWrapper.getStringKmp(Res.string.talent_locked_tips2)
                )
            }

            scroll.conditionType == 100 -> {
                val target = scroll.conditionNum
                val targetTalent = repo.gameCore.getTalentPool(scroll.type).first {
                    it.type == scroll.type && it.position.first() == scroll.position.first() - 1 && it.position[1] == scroll.position[1]
                }
                Pair(
                    (talents[targetTalent.mainId] ?: 0) < target,
                    runBlocking {
                        getString(Res.string.talent_locked_tips1, target)
                    }
                )
            }

            scroll.conditionType == 200 -> {
                // 前一页技能总等级达到指定要求
                val prevType = scroll.type - 1
                val sum = getTotalLevelByType(prevType)
                val target = scroll.conditionNum
                Pair(
                    sum < target,
                    runBlocking {
                        getString(Res.string.talent_locked_tips2, target)
                    }
                )
            }

            else -> {
                val target = scroll.conditionNum
                Pair(
                    false,
                    runBlocking {
                        getString(Res.string.talent_locked_tips3, target)
                    }
                )
            }
        }
    }

    fun getLock2InfoByTalent(scroll: Talent): Pair<Boolean, String> {
        if (DebugManager.unlockTalents) {
            return Pair(false, "")
        }
        return when {
            scroll.conditionHideType == 0 -> Pair(false, "")
            scroll.conditionHideType in 1..11 -> {
                val reputationType = scroll.conditionHideType
                val reputationLevel = scroll.conditionHideNum
                val reputation = AwardManager.toReputationLevelData()[reputationType - 1]
                Pair(
                    reputation.level < reputationLevel,
                    reputationType.toReputationName() + runBlocking {
                        getString(Res.string.talent_locked_tips3, reputationLevel)
                    }
                )
            }

            scroll.conditionHideType in 30000..39999 -> {
                val level = scroll.conditionHideType - 30000
                val talentMainId = scroll.conditionHideNum
                val name = repo.gameCore.getTalentPool(talentMainId.mainIdToTalentType()).first { it.mainId == talentMainId }.name
                Pair(
                    (talents[talentMainId] ?: 0) < level,
                    "Lv${level}" + name + AppWrapper.getStringKmp(Res.string.talent_locked_tips2)
                )
            }

            scroll.conditionHideType == 100 -> {
                val target = scroll.conditionHideNum
                val targetTalent = repo.gameCore.getTalentPool(scroll.type).first {
                    it.type == scroll.type && it.position.first() == scroll.position.first() - 1 && it.position[1] == scroll.position[1]
                }
                Pair(
                    (talents[targetTalent.mainId] ?: 0) < target,
                    runBlocking {
                        getString(Res.string.talent_locked_tips1, target)
                    }
                )
            }

            scroll.conditionHideType == 200 -> {
                // 前一页技能总等级达到指定要求
                val prevType = scroll.type - 1
                val sum = getTotalLevelByType(prevType)
                val target = scroll.conditionHideNum
                Pair(
                    sum < target,
                    runBlocking {
                        getString(Res.string.talent_locked_tips2, target)
                    }
                )
            }

            else -> {
                val target = scroll.conditionHideNum
                Pair(
                    false,
                    runBlocking {
                        getString(Res.string.talent_locked_tips3, target)
                    }
                )
            }
        }
    }

    fun getTotalLevelByType(type: Int): Int {
        return repo.gameCore.getTalentPool(type).filter { it.level == 1 }
            .sumOf { talents[it.mainId] ?: 0 }
    }

    fun getUnlockedPageSize(): Int {
        // 第一个城堡是默认显示的，后续只有声望等级大于1才显示
        return AwardManager.toReputationLevels().drop(1).filter { it >= 1 }.size + 1
    }

    fun getPvpTalents(): Map<Int, Int> {
        return talents.filter { it.value > 0 }
    }

    fun canLearnTalentType1(talent: Talent): Boolean {
        if (DebugManager.unlockTalents) {
            return true
        }
        val unlockOrder = talent.conditionNum - 1
        if (unlockOrder == 0) {
            return true
        }
        val targetTalent = repo.gameCore.getTalent1Pool()
            .first { it.conditionType == 900 && it.conditionNum == unlockOrder }
        return (talents[targetTalent.mainId] ?: 0) >= targetTalent.level
    }

    fun getTotalPower(): Int {
        return totalTalent.value
    }

    /**
     * 统一的数据获取逻辑
     */
    fun buildTalent2UIModel(
        talentMainId: Int,
        talentLevel: Int
    ): Talent2UIModel {
        // 使用本地变量可避免多次 repo.gameCore.getTalentPool() 调用
        val talentPool = repo.gameCore.getTalent2Pool()

        val showTalentLevel = max(1, talentLevel)
        val showTalent =
            talentPool.first { it.mainId == talentMainId && it.level == showTalentLevel }

        // 查技能、滚动条(如果有)以及下一阶
        val skill = repo.gameCore.getSkillById(showTalent.talentSkill)
        val scroll = talentPool.first { it.talentSkill == skill.id }
        val nextScroll = if (talentLevel == 0) scroll else talentPool.firstOrNull {
            it.mainId == scroll.mainId && it.level == showTalentLevel + 1
        }

        // 计算升级消耗
        val costAward = nextScroll?.let { next ->
            val baseAward = if (next.costPool == 0) {
                Award()
            } else {
                repo.gameCore.getPoolById(next.costPool).toAward()
            }
            baseAward + Award(diamond = next.cost)
        } ?: Award()

        // 锁定状态，locked1是对应天赋时候可以升级，locked2是对应天赋时候可见
        // locked1直接用下一级的解锁条件判定，因为是用来显示是否可以升级到下一级
        val (locked, _) = getLockInfoByTalent(nextScroll ?: showTalent)
        val (locked2, _) = getLock2InfoByTalent(showTalent)

        // 是否还有下一级（且玩家目前负担得起）
        val canAfford = AwardManager.isAffordable(costAward)
        val levelNotMax = (scroll.level != scroll.levelLimit) // 仅作示例

        return Talent2UIModel(
            showTalent = showTalent,
            iconRes = showTalent.icon,
            displayName = showTalent.name,
            locked = locked,
            locked2 = locked2,
            nextLevelAvailable = (!locked && levelNotMax && canAfford),
            costAward = costAward
        )
    }

    /**
     * 核心逻辑：对 (row, column) 找到合适的天赋信息，并附带「锁定状态、下一阶、升级消耗」等。
     * 如果在该格位没有天赋，返回 null，UI 可以根据 null 做占位或 Spacer。
     */
    fun buildTalentType3SlotModel(
        row: Int,
        column: Int,
        allTalents: List<Talent>,
    ): TalentType3SlotModel? {
        // 找到当前位置的所有天赋
        val talentsAtPosition = allTalents.filter {
            it.position[0] == row && it.position[1] == column
        }
        if (talentsAtPosition.isEmpty()) return null

        // 拿到玩家当前实际激活的等级
        val mainId = talentsAtPosition.first().mainId
        val talentLevel = talents[mainId] ?: 0

        // 用当前等级或至少显示1级
        val showTalentLevel = max(1, talentLevel)
        val showTalent = talentsAtPosition.first { it.level == showTalentLevel }

        // 查技能
        val skill = repo.gameCore.getSkillById(showTalent.talentSkill)

        // 当前天赋是否锁定
        val locked = getLockInfoByTalent(showTalent).first

        // 下方天赋（下一行同列）与其是否锁定（负责显示竖线）
        val nextTalent = allTalents.firstOrNull {
            it.position[0] == row + 1 && it.position[1] == column
        }
        val nextLocked = nextTalent?.let {
            getLockInfoByTalent(it).first
        } ?: false

        // 找同一个 mainId 的下一级别天赋，用于计算升级消耗
        val nextLevelTalent = repo.gameCore.getTalent3Pool().firstOrNull {
            it.mainId == mainId && it.level == talentLevel + 1
        }

        // 这里判断一下天赋类型是否 type2，进而决定 cost 来自 where
        // （示例里你原本通过 nextTalent 的 type 来判断，这里改为 nextLevelTalent 本身的 isType2）
        val award = nextLevelTalent?.let {
            // type3 则用消耗天赋点
            Award(talentPoint = it.cost)
        } ?: Award()

        // 你代码里常量 MAX_TALENT3_LEVEL，也可以直接放这里。
        // 假设是个固定值 100，或者从配置表拿，也行。
        val maxLevel = MAX_TALENT3_LEVEL

        return TalentType3SlotModel(
            showTalent = showTalent,
            skill = skill,
            talentLevel = talentLevel,
            locked = locked,
            nextTalent = nextTalent,
            nextLocked = nextLocked,
            nextLevelTalent = nextLevelTalent,
            award = award,
            maxLevel = maxLevel
        )
    }

    fun getAllTalentLevel(type: Int): Int {
        return talents.entries.filter { it.key.mainIdToTalentType() == type }
            .sumOf { it.value }
    }
}