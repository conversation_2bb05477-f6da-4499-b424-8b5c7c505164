package com.moyu.chuanqirensheng.feature.role.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.ui.theme.B10
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.DarkGreen
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.propertyBigHeight
import com.moyu.chuanqirensheng.ui.theme.propertyBigImageSize
import com.moyu.chuanqirensheng.ui.theme.propertyBigWidth
import com.moyu.chuanqirensheng.ui.theme.propertyWidth
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.util.percentValueToDotWithOneDigits
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import kotlin.math.roundToInt


@Composable
fun PropertyItem(
    icon: DrawableResource,
    getProperty: () -> Double,
    isBoost: () -> Double = { 0.0 },
    getTips: () -> String,
    name: String,
    showPercent: Boolean = false,
    showBoost: Boolean = false,
    showName: Boolean = true,
    showPlusMinus: Boolean = showBoost,
    showIcon: Boolean = true,
    textColor: Color = Color.White,
    textStyle: TextStyle = MaterialTheme.typography.h3,
) {
    val modifier = if (showName) {
        Modifier
            .padding(vertical = padding2)
            .size(propertyBigWidth, propertyBigHeight)
            .clickable { getTips().toast() }
    } else {
        Modifier
            .padding(vertical = padding2)
            .size(propertyWidth, propertyBigHeight)
            .clickable { getTips().toast() }
    }
    Box(modifier) {
        Row(
            modifier = Modifier
                .fillMaxHeight()
                .padding(horizontal = padding2),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val showNum = getProperty()
            val text =
                if (showPercent) showNum.percentValueToDotWithOneDigits() else "${showNum.roundToInt()}"
            val showText = if (showPlusMinus && isBoost() >= 0) "+$text" else text
            if (showIcon) {
                Image(
                    modifier = Modifier.size(propertyBigImageSize),
                    painter = painterResource(icon),
                    contentDescription = null
                )
                Spacer(modifier = Modifier.size(padding3))
            }
            val boostColor = if (showBoost) {
                if (isBoost() == 0.0) textColor
                else if (isBoost() > 0) DarkGreen
                else DARK_RED
            } else {
                textColor
            }
            if (showName) {
                StrokedText(
                    text = name,
                    style = textStyle,
                    color = boostColor,
                    maxLines = 1,
                    softWrap = false,
                    overflow = TextOverflow.Visible,
                )
                Spacer(modifier = Modifier.size(padding1))
            }
            StrokedText(
                text = showText,
                style = textStyle,
                color = boostColor,
                maxLines = 1,
                softWrap = false,
                overflow = TextOverflow.Visible,
            )
        }
    }
}