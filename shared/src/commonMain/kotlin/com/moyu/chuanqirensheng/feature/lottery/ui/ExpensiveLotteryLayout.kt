package com.moyu.chuanqirensheng.feature.lottery.ui

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.lottery.MAX_RESET_LOTTERY_NUM
import com.moyu.chuanqirensheng.feature.resource.LotteryPoint
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_RESET_EXPENSIVE_LOTTERY_NUM
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.button.MEDIA_GAP
import com.moyu.chuanqirensheng.widget.button.RefreshButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.jetbrains.compose.resources.getString
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.do_lottery
import shared.generated.resources.lottery_money_not_enough
import shared.generated.resources.lottery_run_out
import shared.generated.resources.lottery_top
import shared.generated.resources.reset_lottery
import shared.generated.resources.reset_lottery_tips


@Composable
fun ExpensiveLotteryLayout() {
    Column(Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        Column(Modifier.align(Alignment.End).padding(padding4), horizontalAlignment = Alignment.CenterHorizontally) {
            RefreshButton(text = stringResource(Res.string.reset_lottery)) {
                if (LotteryManager.canResetExpensive()) {
                    LotteryManager.resetExpensiveLottery()
                } else {
                    val toastStr = runBlocking { getString(Res.string.reset_lottery_tips, MAX_RESET_LOTTERY_NUM) }
                    toastStr.toast()
                }
            }
            StrokedText(
                modifier = Modifier.clip(RoundedCornerShape(
                    padding2
                )).background(B50).padding(padding2),
                text = "" + getIntFlowByKey(KEY_RESET_EXPENSIVE_LOTTERY_NUM) + "/" + MAX_RESET_LOTTERY_NUM,
                style = MaterialTheme.typography.h4
            )
            StrokedText(
                modifier = Modifier.clip(RoundedCornerShape(
                    padding2
                )).background(B50).padding(padding2),
                text = stringResource(Res.string.reset_lottery),
                style = MaterialTheme.typography.h4
            )
        }
        val spinning = remember {
            mutableStateOf(false)
        }
        val singleItemAngle = 360 / 8
        val targetAngle = remember {
            mutableFloatStateOf(0f)
        }
        val currentAngle by animateFloatAsState(
            targetValue = if (spinning.value) targetAngle.floatValue else targetAngle.floatValue % 360,
            animationSpec = TweenSpec(
                durationMillis = if (spinning.value) 2000 else 0,
                easing = FastOutSlowInEasing
            ),
            finishedListener = {
                spinning.value = false
            }, label = ""
        )
        Box(Modifier.align(Alignment.CenterHorizontally), contentAlignment = Alignment.Center) {
            LotteryView(
                Modifier
                    .size(padding380).graphicsLayer {
                        // 因为要保证最后一个奖品显示在上面中间，所以要转动显示，保持逻辑不变
                        rotationZ = 360 / 8f
                    }, LotteryManager.getExpensiveAwards(),
                LotteryManager.getExpensiveTurnTables(),
                LotteryManager.expensiveBoughtIndex
            )
            Image(
                modifier = Modifier.size(padding380)
                    .graphicsLayer {
                        // 因为要保证最后一个奖品显示在上面中间，所以要转动显示，保持逻辑不变
                        rotationZ = currentAngle + 360 / 8f
                        scaleY = lotteryScale
                        scaleX = lotteryScale
                    },
                painter = painterResource(Res.drawable.lottery_top),
                contentDescription = null
            )
        }
        Spacer(modifier = Modifier.size(padding40))
        Box(contentAlignment = Alignment.Center) {
            GameButton(clickGap = MEDIA_GAP,
                buttonSize = ButtonSize.Big,
                text = "",
                hapticFeedbackType = HapticFeedbackType.LongPress,
                enabled = (LotteryManager.isExpensiveFreeLottery() || AwardManager.lotteryMoney.value >= LotteryManager.getExpensiveCost()) && LotteryManager.canDoExpensive(),
                onClick = {
                    if (LotteryManager.spinning.value) {
                        // 正在转动
                    } else if (!LotteryManager.isExpensiveFreeLottery() && AwardManager.lotteryMoney.value < LotteryManager.getExpensiveCost()) {
                        AppWrapper.getStringKmp(Res.string.lottery_money_not_enough).toast()
                    } else if (!LotteryManager.canDoExpensive()) {
                        AppWrapper.getStringKmp(Res.string.lottery_run_out).toast()
                    } else {
                        LotteryManager.spinning.value = true
                        MusicManager.playSound(SoundEffect.LotteryRing)
                        if (!LotteryManager.isExpensiveFreeLottery()) {
                            AwardManager.lotteryMoney.value -= LotteryManager.getExpensiveCost()
                        }
                        val (award, index) = LotteryManager.lotteryExpensiveRandomAward()
                        targetAngle.floatValue = index * singleItemAngle.toFloat() + 360 * 10
                        spinning.value = true
                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                            delay(2100)
                            LotteryManager.gainExpensiveLotteryAward(index)
                            AwardManager.gainAward(award)
                            Dialogs.awardDialog.value = award
                            LotteryManager.spinning.value = false
                        }
                    }
                })
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceAround
            ) {
                LotteryPoint(cost = if (LotteryManager.isExpensiveFreeLottery()) 0 else LotteryManager.getExpensiveCost())
                StrokedText(text = stringResource(Res.string.do_lottery), style = MaterialTheme.typography.h1)
            }
        }
    }
}