package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.util.toPixel
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.core.model.Award
import com.moyu.core.model.TurnTable
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.common_choose
import shared.generated.resources.holiday_lottery_ui
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin

const val lotteryScale = 1.09f

@Composable
fun HolidayLotteryView(modifier: Modifier, awards: List<Award>, turnTables: List<TurnTable>, gainedIndex: List<Int>) {
    val itemCount = 8
    val radius = padding120.toPixel()  // 圆的半径
    Box(modifier, contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.fillMaxSize().graphicsLayer {
                // 因为要保证最后一个奖品显示在上面中间，所以要转动显示，保持逻辑不变
                rotationZ = -360 / 8f
                scaleY = lotteryScale
                scaleX = lotteryScale
            },
            painter = painterResource(Res.drawable.holiday_lottery_ui),
            contentDescription = null
        )
        turnTables.forEachIndexed { index, _ ->
            val angle = 2 * PI * index / itemCount - PI / 2
            val x = radius * cos(angle).toFloat()
            val y = radius * sin(angle).toFloat()
            Box(
                modifier = Modifier
                    .graphicsLayer {
                        // 因为要保证最后一个奖品显示在上面中间，所以要转动显示，保持逻辑不变
                        translationX = x
                        translationY = y
                        rotationZ = -360 / 8f
                    }.scale(1.1f)
            ) {
                AwardList(award = awards[index], param = defaultParam.copy(itemSize = ItemSize.MediumPlus, textColor = Color.White))
                if (index in gainedIndex) {
                    Image(
                        modifier = Modifier.padding(top = ItemSize.Large.frameSize / 2).align(Alignment.TopEnd).size(padding26),
                        painter = painterResource(Res.drawable.common_choose),
                        contentDescription = null
                    )
                }
            }
        }
    }
}
