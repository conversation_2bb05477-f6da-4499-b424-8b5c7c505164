package com.moyu.chuanqirensheng.feature.role.ui

import androidx.compose.runtime.Composable
import com.moyu.core.model.property.AdventureProps

@Composable
fun AdventureProps.RolePropertyLine(
    showEmpty: Boolean = true,
    countStart: Int = 0,
    countEnd: Int = 100,
) {
//    var count = 1
//    if (count in countStart until countEnd) {
//        if (showEmpty || science != 0) {
//            AdvPropertyItem(
//                value = science,
//                name = stringResource(Res.string.role_prop1),
//                res = kmpDrawableResource("adv_prop1"),
//                tips = AppWrapper.getStringKmp(Res.string.prop1_tips),
//            )
//        }
//    }
//    count += 1
//    if (count in countStart until countEnd) {
//        if (showEmpty || politics != 0) {
//            AdvPropertyItem(
//                value = politics,
//                name = stringResource(Res.string.role_prop2),
//                res = kmpDrawableResource("adv_prop2"),
//                tips = AppWrapper.getStringKmp(Res.string.prop2_tips),
//            )
//        }
//    }
//    count += 1
//    if (count in countStart until countEnd) {
//        if (showEmpty || military != 0) {
//            AdvPropertyItem(
//                value = military,
//                name = stringResource(Res.string.role_prop3),
//                res = kmpDrawableResource("adv_prop3"),
//                tips = AppWrapper.getStringKmp(Res.string.prop3_tips),
//            )
//        }
//    }
//    count += 1
//    if (count in countStart until countEnd) {
//        if (showEmpty || religion != 0) {
//            AdvPropertyItem(
//                value = religion,
//                name = stringResource(Res.string.role_prop4),
//                res = kmpDrawableResource("adv_prop4"),
//                tips = AppWrapper.getStringKmp(Res.string.prop4_tips),
//            )
//        }
//    }
//    count += 1
//    if (count in countStart until countEnd) {
//        if (showEmpty || commerce != 0) {
//            AdvPropertyItem(
//                value = commerce,
//                name = stringResource(Res.string.role_prop5),
//                res = kmpDrawableResource("adv_prop5"),
//                tips = AppWrapper.getStringKmp(Res.string.prop5_tips),
//            )
//        }
//    }
}