package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.ui.theme.bigButtonWidth
import com.moyu.chuanqirensheng.ui.theme.dialogSmallHeight
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.dialog.EmptyDialog
import com.moyu.chuanqirensheng.widget.effect.ForeverGif
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.playerlevelUpGif
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.pvp_level_up_tips
import shared.generated.resources.pvp_level_up_title
import shared.generated.resources.sell_label

@Composable
fun PvpLevelDialog(show: MutableState<Boolean>) {
    if (show.value) {
        EmptyDialog(onDismissRequest = {
            show.value = false
        }) {
            ForeverGif(
                modifier = Modifier.size(bigButtonWidth),
                gifCount = playerlevelUpGif.count,
                gifDrawable = playerlevelUpGif.gif,
                scale = 2.0f,
                needGap = true
            )
            Column(
                Modifier.fillMaxWidth().height(dialogSmallHeight),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier.size(padding260, padding48),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(Res.drawable.sell_label),
                        contentDescription = null
                    )
                    StrokedText(
                        text = stringResource(Res.string.pvp_level_up_title),
                        style = MaterialTheme.typography.h1,
                        color = Color.White,
                    )
                }
                Spacer(Modifier.size(padding48))
                Box(contentAlignment = Alignment.Center) {
                    Image(
                        painter = kmpPainterResource(PvpManager.getRankLevel().pic),
                        modifier = Modifier.size(
                            padding150
                        ),
                        contentDescription = null
                    )
                }
                Spacer(Modifier.size(padding10))
                StrokedText(
                    text = stringResource(Res.string.pvp_level_up_tips, PvpManager.getRankLevel().name),
                    style = MaterialTheme.typography.h1,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                Spacer(Modifier.size(padding10))
            }
        }
    }
}
