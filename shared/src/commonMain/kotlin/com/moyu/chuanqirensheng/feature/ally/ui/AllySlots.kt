package com.moyu.chuanqirensheng.feature.ally.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.battle.MAX_BATTLE_SIZE
import com.moyu.chuanqirensheng.feature.battle.ui.EnemiesColumn
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding106
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.equipGif
import com.moyu.core.logic.role.battleAllyList
import com.moyu.core.model.Ally
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.ally_slot_selected
import shared.generated.resources.common_plus
import shared.generated.resources.talent_base

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun OtherAllySlots(
    allyMap: Map<Int, Ally>,
    currentSlotIndex: MutableState<Int>,
    deselectCallback: (Ally) -> Unit
) {
    val itemSize = ItemSize.Medium
    val rememberRefreshIndex = remember {
        mutableStateOf(0)
    }
    LaunchedEffect(rememberRefreshIndex.value) {
        currentSlotIndex.value = (0..MAX_BATTLE_SIZE).toList().firstOrNull {
            allyMap[it] == null
        } ?: 0
    }
    Box(modifier = Modifier.fillMaxWidth().height(dialogWidth)) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = padding106).padding(horizontal = padding19),
            verticalAlignment = Alignment.Top
        ) {
            Box(Modifier.weight(1f), contentAlignment = Alignment.TopStart) {
                repeat(8) { index ->
                    val position = battleAllyList[index]
                    Box(Modifier.graphicsLayer {
                        val row = index / 2
                        val column = index % 2
                        translationX = (3 - row) * padding26.toPx() + column * padding48.toPx()
                        translationY = row * padding60.toPx()
                    }) {
                        AllySlot(itemSize, allyMap, currentSlotIndex, position) {
                            allyMap[position]?.let {
                                deselectCallback(it)
                                rememberRefreshIndex.value++
                            }
                        }
                    }
                }
            }
            EnemiesColumn(Modifier.weight(1f).padding(end = padding48).graphicsLayer {
                translationY = -padding12.toPx()
            }, repo.battleRoles)
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AllySlots(
    allyMap: Map<Int, Ally>,
    currentSlotIndex: MutableState<Int>,
    deselectCallback: (Ally) -> Unit
) {
    val itemSize = ItemSize.Medium
    val rememberRefreshIndex = remember {
        mutableStateOf(0)
    }
    LaunchedEffect(rememberRefreshIndex.value) {
        currentSlotIndex.value = (0..MAX_BATTLE_SIZE).toList().firstOrNull {
            allyMap[it] == null
        } ?: 0
    }
    Box(modifier = Modifier.fillMaxWidth().height(padding200)) {
        repeat(8) { index ->
            val position = battleAllyList[index]
            Box(Modifier.graphicsLayer {
                val row = index / 2
                val column = index % 2
                translationX = (4 - row) * padding45.toPx() + column * padding100.toPx()
                translationY = row * padding45.toPx()
            }) {
                AllySlot(itemSize, allyMap, currentSlotIndex, position) {
                    allyMap[position]?.let {
                        deselectCallback(it)
                        rememberRefreshIndex.value++
                    }
                }
            }
        }
    }
}

@Composable
fun AllySlot(
    itemSize: ItemSize,
    allies: Map<Int, Ally>,
    currentSlotIndex: MutableState<Int>,
    index: Int,
    allyClick: () -> Unit
) {
    Box(
        modifier = Modifier.size(itemSize.frameSize), contentAlignment = Alignment.Center
    ) {
        val gif = remember {
            mutableStateOf(false)
        }
        allies[index]?.let { skill ->
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Box(contentAlignment = Alignment.Center) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .width(itemSize.frameSize).graphicsLayer {
                                translationY = padding4.toPx()
                            }.scale(1.1f),
                        painter = painterResource(Res.drawable.talent_base),
                        contentDescription = null
                    )
                    SingleAllyView(
                        modifier = Modifier.graphicsLayer {
                            translationY = -padding4.toPx()
                        }.scale(1.3f),
                        ally = skill,
                        itemSize = itemSize,
                        showName = false,
                        frame = null,
                        showHp = false,
                        showStar = false
                    ) {
                        allyClick()
                    }
                    GifView(
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .size(itemSize.itemSize), gif.value, equipGif.count, equipGif.gif
                    )
                }
            }
        } ?: run {
            gif.value = true
            EffectButton(modifier = Modifier.size(itemSize.frameSize), onClick = {
                currentSlotIndex.value = index
            }) {
                Image(
                    modifier = Modifier.width(itemSize.frameSize / 3),
                    painter = painterResource(Res.drawable.common_plus),
                    contentDescription = null
                )
                Image(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .width(itemSize.frameSize).graphicsLayer {
                            translationY = padding4.toPx()
                        }.scale(1.1f),
                    painter = painterResource(Res.drawable.talent_base),
                    contentDescription = null
                )
            }
        }
        if (index == currentSlotIndex.value) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .size(itemSize.frameSize).scale(1.3f),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(Res.drawable.ally_slot_selected),
                contentDescription = null
            )
        }
    }
}
