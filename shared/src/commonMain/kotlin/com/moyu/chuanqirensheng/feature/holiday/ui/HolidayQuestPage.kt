package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.api.getHolidayRanks
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.ui.SingleQuest
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.chuanqirensheng.widget.common.SearchView
import com.moyu.core.AppWrapper
import com.moyu.core.model.Quest
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import shared.generated.resources.Res
import shared.generated.resources.net_error_retry

@Composable
fun HolidayQuestPage() {
    val tasks = remember {
        mutableStateListOf<Quest>()
    }
    val refresh = remember {
        mutableStateOf(0)
    }
    val search = remember {
        mutableStateOf("")
    }
    LaunchedEffect(refresh.value.toString() + search.value) {
        if (!isNetTimeValid()) {
            refreshNetTime()
        }
        HolidayManager.createHolidayTasks()
        // 完成的任务排前面，已领取的排最后
        HolidayManager.holidayTasks.map {
            it.copy(done = QuestManager.getTaskDoneFlow(it))
        }.sortedBy { it.order }
            .sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }
            .apply {
                tasks.clear()
                if (search.value.isNotEmpty()) {
                    tasks.addAll(this.filter { it.name.contains(search.value) })
                } else {
                    tasks.addAll(this)
                }
            }
    }
    LaunchedEffect(Unit) {
        // 活动排行榜任务依赖定榜，切过来拉一次定榜
        try {
            delay(200)
            if (HolidayManager.canShowOnlyRank()) {
                // 活动已经结束，拉取定榜
                if (holidaySolidRanks.value.isEmpty()) {
                    getHolidayRanks(
                        platformChannel()
                    ).let {
                        holidaySolidRanks.value = json.decodeFromString(
                            ListSerializer(RankData.serializer()), it.message
                        )
                    }
                }
            }
        } catch (e: Exception) {
            AppWrapper.getStringKmp(Res.string.net_error_retry).toast()
        }
    }
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer(modifier = Modifier.size(padding4))
        if (isLite()) {
            SearchView(search)
        }
        LazyColumn(modifier = Modifier
            .fillMaxSize(), content = {
            items(tasks.size) { index ->
                Spacer(modifier = Modifier.size(padding5))
                SingleQuest(tasks[index]) {
                    refresh.value += 1
                }
            }
        })
    }
}