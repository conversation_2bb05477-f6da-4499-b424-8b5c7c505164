package com.moyu.chuanqirensheng.feature.skill.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.skill.getRealDescColorful
import com.moyu.chuanqirensheng.text.skillTag
import com.moyu.chuanqirensheng.text.typeToMagicIcon
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding320
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.widget.common.TitleLabel
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.logic.info.getElementTypeName
import com.moyu.core.model.skill.Skill
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.quality_frame


@Composable
fun SkillDetailDialog(show: MutableState<Skill?>) {
    show.value?.let { skill ->
        LaunchedEffect(Unit) {
            BattleManager.setSkillUnNew(skill)
        }
        PanelDialog(onDismissRequest = { show.value = null }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                StrokedText(text = skill.name, style = MaterialTheme.typography.h1, color = Color.White)
                Spacer(modifier = Modifier.size(padding30))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    skill.skillTagIds.forEach {
                        TitleLabel(
                            frame = Res.drawable.quality_frame,
                            text = it.skillTag(),
                            color = Color.White
                        )
                    }
                    MagicTypeLabel(skill)

                }
                Spacer(modifier = Modifier.size(padding16))
                Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                    StrokedText(
                        modifier = Modifier.width(padding320),
                        text = skill.getRealDescColorful(MaterialTheme.typography.h2.toSpanStyle()),
                        style = MaterialTheme.typography.h2,
                        color = Color.White
                    )
                }
                Spacer(modifier = Modifier.size(padding26))
                skill.getAllEnhancements().forEach {
                    StrokedText(text = it.desc(), style = MaterialTheme.typography.h4, color = Color.Red)
                }
            }
        }
    }
}

@Composable
fun MagicTypeLabel(skill: Skill) {
    if (skill.elementType != 0) {
        Row(
            modifier = Modifier.paint(painterResource(Res.drawable.quality_frame))
                .padding(horizontal = padding4, vertical = padding1),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Image(
                modifier = Modifier.size(padding22),
                painter = painterResource(skill.elementType.typeToMagicIcon()),
                contentDescription = null
            )
            Spacer(Modifier.size(padding2))
            StrokedText(
                text = skill.elementType.getElementTypeName(),
                style = MaterialTheme.typography.h3,
            )
        }
    }
}
