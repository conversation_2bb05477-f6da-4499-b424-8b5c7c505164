package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.battlepass.getBattlePassManager
import com.moyu.chuanqirensheng.feature.resource.CurrentPassPoint
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame


@Composable
fun BattlePassPage(passType: Int) {
    val manager = getBattlePassManager(passType)
    val bought = manager.isBattlePassBought()
    val pool = repo.gameCore.run {
        // 用 when 区分 pool
        when (passType) {
            1 -> getBattlePass1Pool()
            2 -> getBattlePass2Pool()
            3 -> getBattlePass3Pool()
            else -> emptyList()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                // 如果每个 passType 需要不同背景，可再写个 when
                painterResource(Res.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            )
            .padding(vertical = padding10, horizontal = padding10),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 显示当前进度
        CurrentPassPoint(
            passType = passType,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        )
        Box(modifier = Modifier.fillMaxSize()) {
            // 展示右侧(或其他位置)的 Pass 专属内容
            CurrentPassItem(
                passType = passType, modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .graphicsLayer {
                        translationY = -padding60.toPx()
                    }
            )

            // 在 Composable 顶部
            val currentId = remember { manager.getCurrentWarPass()?.id ?: 0 }

            // 第一个已解锁条目的下标；找不到就 0
            val unlockedIndex = remember(pool, currentId) {
                pool.indexOfLast { it.id <= currentId && manager.isThisLevelGained(it) }.takeIf { it != -1 } ?: 0
            }

            val listState = rememberLazyListState(
                initialFirstVisibleItemIndex = unlockedIndex,
                initialFirstVisibleItemScrollOffset = 0
            )
            // 列表
            LazyColumn(
                state = listState,               // 关键
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(bottom = padding8, start = padding4),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                items(pool.size) { index ->
                    if (index == 0) {
                        Spacer(modifier = Modifier.size(padding10))
                    }
                    Column {
                        Spacer(modifier = Modifier.size(padding10))
                        OnePassItem(
                            pass = pool[index],
                            passType = passType
                        ) {
                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                val unlocked =
                                    (manager.getCurrentWarPass()?.id ?: 0) >= pool[index].id
                                if (unlocked) {
                                    // unlockType=2 表示付费
                                    if (pool[index].unlockType == 2 && !bought) {
                                        // 这里替换成你想提示的文案
                                        Dialogs.battlePassUnlockDialog.value = passType
                                    } else {
                                        // 直接领取
                                        manager.gain(pool[index])
                                    }
                                } else {
                                    // 未解锁：弹出详情
                                    Dialogs.battlePassUnlockDialog.value = passType
                                }
                            }
                        }
                    }
                }
            }
        }
        Spacer(modifier = Modifier.size(padding10))
    }
}
