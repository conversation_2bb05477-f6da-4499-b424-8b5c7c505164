package com.moyu.chuanqirensheng.feature.rank.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import coil3.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.text.getRankFrame
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding64
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.rank_frame
import shared.generated.resources.rank_frame2


@Composable
fun SingleRecord(
    deathRole: RankData,
    index: Int,
    content: @Composable BoxScope.(RankData, Int) -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(padding90)
    ) {
        val myRank = deathRole.userId == gameSdkDefaultProcessor().getObjectId()
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(if (myRank) Res.drawable.rank_frame else Res.drawable.rank_frame2),
            contentDescription = null
        )
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding19),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Box(
                contentAlignment = Alignment.Center, modifier = Modifier.size(imageLarge)
            ) {
                StrokedText(
                    text = (index).toString(),
                    style = MaterialTheme.typography.h1,
                )
            }
            Box(modifier = Modifier.size(padding64)) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = painterResource(index.getRankFrame()),
                    contentDescription = null
                )
                Image(
                    modifier = Modifier
                        .fillMaxSize().padding(padding4).clip(RoundedCornerShape(padding4)),
                    painter = if (deathRole.userPic.startsWith("http")) rememberAsyncImagePainter(
                        deathRole.userPic
                    ) else kmpPainterResource(
                        deathRole.userPic
                    ),
                    contentDescription = null
                )
            }
            StrokedText(
                modifier = Modifier.weight(1f),
                text = deathRole.userName,
                style = MaterialTheme.typography.h2
            )
            Box {
                content(deathRole, index)
            }
        }
    }
}
