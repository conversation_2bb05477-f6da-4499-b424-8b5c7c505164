package com.moyu.chuanqirensheng.feature.role.ui

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.feature.award.ui.AwardUIParam
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.core.model.property.AdventureProps

@Composable
fun AdventureProps.DropPropertyLine(param: AwardUIParam = defaultParam) {
//    this.science.takeIf { it != 0 }?.let {
//        SingleAwardItem(
//            drawable = Res.drawable.adv_prop1,
//            name = stringResource(Res.string.role_prop1),
//            num = (if (it > 0) "+" else "") + it,
//            param = param.copy(),
//        ) {
//            BattleManager.adventureProps.value.science >= it
//        }
//    }
//
//    this.politics.takeIf { it != 0 }?.let {
//        SingleAwardItem(
//            drawable = Res.drawable.adv_prop2,
//            name = stringResource(Res.string.role_prop2),
//            num = (if (it > 0) "+" else "") + it,
//            param = param,
//        ) {
//            BattleManager.adventureProps.value.politics >= it
//        }
//    }
//    this.military.takeIf { it != 0 }?.let {
//        SingleAwardItem(
//            drawable = Res.drawable.adv_prop3,
//            name = stringResource(Res.string.role_prop3),
//            num = (if (it > 0) "+" else "") + it,
//            param = param,
//        ) {
//            BattleManager.adventureProps.value.military >= it
//        }
//    }
//    this.religion.takeIf { it != 0 }?.let {
//        SingleAwardItem(
//            drawable = Res.drawable.adv_prop4,
//            name = stringResource(Res.string.role_prop4),
//            num = (if (it > 0) "+" else "") + it,
//            param = param,
//        ) {
//            BattleManager.adventureProps.value.religion >= it
//        }
//    }
//    this.commerce.takeIf { it != 0 }?.let {
//        SingleAwardItem(
//            drawable = Res.drawable.adv_prop5,
//            name = stringResource(Res.string.role_prop5),
//            num = (if (it > 0) "+" else "") + it,
//            param = param,
//        ) {
//            BattleManager.adventureProps.value.commerce >= it
//        }
//    }
}