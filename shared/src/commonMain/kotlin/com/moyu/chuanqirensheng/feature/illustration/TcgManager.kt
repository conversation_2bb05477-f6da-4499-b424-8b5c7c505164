package com.moyu.chuanqirensheng.feature.illustration

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_TCG_IDS
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.core.model.Tcg
import com.moyu.core.model.toAward

object TcgManager {
    val doneTcgs = mutableStateListOf<Tcg>()

    fun init() {
        doneTcgs.clear()
        doneTcgs.addAll(getListObject<Tcg>(KEY_TCG_IDS).map { tcg ->
            repo.gameCore.getTcgPool().first { it.id == tcg.id }
        })
    }

    fun isGain(type: Int, star: Int): Boolean {
        return doneTcgs.any { it.type == type && it.star >= star }
    }

    fun isDone(tcg: Tcg): Boolean {
        return if (tcg.isAlly()) {
            tcg.content.all { mainId ->
                repo.allyManager.data.any { it.mainId == mainId && it.star >= tcg.star }
            }
        } else {
            tcg.content.all { mainId ->
                repo.equipManager.data.any { it.mainId == mainId && it.star >= tcg.star }
            }
        }
    }

    fun size(tcg: Tcg): Int {
        return if (tcg.isAlly()) {
            repo.allyManager.data.count { it.mainId in tcg.content && it.star >= tcg.star }
        } else {
            repo.equipManager.data.count { it.mainId in tcg.content && it.star >= tcg.star }
        }
    }

    fun isShow(type: Int, star: Int): Boolean {
        return (doneTcgs.none { it.type == type } && star == 1) // type没完成，这个是star1
                || doneTcgs.any { it.type == type && it.star.nextTcgStar() == star } // type有完成过，这个是下一个star正在做的等级
                || doneTcgs.any { it.type == type && it.star == star }
    }

    fun getShowTcgs(): List<Tcg> {
        return repo.gameCore.getTcgPool().filter {
            isShow(it.type, it.star)
        }
    }

    fun gain(tcg: Tcg) {
        if (!doneTcgs.any { it.id == tcg.id }) {
            doneTcgs.removeAll { it.type == tcg.type }
            doneTcgs.add(tcg)
            setListObject(KEY_TCG_IDS, doneTcgs)
            Dialogs.awardDialog.value = tcg.toAward()
        }
    }

    fun hasRed(): Boolean {
        return getShowTcgs().any {
            isDone(it) && !isGain(it.type, it.star)
        }
    }
}

private fun Int.nextTcgStar(): Int {
    return if (this == 1) 5 else this + 5
}
