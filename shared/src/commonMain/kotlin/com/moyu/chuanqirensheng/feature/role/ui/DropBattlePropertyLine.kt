package com.moyu.chuanqirensheng.feature.role.ui

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.ui.AwardUIParam
import com.moyu.chuanqirensheng.feature.award.ui.SingleAwardItem
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.core.AppWrapper
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.property.Property
import com.moyu.core.util.percentValueToDotWithOneDigits
import core.generated.resources.attack
import core.generated.resources.attack_tips
import core.generated.resources.defense1_tips
import core.generated.resources.dodge
import core.generated.resources.dodge_tips
import core.generated.resources.fatal_damage
import core.generated.resources.fatal_damage_tips
import core.generated.resources.fatal_rate
import core.generated.resources.fatal_rate_tips
import core.generated.resources.hp
import core.generated.resources.hp_tips
import core.generated.resources.speed
import core.generated.resources.speed_tips
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.battle_attribute_1
import shared.generated.resources.battle_attribute_2
import shared.generated.resources.battle_attribute_3
import shared.generated.resources.battle_attribute_4
import shared.generated.resources.battle_attribute_5
import shared.generated.resources.battle_attribute_6
import shared.generated.resources.battle_attribute_7
import core.generated.resources.Res as CoreRes

@Composable
fun Property.DropBattlePropertyLine(param: AwardUIParam = defaultParam, prefix: String = "") {
    this.attack.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_1,
            name = prefix + stringResource(CoreRes.string.attack),
            num = (if (it > 0) "+" else "") + it,
            param = param.copy(
                colorFilter = if (it >= 0) null else ColorFilter.tint(
                    color = Color.Red,
                    BlendMode.SrcIn
                ),
                callback = {
                    AppWrapper.getStringKmp(CoreRes.string.attack_tips).toast()
                }),
        )
    }
    this.defenses.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_2,
            name = prefix + DamageType.DamageType1.defenseName,
            num = (if (it > 0) "+" else "") + it,
            param = param.copy(
                colorFilter = if (it >= 0) null else ColorFilter.tint(
                    color = Color.Red,
                    BlendMode.SrcIn
                ), callback = {
                    AppWrapper.getStringKmp(CoreRes.string.defense1_tips).toast()
                }),
        )
    }
    this.hp.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_3,
            name = prefix + AppWrapper.kmpStringResource(CoreRes.string.hp),
            num = (if (it > 0) "+" else "") + it,
            param = param.copy(
                colorFilter = if (it >= 0) null else ColorFilter.tint(
                    color = Color.Red,
                    BlendMode.SrcIn
                ), callback = {
                    AppWrapper.getStringKmp(CoreRes.string.hp_tips).toast()
                }),
        )
    }
    this.getRealFatalRate().takeIf { it != 0.0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_4,
            name = prefix + AppWrapper.kmpStringResource(CoreRes.string.fatal_rate),
            num = (if (it > 0) "+" else "") + it.percentValueToDotWithOneDigits(),
            param = param.copy(
                colorFilter = if (it >= 0) null else ColorFilter.tint(
                    color = Color.Red,
                    BlendMode.SrcIn
                ), callback = {
                    AppWrapper.getStringKmp(CoreRes.string.fatal_rate_tips).toast()
                }),
        )
    }
    this.getRealFatalDamage().takeIf { it != 0.0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_5,
            name = prefix + AppWrapper.kmpStringResource(CoreRes.string.fatal_damage),
            num = (if (it > 0) "+" else "") + it.percentValueToDotWithOneDigits(),
            param = param.copy(
                colorFilter = if (it >= 0) null else ColorFilter.tint(
                    color = Color.Red,
                    BlendMode.SrcIn
                ), callback = {
                    AppWrapper.getStringKmp(CoreRes.string.fatal_damage_tips).toast()
                }),
        )
    }
    this.getRealDodgeRate().takeIf { it != 0.0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_6,
            name = prefix + AppWrapper.kmpStringResource(CoreRes.string.dodge),
            num = (if (it > 0) "+" else "") + it.percentValueToDotWithOneDigits(),
            param = param.copy(
                colorFilter = if (it >= 0) null else ColorFilter.tint(
                    color = Color.Red,
                    BlendMode.SrcIn
                ), callback = {
                    AppWrapper.getStringKmp(CoreRes.string.dodge_tips).toast()
                }),
        )
    }
    this.speed.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_7,
            name = prefix + AppWrapper.kmpStringResource(CoreRes.string.speed),
            num = (if (it > 0) "+" else "") + it,
            param = param.copy(
                colorFilter = if (it >= 0) null else ColorFilter.tint(
                    color = Color.Red,
                    BlendMode.SrcIn
                ), callback = {
                    AppWrapper.getStringKmp(CoreRes.string.speed_tips).toast()
                }),
        )
    }
}