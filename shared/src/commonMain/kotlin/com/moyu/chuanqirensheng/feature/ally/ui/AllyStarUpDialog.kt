package com.moyu.chuanqirensheng.feature.ally.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE3
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.resource.CurrentResourcesPoint
import com.moyu.chuanqirensheng.feature.resource.ResourcesPoint
import com.moyu.chuanqirensheng.feature.router.popTop
import com.moyu.chuanqirensheng.feature.skill.getRealDescColorful
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.bigButtonWidth
import com.moyu.chuanqirensheng.ui.theme.cardNumHeight
import com.moyu.chuanqirensheng.ui.theme.cardNumWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.moneyExtraWidth
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding460
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding70
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.CommonBar
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.Stars
import com.moyu.chuanqirensheng.widget.dialog.NewPanelDialog
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.starUpGif
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Ally
import com.moyu.core.model.MAX_STAR
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.bar_blue
import shared.generated.resources.bar_empty
import shared.generated.resources.current_star
import shared.generated.resources.next_star
import shared.generated.resources.star_max
import shared.generated.resources.star_up
import shared.generated.resources.three_arrows

@Composable
fun AllyStarUpDialog(show: MutableState<Ally?>) {
    show.value?.let { ally ->
        val nextAlly = repo.gameCore.getAllyNextStar(ally.id)
        val role = BattleManager.getMyRoleByAlly(ally)
        val nextRole = BattleManager.getMyRoleByAlly(nextAlly)
        NewPanelDialog(resourceContent = {
            CurrentResourcesPoint(
                index = 0, showPlus = true, boxWidth = moneyExtraWidth
            )
        }, onDismissRequest = {
            if (GuideManager.guideIndex.value == 13) {
                GuideManager.guideIndex.value = 14
                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                repo.allyManager.starUp(ally)?.let {
                    repo.allyManager.currentShowAllyMainId.value = it.mainId
                    if (it.star == MAX_STAR) {
                        Dialogs.allyStarUpDialog.value = null
                    } else {
                        Dialogs.allyStarUpDialog.value = it
                    }
                }
            } else {
                if (GuideManager.guideIndex.value == 14) {
                    GuideManager.guideIndex.value = 15
                    setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                    popTop()
                }
                show.value = null
            }
        }) {
            Box {
                Column(Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
                    Spacer(modifier = Modifier.size(padding19))
                    Box(contentAlignment = Alignment.Center, modifier = Modifier.fillMaxWidth()) {
                        SingleAllyView(
                            itemSize = ItemSize.LargePlus, ally = ally, showName = false
                        ) {}
                        if (GuideManager.guideIndex.value == 14) {
                            GuideHand(
                                modifier = Modifier.align(Alignment.TopEnd).padding(end = padding34)
                                    .height(padding60).graphicsLayer {
                                        translationY = -padding30.toPx()
                                    }, handType = HandType.RIGHT_HAND
                            )
                        }
                    }
                    Spacer(modifier = Modifier.size(padding10))
                    AllySkillPanel(ally, nextAlly, role, nextRole)
                    Spacer(modifier = Modifier.weight(1f))
                    CommonBar(
                        modifier = Modifier.size(cardNumWidth, cardNumHeight),
                        currentValue = ally.num,
                        maxValue = ally.getStarUpNum(),
                        fullRes = Res.drawable.bar_blue,
                        emptyRes = Res.drawable.bar_empty,
                        textColor = Color.White,
                        style = MaterialTheme.typography.h5
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    DoAllyStarUpView(ally = ally)
                    Spacer(modifier = Modifier.size(padding19))
                }
                if (GuideManager.guideIndex.value == 13 || GuideManager.guideIndex.value == 14) {
                    Box(modifier = Modifier.fillMaxSize().clickable {
                        GameCore.instance.onBattleEffect(SoundEffect.Click)
                        if (GuideManager.guideIndex.value == 13) {
                            GuideManager.guideIndex.value = 14
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                            repo.allyManager.starUp(ally)?.let {
                                repo.allyManager.currentShowAllyMainId.value = it.mainId
                                if (it.star == MAX_STAR) {
                                    Dialogs.allyStarUpDialog.value = null
                                } else {
                                    Dialogs.allyStarUpDialog.value = it
                                }
                            }
                        } else {
                            if (GuideManager.guideIndex.value == 14) {
                                GuideManager.guideIndex.value = 15
                                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                                popTop()
                                show.value = null
                            }
                        }
                    })
                }
            }
        }
    }
}

@Composable
fun DoAllyStarUpView(ally: Ally) {
    Box(contentAlignment = Alignment.Center) {
        val maxStar = ally.star >= MAX_STAR
        val buttonText =
            if (maxStar) AppWrapper.getStringKmp(Res.string.star_max) else AppWrapper.getStringKmp(
                Res.string.star_up
            )
        val enabled = repo.allyManager.canStarUp(ally)

        val gifShowed = remember {
            mutableStateOf(0)
        }
        val oldLevel = remember(gifShowed.value) {
            mutableStateOf(ally.star)
        }
        if (oldLevel.value != ally.star) {
            GifView(
                modifier = Modifier.size(bigButtonWidth, bigButtonHeight).graphicsLayer {
                    // 要在icon播放，不在button播放，简单移上去，尺寸是固定的
                    translationY = -padding460.toPx()
                }.scale(4f),
                enabled = true,
                gifCount = starUpGif.count,
                gifDrawable = starUpGif.gif,
                pace = starUpGif.pace,
            ) {
                gifShowed.value += 1
            }
        }

        GameButton(
            text = "",
            buttonStyle = ButtonStyle.Green,
            buttonSize = ButtonSize.Big,
            enabled = enabled,
            mute = true,
            onClick = {
                repo.allyManager.starUp(ally)?.let {
                    repo.allyManager.currentShowAllyMainId.value = it.mainId
                    if (it.star == MAX_STAR) {
                        Dialogs.allyStarUpDialog.value = null
                    } else {
                        Dialogs.allyStarUpDialog.value = it
                    }
                } ?: run {
                    GameCore.instance.onBattleEffect(SoundEffect.Click)
                }
            })
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceAround
        ) {
            ResourcesPoint(0, ally.getStarUpRes())
            StrokedText(text = buttonText, style = MaterialTheme.typography.h1)
        }
        if (GuideManager.guideIndex.value == 13) {
            GuideHand(
                modifier = Modifier.align(Alignment.TopCenter)
                    .height(padding70).graphicsLayer {
                        translationY = -padding50.toPx()
                    }.scale(1.14f), handType = HandType.DOWN_HAND
            )
        }
    }
}

@Composable
fun AllySkillPanel(
    ally: Ally,
    nextAlly: Ally,
    role: Role,
    nextRole: Role,
) {
    Row(Modifier.width(padding260), horizontalArrangement = Arrangement.SpaceBetween) {
        StrokedText(
            text = stringResource(Res.string.current_star),
            style = MaterialTheme.typography.h2,
        )
        StrokedText(
            text = stringResource(Res.string.next_star),
            style = MaterialTheme.typography.h2,
        )
    }
    Row(
        Modifier.width(padding260).padding(horizontal = padding16),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Stars(Modifier.size(imageLarge), ally.star)
        Image(
            modifier = Modifier.size(imageLarge),
            painter = painterResource(Res.drawable.three_arrows),
            contentDescription = null
        )
        Stars(Modifier.size(imageLarge), nextAlly.star)
    }
    Spacer(modifier = Modifier.size(padding14))
    val showSkills = if (nextRole.getSkills().size > 1) {
        role.getSkills().drop(1)
    } else {
        role.getSkills()
    }
    showSkills.forEach {
        Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.Top) {
            SingleSkillView(Modifier, skill = it, showName = false)
            Spacer(modifier = Modifier.size(padding2))
            Column(modifier = Modifier.height(ItemSize.Large.frameSize * 1.1f), verticalArrangement = Arrangement.SpaceEvenly) {
                StrokedText(
                    text = "${it.name} Lv.${it.level} >>> Lv.${it.level + 1}",
                    style = MaterialTheme.typography.h3,
                    color = Color.White
                )
                StrokedText(
                    text = it.getRealDescColorful(spanStyle = MaterialTheme.typography.h4.toSpanStyle()),
                    style = MaterialTheme.typography.h4,
                    color = Color.White
                )
            }
        }
        Spacer(Modifier.size(padding10))
    }
}