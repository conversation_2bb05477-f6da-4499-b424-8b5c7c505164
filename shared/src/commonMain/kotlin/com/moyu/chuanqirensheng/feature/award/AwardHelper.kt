package com.moyu.chuanqirensheng.feature.award

import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.Pool
import com.moyu.core.model.toAward
import com.moyu.core.model.toAwards
import kotlin.math.abs

fun Ending.toAward(): Award {
    return if (!pass || StageManager.maxStage.value >= stage) {
        Award()
    } else {
        StageManager.getAwardByStage(stage)
    }
}

fun Event.toAward(win: Boolean): Award {
    return toAwards(win).toAward().let {
        if (this.winReward.first() != 0) {
            val pool = GameCore.instance.getPoolById(this.winReward.first())
            // todo 魔法行会的奖励是随机的，所以要显示问号
            // 局内资源type=3的pool=0不是随机。
            if (pool.type.size > pool.totalNum || (pool.type.none { it == 3 } && pool.pool.any { it == 0 }) ) {
                // 如果奖励是多个里随机一个，事件的详情里则可能要显示问号
                it.copy(showQuestion = true)
            } else it
        } else it
    }
}

fun Event.toAwards(win: Boolean): List<Award> {
    return try {
        if (win) winReward.map { GameCore.instance.getPoolById(it).toAward() }
        else loseReward.map {
            // 这里要小心处理
            val pool = GameCore.instance.getPoolById(it)
            // 这里是局内event可能要丢弃卡牌，我先分割pool为单个pool，一个一个算
            val pools = pool.split()
            val awards = pools.map { singlePool ->
                if (singlePool.singlePoolNeedLoseCard()) {
                    // 如果是需要丢弃卡牌，把数值改正
                    val award = singlePool.copy(num = listOf(abs(singlePool.num.first()))).toAwards(
                        onLoseAlly = { BattleManager.allyGameData },
                        onLoseSkill = { BattleManager.skillGameData },
                    ).toAward()
                    // 转负
                    -award
                } else {
                    singlePool.toAward()
                }
            }
            awards.toAward()
        }
    } catch (e: Exception) {
        emptyList()
    }
}

// todo 太麻烦了，这里是局内可能失去的卡牌枚举
fun Int.isInGameCard(): Boolean {
    return this == 1 || this == 2 || this == 3 || this == 4 || this == 11
}

fun Pool.singlePoolNeedLoseCard(): Boolean {
    return type.first().isInGameCard() && this.num.first() < 0
}
