package com.moyu.chuanqirensheng.feature.login

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE2
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE3
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE4
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE5
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE6
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE9
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.OUT_ALLY
import com.moyu.chuanqirensheng.feature.router.OUT_EQUIP
import com.moyu.chuanqirensheng.feature.router.TALENT_ALL_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.router.gotoSellWithTabIndex
import com.moyu.chuanqirensheng.feature.router.selectedRouter
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.unlock.MENU_MAIN
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_ALLY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_HERO
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_SELL
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TALENT1
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TALENT2
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TALENT3
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.platform.screenWidthInDp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding130
import com.moyu.chuanqirensheng.ui.theme.padding140
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.util.composeDp
import com.moyu.core.AppWrapper
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.ally
import shared.generated.resources.bottom_frame
import shared.generated.resources.equip
import shared.generated.resources.main
import shared.generated.resources.shop
import shared.generated.resources.talent

// 仅用于演示如何把多项配置抽成一个数据类
data class BottomBarItemData(
    val nameRes: String,
    val iconRes: String,
    val unlockIds: List<Int> = emptyList(),
    val redIcon: () -> Boolean = { false },
    val upgradeIcon: () -> Boolean = { false },
    val onClick: () -> Unit
)

// 实际上就是你原先写死的 5 个 BottomItem。
val bottomItems = listOf(
    BottomBarItemData(
        nameRes = AppWrapper.getStringKmp(Res.string.shop),
        iconRes = "icon_shop",
        unlockIds = listOf(UNLOCK_MENU_SELL),
        redIcon = { SellManager.getRedVip() || SellManager.getAllRedFree() },
        onClick = { gotoSellWithTabIndex(0) }
    ),
    BottomBarItemData(
        nameRes = AppWrapper.getStringKmp(Res.string.ally),
        iconRes = "icon_ally",
        unlockIds = listOf(UNLOCK_MENU_ALLY),
        redIcon = { repo.allyManager.haveNew() || repo.allyManager.canInBattleAllyLevelUp() },
        upgradeIcon = { repo.allyManager.canInBattleAllyStarUp() },
        onClick = { goto(OUT_ALLY) }
    ),
    BottomBarItemData(
        nameRes = AppWrapper.getStringKmp(Res.string.main),
        iconRes = "icon_stage",
        unlockIds = listOf(MENU_MAIN),
        onClick = { goto(LOGIN_SCREEN) }
    ),
    BottomBarItemData(
        nameRes = AppWrapper.getStringKmp(Res.string.equip),
        iconRes = "icon_equip",
        unlockIds = listOf(UNLOCK_MENU_HERO),
        redIcon = { repo.equipManager.haveNew() },
        upgradeIcon = { repo.equipManager.canAnyEquippedLevelUp() },
        onClick = { goto(OUT_EQUIP) }
    ),
    BottomBarItemData(
        nameRes = AppWrapper.getStringKmp(Res.string.talent),
        iconRes = "icon_talent",
        unlockIds = listOf(UNLOCK_TALENT1, UNLOCK_TALENT2, UNLOCK_TALENT3),
        redIcon = { TalentManager.hasRed() },
        onClick = { goto(TALENT_ALL_SCREEN) }
    ),
)

@Composable
fun LoginBottomItems(modifier: Modifier = Modifier) {
    Box(Modifier.fillMaxSize(), contentAlignment = Alignment.BottomCenter) {
        Box(
            modifier = modifier
                .fillMaxWidth().height(padding90).paint(
                    painter = painterResource(Res.drawable.bottom_frame),
                    contentScale = ContentScale.FillBounds
                )
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(padding1)
            ) {
                bottomItems.forEachIndexed { index, itemData ->
                    // 根据是否选中，确定“目标权重”
                    val isSelected = (selectedRouter.value == index)
                    // 让选中项更宽，比如 1.6，没选中项更窄，比如 0.85
                    // 将动画后的 weight 传给 BottomItem
                    BottomItem(
                        modifier = Modifier.weight(1f).scale(if (isSelected) 1.2f else 1f),
                        name = itemData.nameRes,
                        icon = itemData.iconRes,
                        selected = isSelected,
                        unlock = itemData.unlockIds.map { repo.gameCore.getUnlockById(it) },
                        redIcon = itemData.redIcon,
                        upgradeIcon = itemData.upgradeIcon
                    ) {
                        itemData.onClick()
                    }
                }
            }
        }
        // 这里我假设你有 5 个 BottomItem 的信息，可以放在一个 List 里
        LaunchedEffect(Dialogs.unlockStatusDialog.value, GuideManager.guideIndex.value) {
            delay(300)
            if (unlockDialogDismissed() && GuideManager.guideIndex.value in 1..5) {
                if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_MENU_SELL))) {
                    GuideManager.guideIndex.value = 6
                    setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE2)
                    GuideManager.showGuide.value = true
                }
            }

            if (unlockDialogDismissed() && GuideManager.guideIndex.value == 8) {
                if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_MENU_ALLY))) {
                    GuideManager.guideIndex.value = 9
                    setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE3)
                    GuideManager.showGuide.value = true
                }
            }

            if (unlockDialogDismissed() && GuideManager.guideIndex.value == 17) {
                if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_TALENT1))) {
                    GuideManager.guideIndex.value = 18
                    setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE4)
                    GuideManager.showGuide.value = true
                }
            }

            if (unlockDialogDismissed() && GuideManager.guideIndex.value == 22) {
                if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_MENU_HERO))) {
                    GuideManager.guideIndex.value = 23
                    setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE5)
                    GuideManager.showGuide.value = true
                }
            }

            if (unlockDialogDismissed() && GuideManager.guideIndex.value == 25) {
                if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_TALENT2))) {
                    GuideManager.guideIndex.value = 26
                    setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE6)
                    GuideManager.showGuide.value = true
                }
            }

            if (unlockDialogDismissed() && GuideManager.guideIndex.value == 39) {
                if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_TALENT3))) {
                    GuideManager.guideIndex.value = 40
                    setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE9)
                    GuideManager.showGuide.value = true
                }
            }
        }
        if (GuideManager.guideIndex.value == 6) {
            GuideHand(
                modifier = Modifier.align(Alignment.BottomStart)
                    .padding(bottom = padding80, start = padding10).height(padding80),
                handType = HandType.DOWN_HAND
            )
        } else if (GuideManager.guideIndex.value == 9) {
            GuideHand(
                modifier = Modifier.align(Alignment.BottomStart).padding(
                    bottom = padding80,
                    start = (screenWidthInDp() / 5).composeDp() + padding10
                ).height(padding80),
                handType = HandType.DOWN_HAND
            )
        } else if (GuideManager.guideIndex.value == 18) {
            GuideHand(
                modifier = Modifier.align(Alignment.BottomEnd)
                    .padding(bottom = padding80, end = padding10).height(padding80),
                handType = HandType.DOWN_HAND
            )
        } else if (GuideManager.guideIndex.value == 23) {
            GuideHand(
                modifier = Modifier.align(Alignment.BottomEnd).padding(
                    bottom = padding80,
                    end = (screenWidthInDp() / 5).composeDp() + padding10
                ).height(padding80),
                handType = HandType.DOWN_HAND
            )
        } else if (GuideManager.guideIndex.value == 26) {
            GuideHand(
                modifier = Modifier.align(Alignment.BottomEnd)
                    .padding(bottom = padding80, end = padding10).height(padding80),
                handType = HandType.DOWN_HAND
            )
        } else if (GuideManager.guideIndex.value == 40) {
            GuideHand(
                modifier = Modifier.align(Alignment.BottomEnd)
                    .padding(bottom = padding80, end = padding10).height(padding80),
                handType = HandType.DOWN_HAND
            )
        } else if (GuideManager.guideIndex.value == 15) {
            GuideHand(
                modifier = Modifier.align(Alignment.BottomStart)
                    .padding(start = padding140, bottom = padding130)
                    .height(padding80), handType = HandType.DOWN_HAND
            )
        } else if (GuideManager.guideIndex.value == 16) {
            GuideHand(
                modifier = Modifier.align(Alignment.TopStart).padding(top = padding180, start = padding110)
                    .height(padding80), handType = HandType.DOWN_HAND
            )
        }
    }
}

// 这个unlockStatusDialog的逻辑是，保存所有需要显示解锁的item状态，是否已解锁
// 每次回到首页，看下新状态，只要有原来是未解锁false，后面是已解锁true，则要弹窗
// 所以如果他们所有的数值都是一样的，那就是没有弹窗
fun unlockDialogDismissed(): Boolean {
    return Dialogs.unlockStatusDialog.value == null || Dialogs.unlockStatusDialog.value?.let {
        it.first.zip(it.second).all {
            it.first == it.second
        }
    } ?: true
}