package com.moyu.chuanqirensheng.feature.event

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.util.fastForEachReversed
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.Event
import com.moyu.core.model.toAward

class EventRecorder {

    val succeededEvents = mutableStateListOf<Event>()
    val failedEvents = mutableStateListOf<Event>()
    val usedEvents = mutableStateListOf<Event>()
    
    fun clear() {
        succeededEvents.clear()
        failedEvents.clear()
        usedEvents.clear()
    }

    fun addResult(event: Event, adjustedResult: Boolean): Boolean {
        if (usedEvents.any { it.uuid == event.uuid }) return true
        if (usedEvents.any { it.selectAge == BattleManager.getAge() }) return true
        event.copy(selectAge = BattleManager.getAge()).let {
            usedEvents.add(it.simple())
            if (adjustedResult) {
                succeededEvents.add(it.simple())
            } else {
                failedEvents.add(it.simple())
            }
            return false
        }
    }

    fun resetEvents(used: List<Event>, success: List<Event>, failed: List<Event>) {
        usedEvents.clear()
        usedEvents.addAll(used)
        succeededEvents.clear()
        succeededEvents.addAll(success)
        failedEvents.clear()
        failedEvents.addAll(failed)
    }
}