package com.moyu.chuanqirensheng.feature.event.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.role.ui.MainPropertyLine
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.B10
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.core.logic.role.ALLY_ROW2_SECOND
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.property_frame


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun PropertyView(modifier: Modifier) {
    Box(
        modifier.fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(Res.drawable.property_frame),
            contentDescription = null
        )
        FlowRow(
            modifier = Modifier.clip(
                RoundedCornerShape(5f)
            ).background(B10),
            horizontalArrangement = Arrangement.spacedBy(padding30),
            overflow = FlowRowOverflow.Visible,
            maxItemsInEachRow = 3
        ) {
            repo.battleRoles[ALLY_ROW2_SECOND]?.getCurrentProperty()?.MainPropertyLine(
                originProperty = BattleManager.initialProperty.value,
                textStyle = MaterialTheme.typography.h3,
                textColor = Color.White,
                showName = false,
                hideHp = true,
                showBoost = true,
                needMinus = false,
                showPlusMinus = false
            )
        }
    }
}
