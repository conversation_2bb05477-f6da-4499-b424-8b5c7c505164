package com.moyu.chuanqirensheng.feature.holiday

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.api.postHolidayRankData
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_FREE_HOLIDAY_LOTTERY_DONE
import com.moyu.chuanqirensheng.sub.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.sub.datastore.KEY_LOTTERY_HOLIDAY_BOUGHT
import com.moyu.chuanqirensheng.sub.datastore.KEY_LOTTERY_HOLIDAY_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_LOTTERY_HOLIDAY_TOTAL
import com.moyu.chuanqirensheng.sub.datastore.KEY_RESET_HOLIDAY_LOTTERY_NUM
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.AppWrapper
import com.moyu.core.model.Award
import com.moyu.core.model.TurnTable
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.lottery_reset
import shared.generated.resources.not_login
import kotlin.random.Random

object HolidayLotteryManager {
    val spinning = mutableStateOf(false)
    val holidayBoughtIndex = mutableStateListOf<Int>()
    val holidaySpinTotal = Guarded(KEY_LOTTERY_HOLIDAY_TOTAL)

    // 返回最终随机到的奖励，以及在全部8个奖品里，随机到的奖品的index[0, 7]
    fun lotteryCheatRandomAward(): Pair<Award, Int> {
        // 确保无法通过回滚存档修改随机结果
        val random = Random(
            (gameSdkDefaultProcessor().getObjectId()?:"no user").toCharArray().sumOf { it.code } + getIntFlowByKey(
                KEY_LOTTERY_HOLIDAY_NUM
            ) + LoginManager.instance.loginData.value.serverData.serverId + 1000 * getIntFlowByKey(KEY_RESET_HOLIDAY_LOTTERY_NUM))
        val leftAwards = getCheapLotteryAwards().filterIndexed { index, _ -> !holidayBoughtIndex.contains(index) }
        val leftTurnTables = repo.gameCore.getTurnTablePool().filter { it.isHoliday() }.filterIndexed { index, _ -> !holidayBoughtIndex.contains(index) }
        val totalWeight = leftTurnTables.sumOf { it.price }
        val randomAwardWeight = random.nextInt(totalWeight)
        var cumulativeWeight = 0
        val randomAwardIndex = leftTurnTables.indexOfFirst {
            cumulativeWeight += it.price
            randomAwardWeight < cumulativeWeight
        }
        val realRandomAwardIndex = if (holidayBoughtIndex.size < 4) {
            // 前4次不能随机到重要奖励，也就是不能获得最后一个
            if (randomAwardIndex == leftTurnTables.size - 1) {
                0
            } else {
                randomAwardIndex
            }
        } else {
            randomAwardIndex
        }
        val allIndex = repo.gameCore.getTurnTablePool().filter { it.isHoliday() }.indexOfFirst { it.id == leftTurnTables[realRandomAwardIndex].id }
        return leftAwards[realRandomAwardIndex] to allIndex
    }

    fun isCheapFreeLottery(): Boolean {
        return !getBooleanFlowByKey(KEY_FREE_HOLIDAY_LOTTERY_DONE)
    }

    fun canResetCheap(): Boolean {
        return true
    }

    fun resetCheapLottery() {
        AppWrapper.getStringKmp(Res.string.lottery_reset).toast()
        increaseIntValueByKey(KEY_RESET_HOLIDAY_LOTTERY_NUM, 1)
        setIntValueByKey(KEY_LOTTERY_HOLIDAY_NUM, 0)
        holidayBoughtIndex.clear()
        setListObject(KEY_LOTTERY_HOLIDAY_BOUGHT, holidayBoughtIndex)
    }

    fun getCheapLotteryAwards(): List<Award> {
        val weekIndex = 0
        val turnTables = repo.gameCore.getTurnTablePool().filter { it.isHoliday() }
        return turnTables.map {
            when (weekIndex) {
                0 -> it.reward1
                1 -> it.reward2
                2 -> it.reward3
                3 -> it.reward4
                else -> 0
            }
        }.map { if (it == 0) Award() else repo.gameCore.getPoolById(it).toAward() }
    }

    fun gainCheapLotteryAward(index: Int) {
        setBooleanValueByKey(KEY_FREE_HOLIDAY_LOTTERY_DONE, true)
        increaseIntValueByKey(KEY_LOTTERY_HOLIDAY_NUM, 1)
        holidaySpinTotal.value += 1
        holidayBoughtIndex.add(index)
        setListObject(KEY_LOTTERY_HOLIDAY_BOUGHT, holidayBoughtIndex)
        if (holidayBoughtIndex.size >= 8 && canResetCheap()) {
            resetCheapLottery()
        }
        AppWrapper.globalScope.launch(Dispatchers.IO) {
            val value = holidaySpinTotal.value
            delay(5000)
            // 5秒delay后，数据没有变化，上传，如果快速转盘，就不必每次都上报
            if (value == holidaySpinTotal.value) {
                uploadHolidayRank()
            }
        }
    }

    fun refresh() {
        holidayBoughtIndex.clear()
        holidayBoughtIndex.addAll(getListObject(KEY_LOTTERY_HOLIDAY_BOUGHT))
    }

    fun getCheapTurnTables(): List<TurnTable> {
        return repo.gameCore.getTurnTablePool().filter { it.isHoliday() }
    }

    fun getCheapCost(): Int {
        return repo.gameCore.getHolidayLotteryCosts().getOrNull(holidayBoughtIndex.size) ?: repo.gameCore.getCheapLotteryCosts().last()
    }

    fun canDoCheap(): Boolean {
        return holidayBoughtIndex.size < 8
    }

    fun isLotteryTaskDone(num: Int): Boolean {
        return holidaySpinTotal.value >= num
    }

    fun isLotteryTaskDoneString(num: Int): String {
        return holidaySpinTotal.value.toString() + "/" + num
    }

    fun uploadHolidayRank() {
        if ((!isLite() || DebugManager.unlockAll)) {
            AppWrapper.globalScope.launch(Dispatchers.IO) {
                try {
                    postHolidayRankData(
                        RankData(
                            time = getCurrentTime(),
                            versionCode = getVersionCode(),
                            userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else gameSdkDefaultProcessor().getUserName()
                                ?: AppWrapper.getStringKmp(Res.string.not_login),
                            userId = gameSdkDefaultProcessor().getObjectId() ?: "0",
                            userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "deafult_head" else gameSdkDefaultProcessor().getAvatarUrl() ?: "0",
                            electric = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) AwardManager.electric.value else 0,
                            platformChannel = platformChannel(),
                            holidayNum = holidaySpinTotal.value,
                            serverId = LoginManager.instance.getSavedServerId()
                        )
                    )
                } catch (e: Exception) {
//                    Timber.e(e)
                }
            }
        }
    }
}