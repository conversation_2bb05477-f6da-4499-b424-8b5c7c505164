package com.moyu.chuanqirensheng.feature.holiday

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.quest.FOREVER
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.sell.SELL_FOREVER
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC1
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC2
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC3
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC4
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC5
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC6
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC7
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_HOLIDAY_GAME_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_HOLIDAY_SELLS
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getDaySinceDec24
import com.moyu.chuanqirensheng.util.getNYStartTimeMillis
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.model.Quest
import com.moyu.core.model.Sell
import com.moyu.core.model.toAward

object HolidayManager {
    val packages = mutableStateListOf<Sell>()
    val holidayTasks = mutableStateListOf<Quest>()

    val electricDay1 = Guarded(KEY_ELECTRIC1)
    val electricDay2 = Guarded(KEY_ELECTRIC2)
    val electricDay3 = Guarded(KEY_ELECTRIC3)
    val electricDay4 = Guarded(KEY_ELECTRIC4)
    val electricDay5 = Guarded(KEY_ELECTRIC5)
    val electricDay6 = Guarded(KEY_ELECTRIC6)
    val electricDay7 = Guarded(KEY_ELECTRIC7)

    fun init(force: Boolean = false) {
        if (canShowEntrance() || force) {
            HolidaySignManager.init()
            createHolidayTasks()
            createPackages()
        }
    }

    fun createHolidayTasks() {
        if (holidayTasks.isEmpty()) {
            getListObject<Quest>(KEY_HOLIDAY_GAME_TASK).let { taskList ->
                val tasks = taskList.mapNotNull { task ->
                    repo.gameCore.getGameTaskPool().firstOrNull { it.id == task.id }
                        ?.copy(done = task.done, opened = task.opened, needRemoveCount = task.needRemoveCount)
                }
                holidayTasks.addAll(tasks)
            }
        }
        val day = getDaySinceDec24()
        val shouldShowTasks = repo.gameCore.getGameTaskPool().filter {
            it.isHolidayTask()
        }
        val currentTaskIds = holidayTasks.map { it.id }
        val tobeAdded = shouldShowTasks.filter {
            !currentTaskIds.contains(it.id)
        }.map { task ->
            // 要用永久计数，但是又要移除之前的计数
            val needRemove = if (task.isMaxRecordQuest()) {
                0
            } else {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + task.type + postFix, 0)
            }
            task.copy(needRemoveCount = needRemove)
        }.sortedBy {
            it.order
        }
        holidayTasks.addAll(tobeAdded)

        val shouldRemoveTasks = repo.gameCore.getGameTaskPool().filter {
            it.isHolidayTask()
        }.filter { task ->
            if (DebugManager.unlockAll) false else
                repo.gameCore.getDayRewardPool().filter { it.isHoliday() }.firstOrNull { it.value == task.id }?.let {
                    it.unlock > day || it.disappear <= day
                } ?: false
        }
        holidayTasks.removeAll { task ->
            shouldRemoveTasks.any { it.id == task.id }
        }

        setListObject(KEY_HOLIDAY_GAME_TASK, holidayTasks, Quest.serializer())
    }
    
    fun createPackages() {
        if (!isNetTimeValid()) {
            return
        }
        if (packages.isEmpty()) {
            getListObject(KEY_HOLIDAY_SELLS, Sell.serializer()).let {
                packages.addAll(it.mapNotNull { sell ->
                    repo.gameCore.getSellPool().firstOrNull { it.id == sell.id }
                        ?.copy(opened = sell.opened, num = sell.num, storage = sell.storage)
                })
            }
        }
        val day = getDaySinceDec24()
        val shouldShowPackages = repo.gameCore.getSellPool().filter {
            it.isHoliday()
        }.filter { sell ->
            if (DebugManager.unlockAll) true else
            repo.gameCore.getDayRewardPool().filter { it.isHoliday() }.firstOrNull { it.value == sell.id }?.let {
                it.unlock <= day
            } ?: false
        }.sortedBy {
            it.priority
        }

        val currentPackageIds = packages.map { it.id }
        val tobeAdded = shouldShowPackages.filter {
            !currentPackageIds.contains(it.id)
        }

        packages.addAll(tobeAdded)
        packages.removeAll { sell ->
            shouldShowPackages.none { it.id == sell.id }
        }
        setListObject(KEY_HOLIDAY_SELLS, packages, Sell.serializer())
    }

    fun getElectricDayValue(day: Int): Guarded? {
        return when (day) {
            1 -> electricDay1
            2 -> electricDay2
            3 -> electricDay3
            4 -> electricDay4
            5 -> electricDay5
            6 -> electricDay6
            7 -> electricDay7
            else -> null
        }
    }


    fun gainElectric(gain: Int) {
        if (canShowHoliday()) {
            getElectricDayValue(getDaySinceDec24())?.let {
                it.value += gain
            }
        }
    }

    fun isChargeTaskDone(num: Int): Boolean {
        return getElectricDayValue(getDaySinceDec24())
            ?.let {
            it.value >= num
        } ?: false
    }

    fun isChargeTaskDoneString(num: Int): String {
        return getElectricDayValue(getDaySinceDec24())
            ?.let {
            it.value.toString() + "/" + num
        } ?: ""
    }
    
    fun hasRed(): Boolean {
        return (holidayTasks).any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        }
    }

    fun getRedIcons(): List<Boolean> {
        return listOf(HolidaySignManager.showRed(), hasRed(), HolidayLotteryManager.isCheapFreeLottery(), false, false)
    }

    suspend fun openPackage(sell: Sell) {
        if (sell.isDiamondMoney()) {
            AwardManager.gainDiamond(-sell.price)
        } else if (sell.isKeyMoney()) {
            AwardManager.gainKey(-sell.price)
        }
        reportManager().onShopPurchase(
            sellId = sell.id,
            price = sell.price,
            priceType = sell.priceType
        )

        val realAward = sell.toAward()
        AwardManager.gainAward(realAward)
        Dialogs.awardDialog.value = realAward
        setBooleanValueByKey(SELL_FOREVER + sell.id, true)
        markGoogleSellItem(sell)
    }

    fun markGoogleSellItem(sell: Sell) {
        packages.indexOfFirst { it.id == sell.id }.takeIf { it >= 0 }?.let {
            if (!sell.isMonthCard()) { // 月卡的storage是用来表示购后可以领取的天数，不用-1
                packages[it] = packages[it].copy(storage = packages[it].storage - 1)
            }
            setListObject(KEY_HOLIDAY_SELLS, packages, Sell.serializer())
        }
    }

    fun canShowHoliday(): Boolean {
        if (!isNetTimeValid()) {
            return false
        }
        // 获取纽约时间的 12 月 24 日 0 时开始的时间戳（毫秒）
        val nyStartTimeMillis = getNYStartTimeMillis()
        val nyEndTimeMillis = nyStartTimeMillis + 7 * 24 * 60 * 60 * 1000L // 7 天后的时间戳

        // 转换为北京时间的范围
        val bjStartTimeMillis = nyStartTimeMillis
        val bjEndTimeMillis = nyEndTimeMillis

        // 获取当前北京时间
        val currentBeijingTime = getCurrentTime()

        // 判断当前时间是否在北京时间范围内
        return currentBeijingTime in bjStartTimeMillis..bjEndTimeMillis
    }

    fun canShowEntrance(): Boolean {
        if (!isNetTimeValid()) {
            return false
        }
        // 持续10天可以看到入口
        // 获取纽约时间的 12 月 24 日 0 时开始的时间戳（毫秒）
        val nyStartTimeMillis = getNYStartTimeMillis()
        val nyEndTimeMillis = nyStartTimeMillis + 10 * 24 * 60 * 60 * 1000L // 7 天后的时间戳

        val bjStartTimeMillis = nyStartTimeMillis

        // 获取当前时间
        val currentBeijingTime = getCurrentTime()

        return currentBeijingTime in bjStartTimeMillis..nyEndTimeMillis
    }

    fun canShowOnlyRank(): Boolean {
        return canShowEntrance() && !canShowHoliday()
    }

    fun canShowHolidayIcon(): Boolean {
        if (!isNetTimeValid()) {
            return false
        }
        // 持续10天可以看到入口
        // 获取纽约时间的 12 月 24 日 0 时开始的时间戳（毫秒）
        val nyStartTimeMillis = getNYStartTimeMillis()
        val nyEndTimeMillis = nyStartTimeMillis + 10 * 24 * 60 * 60 * 1000L // 7 天后的时间戳

        // 获取当前时间
        val currentBeijingTime = getCurrentTime()

        return currentBeijingTime <= nyEndTimeMillis
    }
}