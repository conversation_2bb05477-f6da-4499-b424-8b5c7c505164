package com.moyu.chuanqirensheng.feature.event.handler

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.core.model.Award
import com.moyu.core.model.Event

class NonePlayHandler(
    override val skipWin: Boolean = true,
    override val hasLayout: MutableState<Boolean> = mutableStateOf(false),
    override val playId: Int = 0
): PlayHandler() {

    @Composable
    override fun Layout(event: Event) { }

    override suspend fun onEventSelect(event: Event) {
        EventManager.doEventResult(event, true)
    }

    @Composable
    override fun HandlerButtons(event: Event) { }

    @Composable
    override fun PostAwardButtons(event: Event) {
        // do nothing
    }

    override fun getAwards(): List<Award> {
        return emptyList()
    }
}