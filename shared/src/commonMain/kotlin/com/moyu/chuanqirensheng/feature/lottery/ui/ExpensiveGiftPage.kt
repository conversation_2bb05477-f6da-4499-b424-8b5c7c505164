package com.moyu.chuanqirensheng.feature.lottery.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentLotteryMoney
import com.moyu.chuanqirensheng.feature.sell.ui.OneSellItem
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding44
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding80


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ExpensiveGiftPage() {
    val shopChests = LotteryManager.packages.sortedBy { it.priority }
    LaunchedEffect(Unit) {
        LotteryManager.createGifts()
    }
    Column(
        modifier = Modifier
            .fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.size(padding10))
        Row(
            Modifier
                .align(Alignment.End)
                .padding(end = padding6)
        ) {
            CurrentLotteryMoney()
            CurrentKeyPoint(showPlus = true)
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(padding44))
            FlowRow(
                modifier = Modifier,
                horizontalArrangement = Arrangement.spacedBy(padding16),
                verticalArrangement = Arrangement.spacedBy(padding4),
            ) {
                shopChests.forEach {
                    OneSellItem(it)
                }
            }
            Spacer(modifier = Modifier.size(padding80))
        }
    }
}