package com.moyu.chuanqirensheng.feature.drawactivity.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs.drawPoolDialog
import com.moyu.chuanqirensheng.feature.drawactivity.DrawActivityManager
import com.moyu.chuanqirensheng.feature.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_INIT_TIME_IN_A_WEEK
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.timeLeft
import com.moyu.chuanqirensheng.util.toDayHourMinuteSecond
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.GameLabel2
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.pool_title
import shared.generated.resources.time_left


@Composable
fun DrawActivityPage() {
    val leftUpdateTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(Unit) {
        while (true) {
            leftUpdateTime.longValue = timeLeft(
                getCurrentTime(), getLongFlowByKey(
                    KEY_DRAW_INIT_TIME_IN_A_WEEK
                ), 2
            )
            if (leftUpdateTime.longValue <= 1000) {
                delay(1000)
                goto(LOGIN_SCREEN)
            }
            delay(500)
        }
    }
    Column(
        Modifier.paint(
            painterResource(Res.drawable.common_big_frame),
            contentScale = ContentScale.FillBounds
        ).padding(vertical = padding10, horizontal = padding10)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth()
                .padding(horizontal = padding14), verticalAlignment = Alignment.CenterVertically
        ) {
            GameButton(
                modifier = Modifier,
                buttonSize = ButtonSize.MediumMinus,
                buttonStyle = ButtonStyle.Blue,
                text = stringResource(Res.string.pool_title),
            ) {
                drawPoolDialog.value = true
            }
            if (leftUpdateTime.longValue > 0 && DrawActivityManager.show()) {
                GameLabel2(
                    modifier = Modifier
                        .padding(start = padding12).size(padding120, padding30)) {
                    StrokedText(
                        text = stringResource(Res.string.time_left) + leftUpdateTime.longValue.toDayHourMinuteSecond(),
                        style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.weight(1f))
            CurrentKeyPoint(
                showPlus = true,
                showFrame = true
            )
        }
        Column(
            modifier = Modifier
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(vertical = padding8),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(padding16))
                ActivityCouponItem()
                Spacer(modifier = Modifier.size(padding16))
            }
        }
    }
}