package com.moyu.chuanqirensheng.feature.drawactivity

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.draw.MUST_ORANGE_DRAW
import com.moyu.chuanqirensheng.feature.gift.GiftManager.onDrawUnlockId
import com.moyu.chuanqirensheng.feature.quest.FOREVER
import com.moyu.chuanqirensheng.feature.quest.QuestEvent
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.getMaxAge
import com.moyu.chuanqirensheng.feature.quest.onTaskDrawActivityCard
import com.moyu.chuanqirensheng.feature.sell.SellManager.isDrawing
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_DRAW_ACTIVITY
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_ACTIVITY_COUPON_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_INIT_RAW_TIME
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_INIT_TIME_IN_A_WEEK
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_WEEK_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_DRAW_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_DRAW_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_ONE_TURN_CONSUME_ACTIVITY_COUPON
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.mapData
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.util.gapWeek
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.Quest
import com.moyu.core.model.toAward
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlin.math.min
import kotlin.random.Random


object DrawActivityManager {
    val drawTasks = mutableStateListOf<Quest>()
    val savedWeekNum = mutableStateOf(0)

    fun init() {
        createDrawTasks()
    }

    fun createDrawTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
        if (drawTasks.isEmpty()) {
            try {
                getListObject<Quest>(KEY_GAME_DRAW_TASK).let {
                    drawTasks.addAll(it.map { task ->
                        repo.gameCore.getGameTaskById(task.id)
                            .copy(done = task.done, opened = task.opened)
                    })
                }
            } catch (e: Exception) {
                println(e.toString())
            }
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (drawTasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getMaxAge()
            )
            drawTasks.addAll(filteredTasks.filter { it.isDrawActivityTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM))

            setLongValueByKey(KEY_GAME_DRAW_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_DRAW_TASK, drawTasks)
        }
    }

    fun getActivityDrawAward(index: Int): Award {
        val drawItems = repo.gameCore.getDrawPool().filter { it.isDrawActivity() }
        // 计算所有符合条件抽奖项的总概率
        val totalRate = drawItems.sumOf { it.rate }
        // 生成一个随机数 [0, totalRate) 之间
        val randomValue = randomActivity(index).nextDouble(0.0, totalRate)
        // 遍历抽奖项，根据概率选择
        var cumulativeRate = 0.0
        drawItems.forEach { item ->
            cumulativeRate += item.rate
            if (randomValue <= cumulativeRate) {
                return repo.gameCore.getPoolById(item.pool).toAward(random = randomActivity(index))
            }
        }
        return Award()
    }

    fun getActivityOrangeAward(): Award {
        val drawItems = repo.gameCore.getDrawPool().filter { it.isDrawActivity() }
        return repo.gameCore.getPoolById(drawItems.dropLast(1).last().pool).toAward(random = randomActivity())
    }

    suspend fun buyActivityCoupon(repeat: Int = 1) {
        if (isDrawing) {
            return
        }
        isDrawing = true
        GameCore.instance.onBattleEffect(SoundEffect.Draw)
        var totalAward = Award()
        repeat(repeat) {
            var this10DrawHaveOrange = false
            val award = (1..10).map {
                // 确保橙卡
                val singleAward =
                    if (needOrangeActivityThisTurn() && it == 10 && !this10DrawHaveOrange) {
                        getActivityOrangeAward()
                    } else getActivityDrawAward(it)
                this10DrawHaveOrange =
                    if (!this10DrawHaveOrange) singleAward.outAllies.any { it.quality >= 3 } || singleAward.outEquips.any { it.quality >= 3 } else true
                totalAward += singleAward
                Dialogs.drawActivityResultDialog.value = totalAward
                // delay给main线程时间去更新UI
                delay(100)
                singleAward
            }.reduce { acc, award -> acc + award }
            if (this10DrawHaveOrange) {
                setActivityOrangeDraw(0)
            } else {
                setActivityOrangeDraw(getIntFlowByKey(KEY_ONE_TURN_CONSUME_ACTIVITY_COUPON) + 10)
            }
            increaseIntValueByKey(KEY_CONSUME_ACTIVITY_COUPON_ALL, 1)
            AppWrapper.globalScope.async {
                val lack = 10
                AwardManager.gainAward(
                    award.copy(
                        key = -lack * repo.gameCore.getActivityCouponRate()
                    )
                )
                award.outEquips.map { it.id }.forEach {
                    onDrawUnlockId(it)
                }
                award.outAllies.map { it.id }.forEach {
                    onDrawUnlockId(it)
                }
                delay(600)
                if (award.outEquips.any { it.quality == 4 } || award.outAllies.any { it.quality == 4 }) {
                    MusicManager.playSound(SoundEffect.DrawRed)
                } else if (award.outEquips.any { it.quality == 3 } || award.outAllies.any { it.quality == 3 }) {
                    MusicManager.playSound(SoundEffect.DrawOrange)
                }
            }
        }
        onTaskDrawActivityCard(10 * repeat)
        isDrawing = false
    }

    fun randomActivity(index: Int = 0): Random {
        return Random(
            (gameSdkDefaultProcessor().getObjectId()?: "empty").toCharArray().sumOf { it.code } + getIntFlowByKey(
                KEY_CONSUME_ACTIVITY_COUPON_ALL
            ).toLong() * 951 + index * 63917 + LoginManager.instance.loginData.value.serverData.serverId)
    }

    fun getActivityLeftDraw(): Int {
        return MUST_ORANGE_DRAW - getIntFlowByKey(KEY_ONE_TURN_CONSUME_ACTIVITY_COUPON)
    }


    fun needOrangeActivityThisTurn(): Boolean {
        return getActivityLeftDraw() <= 10
    }

    fun setActivityOrangeDraw(value: Int) {
        setIntValueByKey(KEY_ONE_TURN_CONSUME_ACTIVITY_COUPON, value)
    }

    fun show(): Boolean {
        if (getLongFlowByKey(KEY_DRAW_INIT_TIME_IN_A_WEEK) == 0L) {
            return true
        }
        return ((getCurrentTime() - getLongFlowByKey(KEY_DRAW_INIT_TIME_IN_A_WEEK)) / (1000 * 60 * 60 * 24)) % 7 < 2
    }

    fun refresh() {
        if (getLongFlowByKey(KEY_DRAW_INIT_TIME_IN_A_WEEK) == 0L || getLongFlowByKey(
                KEY_DRAW_INIT_RAW_TIME
            ) == 0L
        ) {
            setLongValueByKey(KEY_DRAW_INIT_TIME_IN_A_WEEK, getCurrentTime())
            setLongValueByKey(KEY_DRAW_INIT_RAW_TIME, getCurrentTime())
        }
        savedWeekNum.value = getIntFlowByKey(KEY_DRAW_WEEK_NUM)

        val currentWeekIndex = gapWeek(
            getCurrentTime(), getLongFlowByKey(
                KEY_DRAW_INIT_TIME_IN_A_WEEK
            )
        )
        if (currentWeekIndex != savedWeekNum.value) {
            // 更新周数
            savedWeekNum.value = currentWeekIndex
            setIntValueByKey(KEY_DRAW_WEEK_NUM, currentWeekIndex)
            // 清除任务记录
            drawTasks.clear()
            setListObject<Quest>(KEY_GAME_DRAW_TASK, emptyList())
            createDrawTasks()
            // 所有的抽卡记录清理掉
            mapData.keys.filter {
                it.startsWith(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ACTIVITY_DRAW.id
                )
            }.forEach {
                setIntValueByKey(it, 0)
            }

            // 更新刷新时间，时间还是保持和KEY_LOTTERY_INIT_TIME初始一样，只是如果超过了一周，往前推进一周
            val rawGapTimeFromAWeek =
                getLongFlowByKey(KEY_DRAW_INIT_RAW_TIME) % (1000 * 60 * 60 * 24 * 7)
            val currentGapTimeFromAWeek = getCurrentTime() % (1000 * 60 * 60 * 24 * 7)
            setLongValueByKey(
                KEY_DRAW_INIT_TIME_IN_A_WEEK,
                getCurrentTime() - currentGapTimeFromAWeek + rawGapTimeFromAWeek
            )
        }
    }

    fun getReds(): List<Boolean> {
        return listOf(false, drawTasks.any {
            QuestManager.getTaskDoneFlow(it)
                    && !it.opened
        })
    }

    fun getAwardPool(): Award {
        val drawItems = repo.gameCore.getDrawPool().filter { it.isDrawActivity() }
        return drawItems.map {
            val pool = repo.gameCore.getPoolById(it.pool)
            // todo 抽奖都是把pool的totalNum设置1，这里要显示所有奖项，所以强制设置为pool的size
            pool.copy(totalNum = pool.type.size).toAward()
        }
            .reduce { acc, award -> acc + award }
    }

    fun hasRed(): Boolean {
        return drawTasks.any {
            QuestManager.getTaskDoneFlow(it)
                    && !it.opened
        }
    }

    fun unlocked(): Boolean {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_DRAW_ACTIVITY)
        return UnlockManager.getUnlockedFlow(unlock)
    }
}