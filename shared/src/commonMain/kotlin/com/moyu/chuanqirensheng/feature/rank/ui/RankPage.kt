package com.moyu.chuanqirensheng.feature.rank.ui

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import coil3.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.api.getRanks
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.getPlatform
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.platform.refreshRankList
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.text.getRankFrame
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.padding64
import com.moyu.chuanqirensheng.ui.theme.padding7
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.util.Platform
import com.moyu.chuanqirensheng.util.dpToPixel
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.serialization.builtins.ListSerializer
import org.jetbrains.compose.resources.painterResource


@Composable
fun RankPage(
    type: Int,
    data: MutableState<List<RankData>>,
    filter: (RankData) -> Boolean = { true },
    content: @Composable BoxScope.(RankData, Int) -> Unit
) {
    LaunchedEffect(Unit) {
        try {
            if (data.value.isEmpty()) {
                if (type == 1 && getPlatform() == Platform.Desktop) {
                    refreshRankList(type) {
                        data.value = it.filter(filter)
                    }
                } else {
                    getRanks(
                        platformChannel(), type
                    ).let {
                        data.value =
                            json.decodeFromString(ListSerializer(RankData.serializer()), it.message)
                                .filter(filter)
                    }
                }
            }
        } catch (e: Exception) {
//            AppWrapper.getStringKmp(Res.string.net_error_retry).toast()
        }
    }
    Spacer(modifier = Modifier.size(padding10))
    Leaderboard(data.value, content)
}

@Composable
fun Leaderboard(data: List<RankData>, content: @Composable BoxScope.(RankData, Int) -> Unit) {
    Box(Modifier.fillMaxSize()) {
        LazyColumn(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(padding2),
            modifier = Modifier
                .fillMaxSize().padding(bottom = padding28)
        ) {
            // 如果有至少 3 条数据，先展示“领奖台”
            item {
                Row(
                    horizontalArrangement = Arrangement.SpaceAround,
                    verticalAlignment = Alignment.Bottom,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 第二名
                    PodiumItem(
                        user = data.getOrNull(1),
                        rankTitle = "rank_title_2",
                        rankBoard = "rank_board_2",
                        rankIndex = 1,
                        content = content
                    )
                    // 第一名
                    PodiumItem(
                        user = data.getOrNull(0),
                        rankTitle = "rank_title_1",
                        rankBoard = "rank_board_1",
                        rankIndex = 0,
                        content = content
                    )
                    // 第三名
                    PodiumItem(
                        user = data.getOrNull(2),
                        rankTitle = "rank_title_3",
                        rankBoard = "rank_board_3",
                        rankIndex = 2,
                        content = content
                    )
                }
            }

            if (data.size > 3) {
                items(data.size - 3) { index ->
                    val actualRank = index + 4  // 因为前三名已经在上面了，所以从第4名开始
                    SingleRecord(data[index + 3], actualRank, content)
                    if (index == data.size - 4) {
                        // 最后一个 item 的时候，添加底部间距
                        Spacer(modifier = Modifier.size(padding100))
                    }
                }
            }

        }
        Box(Modifier.fillMaxWidth().align(Alignment.BottomCenter)) {
            data.indexOfFirst { it.userId == gameSdkDefaultProcessor().getObjectId() }.takeIf { it != -1 }
                ?.let {
                    SingleRecord(data[it], it + 1, content)
                }
        }
    }
}


@Composable
fun PodiumItem(
    user: RankData?,
    rankTitle: String,  // 比如 R.drawable.rank_title_2
    rankBoard: String,  // 比如 R.drawable.rank_board_2
    rankIndex: Int,               // 用于 content() 函数里做不同逻辑
    content: @Composable BoxScope.(RankData, Int) -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Bottom,
        modifier = Modifier.width(padding100)
    ) {
        user?.let {
            // Create an infinite transition
            val infiniteTransition = rememberInfiniteTransition()

            // Define the bouncing animation
            val bounceOffset by infiniteTransition.animateFloat(
                initialValue = 0f,
                targetValue = 8f, // Maximum bounce height in dp
                animationSpec = infiniteRepeatable(
                    animation = tween(durationMillis = 1500, easing = LinearEasing),
                    repeatMode = RepeatMode.Reverse // Reverses the animation for smooth up-down motion
                )
            )

            // 顶部名次图 (Crown with bouncing effect)
            Box(
                Modifier
                    .width(padding72)
                    .graphicsLayer {
                        translationY = bounceOffset.dpToPixel() // Apply bouncing offset
                    }, contentAlignment = Alignment.Center
            ) {
                Image(
                    contentScale = ContentScale.FillWidth,
                    painter = painterResource(kmpDrawableResource(rankTitle)),
                    contentDescription = null
                )
                StrokedText(modifier = Modifier.graphicsLayer {
                    translationY = padding7.toPx()
                }, text = (rankIndex + 1).toString(), style = MaterialTheme.typography.h1)
            }

            // 用户头像
            Box(Modifier.size(padding64)) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = painterResource(rankIndex.getRankFrame()),
                    contentDescription = null
                )
                Image(
                    modifier = Modifier
                        .fillMaxSize().padding(padding4).clip(RoundedCornerShape(padding4)),
                    painter = if (user.userPic.startsWith("http")) rememberAsyncImagePainter(user.userPic) else kmpPainterResource(
                        user.userPic
                    ),
                    contentDescription = null
                )
            }
            // 底部牌子
            Box {
                Image(
                    modifier = Modifier.fillMaxWidth(),
                    contentScale = ContentScale.FillWidth,
                    painter = painterResource(kmpDrawableResource(rankBoard)),
                    contentDescription = null
                )
                Column(
                    Modifier.fillMaxWidth().matchParentSize(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(Modifier.size(padding5))
                    StrokedText(
                        text = user.userName,
                        maxLines = 1,
                        style = MaterialTheme.typography.h6
                    )
                    Box(Modifier.weight(1f).graphicsLayer {
                        translationY = padding3.toPx()
                    }, contentAlignment = Alignment.Center) {
                        content(user, rankIndex)
                    }
                }
            }
        }
    }
}

