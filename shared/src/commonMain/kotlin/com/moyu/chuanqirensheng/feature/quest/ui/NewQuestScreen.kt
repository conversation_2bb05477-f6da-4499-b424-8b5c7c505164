package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.mission.MissionManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageLargeFrame
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Quest
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_arrow_left
import shared.generated.resources.common_arrow_right
import shared.generated.resources.main_frame1
import shared.generated.resources.new_quest
import shared.generated.resources.new_task_chapter
import shared.generated.resources.next_page
import shared.generated.resources.prev_page
import kotlin.math.min


@Composable
fun DrawLevel(
    modifier: Modifier = Modifier,
    cheatLevel: String,
    frame: Painter = painterResource(Res.drawable.main_frame1)
) {
    Box(modifier = modifier.height(imageLarge), contentAlignment = Alignment.CenterStart) {
        Image(
            modifier = Modifier
                .height(imageLarge),
            contentScale = ContentScale.FillHeight,
            painter = frame,
            contentDescription = null
        )
        StrokedText(
            modifier = Modifier.padding(start = padding10),
            text = cheatLevel,
            style = MaterialTheme.typography.h1
        )
    }
}


@Composable
fun NewQuestScreen() {
    val tasks = remember {
        mutableStateListOf<Quest>()
    }
    val refresh = remember {
        mutableIntStateOf(0)
    }
    val pagerState = remember {
        mutableStateOf(0)
    }
    val scrollToTarget = remember {
        mutableIntStateOf(-1)
    }
    LaunchedEffect(refresh.intValue.toString()) {
        if (!isNetTimeValid()) {
            refreshNetTime()
        }
        QuestManager.createNewTasks()
        // 完成的任务排前面，已领取的排最后
        QuestManager.newTasks.map {
            it.copy(done = QuestManager.getTaskDoneFlow(it))
        }.sortedBy { it.order }.sortedBy { it.order }
            .sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }.apply {
            tasks.clear()
            tasks.addAll(this)
        }
        delay(100)
        val newQuestInitPageIndex = QuestManager.getInitPageIndex(tasks)
        val missionInitPageIndex = MissionManager.getInitPageIndex()
        if (scrollToTarget.intValue == -1) {
            // 只有首次才滚动，其他时候，不要乱跳
            scrollToTarget.intValue = min(newQuestInitPageIndex, missionInitPageIndex)
            pagerState.value = scrollToTarget.intValue
        }
    }
    GameBackground(
        background = kmpDrawableResource("environment_${pagerState.value + 1}"),
        bgMask = B50,
        title = stringResource(Res.string.new_quest) + (pagerState.value + 1)
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            val innerTask = QuestManager.getNewQuestByPageIndex(tasks, pagerState.value)
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = padding48)
                    .verticalScroll(rememberScrollState())
            ) {
                innerTask.forEach { task ->
                    SingleQuest(task) {
                        refresh.intValue += 1
                    }
                    Spacer(modifier = Modifier.size(padding5))
                }
            }
        }
        if (pagerState.value > 0) {
            EffectButton(modifier = Modifier.align(Alignment.CenterStart), onClick = {
                pagerState.value -= 1
            }) {
                Image(
                    modifier = Modifier.size(imageLargeFrame),
                    painter = painterResource(Res.drawable.common_arrow_left),
                    contentDescription = stringResource(
                        Res.string.prev_page
                    )
                )
            }
        }
        if (pagerState.value < QuestManager.getNewQuestPageCount(tasks)- 1) {
            EffectButton(modifier = Modifier.align(Alignment.CenterEnd), onClick = {
                pagerState.value += 1
            }) {
                Image(
                    modifier = Modifier.size(imageLargeFrame),
                    painter = painterResource(Res.drawable.common_arrow_right),
                    contentDescription = stringResource(
                        Res.string.next_page
                    )
                )
            }
        }
        DrawLevel(
            modifier = Modifier
                .align(Alignment.TopStart),
            cheatLevel = stringResource(Res.string.new_task_chapter, pagerState.value + 1),
        )
    }
}