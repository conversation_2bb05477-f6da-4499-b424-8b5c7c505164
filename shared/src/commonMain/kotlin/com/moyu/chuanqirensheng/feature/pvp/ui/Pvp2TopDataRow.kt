package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.resource.CurrentPvp2Score
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.all_pk
import shared.generated.resources.today_pk

@Composable
fun Pvp2TopDataRow(modifier: Modifier) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Spacer(modifier = Modifier.size(padding22))
        CurrentPvp2Score(modifier = Modifier.padding(start = padding2))
        Spacer(modifier = Modifier.size(padding2))
        Spacer(modifier = Modifier.size(padding12))
        Column {
            StrokedText(
                text = stringResource(
                    Res.string.today_pk,
                    Pvp2Manager.pkWinToday.value,
                    Pvp2Manager.pkLoseToday.value
                ),
                style = MaterialTheme.typography.h6
            )
            StrokedText(
                text = stringResource(
                    Res.string.all_pk,
                    Pvp2Manager.pkWin.value,
                    Pvp2Manager.pkLose.value
                ),
                style = MaterialTheme.typography.h6
            )
        }
        Spacer(modifier = Modifier.size(padding12))
    }
}