package com.moyu.chuanqirensheng.feature.sign.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.quest.getLoginDays
import com.moyu.chuanqirensheng.feature.sign.SignManager
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.common_choose
import shared.generated.resources.day_of
import shared.generated.resources.red_icon
import shared.generated.resources.sign_title

@Composable
fun SignScreen() {
    val signs = SignManager.getShowSigns()
    GameBackground(title = stringResource(Res.string.sign_title)) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                ),
        )
        Column {
            Spacer(modifier = Modifier.size(padding6))
            LazyVerticalGrid(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding10).padding(bottom = padding10),
                columns = GridCells.Fixed(4),
                verticalArrangement = Arrangement.spacedBy(padding10),
                content = {
                    items(signs.size) { index ->
                        val sign = signs[index]
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            StrokedText(
                                text = stringResource(Res.string.day_of, sign.day),
                                color = if (getLoginDays() >= sign.day) Color.White else W50,
                                style = MaterialTheme.typography.h3
                            )
                            Box(contentAlignment = Alignment.Center) {
                                val param = if (getLoginDays() >= sign.day) {
                                    defaultParam.copy(
                                        itemSize = ItemSize.Large,
                                        showName = false,
                                        showEffect = null
                                    ) {
                                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                                            SignManager.gain(sign)
                                        }
                                    }
                                } else {
                                    defaultParam.copy(
                                        itemSize = ItemSize.Large,
                                        showName = false,
                                        showEffect = null
                                    )
                                }
                                AwardList(
                                    award = sign.toAward(),
                                    param = param,
                                    paddingHorizontalInDp = padding0,
                                    paddingVerticalInDp = padding0
                                )
                                if (SignManager.isSignGained(sign)) {
                                    Image(
                                        painter = painterResource(Res.drawable.common_choose),
                                        contentDescription = null,
                                        modifier = Modifier.size(imageLarge)
                                    )
                                }
                                if (getLoginDays() >= sign.day && !SignManager.isSignGained(sign)) {
                                    Image(
                                        modifier = Modifier
                                            .align(Alignment.TopEnd)
                                            .size(imageSmall),
                                        painter = painterResource(Res.drawable.red_icon),
                                        contentDescription = null
                                    )
                                }
                            }
                        }
                    }
                })
        }
    }
}