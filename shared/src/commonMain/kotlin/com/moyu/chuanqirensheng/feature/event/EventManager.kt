package com.moyu.chuanqirensheng.feature.event

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.event.handler.BattlePlayHandler
import com.moyu.chuanqirensheng.feature.event.handler.DIRECT_AWARD_EVENT
import com.moyu.chuanqirensheng.feature.event.handler.DirectAwardHandler
import com.moyu.chuanqirensheng.feature.event.handler.HALO2_BATTLE
import com.moyu.chuanqirensheng.feature.event.handler.HALO_BATTLE
import com.moyu.chuanqirensheng.feature.event.handler.LOTTERY_PLAY_EVENT
import com.moyu.chuanqirensheng.feature.event.handler.LotteryPlayHandler
import com.moyu.chuanqirensheng.feature.event.handler.NonePlayHandler
import com.moyu.chuanqirensheng.feature.event.handler.PlayHandler
import com.moyu.chuanqirensheng.feature.event.handler.SELECT_AWARD_EVENT
import com.moyu.chuanqirensheng.feature.event.handler.SIEGE_BATTLE
import com.moyu.chuanqirensheng.feature.event.handler.SelectAwardHandler
import com.moyu.chuanqirensheng.feature.quest.onTaskAge
import com.moyu.chuanqirensheng.feature.quest.onTaskDoneEvent
import com.moyu.chuanqirensheng.feature.quest.onTaskEnding
import com.moyu.chuanqirensheng.feature.quest.onTaskEnterEvent
import com.moyu.chuanqirensheng.feature.skill.AgeEvent
import com.moyu.chuanqirensheng.feature.skill.EnterEvent
import com.moyu.chuanqirensheng.feature.skill.FailedEvent
import com.moyu.chuanqirensheng.feature.skill.SucceededEvent
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.review.GameReviewManager
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.Timing
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.first_day_tips

object EventManager {
    val selectedEvent = mutableStateOf<Event?>(null)// 当前选中事件
    val selectionEvents = mutableStateListOf<Event>() // 当前可选事件
    val eventRecorder = EventRecorder()
    var pool by mutableStateOf<List<Event>>(emptyList())

    private val eventHandlerClasses = hashMapOf(
        1 to { DirectAwardHandler(playId = DIRECT_AWARD_EVENT) },
        2 to { SelectAwardHandler(playId = SELECT_AWARD_EVENT) },
        3 to { BattlePlayHandler(playId = HALO_BATTLE, hasLayout = mutableStateOf(false)) },
        4 to { BattlePlayHandler(playId = HALO2_BATTLE, hasLayout = mutableStateOf(false)) },
        5 to { BattlePlayHandler(playId = SIEGE_BATTLE, hasLayout = mutableStateOf(false)) },
        6 to { LotteryPlayHandler(playId = LOTTERY_PLAY_EVENT) },
    )
    private val playHandlers = hashMapOf<Int, PlayHandler>()

    fun onNewGame() {
        playHandlers.clear()
        eventRecorder.clear()
        selectedEvent.value = null
        selectionEvents.clear()
        repo.onBattleInfo(
            AppWrapper.getStringKmp(Res.string.first_day_tips),
            type = BattleInfoType.DayEvent,
            play = 0
        )
    }

    fun generateNextEvent(): Event? {
        return getNextEvents().shuffled().firstOrNull()
    }

    fun getNextEvents(): List<Event> {
        if (selectionEvents.isNotEmpty()) { // 保护
            return selectionEvents
        }

        if (DebugManager.allEvent) {
            return repo.gameCore.getEventPool()
                .filter { it.levelId == StageManager.currentStage.value.id }.map { it.createUUID() }
        }

        val age = BattleManager.adventureProps.value.age
        if (pool.isEmpty() || pool.first().levelId != StageManager.currentStage.value.id) {
            pool = repo.gameCore.getEventPool()
                .filter { it.levelId == StageManager.currentStage.value.id }
        }
        val innerList = if (age != 30 && age != 60 && pool.any { it.same.contains(age) || it.same.contains(age - 30) }) {
            pool.filter { it.same.contains(age) || it.same.contains(age - 30) }
        } else {
            pool.filter { it.same.first() == 0 }.filter { age >= it.age[0] && age <= it.age[1] }
        }
            .filter { it.id != eventRecorder.usedEvents.lastOrNull()?.id }
            .filter { event -> !(!DebugManager.repeatEvent && event.isRepeat == 0 && getUsedEvents().any { it.id == event.id }) }
            .shuffled(RANDOM)
//        println("debug event: 关卡=${StageManager.currentStage.value.id} 天数=${age}, 符合条件的事件数:${innerList.size}")
        val allEvents = if (DebugManager.unlockTalents) innerList else {
            selectEvents(innerList.toMutableList())
        }.map { it.createUUID() }

        val events = allEvents.take(EVENT_SIZE)
        selectionEvents.clear()
        selectionEvents.addAll(events)
        return events.map { it.createUUID() }
    }

    fun selectEvents(innerList: MutableList<Event>, targetCount: Int = EVENT_SIZE): List<Event> {
        val selectedEvents = mutableListOf<Event>()
        while (selectedEvents.size < targetCount && innerList.isNotEmpty()) {
            val selectedEvent = selectEventByWeight(innerList)
            selectedEvents.add(selectedEvent)
            // 移除当前选出来的事件
            innerList.removeAll { it.uuid == selectedEvent.uuid }
        }

        return selectedEvents
    }

    private fun selectEventByWeight(events: List<Event>): Event {
        val groups = events.groupBy { it.play }.entries
        val totalWeight = groups.sumOf { it.value.first().weight }

        if (totalWeight == 0) return events.shuffled().first()
        val randomWeight = RANDOM.nextInt(totalWeight) + 1
//        println("debug event:随机逻辑1：总权重=${totalWeight} 随机数(1到总权重之间)=${randomWeight}")
//        println("debug event:随机逻辑2：当前事件列表[位置，play，权重]=${groups.mapIndexed { index, group -> Triple(index + 1, group.key, group.value.first().weight) }}")
        var currentWeight = 0
        for (group in groups) {
            currentWeight += group.value.first().weight
            if (randomWeight <= currentWeight) {
//                println("debug event:随机逻辑3：从第一个开始累加权限，选出首次总权限大于等于随机权限的事件play是第${groups.indexOfFirst { it.key == group.key } + 1}个事件类别play=${group.key}")
                val event = events.filter { it.play == group.key }.shuffled(RANDOM).first()
//                println("debug event:随机逻辑4：在选中的play里，随机一个事件=${event.id} ${event.name}")
                return event
            }
        }
        throw IllegalStateException("Should never reach here if the list is not empty")
    }

    suspend fun selectEvent(event: Event) {
        if (!DebugManager.repeatEvent && event.isRepeat == 0 && getUsedEvents().any { it.id == event.id }) {
            GameCore.instance.onBattleEffect(SoundEffect.Click)
            return
        }
        if (event.isKeyBattle() && getUsedEvents().any { it.isKeyBattle() }) {
            // 游戏最后一关打完，有一瞬间玩家还能再点击开始战斗，就会有两次攻城战，这里屏蔽下
            return
        }
        GameCore.instance.onBattleEffect(SoundEffect.SelectEvent)
        selectedEvent.value = event.copy(selectAge = BattleManager.getAge())
        ContinueManager.selectEvent(event)

        doEvent(event)
        playerMusicByScreen()
    }

    fun getOrCreateHandler(event: Event): PlayHandler {
        val handler = playHandlers[event.id]
        return if (handler == null) {
            val newHandler = eventHandlerClasses[event.play]?.invoke() ?: NonePlayHandler()
            playHandlers[event.id] = newHandler
            newHandler
        } else {
            handler
        }
    }

    private suspend fun doEvent(event: Event) {
        onTaskEnterEvent(event)
        repo.onBattleInfo(
            event.name + if (isLite()) event.id else "",
            play = event.play,
            type = BattleInfoType.DayEvent,
            day = BattleManager.getAge()
        )
        if (event.play != 1) {
            // play == 1为固定奖励，直接和showText合并，不单独多一条
            repo.onBattleInfo(event.showText, play = event.play, type = BattleInfoType.ExtraSkill)
        }
        adventureSkillTrigger(triggerSkill = EnterEvent.copy(mainId = event.play))
        val handler = getOrCreateHandler(event)
        handler.eventSelect(event)
    }

    fun doEventBattleResult(
        event: Event?,
        result: Boolean,
        forceKill: Boolean = true
    ) {
        if (event == null) return
        if (getOrCreateHandler(event).eventFinished.value) return

        // 战斗失败
        playerMusicByScreen() // 音乐
        AppWrapper.globalScope.launch(Dispatchers.Main) {
            // 需要杀死你的出战军团卡，因为可能你强行退出战斗了
            if (forceKill) {
                repo.battle.value.getAllPlayers().filter { !it.isDeath() }.forEach {
                    BattleManager.updateAllyInGameById(it, 0)
                }
            }
            BattleManager.checkAnyAllyDied()
            getOrCreateHandler(event).apply {
                eventFinished.value = true
                eventResult.value = result
                doEventResult(event, result)
            }
        }
    }

    fun doEventResult(event: Event, result: Boolean) {
        val handler = getOrCreateHandler(event)
        handler.eventFinished.value = true
        val award = handler.getEventAward().copy(showQuestion = false)

        if (event.dialogType == 1) {
            // 1=普通对话时
            goNextEvent(event, result, award)
        } else if (event.dialogType == 2 && result) {
            // 结束后对话
        } else if (event.dialogType == 3 && !result) {
            // 失败后对话
        } else {
            goNextEvent(event, result, award)
        }
    }

    private fun goNextEvent(event: Event, result: Boolean, award: Award? = null) {
        AppWrapper.globalScope.launch(Dispatchers.Main) {
            if (event.play == 2) {
                val handler = getOrCreateHandler(event)
                // 通过奖励判定玩家是选择了哪个选项
                if (handler.eventChoice.value == 1) {
                    repo.onBattleInfo(
                        event.loseText,
                        play = event.play,
                        type = BattleInfoType.ExtraSkill,
                        award = handler.getEventAward()
                    )
                } else {
                    repo.onBattleInfo(
                        event.winText,
                        play = event.play,
                        type = BattleInfoType.ExtraSkill,
                        award = handler.getEventAward()
                    )
                }
            } else if (event.play == HALO2_BATTLE && result) { // play == 1为固定奖励，直接和showText合并，不单独多一条
                val choiceText = if (getOrCreateHandler(event).eventChoice.value == 0) {
                    event.winText
                } else {
                    event.loseText
                }
                repo.onBattleInfo(
                    choiceText, BattleInfoType.ExtraSkill, play = event.play, award = award
                )
            } else if (event.isBattle()) {
                if (result) {
                    repo.onBattleInfo(
                        event.startText, BattleInfoType.ExtraSkill, play = event.play, award = award
                    )
                }
            } else if (event.play == LOTTERY_PLAY_EVENT) {
                repo.onBattleInfo(
                    event.startText,
                    BattleInfoType.ExtraSkill,
                    play = event.play,
                    award = award
                )
            } else {
                repo.onBattleInfo(
                    event.showText,
                    BattleInfoType.ExtraSkill,
                    play = event.play,
                    award = award
                )
            }
            award?.let {
                AwardManager.gainAward(it)
            }
            gotoNextEvent(event, result)
        }
    }

    fun getUsedEvents(startAge: Int? = null): List<Event> {
        return startAge?.let {
            eventRecorder.usedEvents.filter { it.selectAge >= startAge }
        } ?: eventRecorder.usedEvents
    }

    fun getSucceededEvents(startAge: Int? = null): List<Event> {
        return startAge?.let {
            eventRecorder.succeededEvents.filter { it.selectAge >= startAge }
        } ?: eventRecorder.succeededEvents
    }

    fun getFailedEvents(startAge: Int? = null): List<Event> {
        return startAge?.let {
            eventRecorder.failedEvents.filter { it.selectAge >= startAge }
        } ?: eventRecorder.failedEvents
    }

    suspend fun gotoNextEvent(
        event: Event?,
        pass: Boolean,
        agePlus: Int = 1,
        surrender: Boolean = false,
    ) {
        event?.let {
            eventRecorder.addResult(event, pass)
        }
        BattleManager.onNextEvent()
        selectionEvents.clear()
        if (getUsedEvents().isNotEmpty()) {
            if (pass) {
                adventureSkillTrigger(triggerSkill = SucceededEvent.copy(mainId = event?.play ?: 0))
            } else {
                adventureSkillTrigger(triggerSkill = FailedEvent.copy(mainId = event?.play ?: 0))
            }
        }
        val story = EndingManager.saveEnding(
            BattleManager.you.value,
            eventRecorder.usedEvents,
            pass
        )
        if (pass && event != null) {
            onTaskDoneEvent(event)
        }
        event?.let {
            onTaskEnding(it, pass)
        }
        if (!pass) {
            EndingManager.ending(story)
        } else if (event?.isEnd == true) {
            EndingManager.ending(story)
        } else if (BattleManager.getGameAllies().all { it.isDead() }) {
            EndingManager.ending(story)
        } else if (BattleManager.getAge() == StageManager.currentStage.value.limit) {
            EndingManager.ending(story)
        } else if (surrender) {
            EndingManager.ending(story)
        } else {
            adventureSkillTrigger(triggerSkill = AgeEvent)
            BattleManager.gainAdventureProp(AdventureProps(age = agePlus))
            repeat(agePlus) {
                BattleManager.oneYearPass()
            }
            onTaskAge(BattleManager.adventureProps.value.age)
            BattleManager.you.value.markSkillNewTurn(BattleManager.you.value)
            BattleManager.you.value.clearGrave(Timing.TurnBegin)
            BattleManager.updateYou()

            if (hasGoogleService()) {
                GameReviewManager.checkTriggerReviewDialog(BattleManager.getAge())
            }
            if (BattleManager.adventureProps.value.age > 0) {
                ContinueManager.onSelections(emptyList())
            }
            playerMusicByScreen()
            if (StageManager.currentStage.value.isShip()) {
                MusicManager.playSound(SoundEffect.ShipSail, loop = true)
            } else {
                MusicManager.playSound(SoundEffect.HorseWalk, loop = true)
            }

            // 每个事件后，把当前的角色放入战场，方便缩略图显示
            repo.setCurrentAllies(BattleManager.getBattleRoles(8))
            // 每个事件后，清空事件信息
            playHandlers.clear()
        }
    }

    fun extraIsGameFailed(): Boolean {
        if (repo.gameMode.value.isTowerMode() && TowerManager.targetLevel.value.type.first() == 4) {
            return repo.battleTurn.intValue > (TowerManager.targetLevel.value.playPara1.first())
        }
        return false
    }

    fun extraIsGameWin(): Boolean {
        return false
    }

    fun resetEventRecorder(used: List<Event>, success: List<Event>, failed: List<Event>) {
        eventRecorder.resetEvents(used, success, failed)
    }

    suspend fun startGame() {
        adventureSkillTrigger(triggerSkill = AgeEvent)
        BattleManager.gainAdventureProp(AdventureProps(age = 1))
        if (StageManager.currentStage.value.isShip()) {
            MusicManager.playSound(SoundEffect.ShipSail, loop = true)
        } else {
            MusicManager.playSound(SoundEffect.HorseWalk, loop = true)
        }
        // 每个事件后，把当前的角色放入战场，方便缩略图显示
        repo.setCurrentAllies(BattleManager.getBattleRoles(8))
    }

    fun getShowDay(): String {
        return selectedEvent.value?.selectAge?.toString()
            ?: BattleManager.adventureProps.value.age.toString()
    }
}