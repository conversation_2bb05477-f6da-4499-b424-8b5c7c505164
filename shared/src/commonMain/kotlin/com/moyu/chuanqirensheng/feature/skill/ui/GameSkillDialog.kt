package com.moyu.chuanqirensheng.feature.skill.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.skill.isMagic
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.magic


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun GameSkillDialog(show: MutableState<Boolean>) {
    show.value.takeIf { show.value }?.let {
        val list = BattleManager.getGameSkills().filter { it.isMagic() }
        PanelDialog(onDismissRequest = {
            show.value = false
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding4).verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                StrokedText(
                    text = stringResource(Res.string.magic),
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding19))
                FlowRow(
                    modifier = Modifier
                        .padding(horizontal = padding16),
                    overflow = FlowRowOverflow.Visible,
                    maxItemsInEachRow = 3,
                    horizontalArrangement = Arrangement.spacedBy(padding36)
                ) {
                    list.forEach { skill ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Spacer(modifier = Modifier.size(padding4))
                            SingleSkillView(
                                skill = skill,
                                showName = true,
                                showRed = false,
                                showStars = false,
                                itemSize = ItemSize.Large
                            )
                            Spacer(modifier = Modifier.size(padding4))
                        }
                    }
                }
            }
        }
    }
}
