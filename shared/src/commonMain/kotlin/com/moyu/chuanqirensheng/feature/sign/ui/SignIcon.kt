package com.moyu.chuanqirensheng.feature.sign.ui

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.feature.quest.getLoginDays
import com.moyu.chuanqirensheng.feature.router.SIGN_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.sign.SignManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_SIGN
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.MainIcon
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.icon_sign
import shared.generated.resources.sign_title

@Composable
fun SignIcon(itemSize: ItemSize) {
    MainIcon(
        itemSize = itemSize,
        unlocks = listOf(repo.gameCore.getUnlockById(UNLOCK_SIGN)),
        click = {
            goto(SIGN_SCREEN)
        },
        red = {
            SignManager.getShowSigns().any {
                getLoginDays() >= it.day && !SignManager.isSignGained(it)
            }
        },
        title = stringResource(Res.string.sign_title),
        icon = Res.drawable.icon_sign
    )
}
