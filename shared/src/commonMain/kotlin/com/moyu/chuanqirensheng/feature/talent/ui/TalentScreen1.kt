package com.moyu.chuanqirensheng.feature.talent.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.resource.CurrentDiamondPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentResourcesPoint
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding53
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.talent1Gif
import com.moyu.core.AppWrapper
import com.moyu.core.model.EMPTY_RESOURCES
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_choose
import shared.generated.resources.dark_line_horizontal
import shared.generated.resources.dark_line_vertical
import shared.generated.resources.diamond_not_enough
import shared.generated.resources.line_horizontal
import shared.generated.resources.line_vertical
import shared.generated.resources.one_click_talent
import shared.generated.resources.one_click_talent_postfix
import shared.generated.resources.one_click_talent_tips
import shared.generated.resources.resource1_not_enough_tips
import shared.generated.resources.star
import shared.generated.resources.talent1_frame_dark
import shared.generated.resources.talent1_frame_light
import shared.generated.resources.talent_locked_tips1
import shared.generated.resources.talent_title1

@Composable
fun TalentScreen1() {
    val talentHeight = padding80
    val lineWidth = padding10
    val itemSize = ItemSize.Large
    val talents = repo.gameCore.getTalent1Pool()
        .filter { it.type == 1 }
        .sortedBy { it.conditionNum }

    // 1) 先將天賦資料按每3個分組
    val chunkedTalents = talents.chunked(3)
    // 2) 交替反轉每一組
    val zigzagTalents = chunkedTalents.mapIndexed { index, chunk ->
        if (index % 2 == 1) chunk.reversed() else chunk
    }

    // 找到第一個沒有學習的天賦索引，用它的行索引來滾動
    val firstUnlearnedIndex =
        talents.indexOfFirst { (TalentManager.talents[it.mainId] ?: 0) < it.level }
    val firstUnlearnedChunkIndex =
        if (firstUnlearnedIndex >= 0) firstUnlearnedIndex / 3 else talents.size / 3
    val maxShowChunkSize = (firstUnlearnedChunkIndex + 15).coerceIn(0, talents.size / 3)
    // 為了能自動滾動，需要一個 LazyListState
    val listState = rememberLazyListState()

    // 組件載入 / talents 變動時，自動滾動到對應的行
    LaunchedEffect(talents) {
        if (firstUnlearnedChunkIndex > 0) {
            listState.scrollToItem(firstUnlearnedChunkIndex - 1) // 底部多了一个一键升级按钮
        }
    }

    GameBackground(
        background = kmpDrawableResource("tower_bg"),
        bgMask = B50,
        title = stringResource(Res.string.talent_title1),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize(),
        ) {
            Spacer(modifier = Modifier.size(padding6))
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = padding10),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                CurrentResourcesPoint(index = 0, showPlus = true)
                CurrentDiamondPoint(showPlus = true)
            }
            Spacer(modifier = Modifier.size(padding6))
            Box(
                modifier = Modifier.fillMaxWidth().weight(1f)
            ) {
                // 3) 这里改用「按组显示」，并且 reverseLayout = true 让列表由下往上排列
                LazyColumn(
                    state = listState,
                    modifier = Modifier.fillMaxSize().padding(horizontal = padding26),
                    verticalArrangement = Arrangement.spacedBy(padding80),
                    reverseLayout = true
                ) {
                    items(maxShowChunkSize) { chunkIndex ->
                        if (chunkIndex == 0) {
                            Spacer(Modifier.size(talentHeight))
                        }
                        val currentChunk = zigzagTalents[chunkIndex]
                        val isEvenRow = chunkIndex % 2 == 0
                        Row(Modifier.fillMaxWidth()) {
                            var lineVerticalRes = Res.drawable.dark_line_vertical
                            Box(modifier = Modifier.weight(1f)) {
                                // 添加垂直连接线 - 仅在特定位置添加
                                if (chunkIndex < zigzagTalents.size - 1) {
                                    val nextChunk = zigzagTalents[chunkIndex + 1]
                                    // 如果当前是偶数行，则在右边添加一条线连接到上一行的右边
                                    // 如果当前是奇数行，则在左边添加一条线连接到上一行的左边
                                    if (isEvenRow && currentChunk.size >= 3 && nextChunk.isNotEmpty()) {
                                        // 连接当前行的第3个元素和上一行的第1个元素 (3->4)
                                        val current = currentChunk[2]
                                        val currentLevel =
                                            TalentManager.talents[current.mainId] ?: 0
                                        lineVerticalRes = if (currentLevel >= current.level) {
                                            Res.drawable.line_vertical
                                        } else {
                                            Res.drawable.dark_line_vertical
                                        }

                                        // 在右侧放置垂直线
                                        Image(
                                            modifier = Modifier
                                                .width(lineWidth)
                                                .height(talentHeight)
                                                .align(Alignment.CenterEnd)
                                                .graphicsLayer {
                                                    translationX =
                                                        (-itemSize.itemSize / 2 - padding1).toPx()
                                                    translationY = -itemSize.frameSize.toPx()
                                                    scaleY = 2f
                                                },
                                            painter = painterResource(lineVerticalRes),
                                            contentScale = ContentScale.FillBounds,
                                            contentDescription = null
                                        )
                                    } else if (!isEvenRow && currentChunk.isNotEmpty() && nextChunk.size >= 3) {
                                        // 连接当前行的第1个元素和上一行的第3个元素 (6->7)
                                        val current = currentChunk[0]
                                        val currentLevel =
                                            TalentManager.talents[current.mainId] ?: 0
                                        lineVerticalRes = if (currentLevel >= current.level) {
                                            Res.drawable.line_vertical
                                        } else {
                                            Res.drawable.dark_line_vertical
                                        }

                                        // 在左侧放置垂直线
                                        Image(
                                            modifier = Modifier
                                                .width(lineWidth)
                                                .height(talentHeight)
                                                .align(Alignment.CenterStart)
                                                .graphicsLayer {
                                                    translationX =
                                                        (itemSize.itemSize / 2 + padding1).toPx()
                                                    translationY = -itemSize.frameSize.toPx()
                                                    scaleY = 2f
                                                },
                                            painter = painterResource(lineVerticalRes),
                                            contentScale = ContentScale.FillBounds,
                                            contentDescription = null
                                        )
                                    }
                                }

                                // 显示当前行的天赋和水平连接线
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    // 显示这行的天赋
                                    currentChunk.forEachIndexed { talentIndex, talent ->
                                        // 添加水平线
                                        if (talentIndex > 0) {
                                            val currentLevel = if (isEvenRow) {
                                                // 从左往右和从右往左不一样，从左往右要算前一个天赋是否学习了
                                                val targetTalent = talents[talent.conditionNum - 2]
                                                TalentManager.talents[targetTalent.mainId] ?: 0
                                            } else {
                                                // 从右往左算自己是否学习了就行
                                                TalentManager.talents[talent.mainId] ?: 0
                                            }
                                            val lineRes = if (currentLevel >= talent.level) {
                                                Res.drawable.line_horizontal
                                            } else {
                                                Res.drawable.dark_line_horizontal
                                            }
                                            Image(
                                                modifier = Modifier
                                                    .weight(1f).zIndex(-99f)
                                                    .height(lineWidth).graphicsLayer {
                                                        scaleX = 1.18f
                                                    },
                                                contentScale = ContentScale.FillBounds,
                                                painter = painterResource(lineRes),
                                                contentDescription = null
                                            )
                                        }

                                        // 显示天赋按钮
                                        EffectButton(
                                            modifier = Modifier.width(itemSize.frameSize)
                                                .scale(1.4f),
                                            onClick = {
                                                val learnedLevel =
                                                    TalentManager.talents[talent.mainId] ?: 0
                                                when {
                                                    learnedLevel >= talent.level -> {
                                                        Dialogs.detailTalent1Dialog.value =
                                                            talent.id
                                                    }

                                                    TalentManager.canLearnTalentType1(talent) -> {
                                                        Dialogs.detailTalent1Dialog.value =
                                                            talent.id
                                                    }

                                                    else -> {
                                                        AppWrapper.getStringKmp(Res.string.talent_locked_tips1)
                                                            .toast()
                                                    }
                                                }
                                            },
                                        ) {
                                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                                Box(contentAlignment = Alignment.Center) {
                                                    val canLearn =
                                                        TalentManager.canLearnTalentType1(talent)
                                                    val learned =
                                                        (TalentManager.talents[talent.mainId]
                                                            ?: 0) >= talent.level
                                                    Image(
                                                        modifier = Modifier
                                                            .size(itemSize.itemSize),
                                                        contentScale = ContentScale.FillBounds,
                                                        painter = painterResource(
                                                            if (canLearn) Res.drawable.talent1_frame_light else Res.drawable.talent1_frame_dark
                                                        ),
                                                        contentDescription = null
                                                    )
                                                    Image(
                                                        modifier = Modifier
                                                            .size(itemSize.itemSize / 2),
                                                        painter = kmpPainterResource(talent.icon),
                                                        contentDescription = null
                                                    )
                                                    val oldLearnedValue = remember(talent.id) {
                                                        mutableStateOf(learned)
                                                    }
                                                    if (learned) {
                                                        Image(
                                                            modifier = Modifier
                                                                .size(itemSize.itemSize / 3)
                                                                .graphicsLayer {
                                                                    translationY = padding10.toPx()
                                                                    translationX = padding10.toPx()
                                                                },
                                                            painter = painterResource(Res.drawable.common_choose),
                                                            contentDescription = null
                                                        )
                                                        if (!oldLearnedValue.value) {
                                                            GifView(
                                                                modifier = Modifier
                                                                    .size(itemSize.itemSize)
                                                                    .scale(2f),
                                                                enabled = true,
                                                                gifCount = talent1Gif.count,
                                                                gifDrawable = talent1Gif.gif,
                                                                pace = talent1Gif.pace,
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            Box(Modifier.size(itemSize.itemSize).graphicsLayer {
                                translationX = padding8.toPx()
                            }) {
                                Image(
                                    modifier = Modifier
                                        .width(lineWidth)
                                        .height(talentHeight)
                                        .align(Alignment.Center).graphicsLayer {
                                            scaleY = 2.4f
                                            translationY = -itemSize.frameSize.toPx() * 1.2f
                                        },
                                    painter = painterResource(lineVerticalRes),
                                    contentScale = ContentScale.FillBounds,
                                    contentDescription = null
                                )
                                Image(
                                    modifier = Modifier.align(Alignment.Center)
                                        .size(padding53),
                                    painter = painterResource(Res.drawable.star),
                                    contentDescription = null
                                )
                                StrokedText(
                                    modifier = Modifier.align(Alignment.Center).graphicsLayer {
                                        translationY = padding3.toPx()
                                    },
                                    text = ((chunkIndex + 1) * 3).toString(),
                                    style = MaterialTheme.typography.h2,
                                    color = Color.White
                                )
                            }
                        }
                        if (chunkIndex == maxShowChunkSize - 1) {
                            Spacer(Modifier.size(padding20))
                        }
                    }
                }
            }
        }
        if (GuideManager.guideIndex.value == 20) {
            GuideHand(
                modifier = Modifier.align(Alignment.BottomStart)
                    .padding(bottom = padding30, start = padding40).height(
                        padding80
                    ),
                handType = HandType.UP_HAND
            )
        }
        if (TalentManager.talents.isNotEmpty()) {
            GameButton(
                Modifier.align(Alignment.BottomCenter).padding(bottom = padding22),
                buttonSize = ButtonSize.Big, text = stringResource(Res.string.one_click_talent)
            ) {
                if (Dialogs.commonBlockDialog.value == null) {
                    Dialogs.commonBlockDialog.value =
                        (AppWrapper.getStringKmp(Res.string.one_click_talent_tips) + "\n")
                    var learnedCount = 0
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        val talentsToLearn = talents.filter { (TalentManager.talents[it.mainId]?: 0) < it.level }
                        for (t in talentsToLearn) {
                            delay(100)
                            if (AwardManager.diamond.value < t.cost) {
                                GiftManager.onDiamondNotEnough()
                                AppWrapper.getStringKmp(Res.string.diamond_not_enough)
                                    .toast()
                                break
                            } else if (AwardManager.resources[0] < t.cost2) {
                                GiftManager.onResource1NotEnough()
                                AppWrapper.getStringKmp(Res.string.resource1_not_enough_tips)
                                    .toast()
                                break
                            } else {
                                AwardManager.gainDiamond(-t.cost)
                                AwardManager.gainResources(EMPTY_RESOURCES.toMutableList().apply {
                                    this[0] = -t.cost2
                                })
                                TalentManager.upgradeTalent(t)
                                learnedCount += 1
                                Dialogs.commonBlockDialog.value += ("Lv.${t.level}" + t.name + AppWrapper.getStringKmp(Res.string.one_click_talent_postfix) + "\n")
                            }
                            if (learnedCount >= 100) {
                                break
                            }
                        }
                        if (learnedCount < 20) {
                            delay(2000L - learnedCount * 100)
                        } else {
                            delay(1000L)
                        }
                        Dialogs.commonBlockDialog.value = null
                        if (learnedCount > 0) {
                            EndingManager.uploadRank()
                        }
                    }
                }
            }
        }
    }
}