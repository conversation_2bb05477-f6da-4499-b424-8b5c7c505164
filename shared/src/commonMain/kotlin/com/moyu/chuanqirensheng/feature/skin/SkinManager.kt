package com.moyu.chuanqirensheng.feature.skin

import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.Skin
import com.moyu.core.model.property.Property


fun Skin.getProperty(): Property {
    return effectType.zip(effectNum).map {
        Property.getPropertyByEnum(it.first, it.second)
    }.reduce { acc, property -> acc + property }
}

object SkinManager {

    fun getGameMasterWithSkinDrawable(): String {
//        if (StoryManager.selectedEndless()) {
//            return BattleManager.allyGameData.first().getRace().getHeadIcon()
//        } else {
//            val resource = if (currentSkins.any { it.type == StoryManager.getSelectStory().id }
//            ) {
//                currentSkins.first { it.type == StoryManager.getSelectStory().id }.pic
//            } else {
//                repo.gameCore.getRaceById(StoryManager.getSelectStory().id).getHeadIcon()
//            }
//            return resource
//        }
        return repo.getMasterAllyFromPool().getHeadIcon()
    }

    fun getGameMasterWithSkinName(): String {
//        return if (StoryManager.selectedEndless()) {
//            BattleManager.allyGameData.first().getRace().name
//        } else {
//            if (currentSkins.any { it.type == StoryManager.getSelectStory().id }) {
//                currentSkins.first { it.type == StoryManager.getSelectStory().id }.name
//            } else {
//                repo.gameCore.getRaceById(StoryManager.getSelectStory().id).name
//            }
//        }
        return repo.getMasterAllyFromPool().name
    }
}