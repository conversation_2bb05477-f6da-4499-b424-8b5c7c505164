package com.moyu.chuanqirensheng.feature.stage.ui


import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding435
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlin.math.abs

@Composable
fun ParallaxScrollingBackground(
    foregroundPainter: Painter,
    backgroundPainter: Painter,
    modifier: Modifier = Modifier,
    scrollSpeedForeground: Float = 0.45f,
    scrollSpeedBackground: Float = 0.15f,
    isScrolling: MutableState<Boolean> = mutableStateOf(true)
) {
    // Use mutableStateOf directly instead of Animatable for better performance
    val foregroundOffset = remember { mutableStateOf(0f) }
    val backgroundOffset = remember { mutableStateOf(0f) }

    // Compute normalized offsets (0f to 1f) to ensure proper looping
    val normalizedForegroundOffset by remember {
        derivedStateOf { abs(foregroundOffset.value % 1f) }
    }
    val normalizedBackgroundOffset by remember {
        derivedStateOf { abs(backgroundOffset.value % 1f) }
    }

    // Use a single LaunchedEffect with timer-based animation
    LaunchedEffect(isScrolling.value) {
        if (isScrolling.value) {
            // Constants for animation smoothness
            val frameDelayMs = 16L // ~60fps
            val foregroundStep = scrollSpeedForeground / 400f
            val backgroundStep = scrollSpeedBackground / 400f

            // Handle animation in a single coroutine
            while (isActive) {
                foregroundOffset.value += foregroundStep
                backgroundOffset.value += backgroundStep
                delay(frameDelayMs)
            }
        }
    }

    BoxWithConstraints(modifier = modifier) {
        val containerWidthPx = constraints.maxWidth.toFloat()
        // Background layer
        val scaleBg1 = 4.6f
        TiledImage(
            painter = backgroundPainter,
            offsetX = { -normalizedBackgroundOffset * containerWidthPx },
            modifier = Modifier.height(padding260).fillMaxSize().graphicsLayer {
                translationY = padding260.toPx() * (scaleBg1 - 1) / 2
            }.scale(scaleBg1),
        )
        // Foreground layer
        val scaleBg3 = 4.8f
        TiledImage(
            painter = foregroundPainter,
            drawFromTop = false,
            offsetX = { -normalizedForegroundOffset * containerWidthPx },
            modifier = Modifier.fillMaxSize().graphicsLayer {
                translationY = -padding435.toPx() * (scaleBg3 - 1) / 2
            }.scale(scaleBg3),
        )
    }
}