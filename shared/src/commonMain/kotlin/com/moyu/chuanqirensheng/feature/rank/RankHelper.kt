package com.moyu.chuanqirensheng.feature.rank

fun scoreRanks(currentScore: Int, topScores: List<Int>): Double {
    if (topScores.isEmpty()) return 0.0
    val topScore = topScores.first()
    val result =  when {
        currentScore <= 0.1 * topScore -> (currentScore / (0.1 * topScore)) * 40.0
        currentScore <= 0.5 * topScore -> 40 + ((currentScore - 0.1 * topScore) / (0.4 * topScore)) * 30.0
        currentScore <= 0.9 * topScore -> 70 + ((currentScore - 0.5 * topScore) / (0.4 * topScore)) * 20.0
        currentScore >= topScore -> 100.0
        else -> 90 + ((currentScore - 0.9 * topScore) / (0.1 * topScore)) * 10.0
    }
    return  (result.coerceIn(0.0, 100.0) * 100.0).toInt() / 100.0
}
