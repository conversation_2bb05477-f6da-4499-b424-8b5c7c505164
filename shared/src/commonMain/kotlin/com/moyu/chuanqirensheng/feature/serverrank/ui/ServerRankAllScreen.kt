package com.moyu.chuanqirensheng.feature.serverrank.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.serverrank.ServerRankManager
import com.moyu.chuanqirensheng.feature.serverrank.ServerRankManager.canShowServerRankEntrance
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import com.moyu.core.AppWrapper
import shared.generated.resources.Res
import shared.generated.resources.icon_sign
import shared.generated.resources.rank_icon
import shared.generated.resources.server_rank_activity1
import shared.generated.resources.server_rank_activity2
import shared.generated.resources.server_rank_activity3


@Composable
fun ServerRankAllScreen(type: Int) {
    val listTabItems = remember {
        mutableStateListOf(
            Res.drawable.icon_sign,
            Res.drawable.rank_icon,
        )
    }
    val pagerState = remember {
        mutableStateOf(0)
    }
    LaunchedEffect(Unit) {
        ServerRankManager.init(true)
        if (canShowServerRankEntrance()) {
            ServerRankManager.refreshServerRankList(1)
            ServerRankManager.refreshServerRankList(2)
            ServerRankManager.refreshServerRankList(3)
        }
    }
    GameBackground(
        title = when (type) {
            1 -> AppWrapper.getStringKmp(Res.string.server_rank_activity1)
            2 -> AppWrapper.getStringKmp(Res.string.server_rank_activity2)
            else -> AppWrapper.getStringKmp(Res.string.server_rank_activity3)
        },
        bgMask = B50,
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
            ) {
                when (pagerState.value) {
                    0 -> ServerRankQuestPage(type)
                    else -> ServerRankPage(type)
                }
            }
            NavigationTab(
                modifier = Modifier.padding(bottom = padding6),
                pageState = pagerState,
                titles = listTabItems,
                redIcons = ServerRankManager.getRedIcons(type)
            )
        }
    }
}