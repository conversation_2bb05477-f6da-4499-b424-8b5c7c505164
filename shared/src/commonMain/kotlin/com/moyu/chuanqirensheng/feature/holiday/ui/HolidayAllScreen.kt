package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import com.moyu.core.AppWrapper
import shared.generated.resources.Res
import shared.generated.resources.daily_gift_icon
import shared.generated.resources.holiday_bg
import shared.generated.resources.holiday_title
import shared.generated.resources.icon_sign
import shared.generated.resources.lottery_icon
import shared.generated.resources.pvp_icon
import shared.generated.resources.rank_icon


@Composable
fun HolidayAllScreen() {
    val listTabItems = remember {
        mutableStateListOf(
            Res.drawable.icon_sign,
            Res.drawable.pvp_icon,
            Res.drawable.lottery_icon,
            Res.drawable.daily_gift_icon,
            Res.drawable.rank_icon,
        )
    }
    val pagerState = remember {
        mutableStateOf(0)
    }
    LaunchedEffect(Unit) {
        SellManager.init()
        HolidayManager.init(true)
    }
    GameBackground(
        title = AppWrapper.getStringKmp(Res.string.holiday_title),
        bgMask = B50,
        background = Res.drawable.holiday_bg
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
            ) {
                when (pagerState.value) {
                    0 -> HolidaySignPage()
                    1 -> HolidayQuestPage()
                    2 -> HolidayLotteryPage()
                    3 -> HolidaySellPage()
                    else -> HolidayRankPage()// 加密，不然可能有风险
                }
            }
            NavigationTab(
                modifier = Modifier.padding(bottom = padding6),
                pageState = pagerState,
                titles = listTabItems,
                redIcons = HolidayManager.getRedIcons()
            )
        }
    }
}