package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import com.moyu.chuanqirensheng.ui.theme.padding10
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource

@Composable
fun SingleCardBack(modifier: Modifier, drawableRes: DrawableResource, frame: DrawableResource? = null) {
    Image(
        painterResource(drawableRes),
        modifier = modifier.clip(RoundedCornerShape(padding10)),
        contentDescription = null
    )
    frame?.let {
        Image(
            painterResource(frame),
            modifier = modifier.clip(RoundedCornerShape(padding10)),
            contentDescription = null
        )
    }
}