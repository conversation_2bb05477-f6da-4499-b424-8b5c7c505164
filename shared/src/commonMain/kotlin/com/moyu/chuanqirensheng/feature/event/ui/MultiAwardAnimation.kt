package com.moyu.chuanqirensheng.feature.event.ui

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding130
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.core.model.Award
import kotlin.random.Random

@Composable
fun MultiAwardsAnimation(
    awards: List<Award>,       // 多份奖品的数据
    modifier: Modifier = Modifier
) {
    // 举个例子，统一使用一套 scale/alpha/translation 的动画
    var startAnimation by remember { mutableStateOf(false) }
    val alpha by animateFloatAsState(
        targetValue = if (startAnimation) 0f else 1.4f,
        animationSpec = tween(durationMillis = 1500)
    )

    // 当进入组合后启动动画
    LaunchedEffect(Unit) {
        startAnimation = true
    }

    // 生成一个与 awards 大小对应的列表，每个元素都有一个随机偏移量 (randX, randY)
    val randomOffsets = remember(awards.size) {
        List(awards.size) {
            val randX = (Random.nextFloat() - 0.5f) * 200f  // 范围大约 -20 到 +20
            val randY = (Random.nextFloat() - 0.5f) * 200f  // 范围大约 -20 到 +20
            val randSpeed = 1f + (Random.nextFloat() - 0.5f) * 0.4f  // 范围大约 -20 到 +20
            Triple(randX, randY, randSpeed)
        }
    }

    Box(modifier) {
        awards.forEachIndexed { index, award ->
            // 这里也可以直接复用 AwardList，或者你原先的“奖品”UI
            val (randX, randY, randSpeed) = randomOffsets[index]
            AwardList(
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = alpha * randSpeed
                        scaleY = alpha * randSpeed
                        this.alpha = alpha * randSpeed
                        translationX = (1.2f - alpha * randSpeed) * -padding130.toPx() + randX
                        translationY = (1.2f - alpha * randSpeed) * padding100.toPx() + randY
                    },
                award = award, param = if (awards.size > 1) {
                    defaultParam.copy(
                        itemSize = ItemSize.Medium,
                        showName = false,
                        showNum = false,
                        frameDrawable = null,
                        numInFrame = false,
                        noFrameForItem = true,
                        callback = {})
                } else {
                    defaultParam.copy(
                        showColumn = false,
                        showPlus = true,
                        itemSize = ItemSize.Medium,
                        showName = true,
                        showNum = true,
                        frameDrawable = null,
                        numInFrame = false,
                        callback = {})
                }
            )
        }
    }
}