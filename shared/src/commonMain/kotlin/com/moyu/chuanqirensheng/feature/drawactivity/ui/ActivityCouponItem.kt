package com.moyu.chuanqirensheng.feature.drawactivity.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.drawactivity.DrawActivityManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.resource.InfoIcon
import com.moyu.chuanqirensheng.feature.resource.KeyPoint
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding17
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.draw_activity
import shared.generated.resources.draw_activity_tips
import shared.generated.resources.draw_card
import shared.generated.resources.draw_card_front3
import shared.generated.resources.key_not_enough
import shared.generated.resources.left_draw_orange

@Composable
fun ActivityCouponItem() {
    Column(
        Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val gifEnabled = remember {
            mutableStateOf(false)
        }
        Box(Modifier.size(padding300)) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding8),
                painter = painterResource(Res.drawable.draw_card_front3),
                contentDescription = null
            )
            StrokedText(
                modifier = Modifier.align(Alignment.TopCenter).padding(
                    top = padding17
                ),
                text = stringResource(Res.string.draw_activity),
                style = MaterialTheme.typography.h1,
                textAlign = TextAlign.Center,
                color = Color.White
            )
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd).padding(padding20),
                contentAlignment = Alignment.Center
            ) {
                InfoIcon {
                    Dialogs.alertDialog.value = CommonAlert(
                        title = AppWrapper.getStringKmp(Res.string.draw_activity),
                        content = AppWrapper.getStringKmp(Res.string.draw_activity_tips),
                        onlyConfirm = true
                    )
                }
            }
            StrokedText(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(padding40),
                text = stringResource(Res.string.left_draw_orange, DrawActivityManager.getActivityLeftDraw()),
                style = MaterialTheme.typography.h2,
                textAlign = TextAlign.Center
            )
        }
        Box(contentAlignment = Alignment.Center) {
            GameButton(text = "",
                buttonSize = ButtonSize.Huge,
                enabled = AwardManager.key.value / repo.gameCore.getActivityCouponRate() >= 10,
                onClick = {
                    if (AwardManager.key.value / repo.gameCore.getActivityCouponRate() < 10) {
                        AppWrapper.getStringKmp(Res.string.key_not_enough).toast()
                        GiftManager.onKeyNotEnough()
                    } else {
                        gifEnabled.value = true
                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                            DrawActivityManager.buyActivityCoupon()
                        }
                    }
                })
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceAround
            ) {
                KeyPoint(repo.gameCore.getActivityCouponRate() * 10)
                StrokedText(text = stringResource(Res.string.draw_card), style = MaterialTheme.typography.h1)
            }
        }
    }
}