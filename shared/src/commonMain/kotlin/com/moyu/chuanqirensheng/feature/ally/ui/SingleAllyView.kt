package com.moyu.chuanqirensheng.feature.ally.ui

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.battle.ui.RoleHpWithAnim
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.text.getEquipQualityFrame
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.hpHeight
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.Stars
import com.moyu.chuanqirensheng.widget.common.getTextStyle
import com.moyu.chuanqirensheng.widget.common.getTextStyleSmall
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.orangeItemGif
import com.moyu.chuanqirensheng.widget.effect.redItemGif
import com.moyu.core.model.Ally
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.common_question
import shared.generated.resources.red_icon
import kotlin.math.roundToInt


@Composable
fun SingleAllyView(
    modifier: Modifier = Modifier,
    ally: Ally,
    showName: Boolean = true,
    showHp: Boolean = false,
    extraInfo: String = "",
    textColor: Color = Color.White,
    frame: DrawableResource? = ally.quality.getEquipQualityFrame(),
    showRed: Boolean = false,
    showStar: Boolean = true,
    showLevel: Boolean = false,
    colorFilter: ColorFilter? = null,
    hide: Boolean = false,
    showEffect: Boolean = false,
    itemSize: ItemSize = ItemSize.LargePlus,
    selectCallBack: (Ally) -> Unit = { Dialogs.allyDetailDialog.value = it }
) {
    EffectButton(modifier = modifier.width(itemSize.frameSize), onClick = {
        selectCallBack(ally)
    }) {
        Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
            Box(contentAlignment = Alignment.Center) {
                frame?.let {
                    Image(
                        modifier = Modifier.size(itemSize.frameSize),
                        painter = kmpPainterResource(it),
                        colorFilter = colorFilter,
                        contentDescription = null,
                    )
                }?: Spacer(Modifier.size(itemSize.frameSize))
                Image(
                    modifier = Modifier.size(itemSize.itemSize).scale(ally.getScaleByQuality()),
                    painter = painterResource(if (hide) Res.drawable.common_question else kmpDrawableResource(
                        ally.getHeadIcon()
                    )),
                    colorFilter = colorFilter,
                    contentDescription = null
                )
                if (showEffect && ally.quality >= 3) {
                    val infiniteTransition = rememberInfiniteTransition(label = "")
                    val gifData = if (ally.quality == 3) orangeItemGif else redItemGif
                    val index = infiniteTransition.animateFloat(
                        initialValue = 1f,
                        targetValue = gifData.count.toFloat(),
                        animationSpec = infiniteRepeatable(
                            animation = tween(2200, easing = LinearEasing),
                            repeatMode = RepeatMode.Restart,
                        ),
                        label = ""
                    )
                    if (index.value.roundToInt() <= gifData.count) { // 做一个间歇的效果
                        Image(
                            modifier = Modifier
                                .size(itemSize.frameSize)
                                .scale(1.62f).graphicsLayer {
                                    translationX = -padding2.toPx()
                                    translationY = -padding2.toPx()
                                },
                            painter = kmpPainterResource("${gifData.gif}${index.value.roundToInt()}"),
                            contentDescription = null
                        )
                    }
                }
                if (extraInfo.isNotEmpty()) {
                    StrokedText(
                        text = extraInfo,
                        style = itemSize.getTextStyle(),
                        color = Color.White
                    )
                }
                if (showStar) {
                    Stars(
                        modifier = Modifier
                            .align(Alignment.TopStart)
                            .padding(itemSize.itemSize / 10).size(itemSize.itemSize / 3),
                        ally.star,
                        style = itemSize.getTextStyleSmall()
                    )
                }
                if (showRed) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .size(imageSmall),
                        painter = painterResource(Res.drawable.red_icon),
                        contentDescription = null
                    )
                }
                if (showLevel) {
                    StrokedText(
                        modifier = Modifier
                            .align(Alignment.BottomEnd).padding(itemSize.itemSize / 8).clip(
                                RoundedCornerShape(padding2)).background(
                                B50
                            ).padding(horizontal = padding4, vertical = padding1),
                        text = "Lv" + ally.level,
                        style = MaterialTheme.typography.h5,
                        textAlign = TextAlign.Center,
                    )
                }
            }
            if (showHp) {
                Box(
                    modifier = Modifier.size(itemSize.frameSize * 0.88f, hpHeight)
                ) {
                    RoleHpWithAnim(BattleManager.getMyRoleByAlly(ally = ally))
                }
            }
            if (showName) {
                StrokedText(
                    modifier = Modifier.width(itemSize.frameSize * 1.2f),
                    text = if (hide) "???" else ally.name,
                    style = itemSize.getTextStyle(),
                    maxLines = LanguageManager.getTextLines(),
                    minLines = LanguageManager.getTextLines(),
                    textAlign = TextAlign.Center,
                    color = textColor
                )
            }
        }
    }
}