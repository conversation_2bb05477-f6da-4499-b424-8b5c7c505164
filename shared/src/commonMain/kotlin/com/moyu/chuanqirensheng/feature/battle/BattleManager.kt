package com.moyu.chuanqirensheng.feature.battle

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.ally.ui.SelectAllyData
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.feature.illustration.TcgManager
import com.moyu.chuanqirensheng.feature.role.createPvpPlayerRole
import com.moyu.chuanqirensheng.feature.role.createTowerPlayerRole
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.role.ALLY_ROW1_SECOND
import com.moyu.core.logic.role.HERO_POSITION
import com.moyu.core.logic.role.positionList
import com.moyu.core.logic.role.positionOrderedListAllies
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.GameItem
import com.moyu.core.model.Pool
import com.moyu.core.model.PropertyAward
import com.moyu.core.model.RACE_SIZE
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS
import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.role.RoleExtraInfo
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isEquipBattle
import com.moyu.core.model.skill.isMagic
import com.moyu.core.model.skill.isTalentAdv
import com.moyu.core.model.skill.isTalentBattle
import com.moyu.core.model.toAward
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import org.jetbrains.compose.resources.getString
import shared.generated.resources.Res
import shared.generated.resources.can_not_learn_more_skills
import shared.generated.resources.carry_to_game
import shared.generated.resources.only_one_master_tips
import kotlin.math.max

const val MAX_BATTLE_SIZE = 8
const val MAX_SKILL_IN_GAME = 10

object BattleManager {
    val battleRolePositions = mutableMapOf<String, Pair<Dp, Dp>>()

    val you = mutableStateOf(Role(roleIdentifier = Identifier.player()))
    val initialProperty = mutableStateOf(Property())

    val skillGameData = mutableStateListOf<Skill>()
    val allyGameData = mutableStateListOf<Ally>()
    val adventureProps = mutableStateOf(EMPTY_ADV_PROPS)
    val propertyAwards = mutableStateListOf<PropertyAward>()
    val battleProp = mutableStateOf(EMPTY_PROPERTY)
    val battleSkillPropMap = mutableMapOf<Int, Property>()
    val battleRaceProps = mutableListOf(EMPTY_PROPERTY)
    val masterSkills = mutableListOf<Skill>()

    val currentBgMusic = mutableStateOf(MusicManager.getRandomDungeonMusic())
    val effectedSkills = mutableStateListOf<Skill>()
    val battleAward = mutableStateOf(Award())

    val healingStates = mutableStateOf(false)

    fun init() {
        currentBgMusic.value = MusicManager.getRandomDungeonMusic()
        battleRolePositions.clear()

        battleRaceProps.clear()
        repeat(RACE_SIZE) {
            battleRaceProps.add(EMPTY_PROPERTY)
        }

        adventureProps.value = EMPTY_ADV_PROPS

        propertyAwards.clear()
        battleProp.value = EMPTY_PROPERTY
        battleSkillPropMap.clear()

        masterSkills.clear()
        allyGameData.clear()
        skillGameData.clear()
        effectedSkills.clear()
        battleAward.value = Award()

        // 主角固定在第二排第一个
        addMasterToGame()
        allyGameData.addAll(repo.allyManager.data.filter {
            it.battlePosition >= 0
        }.map { it.copy(num = 0).copyToGame() })
        createYou()
    }

    fun addMasterToGame() {
        allyGameData.add(
            repo.getMasterAllyFromPool().copyToGame().copy(battlePosition = HERO_POSITION)
        )
    }

    fun onNewGame() {
        init() // 还要再init一次，改变了选择，要重新梳理下技能和军团卡的关系
    }

    fun createYou() {
        adventureProps.value = AdventureProps.createNew()
        val talents = TalentManager.talents.map { talent ->
            repo.gameCore.getSkillPool(talent.value).first { it.mainId == talent.key }
        }.filter { it.isTalentAdv() }
        you.value = Role(
            extraInfo = RoleExtraInfo(allyUuid = UUID.generateUUID().toString()),
            roleIdentifier = Identifier.player()
        ).apply {
            talents.forEach {
                learnSkill(it, roleIdentifier)
            }
        }
        // 英雄自带的冒险技能要算进去
        getGameMaster().fixedSkills.map { repo.gameCore.getSkillById(it) }
            .filter { it.isAdventure() }.forEach {
                you.value.learnSkill(it, you.value)
                skillGameData.add(it)
            }
    }

    fun selectAllToGame() {
        allyGameData.clear()
        // 默认都下阵
        allyGameData.addAll(repo.allyManager.data.map {
            it.copyToGame().copy(battlePosition = -1)
        })
        addMasterToGame()
    }

    fun selectToGame(target: Ally, battlePosition: Int = -1) {
        val index = allyGameData.indexOfFirst { target.id == it.id } // 这里没有重复技能，未进入游戏，没有uuid，用id判定
        if (index != -1) {
            allyGameData.removeAt(index)
            repo.allyManager.selectToGame(target)
        } else {
            if (!target.isHero() && allyGameData.filter { !it.isHero() }.size >= UnlockManager.getInitAllyNum()) {
                val toastStr = runBlocking {
                    getString(
                        Res.string.carry_to_game,
                        UnlockManager.getInitAllyNum()
                    )
                }
                toastStr.toast()
            } else {
                repo.allyManager.selectToGame(target)
                allyGameData.add(target.copyToGame().copy(battlePosition = battlePosition))
            }
        }
    }

    suspend fun gainInGame(target: Ally) {
        // todo 注意，如果是首次获得，数量要-1，本体扣除1
        allyGameData.firstOrNull { it.mainId == target.mainId }?.let {
            allyGameData.indexOfItemInGame(it.uuid) { index ->
                allyGameData[index] =
                    allyGameData[index].copy(num = allyGameData[index].num + target.num, new = true)
            }
        } ?: allyGameData.add(target.copyToGame().copy(num = target.num - 1))
        DetailProgressManager.gainInGame(target)
    }

    suspend fun gainSkillInGame(target: Skill) {
        if (target.isMagic() && masterSkills.filter { it.isMagic() }.size >= MAX_SKILL_IN_GAME) {
            AppWrapper.getStringKmp(Res.string.can_not_learn_more_skills).toast()
            return
        }
        target.copyToGame().let {
            // 战斗技能，只能是主角技能
            if (it.level > 1) {
                masterSkills.removeAll { learnedSkill -> learnedSkill.mainId == it.mainId }
                skillGameData.removeAll { learnedSkill -> learnedSkill.mainId == it.mainId }
            }
            if (masterSkills.map { it.id }.contains(it.id)) {
                masterSkills.removeAll { learnedSkill -> learnedSkill.id == it.id }
                skillGameData.removeAll { learnedSkill -> learnedSkill.id == it.id }
            }
            masterSkills.add(it)
            if (!skillGameData.map { it.uuid }.contains(it.uuid)) {
                skillGameData.add(it)
                DetailProgressManager.gainInGame(it)
            }
        }
    }

    fun addBattleAward(award: Award) {
        battleAward.value += award.copy(skills = emptyList(), allies = emptyList())
    }

    suspend fun dropFromGame(target: Ally, needTrigger: Boolean = true) {
        if (target.isMaster()) {
            // 主角死亡，直接下阵
            if (isAllyInBattle(getGameMaster())) {
                allyGameData.indexOfItemInGame(getGameMaster().uuid) {
                    allyGameData[it] = allyGameData[it].switchSelectToBattle(-1)
                }
            }
        } else {
            allyGameData.removeAll { it.uuid == target.uuid }
            if (needTrigger) {
                DetailProgressManager.dropFromGame(target)
            }
        }
    }

    fun getGameAllies(): List<Ally> {
        return allyGameData.sortedByDescending { it.battlePosition }
    }

    fun getBattleAllies(): Map<Int, Ally> {
        val mutableMap = mutableMapOf<Int, Ally>()
        allyGameData.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = it
        }
        return mutableMap
    }

    fun getBattleRoles(capacity: Int): Map<Int, Role> {
        val mutableMap = mutableMapOf<Int, Role>()
        if (capacity == 1) {
            allyGameData.firstOrNull { it.battlePosition == ALLY_ROW1_SECOND }?.let {
                mutableMap[ALLY_ROW1_SECOND] = getMyRoleByAlly(it)
            }
        } else {
            allyGameData.filter { it.battlePosition >= 0 }.forEach {
                mutableMap[it.battlePosition] = getMyRoleByAlly(it)
            }
        }
        return mutableMap
    }

    fun getPvpBattleRoles(): Map<Int, Role> {
        val mutableMap = mutableMapOf<Int, Role>()
        allyGameData.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = createPvpPlayerRole(
                it,
                TalentManager.talents,
                repo.equipManager.data.filter { it.equipped }.map { it.id })
        }
        return mutableMap
    }

    fun getTowerBattleRoles(): Map<Int, Role> {
        val mutableMap = mutableMapOf<Int, Role>()
        allyGameData.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = createTowerPlayerRole(
                it,
                TalentManager.talents,
                repo.equipManager.data.filter { it.equipped }.map { it.id })
        }
        return mutableMap
    }

    fun availablePosition(): Int {
        val battleAllies = getBattleAllies().keys
        return positionOrderedListAllies.firstOrNull { position ->
            !battleAllies.contains(position)
        } ?: -1
    }

    fun selectAllyToBattle(ally: Ally, position: Int) {
        if (isAllyInBattle(ally)) {
            allyGameData.indexOfItemInGame(ally.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle(-1)
            }
        } else if (position !in positionList && position != -1) {
            return
        } else if (allyGameData.any { it.battlePosition == position }) {
            allyGameData.indexOfItemInGame(allyGameData.first { it.battlePosition == position }.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle(-1)
            }
            allyGameData.indexOfItemInGame(ally.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle(position)
            }
        } else if (ally.isHero() && allyGameData.any { it.battlePosition > 0 && it.isHero() }) {
            AppWrapper.getStringKmp(Res.string.only_one_master_tips).toast()
        } else {
            allyGameData.indexOfItemInGame(ally.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle(position)
            }
        }
    }

    suspend fun checkAnyAllyDied() {
        // 不可以直接foreach，因为要改变list内容
        allyGameData.filter { it.isDead() }.map { it.uuid }.forEach {
            allyGameData.indexOfItemInGame(it) { index ->
                if (allyGameData[index].isDead()) {
                    dropFromGame(allyGameData[index])
                }
            }
        }
    }

    fun updateAllyInGameById(role: Role, hp: Int) {
        allyGameData.indexOfItemInGame(role.extraInfo.allyUuid) { index ->
            val minHp = if (hp > 0) 1 else 0
            allyGameData[index] =
                allyGameData[index].copy(
                    gameHp = if (hp >= role.getOriginProperty().hp) 100 else max(
                        minHp,
                        hp * 100 / role.getOriginProperty().hp
                    )
                )
        }
    }

    fun reliveAllyInGame(ally: Ally, relive: Boolean = true) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].relive()
        }
        if (relive) {
            GameCore.instance.onBattleEffect(SoundEffect.ReliveAlly)
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.HealAlly)
        }
    }

    fun healBattleAllyInGame(percent: Int) {
        getBattleAllies().values.forEach { ally ->
            allyGameData.indexOfItemInGame(ally.uuid) { index ->
                allyGameData[index] = allyGameData[index].heal(percent)
            }
        }
        if (getGameMaster().isDead()) {
            allyGameData.indexOfItemInGame(getGameMaster().uuid) { index ->
                allyGameData[index] = allyGameData[index].heal(percent)
            }
        }
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
    }

    fun healAllAllyInGame(percent: Int) {
        allyGameData.forEachIndexed { index, _ ->
            allyGameData[index] = allyGameData[index].heal(percent)
        }
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
        healingStates.value = true
    }

    fun healOneAllyInGame(ally: Ally, percent: Int) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].heal(percent)
        }
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
    }

    suspend fun hurtAllyInGame(ally: Ally, percent: Int) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].hurt(percent)
            if (allyGameData[index].isDead()) {
                dropFromGame(allyGameData[index])
            }
        }
        GameCore.instance.onBattleEffect(SoundEffect.Damage1)
    }

    fun getGameSkills(): List<Skill> {
        return skillGameData
    }

    fun getMyRoleByAlly(ally: Ally, skipBattleProperty: Boolean = false): Role {
        val masterEquipProperty = if (ally.isMaster()) {
            repo.equipManager.data.filter { it.equipped }.map { it.getProperty() }
                .reduceOrNull { acc, property -> acc + property }
                ?: Property()
        } else {
            Property()
        }
        val tcgProperty = TcgManager.doneTcgs.map {
            it.toAward().battleProperty.first()
        }.filter { ally.match(it, true) && !skipBattleProperty }.map { it.property }.reduceOrNull { acc, propertyAward ->
            acc + propertyAward
        } ?: Property()

        if (repo.inGame.value) {
            val extraProperty =
                battleProp.value + (battleRaceProps.getOrNull(ally.raceType - 1) ?: Property())
            val gainProperty = propertyAwards.filter {
                ally.match(it, true) && !skipBattleProperty
            }.map { it.property }.reduceOrNull { acc, propertyAward ->
                acc + propertyAward
            } ?: Property()
            return DefaultAllyCreator.create(
                race = ally,
                identifier = Identifier.player(name = ally.name),
                diffProperty = extraProperty
                        + masterEquipProperty
                        + tcgProperty
                        + gainProperty
                        + (ally.exerciseProperty ?: Property()) // 附加属性加上
            ).copy(extraInfo = RoleExtraInfo(ally.uuid, allyQuality = ally.quality))
                .apply {
                    if (ally.isMaster()) {
                        masterSkills.forEach {
                            learnSkill(it, roleIdentifier)
                        }
                        // 装备里的战斗技能要弄上去
                        repo.equipManager.data.filter { it.equipped }.filter { it.skillEffect != 0 }
                            .map { repo.gameCore.getSkillById(it.skillEffect) }
                            .filter { it.isEquipBattle() }.forEach {
                                learnSkill(it, roleIdentifier)
                            }
                        // 天赋战斗技能
                        TalentManager.talents.map { talent ->
                            repo.gameCore.getSkillPool(talent.value)
                                .first { it.mainId == talent.key }
                        }.filter { it.isTalentBattle() }.forEach {
                            learnSkill(it, roleIdentifier)
                        }
                    }

                    val extraSkillProp = getSkills().mapNotNull {
                        battleSkillPropMap[it.id]
                    }.reduceOrNull { acc, property -> acc + property } ?: Property()
                    setInitProperty(getInitProperty() + extraSkillProp)
                    setPropertyToDefault()
                    setCurrentHp(max(1, this.getOriginProperty().hp * ally.gameHp / 100))
                }
        } else {
            return DefaultAllyCreator.create(
                ally,
                masterEquipProperty,
                identifier = Identifier.player(name = ally.name)
            ).copy(extraInfo = RoleExtraInfo(ally.uuid, allyQuality = ally.quality))
                .apply {
                    setPropertyToDefault()
                }
        }
    }

    fun onNextEvent() {
        // todo 闪退保护
        try {
            if (getGameMaster().isDead()) {
                allyGameData.indexOfItemInGame(getGameMaster().uuid) {
                    // 主角不会死，保留1点血
                    allyGameData[it] = allyGameData[it].copy(gameHp = 1)
                }
            }
        } catch (e: Exception) {
            e.message?.toast()
            e.printStackTrace()
        }
        // 防止数据过大
        battleRolePositions.clear()
    }

    fun isAllyInBattle(ally: Ally): Boolean {
        return allyGameData.any { it.uuid == ally.uuid && it.battlePosition >= 0 }
    }

    fun onPermanentDiff(target: Role, diff: Property) {
        allyGameData.indexOfItemInGame(target.extraInfo.allyUuid) {
            val newProperty = (allyGameData[it].exerciseProperty ?: Property()) + diff
            allyGameData[it] = allyGameData[it].copy(exerciseProperty = newProperty)
        }
    }

    fun setSkillUnNew(skill: Skill) {
        skillGameData.indexOfItemInGame(skill.uuid) {
            skillGameData[it] = skillGameData[it].copy(new = false)
        }
    }

    fun oneYearPass() {
        skillGameData.map {
            it.nextYear()
        }.let {
            skillGameData.clear()
            skillGameData.addAll(it)
        }
        you.value.oneYearPass()
    }

    fun gainBattleProp(propertyAward: PropertyAward) {
        propertyAwards.add(propertyAward.copy(age = adventureProps.value.age))
    }

    suspend fun gainAdventureProp(property: AdventureProps) {
        withContext(Dispatchers.Main) {
            adventureProps.value += property
            adventureProps.value = adventureProps.value.ensureNonNegative()
        }
    }

    fun gainPermanentProp(propertyByEnum: Property) {
        battleProp.value += propertyByEnum
    }

    fun gainPermanentRaceProp(race: Int, property: Property) {
        battleRaceProps[race] = battleRaceProps[race] + property
    }

    fun gainPermanentSkillProp(skillId: Int, property: Property) {
        battleSkillPropMap[skillId] = (battleSkillPropMap[skillId] ?: EMPTY_PROPERTY) + property
    }

    fun getGameMaster(): Ally {
        return allyGameData.first { it.isHero() }
    }

    fun getGameMasterOutOfGame(): Role {
        val ally = repo.getMasterAllyFromPool()
        return getMyRoleByAlly(ally)
    }

    fun Ally.isMaster(): Boolean {
        return this.isHero()
    }

    fun haveMoreThan(poolById: Pool): Boolean {
        return false
    }

    fun getAge(): Int {
        return adventureProps.value.age
    }

    fun updateYou() {
        you.value = you.value.copy(updateId = you.value.updateId + 1)
    }

    fun oneShotDeselect() {
        getBattleAllies().forEach {
            selectAllyToBattle(it.value, -1)
        }
        selectAllyToBattle(getGameMaster(), HERO_POSITION)
    }

    fun oneShotSelect(data: SelectAllyData) {
        oneShotDeselect()
        val battleAllies = getBattleAllies()
        positionOrderedListAllies.forEach { targetPosition ->
            if (targetPosition !in battleAllies.keys) {
                getGameAllies().asSequence().filter { !it.isHero() }.filter { data.filter(it) }
                    .filter { it.battlePosition == -1 }
                    .sortedByDescending { it.getAllPower() }
                    .firstOrNull { !it.isDead() }
                    ?.let {
                        selectAllyToBattle(it, targetPosition)
                    }
            }
        }
    }

    fun canLearnSkill(skill: Skill): Boolean {
        return !getGameSkills().map { it.mainId }.contains(skill.mainId)
    }
}

inline fun <T : GameItem> List<T>.indexOfItemInGame(uuid: String, callback: (Int) -> Unit) {
    indexOfFirst { uuid == it.uuid }.takeIf { it != -1 }?.let { index ->
        callback(index)
    }
}