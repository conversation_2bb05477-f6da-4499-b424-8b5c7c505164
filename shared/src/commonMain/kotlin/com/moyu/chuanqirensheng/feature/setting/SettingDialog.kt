package com.moyu.chuanqirensheng.feature.setting

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.feature.ending.EndingManager.uploadRank
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_AUTO_STAGE
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_AUTO_SELECT
import com.moyu.chuanqirensheng.sub.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_MUSIC
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_SOUND
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.util.BackUtil
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.auto_select
import shared.generated.resources.auto_select2
import shared.generated.resources.do_not_selected
import shared.generated.resources.do_selected
import shared.generated.resources.quit
import shared.generated.resources.setting_hide_electric
import shared.generated.resources.setup_off
import shared.generated.resources.setup_on
import shared.generated.resources.turn_off_music
import shared.generated.resources.turn_off_sound


@Composable
fun SettingDialog(show: MutableState<Boolean>) {
    if (show.value) {
        var uploadRanks = false
        PanelDialog(
            onDismissRequest = {
                show.value = false
            },
            contentBelow = {
                GameButton(text = stringResource(Res.string.quit), buttonSize = ButtonSize.Big) {
                    BackUtil.actionBack()
                    show.value = false
                }
            }
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                Spacer(modifier = Modifier.size(padding45))
                SettingItem(
                    getBooleanFlowByKey(KEY_MUTE_MUSIC), stringResource(
                        Res.string.turn_off_music
                    )
                ) {
                    setBooleanValueByKey(KEY_MUTE_MUSIC, it)
                    MusicManager.switchMuteMusic()
                }
                Spacer(modifier = Modifier.size(padding12))
                SettingItem(
                    getBooleanFlowByKey(KEY_MUTE_SOUND), stringResource(
                        Res.string.turn_off_sound
                    )
                ) {
                    setBooleanValueByKey(KEY_MUTE_SOUND, it)
                    MusicManager.switchMuteSound()
                }
                Spacer(modifier = Modifier.size(padding12))
                SettingItem(
                    getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK),
                    content = stringResource(Res.string.setting_hide_electric)
                ) {
                    setBooleanValueByKey(KEY_INVISIBLE_IN_RANK, it)
                    if (it && !uploadRanks) {
                        uploadRank()
                        uploadRanks = true
                    }
                }
                Spacer(modifier = Modifier.size(padding12))
                if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_AUTO_STAGE))) {
                    SettingItem(
                        getBooleanFlowByKey(KEY_AUTO_SELECT), if (repo.inGame.value) {
                            stringResource(
                                Res.string.auto_select2
                            )
                        } else {
                            stringResource(
                                Res.string.auto_select
                            )
                        }
                    ) {
                        setBooleanValueByKey(KEY_AUTO_SELECT, it)
                        SettingManager.autoSelect.value = it
                    }
                    Spacer(modifier = Modifier.size(padding12))
                }
                Spacer(modifier = Modifier.size(padding12))
                if (hasGoogleService() && isLite()) {
                    LanguageManager.LanguageSelectorView()
                }
            }
        }
    }
}

@Composable
fun SettingItem(checked: Boolean, content: String, onChecked: suspend (Boolean) -> Unit) {
    EffectButton(onClick = {
        AppWrapper.globalScope.launch {
            onChecked(!checked)
        }
    }) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.padding(start = padding14)
        ) {
            Image(
                modifier = Modifier.size(padding26),
                painter = painterResource(if (checked) Res.drawable.setup_on else Res.drawable.setup_off),
                contentDescription = if (checked) stringResource(Res.string.do_selected) else stringResource(
                    Res.string.do_not_selected
                ),
            )
            Spacer(modifier = Modifier.size(padding10))
            StrokedText(
                text = content.trimIndent(),
                style = MaterialTheme.typography.h3,
                color = Color.White
            )
        }
    }
}