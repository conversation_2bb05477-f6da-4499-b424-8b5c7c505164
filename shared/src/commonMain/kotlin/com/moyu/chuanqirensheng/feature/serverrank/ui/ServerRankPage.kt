package com.moyu.chuanqirensheng.feature.serverrank.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.api.getServerRankAward
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.ui.Leaderboard
import com.moyu.chuanqirensheng.feature.serverrank.ServerRankManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.shrinkNumber
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.battle_power_icon
import shared.generated.resources.battle_power_tips
import shared.generated.resources.icon_stage
import shared.generated.resources.net_error_retry
import shared.generated.resources.stage_progress_tips
import shared.generated.resources.talent1_icon
import shared.generated.resources.talent_total_level1

val serverRanks1 = mutableStateOf(emptyList<RankData>())
val serverRanks2 = mutableStateOf(emptyList<RankData>())
val serverRanks3 = mutableStateOf(emptyList<RankData>())
val serverSolidRanks1 = mutableStateOf(emptyList<RankData>())
val serverSolidRanks2 = mutableStateOf(emptyList<RankData>())
val serverSolidRanks3 = mutableStateOf(emptyList<RankData>())

val serverRankList = listOf(
    serverRanks1,
    serverRanks2,
    serverRanks3
)

val serverSolidRankList = listOf(
    serverSolidRanks1,
    serverSolidRanks2,
    serverSolidRanks3
)

@Composable
fun ServerRankPage(type: Int) {
    LaunchedEffect(Unit) {
        try {
            delay(200)
            // 活动已经结束，拉取定榜
            getServerRankAward(
                platform = platformChannel(),
                type = type

            ).let {
                if (ServerRankManager.canShowOnlyRank()) {
                    serverSolidRankList[type - 1].value = json.decodeFromString(
                        ListSerializer(RankData.serializer()), it.message
                    )
                } else {
                    serverRankList[type - 1].value = json.decodeFromString(
                        ListSerializer(RankData.serializer()), it.message
                    )
                }
            }
        } catch (e: Exception) {
            AppWrapper.getStringKmp(Res.string.net_error_retry).toast()
        }
    }
    val ranks = if (ServerRankManager.canShowOnlyRank()) serverSolidRankList[type - 1] else serverRankList[type - 1]
    Spacer(modifier = Modifier.size(padding10))
    Leaderboard(ranks.value) { rankData, _ ->
        when (type) {
            1-> {
                Row(modifier = Modifier.clickable {
                    MusicManager.playSound(SoundEffect.Click)
                    AppWrapper.getStringKmp(Res.string.battle_power_tips).toast()
                }, verticalAlignment = Alignment.CenterVertically) {
                    Image(
                        modifier = Modifier.size(imageSmallPlus),
                        painter = painterResource(Res.drawable.battle_power_icon),
                        contentDescription = null
                    )
                    StrokedText(
                        text = rankData.battlePower.toString().shrinkNumber(!hasGoogleService()),
                        style = MaterialTheme.typography.h2
                    )
                }
            }
            2 -> {
                Row(modifier = Modifier.clickable {
                    MusicManager.playSound(SoundEffect.Click)
                    (AppWrapper.getStringKmp(Res.string.talent_total_level1) + rankData.talentLevel).toast()
                }, verticalAlignment = Alignment.CenterVertically) {
                    Image(
                        modifier = Modifier.size(imageSmallPlus),
                        painter = painterResource(Res.drawable.talent1_icon),
                        contentDescription = null
                    )
                    StrokedText(
                        text = rankData.talentLevel.toString(),
                        style = MaterialTheme.typography.h2
                    )
                }
            }
            else -> {
                Row(modifier = Modifier.clickable {
                    MusicManager.playSound(SoundEffect.Click)
                    AppWrapper.getStringKmp(Res.string.stage_progress_tips).toast()
                }, verticalAlignment = Alignment.CenterVertically) {
                    Image(
                        modifier = Modifier.size(imageSmallPlus),
                        painter = painterResource(Res.drawable.icon_stage),
                        contentDescription = null
                    )
                    StrokedText(
                        text = rankData.endingNum.toString(),
                        style = MaterialTheme.typography.h2
                    )
                }
            }
        }
    }
}
