package com.moyu.chuanqirensheng.feature.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.SkillLevel5Color
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Gift
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.gift_frame


@Composable
fun GiftDetailIcon(itemSize: ItemSize) {
    val refreshIndex = remember {
        mutableIntStateOf(0)
    }
    // todo 为了在计时结束触发刷新
    if (isNetTimeValid() && LoginManager.instance.canShowAifadian()) {
        // 礼包刷新条件：时间到了；买东西了；看广告了，以及默认的打开首页
        val gifts = remember {
            mutableStateOf(emptyList<Gift>())
        }
        val showGift = remember(gifts.value) {
            derivedStateOf {
                gifts.value.firstOrNull { !it.dialogShowed }
            }
        }
        LaunchedEffect(refreshIndex.intValue, AwardManager.electric.value, AwardManager.adNum.value) {
            gifts.value = GiftManager.getDisplayGifts()
        }
        gifts.value.take(3).forEach {
            val pressing = remember {
                mutableStateOf(false)
            }
            val colorFilter = if (pressing.value) {
                ColorFilter.tint(
                    B50, BlendMode.SrcAtop
                )
            } else {
                null
            }
            val color = if (pressing.value) W50 else Color.White
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                EffectButton(modifier = Modifier.graphicsLayer {
                    translationY = padding12.toPx()
                }, pressing = pressing, onClick = {
                    Dialogs.giftDetailDialog.value = it
                }) {
                    Image(
                        modifier = Modifier
                            .height(itemSize.frameSize * 1.2f),
                        painter = painterResource(Res.drawable.gift_frame),
                        contentScale = ContentScale.FillHeight,
                        colorFilter = colorFilter,
                        contentDescription = null
                    )
                    Image(
                        modifier = Modifier
                            .height(itemSize.itemSize / 1.18f),
                        painter = painterResource(kmpDrawableResource(it.icon)),
                        contentScale = ContentScale.FillHeight,
                        colorFilter = colorFilter,
                        contentDescription = null
                    )
                }
                StrokedText(
                    modifier = Modifier.width(itemSize.frameSize * 1.3f),
                    text = it.name,
                    maxLines = 1,
                    color = color,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.h6
                )
                if (it.limitTime != 0 && it.displayTime != 0L && isNetTimeValid()) {
                    UpdateTimeText(it) {
                        refreshIndex.intValue += 1
                    }
                }
            }
        }
        if (getCurrentTime() - GiftManager.lastShowTime > 5 * 60 * 1000L) {
            showGift.value?.let {
                Dialogs.giftDetailDialog.value = it
            }
        }
    }
}

@Composable
fun UpdateTimeText(gift: Gift, callback: () -> Unit) {
    val currentTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(Unit) {
        if (isNetTimeValid()) {
            while (true) {
                currentTime.longValue = getCurrentTime()
                if (currentTime.longValue >= gift.displayTime + gift.limitTime * 60 * 1000L) {
                    callback()
                }
                delay(500)
            }
        }
    }
    if (currentTime.longValue < gift.displayTime + gift.limitTime * 60 * 1000L) {
        StrokedText(
            text = ((gift.displayTime + gift.limitTime * 60 * 1000L) - currentTime.longValue).millisToHoursMinutesSeconds(),
            style = MaterialTheme.typography.body1,
            color = SkillLevel5Color
        )
    }
}
