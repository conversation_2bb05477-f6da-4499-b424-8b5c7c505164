package com.moyu.chuanqirensheng.feature.stage.ui

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.drawscope.clipRect
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.graphics.painter.Painter

@Composable
fun TiledImage(
    painter: Painter,
    offsetX: () -> Float,
    modifier: Modifier = Modifier.fillMaxSize(),
    drawFromTop: Boolean = true,
    overlapPx: Float = 1f                       // ← 新增：左右两块重叠的像素宽度
) {
    Canvas(modifier) {
        // 1) 计算缩放后尺寸
        val imageW = painter.intrinsicSize.width
        val imageH = painter.intrinsicSize.height
        val scaledW = size.width                // 宽度拉满
        val scaledH = imageH * size.width / imageW

        // 2) 补偿浮点误差：repeatCount 多 +1，startOffset 多 -overlapPx
        val tileSpan   = scaledW - overlapPx    // ← 关键：相邻贴图左起点之间少 overlapPx
        val repeatCnt  = (size.width / tileSpan).toInt() + 3
        val normOffset = (offsetX() % tileSpan + tileSpan) % tileSpan
        val startOff   = normOffset - tileSpan  // 保证首块一定覆盖左边

        // 3) 竖向对齐：顶部/底部
        val y = if (drawFromTop) 0f else size.height - scaledH

        clipRect(0f, 0f, size.width, size.height) {
            translate(left = startOff, top = y) {
                for (i in 0 until repeatCnt) {
                    val x = i * tileSpan
                    translate(left = x) {
                        with(painter) {
                            draw(
                                size = Size(scaledW, scaledH),
                                alpha = 1f,
                                colorFilter = null
                            )
                        }
                    }
                }
            }
        }
    }
}
