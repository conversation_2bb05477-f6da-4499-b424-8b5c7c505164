package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Award
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.confirm
import shared.generated.resources.pool_title

@Composable
fun SellPoolDialog(show: MutableState<Award?>) {
    show.value?.let {
        PanelDialog(onDismissRequest = {
            show.value = null
        }, contentBelow = {
            GameButton(
                text = stringResource(Res.string.confirm),
                buttonStyle = ButtonStyle.Blue,
                onClick = {
                    show.value = null
                })
        }) {
            Column(
                Modifier
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                StrokedText(
                    text = stringResource(Res.string.pool_title),
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding10))
                Column(
                    Modifier
                        .verticalScroll(rememberScrollState())
                ) {
                    AwardList(
                        award = it,
                        mainAxisAlignment = Arrangement.spacedBy(padding22),
                        param = defaultParam.copy(textColor = Color.White)
                    )
                }
            }
        }
    }
}