package com.moyu.chuanqirensheng.feature.unlock

enum class UnlockId(val id: Int) {
    Age(1),
    LoginDay(2),
    Talent(3),
    Code(4),
    VipLevel(5),
    GiftId(6),
    Charge(8),
    <PERSON>(9),
    <PERSON>(10),
    <PERSON><PERSON><PERSON>(11),
    <PERSON>vp<PERSON><PERSON>(12),
    ElectricMoreOrEqual(13),
    ElectricLess(14),
    Coupon(15),
    PASS_STAGE(16),
    DRAW_FAILED(17),
    TALENT_RESOURCE_FAILED(19),
    MASTER_LEVEL(20),
    DRAW_ID(21),
    Tower(22),
    PvpLevel(23),
    Talent1(301),
    Talent2(302),
    Talent3(303),
    AnyReputation(1800),
    Reputation1(1801),
    Reputation2(1802),
    Reputation3(1803),
    Reputation4(1804),
    Reputation5(1805),
    Reputation6(1806),
    Reputation7(1807),
    Reputation8(1808),
    Reputation9(1809),
    Reputation10(1810),
    Reputation11(1811),
}