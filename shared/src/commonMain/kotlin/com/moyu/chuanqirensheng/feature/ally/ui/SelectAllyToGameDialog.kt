package com.moyu.chuanqirensheng.feature.ally.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.*
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.filter.CommonFilterView
import com.moyu.chuanqirensheng.widget.filter.CommonOrderView
import com.moyu.chuanqirensheng.widget.filter.FilterLayout
import com.moyu.chuanqirensheng.widget.filter.ItemFilter
import com.moyu.chuanqirensheng.widget.filter.OrderLayout
import com.moyu.chuanqirensheng.widget.filter.allyFilterList
import com.moyu.chuanqirensheng.widget.filter.allyOrderList
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding44
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.AppWrapper
import com.moyu.core.model.Ally
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun SelectAllyToGameDialog(show: MutableState<Boolean?>) {
    show.value?.let {
        val showHero = it
        val showOrder = remember {
            mutableStateOf(false)
        }
        val order = remember {
            mutableStateOf(allyOrderList.first())
        }
        val showFilter = remember {
            mutableStateOf(false)
        }
        val filter = remember {
            mutableStateListOf<ItemFilter<Ally>>()
        }
        val list = repo.allyManager.data.filter { showHero == it.isHero() }.filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }.sortedByDescending { order.value.order?.invoke(it) }
        PanelDialog(onDismissRequest = { show.value = null }) {
            Box(Modifier.fillMaxSize()) {
                LazyVerticalGrid(
                    modifier = Modifier
                        .fillMaxSize(),
                    columns = GridCells.Fixed(4),
                    content = {
                        items(list.size) { index ->
                            val ally = list[index]
                            val selected = if (showHero) {
                                list.filter { it.selected }.any { it.id == ally.id }
                            } else {
                                BattleManager.getGameAllies().any { it.id == ally.id }
                            }
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Box {
                                    SingleAllyView(
                                        ally = ally,
                                        itemSize = ItemSize.Large,
                                        showHp = false,
                                    )
                                    if (selected) {
                                        Image(
                                            painter = painterResource(Res.drawable.common_choose),
                                            modifier = Modifier
                                                .size(imageSmallPlus)
                                                .align(Alignment.BottomEnd),
                                            contentDescription = null
                                        )
                                    }
                                }
                                val button =
                                    if (selected) stringResource(Res.string.cancel) else stringResource(
                                        Res.string.do_select
                                    )
                                GameButton(
                                    text = button,
                                    buttonStyle = if (selected) ButtonStyle.Green else ButtonStyle.Blue,
                                    buttonSize = ButtonSize.Small,
                                    onClick = {
                                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                                            BattleManager.selectToGame(ally)
                                        }
                                    })
                                Spacer(modifier = Modifier.size(padding10))
                            }
                        }
                    }
                )
                Row(
                    Modifier.align(Alignment.BottomEnd)
                        .fillMaxWidth()
                        .height(padding28)
                        .graphicsLayer {
                            translationX = padding26.toPx()
                            translationY = padding44.toPx()
                        },
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    CommonOrderView(
                        Modifier.padding(start = padding10), showOrder
                    )
                    CommonFilterView(
                        Modifier.padding(end = padding10), showFilter
                    )
                }
            }
            OrderLayout(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = padding6, end = padding10),
                show = showOrder,
                filter = order,
                filterList = allyOrderList
            )
            FilterLayout(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = padding6, end = padding10),
                show = showFilter,
                filter = filter,
                filterList = allyFilterList
            )
        }
    }
}


