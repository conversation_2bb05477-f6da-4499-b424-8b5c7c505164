package com.moyu.chuanqirensheng.feature.talent.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.resource.CurrentResourcesPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentTalentPoint
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.dark_line_vertical
import shared.generated.resources.hero_starup
import shared.generated.resources.line_vertical
import shared.generated.resources.talent_title3
import shared.generated.resources.user_head_frame

const val MAX_TALENT3_LEVEL = 30

@Composable
fun TalentScreen3() {
    val allTalents = repo.gameCore.getTalent3Pool().filter { it.type == 3 }
    val itemSize = ItemSize.LargePlus
    GameBackground(
        background = kmpDrawableResource("environment_3"),
        bgMask = B50,
        title = stringResource(Res.string.talent_title3),
    ) {
        Column(Modifier.fillMaxSize().verticalScroll(rememberScrollState())) {
            Spacer(modifier = Modifier.size(padding6))
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = padding10),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                CurrentResourcesPoint(index = 0, showPlus = true)
                CurrentTalentPoint(showPlus = true)
            }
            Spacer(modifier = Modifier.size(padding6))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                repeat(3) { column ->
                    Column(
                        Modifier.weight(1f),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        repeat(45) { row ->
                            TalentManager.buildTalentType3SlotModel(row + 1, column + 1, allTalents)
                                ?.let { slot ->
                                    // 外层包一个 Box：绘制竖线、技能图标、锁定图标、文字等
                                    Box(contentAlignment = Alignment.Center) {
                                        // 如果下方有 nextTalent，就绘制竖线
                                        slot.nextTalent?.let { _ ->
                                            Image(
                                                modifier = Modifier
                                                    .height(padding72)
                                                    .width(padding16)
                                                    .graphicsLayer {
                                                        // 让竖线往下偏移一点
                                                        translationY = padding80.toPx()
                                                        scaleY = 1.2f
                                                    },
                                                painter = painterResource(
                                                    if (slot.nextLocked) Res.drawable.dark_line_vertical else Res.drawable.line_vertical
                                                ),
                                                contentDescription = null,
                                                contentScale = ContentScale.FillBounds
                                            )
                                        }

                                        // 技能图标
                                        SingleSkillView(
                                            skill = slot.skill,
                                            itemSize = itemSize,
                                            locked = slot.locked,
                                            frame = Res.drawable.user_head_frame,
                                            showStars = false,
                                            showName = false,
                                        ) {
                                            Dialogs.detailTalentDialog.value = slot.showTalent.mainId
                                        }

                                        // 显示已激活等级 / 总等级
                                        StrokedText(
                                            modifier = Modifier
                                                .align(Alignment.BottomCenter)
                                                .padding(bottom = padding10),
                                            text = "${slot.talentLevel}/${slot.maxLevel}",
                                            style = MaterialTheme.typography.h3,
                                            textAlign = TextAlign.Center
                                        )

                                        // 显示技能名称
                                        StrokedText(
                                            modifier = Modifier
                                                .align(Alignment.BottomCenter)
                                                .graphicsLayer {
                                                    translationY = padding34.toPx()
                                                },
                                            text = slot.skill.name,
                                            maxLines = 2,
                                            minLines = 2,
                                            style = MaterialTheme.typography.h2,
                                            textAlign = TextAlign.Center
                                        )

                                        // 如果可升级且能负担消耗，则显示星星/加号动效
                                        val canUpgrade =
                                            (!slot.locked && slot.nextLevelTalent != null && AwardManager.isAffordable(
                                                slot.award
                                            ))
                                        if (canUpgrade) {
                                            Image(
                                                modifier = Modifier
                                                    .align(Alignment.BottomStart)
                                                    .padding(bottom = padding6)
                                                    .height(padding34),
                                                contentScale = ContentScale.FillHeight,
                                                painter = painterResource(Res.drawable.hero_starup),
                                                contentDescription = null
                                            )
                                        }
                                        if (GuideManager.guideIndex.value == 42 && column == 0 && row == 0) {
                                            GuideHand(
                                                modifier = Modifier.align(Alignment.BottomCenter).height(
                                                    padding80
                                                ).graphicsLayer {
                                                    translationY = padding45.toPx()
                                                },
                                                handType = HandType.UP_HAND
                                            )
                                        }
                                    }
                                    Spacer(modifier = Modifier.size(padding66))
                                }
                        }
                    }
                }
            }
            Spacer(Modifier.size(padding80))
        }
    }
}