package com.moyu.chuanqirensheng.feature.event.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.debug.EventIdTag
import com.moyu.chuanqirensheng.feature.event.isBattle
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.CardSize
import com.moyu.chuanqirensheng.widget.common.LabelSize
import com.moyu.chuanqirensheng.widget.common.TextLabel2
import com.moyu.chuanqirensheng.widget.effect.MovableImage
import com.moyu.core.model.Event

@Composable
fun SingleEventCard(event: Event, onSelectEvent: (Event) -> Unit) {
    Row(
        Modifier
            .fillMaxWidth()
            .graphicsLayer {
                // todo UI细调整
                translationX = -padding12.toPx()
            }) {
        InnerCard(cardSize = CardSize.Tiny, event = event) {
            onSelectEvent(event)
        }
    }
}

@Composable
fun InnerCard(cardSize: CardSize, event: Event, onClick: () -> Unit) {
    EffectButton(Modifier.size(cardSize.width, cardSize.height), onClick = {
        onClick()
    }) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f), contentAlignment = Alignment.Center
            ) {
                if (event.isBattle()) {
                    MovableImage(
                        modifier = Modifier
                            .fillMaxSize(),
                        imageResource = kmpDrawableResource(event.pic),
                    )
                } else {
                    Image(
                        modifier = Modifier
                            .fillMaxSize(),
                        painter = kmpPainterResource(event.pic),
                        contentDescription = null
                    )
                }
            }

            TextLabel2(
                Modifier
                    .graphicsLayer {
                        translationY = -padding14.toPx()
                        translationX = -padding1.toPx()
                    }
                    .scale(1.22f),
                text = event.name,
                labelSize = LabelSize.Medium2,
            )
        }
        EventIdTag(modifier = Modifier.align(Alignment.Center), event, cardSize)
    }
}