package com.moyu.chuanqirensheng.feature.event.handler

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import com.moyu.chuanqirensheng.application.Dialogs.chooseAllyDialog
import com.moyu.chuanqirensheng.application.Dialogs.chooseEquipDialog
import com.moyu.chuanqirensheng.application.Dialogs.chooseSkillDialog
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.battle.ui.BattleLayout
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE1
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.role.createStageEnemyRole
import com.moyu.chuanqirensheng.feature.setting.SettingManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.hugeButtonWidth
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.role.Role
import com.moyu.core.model.toAward
import com.moyu.core.model.toAwards
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.battling
import shared.generated.resources.next_day
import shared.generated.resources.start_battle

const val HALO_BATTLE = 3
const val HALO2_BATTLE = 4
const val SIEGE_BATTLE = 5

class BattlePlayHandler(
    override val skipWin: Boolean = false,
    override val hasLayout: MutableState<Boolean> = mutableStateOf(true),
    override val playId: Int = HALO_BATTLE
) : PlayHandler() {

    val postAwards = mutableListOf<Award>()

    @Composable
    override fun Layout(event: Event) {
        BattleLayout(event = event)
    }

    override suspend fun onEventSelect(event: Event) {
        hasLayout.value = event.pic == "0"
        // 决斗，普通战斗,宿敌战斗,诅咒战斗
        val positionMap = mutableMapOf<Int, Role?>()
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        pool.pool.map {
            repo.gameCore.getAllyById(it)
        }.map {
            createStageEnemyRole(it, event)
        }.forEachIndexed { index, role ->
            positionMap[positionListEnemy[index]] = role
        }

        repo.setCurrentEnemies(positionMap)
        repo.setCurrentAllies(BattleManager.getBattleRoles(8))

        postAwards.clear()
        if (event.playPara2.first().toInt() != 0) {
            var gainProps = event.playPara2.map { it.toInt() }.map {
                repo.gameCore.getPoolById(it).toAward()
            }
            postAwards.addAll(gainProps)
            // 全屏弹窗的情况下，要等1.5秒，让玩家看清楚选择的事件再弹窗
            if (postAwards.first().outEquips.isNotEmpty()) {
                postAwards.clear()
                postAwards.addAll(
                    repo.gameCore.getPoolById(event.playPara2.first().toInt())
                        .toAwards(forceTotalNum = 2)
                )
            } else if (postAwards.first().outAllies.isNotEmpty()) {
                postAwards.clear()
                postAwards.addAll(
                    repo.gameCore.getPoolById(event.playPara2.first().toInt())
                        .toAwards(forceTotalNum = 2)
                )
            }
        }
    }

    @Composable
    override fun HandlerButtons(event: Event) {
        if (!repo.inBattle.value) {
            Box(contentAlignment = Alignment.Center) {
                LaunchedEffect(Unit) {
                    if (SettingManager.autoSelect.value) {
                        delay(1000)
                        if (GuideManager.guideIndex.value == 3) {
                            GuideManager.guideIndex.value = 4
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                        }
                        EventManager.getOrCreateHandler(event).hasLayout.value = true
                        repo.startBattle()
                    }
                }
                GameButton(
                    text = stringResource(Res.string.start_battle),
                    buttonSize = ButtonSize.Huge,
                    hapticFeedbackType = HapticFeedbackType.LongPress,
                    onClick = {
                        if (GuideManager.guideIndex.value == 3) {
                            GuideManager.guideIndex.value = 4
                            setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                        }
                        EventManager.getOrCreateHandler(event).hasLayout.value = true
                        repo.startBattle()
                    }
                )
                if (GuideManager.guideIndex.value == 3) {
                    GuideHand(
                        modifier = Modifier.align(Alignment.BottomCenter).height(padding80)
                            .graphicsLayer {
                                translationY = -padding90.toPx()
                            },
                        handType = HandType.DOWN_HAND
                    )
                }
            }
        } else {
            GameButton(
                text = stringResource(Res.string.battling),
                buttonSize = ButtonSize.Huge,
                enabled = false,
                onClick = {

                }
            )
        }
    }

    @Composable
    override fun PostAwardButtons(event: Event) {
        if (postAwards.first().skills.isNotEmpty()) {
            // 全屏技能三选一
            chooseSkillDialog.value = postAwards.shuffled().take(3)
            // 全屏技能三选一
            GameButton(
                buttonSize = ButtonSize.Huge,
                mute = true,
                text = stringResource(Res.string.next_day)
            ) {

            }
        } else if (postAwards.first().outEquips.isNotEmpty()) {
            // 全屏宝物二选一
            chooseEquipDialog.value = postAwards.shuffled().take(2)
            // 全屏技能三选一
            GameButton(
                buttonSize = ButtonSize.Huge,
                mute = true,
                text = stringResource(Res.string.next_day)
            ) {

            }
        } else if (postAwards.first().outAllies.isNotEmpty()) {
            // 全屏兵种二选一
            chooseAllyDialog.value = postAwards.shuffled().take(2)
            // 全屏技能三选一
            GameButton(
                buttonSize = ButtonSize.Huge,
                mute = true,
                text = stringResource(Res.string.next_day)
            ) {

            }
        } else {
            if (postAwards.isNotEmpty()) {
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                    Box(contentAlignment = Alignment.Center) {
                        LaunchedEffect(Unit) {
                            if (SettingManager.autoSelect.value) {
                                delay(1000)
                                if (!eventFinished.value) {
                                    eventFinished.value = true
                                    EventManager.getOrCreateHandler(event).eventResult.value = true
                                    EventManager.getOrCreateHandler(event).eventFinished.value =
                                        true
                                    EventManager.getOrCreateHandler(event).setEventAward(
                                        postAwards[0], 0
                                    )
                                    // 战斗胜利
                                    EventManager.doEventResult(event, true)
                                }
                            }
                        }
                        GameButton(
                            text = "",
                            buttonSize = ButtonSize.Huge,
                            onClick = {
                                if (!eventFinished.value) {
                                    eventFinished.value = true
                                    EventManager.getOrCreateHandler(event).eventResult.value = true
                                    EventManager.getOrCreateHandler(event).eventFinished.value =
                                        true
                                    EventManager.getOrCreateHandler(event).setEventAward(
                                        postAwards[0], 0
                                    )
                                    // 战斗胜利
                                    EventManager.doEventResult(event, true)
                                }
                            }
                        )
                        Column(
                            modifier = Modifier.width(hugeButtonWidth),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            StrokedText(text = event.option1, style = MaterialTheme.typography.h1)
                            Spacer(Modifier.size(padding2))
                            AwardList(
                                Modifier,
                                postAwards[0],
                                param = defaultParam.copy(
                                    showColumn = false,
                                    showPlus = true,
                                    itemSize = ItemSize.Small,
                                    showName = true,
                                    showNum = true,
                                    frameDrawable = null,
                                    noFrameForItem = true,
                                    numInFrame = false,
                                    callback = {
                                        if (!eventFinished.value) {
                                            eventFinished.value = true
                                            EventManager.getOrCreateHandler(event).eventResult.value =
                                                true
                                            EventManager.getOrCreateHandler(event).eventFinished.value =
                                                true
                                            EventManager.getOrCreateHandler(event).setEventAward(
                                                postAwards[0], 0
                                            )
                                            // 战斗胜利
                                            EventManager.doEventResult(event, true)
                                        }
                                    }
                                )
                            )
                        }
                    }
                    Box(contentAlignment = Alignment.Center) {
                        GameButton(
                            text = "",
                            buttonSize = ButtonSize.Huge,
                            onClick = {
                                if (!eventFinished.value) {
                                    eventFinished.value = true
                                    EventManager.getOrCreateHandler(event).eventResult.value = true
                                    EventManager.getOrCreateHandler(event).eventFinished.value =
                                        true
                                    EventManager.getOrCreateHandler(event).setEventAward(
                                        postAwards[1], 1
                                    )
                                    // 战斗胜利
                                    EventManager.doEventResult(event, true)
                                }
                            }
                        )
                        Column(
                            modifier = Modifier.width(hugeButtonWidth),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            StrokedText(text = event.option2, style = MaterialTheme.typography.h1)
                            Spacer(Modifier.size(padding2))
                            AwardList(
                                Modifier,
                                postAwards[1],
                                param = defaultParam.copy(
                                    showColumn = false,
                                    showPlus = true,
                                    itemSize = ItemSize.Small,
                                    showName = true,
                                    showNum = true,
                                    frameDrawable = null,
                                    noFrameForItem = true,
                                    numInFrame = false,
                                    callback = {
                                        if (!eventFinished.value) {
                                            eventFinished.value = true
                                            EventManager.getOrCreateHandler(event).eventResult.value =
                                                true
                                            EventManager.getOrCreateHandler(event).eventFinished.value =
                                                true
                                            EventManager.getOrCreateHandler(event).setEventAward(
                                                postAwards[1], 1
                                            )
                                            // 战斗胜利
                                            EventManager.doEventResult(event, true)
                                        }
                                    }
                                )
                            )
                        }
                    }
                }
            }
        }
    }

    override fun getAwards(): List<Award> {
        return postAwards
    }
}