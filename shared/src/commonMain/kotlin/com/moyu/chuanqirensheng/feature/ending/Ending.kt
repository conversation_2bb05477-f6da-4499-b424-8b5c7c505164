package com.moyu.chuanqirensheng.feature.ending

import kotlinx.serialization.Serializable

@Serializable
data class Ending(
    val uuid: String,
    val storyId: Int = 1,
    val pic: String = "",
    val dieReason: String = "",
    val pass: Boolean = false,
    val ending: String = "",
    val lastEvent: String = "",
    val shareTitle: String = "",
    val age: Int = 0,
    val kill: Int = 0,
    val title: String = "",
    val endingText: String,
    val rank: Double = 0.0,
    val difficult: Int = 1,
    val stage: Int = 0,
)