package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.battlepass.getBattlePassManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.ui.SingleQuest
import com.moyu.chuanqirensheng.feature.resource.CurrentPass1Point
import com.moyu.chuanqirensheng.feature.resource.CurrentPass2Point
import com.moyu.chuanqirensheng.feature.resource.CurrentPass3Point
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.chuanqirensheng.util.millisToMidnight
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.chuanqirensheng.widget.common.GameLabel2
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Quest
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.refresh


/**
 * 通用的战令任务界面
 * @param passType 传 1/2/3 分别对应 BattlePass1 / BattlePass2 / BattlePass3
 */
@Composable
fun BattlePassQuestScreen(passType: Int) {
    // 任务列表
    val tasks = remember { mutableStateListOf<Quest>() }
    // 用来手动触发刷新
    val refresh = remember { mutableIntStateOf(0) }
    // 直到下一次刷新(比如每天零点)还剩多少毫秒
    val leftUpdateTime = remember { mutableStateOf(0L) }

    /**
     * 根据 passType 拿到对应的任务列表
     */
    fun getWarPassTasks(): List<Quest> {
        // 你在原先的 WarPassQuestScreen / WarPass2QuestScreen 写的是
        //   QuestManager.warPassTasks / warPass2Tasks / warPass3Tasks
        return when (passType) {
            1 -> QuestManager.warPass1Tasks
            2 -> QuestManager.warPass2Tasks
            3 -> QuestManager.warPass3Tasks
            else -> emptyList()
        }
    }

    /**
     * 初始化并加载任务列表
     */
    LaunchedEffect(refresh.intValue.toString()) {
        if (!isNetTimeValid()) {
            refreshNetTime()
        }
        // 让 QuestManager 初始化
        QuestManager.init()

        // “完成的任务排前面，已领取的排最后” – 你的自定义排序逻辑
        getWarPassTasks()
            .map { it.copy(done = QuestManager.getTaskDoneFlow(it)) }
            .sortedBy { it.order }
            .sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }
            .apply {
                tasks.clear()
                tasks.addAll(this)
            }
    }

    /**
     * 定时更新剩余时间，如果过了午夜就刷新任务
     */
    if (getBattlePassManager(passType).hasQuestCountDown()) {
        LaunchedEffect(refresh) {
            refreshNetTime()
            if (isNetTimeValid()) {
                while (true) {
                    leftUpdateTime.value = millisToMidnight(getCurrentTime())
                    if (leftUpdateTime.value <= 1000) {
                        delay(1000)
                        // 过了零点，再次刷新一次
                        refresh.intValue += 1
                    }
                    delay(500)
                }
            }
        }
    }

    Column(
        Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.size(padding10))

        // 1) 显示“当前战令进度”(pass1 / pass2 / pass3)
        when (passType) {
            1 -> CurrentPass1Point(modifier = Modifier.align(Alignment.CenterHorizontally))
            2 -> CurrentPass2Point(modifier = Modifier.align(Alignment.CenterHorizontally))
            3 -> CurrentPass3Point(modifier = Modifier.align(Alignment.CenterHorizontally))
        }

        // 2) 如果需要在界面显示“xx后刷新任务”，可以在这里显示
        // 如果你不想再显示，可以删除这段
        if (getBattlePassManager(passType).hasQuestCountDown()) {
            GameLabel2(
                modifier = Modifier
                    .align(Alignment.End)
                    .padding(end = padding12).size(padding120, padding30)) {
                StrokedText(
                    text = stringResource(Res.string.refresh) +
                            leftUpdateTime.value.millisToHoursMinutesSeconds(),
                    style = MaterialTheme.typography.h4
                )
            }
        }

        // 3) 列表显示具体任务
        LazyColumn(modifier = Modifier.fillMaxSize()) {
            items(tasks.size) { index ->
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = padding10)
                ) {
                    SingleQuest(tasks[index]) {
                        // 当任务完成或领取后，触发 refresh
                        refresh.intValue += 1
                    }
                    Spacer(modifier = Modifier.size(padding5))
                }
            }
        }
    }
}
