package com.moyu.chuanqirensheng.feature.drawactivity.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.drawactivity.DrawActivityManager
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.draw_activity
import shared.generated.resources.draw_icon
import shared.generated.resources.icon_quest

@Composable
fun DrawAllScreen() {
    val listTabItems = remember {
        mutableStateListOf(
            Res.drawable.draw_icon,
            Res.drawable.icon_quest
        )
    }
    val pagerState = remember {
        mutableStateOf(0)
    }
    LaunchedEffect(Unit) {
        DrawActivityManager.refresh()
    }
    GameBackground(title = stringResource(Res.string.draw_activity)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Box(Modifier.weight(1f)) {
                when (pagerState.value) {
                    0 -> DrawActivityPage()
                    else -> DrawTaskPage()
                }
            }
            NavigationTab(Modifier.padding(bottom = padding6), pagerState, listTabItems, DrawActivityManager.getReds())
        }
    }
}