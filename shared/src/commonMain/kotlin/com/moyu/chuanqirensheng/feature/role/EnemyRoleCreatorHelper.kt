package com.moyu.chuanqirensheng.feature.role

import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.Ally
import com.moyu.core.model.Event
import com.moyu.core.model.Tower
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role

fun createStageEnemyRole(race: Ally, event: Event): Role {
    val diffProperty = event.getDiffProperty() + StageManager.currentStage.value.toProperty()
    val extraProperty = BattleManager.propertyAwards.filter {
        race.match(it, false)
    }.map { it.property }.reduceOrNull { acc, propertyAward ->
        acc + propertyAward
    }?: Property()
    return DefaultAllyCreator.create(
        race,
        diffProperty + extraProperty,
        emptyList(),
        Identifier.enemy(name = race.name)
    )
}

fun createTowerEnemyRole(race: Ally, tower: Tower): Role {
    val diffProperty = tower.getDiffProperty()
    return DefaultAllyCreator.create(
        race,
        diffProperty,
        emptyList(),
        Identifier.enemy(name = race.name)
    )
}