package com.moyu.chuanqirensheng.feature.role.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.battle.ui.RoleHpWithAnim
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.Red50
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.getTextStyle
import com.moyu.chuanqirensheng.widget.effect.ShadowImage
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.role.Role


@Composable
fun SingleRoleView(
    modifier: Modifier = Modifier,
    role: Role,
    showName: Boolean = true,
    showHp: Boolean = true,
    extraInfo: String = "",
    textColor: Color = Color.White,
    itemSize: ItemSize = ItemSize.LargePlus,
    selectCallBack: (Role) -> Unit = {
        Dialogs.roleDetailDialog.value = it
    }
) {
    val race = role.getRace()
    EffectButton(modifier = modifier.width(itemSize.frameSize), onClick = {
        selectCallBack(role)
    }) {
        Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
            Box(contentAlignment = Alignment.Center) {
                val colorFilter = if (role.getShowBadBuff().isEmpty()) {
                    null
                } else {
                    ColorFilter.tint(
                        Red50, BlendMode.SrcAtop
                    )
                }
                ShadowImage(
                    modifier = Modifier
                        .size(itemSize.itemSize).scale(role.getAlly().getScaleByQualityBattle()).graphicsLayer {
                            if (!role.isPlayerSide()) {
                                rotationY = 180f
                            }
                        },
                    imageResource = kmpDrawableResource(race.getHeadIcon()),
                    colorFilter = colorFilter
                )
                if (extraInfo.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .background(B50)
                            .padding(padding4)
                    ) {
                        StrokedText(text = extraInfo, style = itemSize.getTextStyle())
                    }
                }
            }
            if (showHp) {
                Box(
                    modifier = Modifier.size(itemSize.frameSize * 0.68f, itemSize.frameSize * 0.15f)
                ) {
                    RoleHpWithAnim(role)
                }
                Spacer(modifier = Modifier.size(padding2))
            }
            if (showName) {
                StrokedText(
                    text = race.name,
                    style = itemSize.getTextStyle(),
                    maxLines = 1,
                    overflow = TextOverflow.Visible,
                    softWrap = false,
                    textAlign = TextAlign.Center,
                    color = textColor
                )
            }
        }
    }
}