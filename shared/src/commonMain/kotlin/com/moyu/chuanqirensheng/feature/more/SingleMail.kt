package com.moyu.chuanqirensheng.feature.more

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.eygraber.uri.Uri
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.platform.openGamePage
import com.moyu.chuanqirensheng.sub.language.mapToCurrentLanguage
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding400
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.ui.theme.qualityColor1
import com.moyu.chuanqirensheng.ui.theme.qualityColor2
import com.moyu.chuanqirensheng.ui.theme.qualityColorYellow
import com.moyu.chuanqirensheng.util.date2TimeStamp
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.GameLabel2
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.Texture
import com.moyu.chuanqirensheng.widget.effect.magicFrame
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.already_got
import shared.generated.resources.award_got_toast
import shared.generated.resources.gain_award
import shared.generated.resources.go


@Composable
fun SingleMail(quest: EmailData) {
    LaunchedEffect(Unit) {
        MailManager.setRead(quest.id)
    }
    val backgroundColor =  if (quest.award != null) {
        qualityColorYellow
    } else if (quest.link.isNotEmpty()) {
        qualityColor2
    } else {
        qualityColor1
    }
    val borderColor = backgroundColor.copy(
        red = backgroundColor.red * 0.8f,
        green = backgroundColor.green * 0.8f,
        blue = backgroundColor.blue * 0.8f
    )
    Box(
        modifier = Modifier
            .fillMaxWidth().heightIn(padding110, padding400)
            .magicFrame(
                base = borderColor,
                borderColor = Color.Black,
                texture = Texture.CHECKER
            ),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth().padding(horizontal = padding19, vertical = padding10)
        ) {
            Spacer(Modifier.size(padding6))
            StrokedText(
                text = quest.title.mapToCurrentLanguage(),
                style = MaterialTheme.typography.h1
            )
            Spacer(Modifier.size(padding8))
            StrokedText(
                text = quest.content.mapToCurrentLanguage(),
                style = MaterialTheme.typography.h3
            )
            quest.award?.toAward()?.let {
                AwardList(
                    award = it,
                    param = defaultParam.copy(
                        numInFrame = true,
                        showName = false,
                        itemSize = ItemSize.Medium
                    ),
                    paddingVerticalInDp = padding0,
                    paddingHorizontalInDp = padding0
                )
            }
            Spacer(Modifier.size(padding4))
            if (quest.award != null) {
                val gained = MailManager.isGained(quest)
                GameButton(
                    text =
                        if (gained) stringResource(Res.string.already_got)
                        else stringResource(Res.string.gain_award),
                    enabled = !gained,
                    buttonStyle = ButtonStyle.Green,
                    buttonSize = ButtonSize.Medium,
                    onClick = {
                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                            if (gained) {
                                AppWrapper.getStringKmp(Res.string.award_got_toast).toast()
                            } else {
                                AwardManager.doNetAward(quest.awardCode)
                            }
                        }
                    })
            } else if (quest.link.isNotEmpty()) {
                GameButton(
                    text = stringResource(Res.string.go),
                    buttonStyle = ButtonStyle.Green,
                    buttonSize = ButtonSize.Medium,
                    onClick = {
                        val uri: Uri = Uri.parse(quest.link)
                        openGamePage(uri)
                    })
            }
            Spacer(Modifier.size(padding8))
            GameLabel2(
                modifier = Modifier
                    .align(Alignment.End)
                    .padding(end = padding12).size(padding90, padding26)) {
                StrokedText(
                    text = date2TimeStamp(quest.createTime, "yyyy-MM-dd"),
                    style = MaterialTheme.typography.h5
                )
            }
        }
    }
}
