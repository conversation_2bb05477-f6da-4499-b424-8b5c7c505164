package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.activities.ui.ActivityItem
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass1Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass3Manager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.router.gotoBattlePassScreenByIndex
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_BATTLE_PASS1
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_BATTLE_PASS2
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_BATTLE_PASS3
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.battle_pass1
import shared.generated.resources.battle_pass2
import shared.generated.resources.battle_pass3
import shared.generated.resources.battle_pass_top_label
import shared.generated.resources.common_big_frame

val battlePassItems = listOf(
    ActivityItem(
        name = {
            AppWrapper.getStringKmp(Res.string.battle_pass1)
        }, route = {
            gotoBattlePassScreenByIndex(1)
        },
        frame = "battle_pass1_label",
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS1).desc,
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS1)
            UnlockManager.getUnlockedFlow(unlock)
        },
        red = {
            BattlePass1Manager.hasRedItemToGain() || QuestManager.warPass1Tasks.any {
                QuestManager.getTaskDoneFlow(it) && !it.opened
            }
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.battle_pass2) }, route = {
            gotoBattlePassScreenByIndex(2)
        },
        frame = "battle_pass2_label",
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS2).desc,
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS2)
            UnlockManager.getUnlockedFlow(unlock)
        },
        red = {
            BattlePass2Manager.hasRedItemToGain() || QuestManager.warPass2Tasks.any {
                QuestManager.getTaskDoneFlow(it) && !it.opened
            }
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.battle_pass3) }, route = {
            gotoBattlePassScreenByIndex(3)
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS3).desc,
        frame = "battle_pass3_label",
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS3)
            UnlockManager.getUnlockedFlow(unlock)
        },
        red = {
            BattlePass3Manager.hasRedItemToGain() || QuestManager.warPass3Tasks.any {
                QuestManager.getTaskDoneFlow(it) && !it.opened
            }
        }
    ),
)

@Composable
fun BattlePassAllScreen() {
    GameBackground(title = stringResource(Res.string.battle_pass1), topContent = {
        Image(
            modifier = Modifier
                .fillMaxWidth().scale(1.25f),
            painter = painterResource(Res.drawable.battle_pass_top_label),
            contentScale = ContentScale.FillWidth,
            contentDescription = null
        )
    }) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(padding10),
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding10, horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            battlePassItems.forEach {
                ActivityItem(
                    moreItem = it,
                )
            }
        }
    }
}