package com.moyu.chuanqirensheng.feature.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.feature.stage.StageManager.currentStage
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding400
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.stage_label

@Composable
fun GameMenu(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        LaunchedEffect(StageManager.maxStage.value) {
            // 默认选中最后一个解锁了的关卡
            if (StageManager.needAutoSelectLastStage.value) {
                currentStage.value = repo.gameCore.getDungeonPool()[StageManager.maxStage.value.coerceIn(0, repo.gameCore.getDungeonPool().size - 1)]
                StageManager.needAutoSelectLastStage.value = false
            }
        }
        CreateStageLayout(Modifier.height(padding400))
    }
}

@Composable
fun LabelView(modifier: Modifier, text: String) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(Res.drawable.stage_label),
            contentDescription = null
        )
        StrokedText(
            modifier = Modifier.graphicsLayer {
                translationY = -padding3.toPx()
            },
            text = text,
            style = MaterialTheme.typography.h2,
            color = Color.White,
        )
    }
}
