package com.moyu.chuanqirensheng.feature.feedback.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.api.postFeedBack
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.feedback.FeedbackReq
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.resetClipboard
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.button.HUGE_GAP
import com.moyu.chuanqirensheng.widget.common.DecorateTextField
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.contact1
import shared.generated.resources.copied
import shared.generated.resources.feed_back
import shared.generated.resources.feed_back_ok
import shared.generated.resources.network_error

val feedbackCount = mutableStateOf(0)

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun FeedBackScreen() {
    GameBackground(title = stringResource(Res.string.feed_back)) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding10, horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(padding36))
            val text = remember {
                mutableStateOf("")
            }
            Spacer(modifier = Modifier.size(padding19))
            Column(
                modifier = Modifier
                    .align(Alignment.Start)
                    .padding(horizontal = padding26),
            ) {
                DecorateTextField(
                    modifier = Modifier.fillMaxWidth()
                        .height(
                            padding200
                        ), text = text.value
                ) {
                    text.value = it
                }
                Spacer(modifier = Modifier.size(padding2))
                StrokedText(
                    text = "${text.value.length} / 300",
                    style = MaterialTheme.typography.h3,
                    modifier = Modifier.align(Alignment.End).padding(end = padding10)
                )
                Spacer(modifier = Modifier.size(padding10))
                GameButton(
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    enabled = text.value.isNotEmpty(),
                    clickGap = HUGE_GAP * 2,
                    onClick = {
                        if (text.value.isNotEmpty()) {
                            AppWrapper.globalScope.launch(Dispatchers.IO) {
                                feedbackCount.value += 1
                                if (feedbackCount.value >= 5) {
                                    AppWrapper.getStringKmp(Res.string.network_error).toast()
                                } else {
                                    try {
                                        postFeedBack(
                                            FeedbackReq(
                                                content = text.value,
                                                userId = gameSdkDefaultProcessor().getObjectId()
                                                    ?: "none",
                                                userName = gameSdkDefaultProcessor().getUserName()
                                                    ?: "none",
                                            )
                                        )
                                        AppWrapper.getStringKmp(Res.string.feed_back_ok).toast()
                                        text.value = ""
                                    } catch (e: Exception) {
                                        AppWrapper.getStringKmp(Res.string.network_error).toast()
                                    }
                                }
                            }
                        }
                    },
                    text = stringResource(Res.string.feed_back),
                    textColor = Color.White,
                    buttonStyle = ButtonStyle.Green,
                    buttonSize = ButtonSize.Medium
                )
                Spacer(modifier = Modifier.size(padding10))
                StrokedText(
                    text = stringResource(Res.string.contact1),
                    style = MaterialTheme.typography.h3
                )
                Row(modifier = Modifier.clickable {
                    resetClipboard("<EMAIL>")
                    ("gmail：<EMAIL> " + AppWrapper.getStringKmp(Res.string.copied)).toast()
                }) {
                    StrokedText(
                        text = "email：",
                        style = MaterialTheme.typography.h3
                    )
                    StrokedText(
                        text = "<EMAIL>",
                        style = MaterialTheme.typography.h3,
                    )
                }
                if (hasGoogleService()) {
                    Row(modifier = Modifier.clickable {
                        resetClipboard("<EMAIL>")
                        ("email：<EMAIL> " + AppWrapper.getStringKmp(Res.string.copied)).toast()
                    }) {
                        StrokedText(
                            text = "email：",
                            style = MaterialTheme.typography.h3,
                        )
                        StrokedText(
                            text = "<EMAIL>",
                            style = MaterialTheme.typography.h3,
                        )
                    }
                } else {
                    Row(modifier = Modifier.clickable {
                        resetClipboard("T15760456854")
                        ("微信: T15760456854" + AppWrapper.getStringKmp(Res.string.copied)).toast()
                    }) {
                        StrokedText(
                            text = "微信: ",
                            style = MaterialTheme.typography.h3,
                        )
                        StrokedText(
                            text = "T15760456854",
                            style = MaterialTheme.typography.h3,
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding10))
            }
            Spacer(modifier = Modifier.size(padding10))
        }
    }
}