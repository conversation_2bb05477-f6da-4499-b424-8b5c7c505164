package com.moyu.chuanqirensheng.feature.gift.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.login.UpdateTimeText
import com.moyu.chuanqirensheng.platform.billPrepay
import com.moyu.chuanqirensheng.platform.hasBilling
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.text.getPriceText
import com.moyu.chuanqirensheng.ui.theme.SkillLevel5Color
import com.moyu.chuanqirensheng.ui.theme.W10
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding220
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding400
import com.moyu.chuanqirensheng.ui.theme.padding420
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding570
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding64
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.ButtonType
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.TitleCloseButton
import com.moyu.chuanqirensheng.widget.dialog.EmptyDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Gift
import com.moyu.core.model.toAward
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.common_arrow_down
import shared.generated.resources.gift_frame_dialog
import shared.generated.resources.gift_label_frame
import shared.generated.resources.sell_label
import shared.generated.resources.shop_discount
import shared.generated.resources.sold_out

@Composable
fun GiftDetailDialog(show: MutableState<Gift?>) {
    show.value?.takeIf { !getBooleanFlowByKey(KEY_GIFT_AWARDED + it.id) || it.limitBuy == 0 }
        ?.let { gift ->
            val gifts = mutableStateOf(GiftManager.getDisplayGifts())
            LaunchedEffect(gift.id) {
                if (!gift.dialogShowed) {
                    // 首次展示
                    reportManager().giftShow(gift.id)
                }
            }
            EmptyDialog(showTips = false, onDismissRequest = {
                GiftManager.setShowed(gift)
                show.value = null
            }) {
                Box(Modifier.fillMaxSize().clickable {
                    show.value = null
                    GiftManager.setShowed(gift)
                    GameCore.instance.onBattleEffect(SoundEffect.Click)
                }, contentAlignment = Alignment.Center) {
                    Box(
                        modifier = Modifier.align(Alignment.Center)
                            .size(padding400, padding570).graphicsLayer {
                                translationY = -padding60.toPx()
                            }
                    ) {
                        Image(
                            modifier = Modifier
                                .align(Alignment.TopCenter).padding(
                                    top =
                                        padding80
                                )
                                .size(padding380, padding420),
                            painter = painterResource(Res.drawable.gift_frame_dialog),
                            contentDescription = null
                        )
                        Image(
                            modifier = Modifier
                                .align(Alignment.TopCenter).padding(
                                    top =
                                        padding100
                                )
                                .size(padding300),
                            painter = painterResource(kmpDrawableResource(gift.pic)),
                            contentDescription = null
                        )
                        Column(
                            Modifier.fillMaxSize(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Box(Modifier.height(padding60), contentAlignment = Alignment.Center) {
                                Image(
                                    modifier = Modifier
                                        .align(Alignment.TopCenter)
                                        .width(padding400).fillMaxHeight(),
                                    painter = painterResource(Res.drawable.sell_label),
                                    contentDescription = null
                                )
                                TitleCloseButton(
                                    Modifier
                                        .align(Alignment.CenterEnd)
                                ) {
                                    GiftManager.setShowed(gift)
                                    show.value = null
                                }
                                StrokedText(
                                    modifier = Modifier.scale(1.2f),
                                    text = gift.name,
                                    style = MaterialTheme.typography.h1
                                )
                            }
                            Spacer(modifier = Modifier.size(padding19))
                            Box(
                                Modifier.align(Alignment.End).padding(end = padding12)
                                    .size(padding100, padding80),
                                contentAlignment = Alignment.Center
                            ) {
                                Image(
                                    modifier = Modifier.fillMaxSize().scale(1.4f),
                                    painter = painterResource(Res.drawable.shop_discount),
                                    contentDescription = null
                                )
                                StrokedText(
                                    text = gift.content,
                                    style = MaterialTheme.typography.h1
                                )
                            }
                            Spacer(modifier = Modifier.weight(1f))
                            if (gift.content2 != "0") {
                                Box(
                                    Modifier.align(Alignment.Start).padding(start = padding12)
                                        .size(padding220, padding50),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Image(
                                        modifier = Modifier.fillMaxSize(),
                                        painter = painterResource(Res.drawable.gift_label_frame),
                                        contentDescription = null
                                    )
                                    StrokedText(
                                        text = gift.content2,
                                        style = MaterialTheme.typography.h1,
                                        textAlign = TextAlign.Center
                                    )
                                }
                                Spacer(Modifier.size(padding20))
                            }
                            val sell =
                                repo.gameCore.getSellPool().first { it.id == gift.id }
                            AwardList(
                                modifier = Modifier.padding(horizontal = padding12).graphicsLayer {
                                    translationX = padding4.toPx()
                                    translationY = -padding6.toPx()
                                },
                                award = sell.toAward(),
                                param = defaultParam.copy(
                                    itemSize = ItemSize.Large,
                                    textColor = Color.White,
                                    numInFrame = true,
                                    showName = false
                                )
                            )
                            Spacer(Modifier.size(padding4))
                            val hasBilling = hasBilling()
                            GameButton(
                                text = sell.getPriceText(),
                                buttonType = ButtonType.AiFaDian,
                                buttonStyle = ButtonStyle.Green,
                                buttonSize = ButtonSize.Big
                            ) {
                                GiftManager.setShowed(gift)
                                show.value = null
                                if (getBooleanFlowByKey(KEY_GIFT_AWARDED + gift.id) && gift.limitBuy != 0) {
                                    // 已经买了，并且是只能买一次的东西，就不给再买
                                    AppWrapper.getStringKmp(Res.string.sold_out).toast()
                                } else {
                                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                                        if (hasBilling) {
                                            billPrepay(sell) {
                                                AppWrapper.globalScope.launch(Dispatchers.Main) {
                                                    AwardManager.realGainItem(sell, sell.toAward())
                                                }
                                            }
                                        } else {
                                            val realAward = sell.toAward()
                                            AwardManager.realGainItem(sell, realAward)
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (gifts.value.isNotEmpty() && gifts.value.size > 1) {
                        Row(
                            Modifier.align(Alignment.BottomCenter)
                                .padding(horizontal = padding19)
                                .padding(bottom = padding64)
                                .horizontalScroll(rememberScrollState()),
                            horizontalArrangement = Arrangement.spacedBy(padding6),
                            verticalAlignment = Alignment.Bottom
                        ) {
                            gifts.value.forEach {
                                val selected = show.value?.id == it.id
                                EffectButton(onClick = {
                                    GiftManager.setShowed(gift)
                                    Dialogs.giftDetailDialog.value = it
                                }) {
                                    Column(
                                        modifier = Modifier
                                            .clip(
                                                RoundedCornerShape(
                                                    padding8
                                                )
                                            )
                                            .background(W10)
                                            .padding(vertical = padding10),
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Image(
                                            painter = painterResource(kmpDrawableResource(it.icon)),
                                            modifier = Modifier.size(
                                                imageHugeLite
                                            ),
                                            contentDescription = null,
                                        )
                                        if (it.limitTime != 0 && it.displayTime != 0L && isNetTimeValid()) {
                                            UpdateTimeText(it) { }
                                        } else {
                                            StrokedText(
                                                text = "",
                                                style = MaterialTheme.typography.body1,
                                            )
                                        }
                                        StrokedText(
                                            modifier = Modifier.width(imageHugeLite * 1.4f),
                                            maxLines = 2,
                                            minLines = 2,
                                            textAlign = TextAlign.Center,
                                            text = it.name,
                                            color = if (selected) SkillLevel5Color else Color.White,
                                            style = MaterialTheme.typography.h4
                                        )
                                    }
                                    if (selected) {
                                        Image(
                                            modifier = Modifier
                                                .size(imageMedium)
                                                .align(Alignment.BottomCenter)
                                                .graphicsLayer {
                                                    rotationX = 180f
                                                    translationY = padding36.toPx()
                                                },
                                            painter = painterResource(Res.drawable.common_arrow_down),
                                            contentDescription = null
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
}
