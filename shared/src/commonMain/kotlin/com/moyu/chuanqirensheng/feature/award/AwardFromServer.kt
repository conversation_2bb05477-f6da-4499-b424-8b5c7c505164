package com.moyu.chuanqirensheng.feature.award

import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.Award
import com.moyu.core.model.toAward
import kotlinx.serialization.Serializable

@Serializable
data class AwardFromServer(
    val code: String = "",
    val message: String = "",
    val allies: List<Int> = emptyList(),
    val equips: List<Int> = emptyList(),
    val diamond: Int = 0,
    val pvpDiamond: Int = 0,
    val key: Int = 0,
    val electric: Int = 0,
    val realMoney: Int = 0,
    val lotteryMoney: Int = 0,
    val couponAlly: Int = 0,
    val couponHero: Int = 0,
    val unlockList: List<Int> = emptyList(),
    val poolIds: List<Int> = emptyList(),
    val sellId: Int = 0,
    val resultCode: Int = 0,
) {
    fun toAward(): Award {
        val realAllies = allies.map {
            repo.gameCore.getAllyById(it)
        }
        val realEquips = equips.map {
            repo.gameCore.getEquipById(it)
        }
        val poolAwards = poolIds.map {
            repo.gameCore.getPoolById(it).toAward()
        }.reduceOrNull { acc, award -> acc + award }?: Award()
        val sellAward = if (sellId != 0) {
            repo.gameCore.getSellPool().firstOrNull { it.id == sellId }?.toAward() ?: Award()
        } else Award()
        return Award(
            message = message,
            outAllies = realAllies,
            outEquips = realEquips,
            electric = electric,
            diamond = diamond,
            realMoney = realMoney,
            lotteryMoney = lotteryMoney,
            pvpDiamond = pvpDiamond,
            couponAlly = couponAlly,
            couponEquip = couponHero,
            key = key,
            unlockList = unlockList,
            sellId = sellId,
            resultCode = resultCode
        ) + poolAwards + sellAward
    }
}
