package com.moyu.chuanqirensheng.feature.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.ally.ui.UpgradeAnimView
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding62
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Unlock
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_lock
import shared.generated.resources.locked
import shared.generated.resources.menu_not_selected
import shared.generated.resources.red_icon

@Composable
fun BottomItem(
    modifier: Modifier,
    name: String,
    icon: String,
    selected: Boolean,
    unlock: List<Unlock>,
    redIcon: () -> Boolean = { false },
    upgradeIcon: () -> Boolean = { false },
    click: () -> Unit
) {
    val locked = unlock.all { !UnlockManager.getUnlockedFlow(it) }
    val pressing = remember {
        mutableStateOf(false)
    }
    val colorFilter = if (pressing.value) {
        ColorFilter.tint(
            B50, BlendMode.SrcAtop
        )
    } else {
        null
    }
    val color = if (pressing.value) W50 else Color.White
    EffectButton(
        modifier = modifier,
        pressing = pressing,
        onClick = {
            if (locked) {
                // 未解锁，弹提示
                (unlock.firstOrNull()?.desc ?: "").toast()
            } else {
                // 已解锁，真正点击事件
                click()
            }
        }
    ) {
        if (!selected) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(Res.drawable.menu_not_selected),
                contentDescription = null
            )
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(contentAlignment = Alignment.Center) {
                val colorLockFilter =
                    if (locked) ColorFilter.tint(
                        B50,
                        BlendMode.SrcAtop
                    ) else null
                Image(
                    modifier = Modifier.size(padding62),
                    painter = painterResource(kmpDrawableResource(icon)),
                    colorFilter = colorFilter?: colorLockFilter,
                    contentDescription = null
                )
                if (locked) {
                    Image(
                        modifier = Modifier.size(imageMedium),
                        painter = painterResource(Res.drawable.common_lock),
                        contentDescription = stringResource(Res.string.locked),
                    )
                }
                if (redIcon()) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .size(imageSmall),
                        colorFilter = colorFilter?: colorLockFilter,
                        painter = painterResource(Res.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
            StrokedText(
                modifier = Modifier.graphicsLayer {
                    translationY = -padding4.toPx()
                },
                text = name,
                color = color,
                style = MaterialTheme.typography.h2,
            )
        }
        if (upgradeIcon()) {
            UpgradeAnimView(
                Modifier.align(Alignment.BottomEnd).padding(bottom = padding12).height(padding36)
            )
        }
    }
}