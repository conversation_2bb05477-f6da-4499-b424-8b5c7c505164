package com.moyu.chuanqirensheng.feature.role.ui

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import com.moyu.core.AppWrapper
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.property.Property
import core.generated.resources.attack
import core.generated.resources.attack_tips
import core.generated.resources.defense1_tips
import core.generated.resources.dodge
import core.generated.resources.dodge_tips
import core.generated.resources.fatal_damage
import core.generated.resources.fatal_damage_tips
import core.generated.resources.fatal_rate
import core.generated.resources.fatal_rate_tips
import core.generated.resources.hp
import core.generated.resources.hp_tips
import core.generated.resources.speed
import core.generated.resources.speed_tips
import shared.generated.resources.Res
import shared.generated.resources.battle_attribute_1
import shared.generated.resources.battle_attribute_2
import shared.generated.resources.battle_attribute_3
import shared.generated.resources.battle_attribute_4
import shared.generated.resources.battle_attribute_5
import shared.generated.resources.battle_attribute_6
import shared.generated.resources.battle_attribute_7
import kotlin.math.max
import core.generated.resources.Res as CoreRes

@Composable
fun Property.MainPropertyLine(
    originProperty: Property = Property(),
    showBoost: Boolean = false,
    needMinus: Boolean = true, // 是否需要做减法，把当前属性减去origin属性显示
    showPlusMinus: Boolean = showBoost, // 是否需要显示加减号，在数值前面
    countStart: Int = 0,
    countEnd: Int = 100,
    textStyle: TextStyle = MaterialTheme.typography.h3,
    showZero: Boolean = true,
    showName: Boolean = true,
    showNegative: Boolean = false,
    textColor: Color = Color.White,
    showIcon: Boolean = true,
    hideHp: Boolean = false,
) {
    val minValue = if (showNegative) -999999.0 else 0.0
    var count = 1
    if (count in countStart until countEnd) {
        if (showZero || attack.toDouble() - originProperty.attack.toDouble() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_1,
            getProperty = { max(minValue, if (needMinus) attack.toDouble() - originProperty.attack.toDouble() else attack.toDouble()) },
            name = AppWrapper.getStringKmp(CoreRes.string.attack),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.attack_tips) },
            isBoost = { (attack.toDouble() - originProperty.attack) },
            showBoost = showBoost,
            showName = showName,
            showPlusMinus = showPlusMinus,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || defenses.toDouble() - originProperty.defenses.toDouble() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_2,
            getProperty = {
                max(
                    minValue,
                    if (needMinus) defenses.toDouble() - originProperty.defenses.toDouble() else defenses.toDouble()
                )
            },
            name = DamageType.DamageType1.defenseName,
            getTips = { AppWrapper.getStringKmp(CoreRes.string.defense1_tips) },
            isBoost = { (defenses.toDouble() - originProperty.defenses) },
            showBoost = showBoost,
            showName = showName,
            showPlusMinus = showPlusMinus,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (!hideHp) {
            if (showZero || hp.toDouble() - originProperty.hp.toDouble() != 0.0) PropertyItem(
                icon = Res.drawable.battle_attribute_3,
                getProperty = { if (needMinus) hp.toDouble() - originProperty.hp else hp.toDouble() },
                name = AppWrapper.getStringKmp(CoreRes.string.hp),
                getTips = { AppWrapper.getStringKmp(CoreRes.string.hp_tips) },
                isBoost = { (hp.toDouble() - originProperty.hp) },
                showBoost = showBoost,
                showName = showName,
                showPlusMinus = showPlusMinus,
                textStyle = textStyle, textColor = textColor, showIcon = showIcon
            )
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealFatalRate() - originProperty.getRealFatalRate() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_4,
            getProperty = { max(minValue, if (needMinus) getRealFatalRate() - originProperty.getRealFatalRate() else getRealFatalRate()) },
            name = AppWrapper.getStringKmp(CoreRes.string.fatal_rate),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.fatal_rate_tips) },
            showPercent = true,
            isBoost = { (getRealFatalRate() - originProperty.getRealFatalRate()) },
            showBoost = showBoost,
            showName = showName,
            showPlusMinus = showPlusMinus,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealFatalDamage() - originProperty.getRealFatalDamage() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_5,
            getProperty = { max(minValue, if (needMinus) getRealFatalDamage() - originProperty.getRealFatalDamage() else getRealFatalDamage()) },
            name = AppWrapper.getStringKmp(CoreRes.string.fatal_damage),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.fatal_damage_tips) },
            showPercent = true,
            isBoost = { (getRealFatalDamage() - originProperty.getRealFatalDamage()) },
            showBoost = showBoost,
            showName = showName,
            showPlusMinus = showPlusMinus,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealDodgeRate() - originProperty.getRealDodgeRate() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_6,
            getProperty = { max(minValue, if (needMinus) getRealDodgeRate() - originProperty.getRealDodgeRate() else getRealDodgeRate()) },
            name = AppWrapper.getStringKmp(CoreRes.string.dodge),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.dodge_tips) },
            showPercent = true,
            isBoost = { (getRealDodgeRate() - originProperty.getRealDodgeRate()) },
            showBoost = showBoost,
            showName = showName,
            showPlusMinus = showPlusMinus,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || speed.toDouble() - originProperty.speed != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_7,
            getProperty = { max(minValue, if (needMinus) speed.toDouble() - originProperty.speed else speed.toDouble()) },
            name = AppWrapper.getStringKmp(CoreRes.string.speed),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.speed_tips) },
            isBoost = { (speed.toDouble() - originProperty.speed) },
            showBoost = showBoost,
            showName = showName,
            showPlusMinus = showPlusMinus,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
}