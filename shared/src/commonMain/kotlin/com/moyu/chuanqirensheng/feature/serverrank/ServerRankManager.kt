package com.moyu.chuanqirensheng.feature.serverrank

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.api.getServerRankAward
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.quest.FOREVER
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.serverrank.ui.serverRankList
import com.moyu.chuanqirensheng.feature.serverrank.ui.serverSolidRankList
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_SERVER_RANK_GAME_TASK1
import com.moyu.chuanqirensheng.sub.datastore.KEY_SERVER_RANK_GAME_TASK2
import com.moyu.chuanqirensheng.sub.datastore.KEY_SERVER_RANK_GAME_TASK3
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.AppWrapper
import com.moyu.core.model.Quest
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import shared.generated.resources.Res
import shared.generated.resources.net_error_retry

object ServerRankManager {
    val serverRankTasks1 = mutableStateListOf<Quest>()
    val serverRankTasks2 = mutableStateListOf<Quest>()
    val serverRankTasks3 = mutableStateListOf<Quest>()

    fun init(force: Boolean = false) {
        if (canShowServerRankEntrance() || force) {
            createServerRankTasks1()
            createServerRankTasks2()
            createServerRankTasks3()
        }
    }

    fun createServerRankTasks1() {
        if (serverRankTasks1.isEmpty()) {
            getListObject<Quest>(KEY_SERVER_RANK_GAME_TASK1).let { taskList ->
                val tasks = taskList.mapNotNull { task ->
                    repo.gameCore.getGameTaskPool().firstOrNull { it.id == task.id }
                        ?.copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                serverRankTasks1.addAll(tasks)
            }
        }
        val shouldShowTasks = repo.gameCore.getGameTaskPool().filter {
            it.isServerTask1()
        }
        val currentTaskIds = serverRankTasks1.map { it.id }
        val tobeAdded = shouldShowTasks.filter {
            !currentTaskIds.contains(it.id)
        }.map { task ->
            // 要用永久计数，但是又要移除之前的计数
            val needRemove = if (task.isMaxRecordQuest()) {
                0
            } else {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + task.type + postFix, 0)
            }
            task.copy(needRemoveCount = needRemove)
        }.sortedBy {
            it.order
        }
        serverRankTasks1.addAll(tobeAdded)
        setListObject(KEY_SERVER_RANK_GAME_TASK1, serverRankTasks1, Quest.serializer())
    }

    fun createServerRankTasks2() {
        if (serverRankTasks2.isEmpty()) {
            getListObject<Quest>(KEY_SERVER_RANK_GAME_TASK2).let { taskList ->
                val tasks = taskList.mapNotNull { task ->
                    repo.gameCore.getGameTaskPool().firstOrNull { it.id == task.id }
                        ?.copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                serverRankTasks2.addAll(tasks)
            }
        }
        val shouldShowTasks = repo.gameCore.getGameTaskPool().filter {
            it.isServerTask2()
        }
        val currentTaskIds = serverRankTasks2.map { it.id }
        val tobeAdded = shouldShowTasks.filter {
            !currentTaskIds.contains(it.id)
        }.map { task ->
            // 要用永久计数，但是又要移除之前的计数
            val needRemove = if (task.isMaxRecordQuest()) {
                0
            } else {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + task.type + postFix, 0)
            }
            task.copy(needRemoveCount = needRemove)
        }.sortedBy {
            it.order
        }
        serverRankTasks2.addAll(tobeAdded)

        setListObject(KEY_SERVER_RANK_GAME_TASK2, serverRankTasks2, Quest.serializer())
    }

    fun createServerRankTasks3() {
        if (serverRankTasks3.isEmpty()) {
            getListObject<Quest>(KEY_SERVER_RANK_GAME_TASK3).let { taskList ->
                val tasks = taskList.mapNotNull { task ->
                    repo.gameCore.getGameTaskPool().firstOrNull { it.id == task.id }
                        ?.copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                serverRankTasks3.addAll(tasks)
            }
        }
        val shouldShowTasks = repo.gameCore.getGameTaskPool().filter {
            it.isServerTask3()
        }
        val currentTaskIds = serverRankTasks3.map { it.id }
        val tobeAdded = shouldShowTasks.filter {
            !currentTaskIds.contains(it.id)
        }.map { task ->
            // 要用永久计数，但是又要移除之前的计数
            val needRemove = if (task.isMaxRecordQuest()) {
                0
            } else {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + task.type + postFix, 0)
            }
            task.copy(needRemoveCount = needRemove)
        }.sortedBy {
            it.order
        }
        serverRankTasks3.addAll(tobeAdded)

        setListObject(KEY_SERVER_RANK_GAME_TASK3, serverRankTasks3, Quest.serializer())
    }


    fun hasRed1(): Boolean {
        return (serverRankTasks1).any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        }
    }

    fun hasRed2(): Boolean {
        return (serverRankTasks2).any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        }
    }

    fun hasRed3(): Boolean {
        return (serverRankTasks3).any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        }
    }

    fun canShowServerRank(): Boolean {
        if (!isNetTimeValid()) {
            return false
        }
        // 获取纽约时间的 12 月 24 日 0 时开始的时间戳（毫秒）
        val nyStartTimeMillis = LoginManager.instance.loginData.value.serverData.createTimeStamp
        val nyEndTimeMillis = nyStartTimeMillis + 7 * 24 * 60 * 60 * 1000L // 7 天后的时间戳

        // 转换为北京时间的范围
        val bjStartTimeMillis = nyStartTimeMillis
        val bjEndTimeMillis = nyEndTimeMillis

        // 获取当前北京时间
        val currentBeijingTime = getCurrentTime()

        // 判断当前时间是否在北京时间范围内
        return currentBeijingTime in bjStartTimeMillis..bjEndTimeMillis
    }

    fun canShowServerRankEntrance(): Boolean {
        if (!isNetTimeValid()) {
            return false
        }
        // 持续10天可以看到入口
        // 获取纽约时间的 12 月 24 日 0 时开始的时间戳（毫秒）
        val nyStartTimeMillis = LoginManager.instance.loginData.value.serverData.createTimeStamp
        val nyEndTimeMillis = nyStartTimeMillis + 10 * 24 * 60 * 60 * 1000L // 7 天后的时间戳

        val bjStartTimeMillis = nyStartTimeMillis

        // 获取当前时间
        val currentBeijingTime = getCurrentTime()

        return currentBeijingTime in bjStartTimeMillis..nyEndTimeMillis
    }

    fun canShowOnlyRank(): Boolean {
        return canShowServerRankEntrance() && !canShowServerRank()
    }

    fun unlocked(): Boolean {
        return true
    }

    fun getTasksByType(type: Int): List<Quest> {
        return when (type) {
            1 -> serverRankTasks1
            2 -> serverRankTasks2
            else -> serverRankTasks3
        }
    }

    fun getRedIcons(type: Int): List<Boolean> {
        return when (type) {
            1 -> listOf(hasRed1(), false)
            2 -> listOf(hasRed2(), false)
            else -> listOf(hasRed3(), false)
        }
    }

    suspend fun refreshServerRankList(type: Int, clear: Boolean = false) {
        if (clear) {
            serverRankList[type - 1].value = emptyList()
            serverSolidRankList[type - 1].value = emptyList()
        }
        // 活动排行榜任务依赖定榜，切过来拉一次定榜
        try {
            delay(200)
            if (canShowOnlyRank()) {
                // 活动已经结束，拉取定榜
                if (serverSolidRankList[type - 1].value.isEmpty()) {
                    getServerRankAward(
                        platform = platformChannel(),
                        type = type
                    ).let {
                        serverSolidRankList[type - 1].value = json.decodeFromString(
                            ListSerializer(RankData.serializer()), it.message
                        )
                    }
                }
            }
        } catch (e: Exception) {
            AppWrapper.getStringKmp(Res.string.net_error_retry).toast()
        }
    }
}