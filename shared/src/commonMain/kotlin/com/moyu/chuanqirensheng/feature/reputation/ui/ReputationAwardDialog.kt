package com.moyu.chuanqirensheng.feature.reputation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.story.toReputationName
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.dialogHeight
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.toAward
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.already_got
import shared.generated.resources.common_frame_long
import shared.generated.resources.gain_award
import shared.generated.resources.reputation_level_not_enough

@Composable
fun ReputationAwardDialog(show: MutableState<Int>) {
    show.value.takeIf { it >= 0 }?.let { reputationIndex ->
        PanelDialog(onDismissRequest = {
            show.value = -1
        }, dialogHeightDp = dialogHeight) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                StrokedText(
                    text =  (reputationIndex + 1).toReputationName(),
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding10))
                val level = AwardManager.toReputationLevelData()[reputationIndex]
                val pool = repo.gameCore.getReputationLevelPool().filter { it.level > 0 }
                LazyColumn(modifier = Modifier.fillMaxWidth().weight(1f), content = {
                    items(pool.size) { index ->
                        val it = pool[index]
                        val levelEnough = level.level >= it.level
                        val gained =
                            ReputationManager.isReputationAwardGained(it, reputationIndex)
                        if (!gained) {
                            Row(
                                modifier = Modifier
                                    .height(padding120)
                                    .fillMaxWidth()
                                    .paint(
                                        painterResource(Res.drawable.common_frame_long),
                                        contentScale = ContentScale.FillBounds,
                                    )
                                    .padding(horizontal = padding10),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceEvenly,
                            ) {
                                StrokedText(
                                    text = "Lv." + it.name,
                                    style = MaterialTheme.typography.h2,
                                )
                                AwardList(
                                    award = it.toAward(reputationIndex),
                                )
                                GameButton(text = if (gained) stringResource(Res.string.already_got)
                                else if (!levelEnough) stringResource(
                                    Res.string.reputation_level_not_enough
                                )
                                else stringResource(
                                    Res.string.gain_award
                                ),
                                    buttonSize = ButtonSize.Medium,
                                    enabled = levelEnough && !gained,
                                    buttonStyle = ButtonStyle.Blue,
                                    onClick = {
                                        if (levelEnough && !gained) {
                                            ReputationManager.gainAward(it, reputationIndex)
                                        } else {
                                            if (!levelEnough) {
                                                AppWrapper.getStringKmp(Res.string.reputation_level_not_enough)
                                                    .toast()
                                            }
                                        }
                                    })
                            }
                            Spacer(modifier = Modifier.size(padding10))
                        }
                    }
                })
            }
        }
    }
}