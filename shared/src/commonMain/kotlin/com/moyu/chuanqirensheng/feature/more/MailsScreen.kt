package com.moyu.chuanqirensheng.feature.more

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.widget.common.GameBackground
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.mails

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun MailsScreen() {
    val mails = remember {
        mutableStateListOf<EmailData>()
    }
    LaunchedEffect(Unit) {
        mails.addAll(MailManager.getMails())
    }
    GameBackground(title = stringResource(Res.string.mails)) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding10, horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(padding36))
            Column(
                modifier = Modifier
                    .align(Alignment.Start)
                    .padding(horizontal = padding12)
            ) {
                mails.forEach {
                    SingleMail(quest = it)
                    Spacer(modifier = Modifier.size(padding10))
                }
            }
            Spacer(modifier = Modifier.size(padding10))
        }
    }
}