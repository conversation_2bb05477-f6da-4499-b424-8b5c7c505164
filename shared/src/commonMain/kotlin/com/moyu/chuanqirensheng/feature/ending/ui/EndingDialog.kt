package com.moyu.chuanqirensheng.feature.ending.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.award.toAward
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.feature.ending.EndingManager.uploadRank
import com.moyu.chuanqirensheng.feature.limit.GameLimitManager
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding250
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding420
import com.moyu.chuanqirensheng.ui.theme.padding480
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.dialog.EmptyDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.day2
import shared.generated.resources.ending_award
import shared.generated.resources.ending_award_frame
import shared.generated.resources.ending_leave
import shared.generated.resources.ending_lose
import shared.generated.resources.ending_ok
import shared.generated.resources.ending_win
import shared.generated.resources.game_awards
import shared.generated.resources.lose_flag
import shared.generated.resources.win_flag

@Composable
fun EndingDialog(show: MutableState<Ending?>) {
    show.value?.let { ending ->
        val award = remember {
            ending.toAward().let { GameLimitManager.getEndingAward(it) }
        }
        LaunchedEffect(Unit) {
            ContinueManager.clearSave()
            repo.gameCore.onBattleEffect(SoundEffect.GameOver)
            MusicManager.stopAll()
        }
        EmptyDialog(onDismissRequest = {
            if (show.value != null) {
                show.value = null
                repo.onGameOver(ending)
                if (ending.pass) {
                    StageManager.setMaxStage(ending.stage)
                    uploadRank()
                }
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    AwardManager.gainAward(award)
                }
            }
        }) {
            Column(
                modifier = Modifier.fillMaxSize().padding(horizontal = padding19)
                    .padding(top = padding10).verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(padding30))
                val frameRes = if (ending.pass) Res.drawable.win_flag else Res.drawable.lose_flag
                Box(Modifier.size(padding250, padding420).graphicsLayer {
                    translationY = -padding22.toPx()
                }) {
                    Image(
                        painter = painterResource(frameRes),
                        contentDescription = null,
                        modifier = Modifier.size(padding250, padding480),
                        contentScale = ContentScale.FillHeight
                    )
                    Column(
                        Modifier.fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Spacer(Modifier.size(padding16))
                        StrokedText(
                            modifier = Modifier.scale(2f),
                            text = if (ending.pass) stringResource(Res.string.ending_win) else stringResource(
                                Res.string.ending_lose
                            ),
                            style = MaterialTheme.typography.h1,
                            color = Color.White
                        )
                        StrokedText(
                            modifier = Modifier.scale(1.5f),
                            text = stringResource(Res.string.day2, ending.age),
                            style = MaterialTheme.typography.h1,
                            color = Color.White
                        )
                        val stage = repo.gameCore.getDungeonPool()
                            .first { it.id == ending.stage }
                        StrokedText(
                            text = stage.id.toString() + " " + stage.name,
                            style = MaterialTheme.typography.h2,
                            color = Color.White
                        )
                        Spacer(
                            Modifier.size(padding80)
                        )
                    }
                }
                Box(Modifier.fillMaxWidth().height(padding260).graphicsLayer {
                    translationY = -padding28.toPx()
                }.zIndex(-99f), contentAlignment = Alignment.Center) {
                    Image(
                        modifier = Modifier.fillMaxSize().scale(1.5f),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(Res.drawable.ending_award_frame),
                        contentDescription = null
                    )
                    Column(modifier = Modifier.verticalScroll(rememberScrollState()), horizontalAlignment = Alignment.CenterHorizontally) {
                        if (!award.isEmpty()) {
                            StrokedText(
                                text = stringResource(Res.string.ending_award),
                                style = MaterialTheme.typography.h3
                            )
                            Spacer(Modifier.size(padding2))
                            AwardList(
                                award = award,
                                mainAxisAlignment = Arrangement.spacedBy(padding8),
                                param = defaultParam.copy(itemSize = ItemSize.MediumPlus)
                            )
                            Spacer(Modifier.size(padding6))
                        }
                        val award = BattleManager.battleAward.value.copy(
                            battleProperty = emptyList(), allHeal = 0
                        )
                        if (!award.isEmpty()) {
                            StrokedText(
                                text = stringResource(Res.string.game_awards),
                                style = MaterialTheme.typography.h3
                            )
                            Spacer(Modifier.size(padding2))
                            AwardList(
                                award = award,
                                mainAxisAlignment = Arrangement.spacedBy(padding8),
                                param = defaultParam.copy(itemSize = ItemSize.MediumPlus)
                            )
                        }
                    }
                }
                GameButton(
                    text = if (ending.pass) stringResource(Res.string.ending_ok) else stringResource(
                        Res.string.ending_leave
                    ), buttonSize = ButtonSize.Huge, buttonStyle = ButtonStyle.Green
                ) {
                    if (show.value != null) {
                        show.value = null
                        repo.onGameOver(ending)
                        if (ending.pass) {
                            StageManager.setMaxStage(ending.stage)
                            uploadRank()
                        }
                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                            AwardManager.gainAward(award)
                        }
                    }
                }
            }
        }
    }
}