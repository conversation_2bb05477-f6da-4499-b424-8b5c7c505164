package com.moyu.chuanqirensheng.feature.limit

import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_COUNT
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_ENDING_DIAMOND_COUNT
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TIME_COUNT
import com.moyu.chuanqirensheng.sub.datastore.KEY_MAP_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.AppWrapper
import com.moyu.core.model.Award
import shared.generated.resources.Res
import shared.generated.resources.power_not_enough


object GameLimitManager {
    private val gameCount = Guarded(KEY_GAME_COUNT)
    val endingDiamondGainedToday = Guarded(KEY_GAME_ENDING_DIAMOND_COUNT)

    fun init() {
        if (isNetTimeValid()) {
            if (!isSameDay(getLongFlowByKey(KEY_GAME_TIME_COUNT), getCurrentTime())) {
                setLongValueByKey(KEY_GAME_TIME_COUNT, getCurrentTime())
                gameCount.value = 0
                endingDiamondGainedToday.value = 0
            }
        }
    }

    fun tryPlay(callback: () -> Unit) {
        init()
        if (AwardManager.power.value < repo.gameCore.getEachStageConsumePower()) {
            AppWrapper.getStringKmp(Res.string.power_not_enough).toast()
            return
        } else {
            AwardManager.gainPower(-repo.gameCore.getEachStageConsumePower())
        }
        gameCount.value += 1
        callback()
    }

    fun getEndingAward(award: Award): Award {
        return award
    }
}