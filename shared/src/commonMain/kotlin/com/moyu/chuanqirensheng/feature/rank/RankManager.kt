package com.moyu.chuanqirensheng.feature.rank

import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_LIKE
import com.moyu.chuanqirensheng.sub.datastore.KEY_LIKE_TIME
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay

const val FAMOUS_TYPE = 10
const val PVP_TYPE = 9
const val LAST_PVP_TYPE = 8
const val PVP2_TYPE = 19
const val LAST_PVP2_TYPE = 18
const val TOWER_TYPE = 30
const val FAMOUS_MAX_ONE_DAY = 5

object RankManager {
    val famousLiked = Guarded(KEY_LIKE)
    fun init() {
        if (!isNetTimeValid()) {
            return
        }
        if (!isSameDay(getLongFlowByKey(KEY_LIKE_TIME), getCurrentTime())) {
            setLongValueByKey(KEY_LIKE_TIME, getCurrentTime())
            famousLiked.value = 0
        }
    }
}