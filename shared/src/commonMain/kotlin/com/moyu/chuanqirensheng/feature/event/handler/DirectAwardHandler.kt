package com.moyu.chuanqirensheng.feature.event.handler

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.next_day

const val DIRECT_AWARD_EVENT = 1

class DirectAwardHandler(
    override val skipWin: Boolean = true,
    override val hasLayout: MutableState<Boolean> = mutableStateOf(false),
    override val playId: Int = -1
) : PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
    }

    override suspend fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
        delay(1200)
        EventManager.doEventResult(event, true)
    }

    @Composable
    override fun HandlerButtons(event: Event) {
        GameButton(
            text = stringResource(Res.string.next_day),
            buttonSize = ButtonSize.Huge,
            enabled = false,
            onClick = {

            }
        )
    }

    @Composable
    override fun PostAwardButtons(event: Event) {
        // do nothing
    }

    override fun getAwards(): List<Award> {
        return emptyList()
    }
}
