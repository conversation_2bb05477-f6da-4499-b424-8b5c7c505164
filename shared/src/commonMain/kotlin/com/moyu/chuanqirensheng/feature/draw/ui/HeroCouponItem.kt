package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.resource.HeroCouponPoint
import com.moyu.chuanqirensheng.feature.resource.InfoIcon
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding17
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.draw_card
import shared.generated.resources.draw_card_front2
import shared.generated.resources.draw_hero
import shared.generated.resources.draw_hero_tips
import shared.generated.resources.hero_coupon
import shared.generated.resources.hero_coupon_not_enough
import shared.generated.resources.left_draw_orange
import shared.generated.resources.red_icon


@Composable
fun HeroCouponItem() {
    Column(
        Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val gifEnabled = remember {
            mutableStateOf(false)
        }
        Box(Modifier.size(padding300)) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding8),
                painter = painterResource(Res.drawable.draw_card_front2),
                contentDescription = null
            )
            StrokedText(
                modifier = Modifier.align(Alignment.TopCenter).padding(
                    top = padding17
                ),
                text = stringResource(Res.string.draw_hero),
                style = MaterialTheme.typography.h1,
                textAlign = TextAlign.Center,
                color = Color.White
            )
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd).padding(padding20),
                contentAlignment = Alignment.Center
            ) {
                InfoIcon {
                    Dialogs.alertDialog.value = CommonAlert(
                        title = AppWrapper.getStringKmp(Res.string.hero_coupon),
                        content = AppWrapper.getStringKmp(Res.string.draw_hero_tips),
                        onlyConfirm = true
                    )
                }
            }
            StrokedText(
                modifier = Modifier.align(Alignment.BottomCenter).padding(padding20),
                text = stringResource(Res.string.left_draw_orange, DrawManager.getHeroLeftDraw()),
                style = MaterialTheme.typography.h2,
                textAlign = TextAlign.Center
            )
        }
        Box(contentAlignment = Alignment.Center) {
            GameButton(
                text = "",
                buttonSize = ButtonSize.Huge,
                enabled = (AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) >= 10,
                onClick = {
                    if ((AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) < 10) {
                        AppWrapper.getStringKmp(Res.string.hero_coupon_not_enough).toast()
                        GiftManager.onDrawHeroFailed()
                    } else {
                        gifEnabled.value = true
                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                            DrawManager.buyHeroCoupon()
                        }
                    }
                })
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceAround
            ) {
                HeroCouponPoint()
                StrokedText(
                    text = stringResource(Res.string.draw_card),
                    style = MaterialTheme.typography.h1
                )
            }
            if (AwardManager.couponHero.value >= 10) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopEnd).padding(top = padding16)
                        .size(imageSmall),
                    painter = painterResource(Res.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
    }
}