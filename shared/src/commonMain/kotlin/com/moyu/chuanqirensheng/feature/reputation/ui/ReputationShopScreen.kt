package com.moyu.chuanqirensheng.feature.reputation.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.resource.CurrentDiamondPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentReputationPoint
import com.moyu.chuanqirensheng.feature.sell.ui.OneSellItem
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_arrow_left
import shared.generated.resources.common_arrow_right
import shared.generated.resources.common_big_frame
import shared.generated.resources.next_page
import shared.generated.resources.prev_page
import shared.generated.resources.reputation_shop


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ReputationShopScreen(reputationId: Int) {
    val currentRepId = remember {
        mutableStateOf(reputationId)
    }
    GameBackground(title = stringResource(Res.string.reputation_shop)) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(Res.drawable.common_big_frame),
            contentScale = ContentScale.FillBounds,
            contentDescription = null
        )
        val rawChests =
            ReputationManager.packages.filter { it.isReputation(currentRepId.value) }
                .sortedBy { it.priority }.filter {
                    // storageType是永久限量，storage是剩余数量
                    it.storage > 0 || it.storageType != 2
                }
        val unlockedSize = rawChests.filter {
            UnlockManager.getUnlockedFlow(
                repo.gameCore.getUnlockById(
                    it.unlock
                )
            )
        }.size
        // 多显示一行3个未解锁的items
        val realSize = minOf((unlockedSize + 3), rawChests.size)
        val shopChests = rawChests.take(realSize)
        Box {
            Column {
                Spacer(modifier = Modifier.size(padding12))
                Row(
                    modifier = Modifier
                        .padding(start = padding14)
                ) {
                    CurrentKeyPoint(showPlus = true)
                    CurrentDiamondPoint(showPlus = false)
                    CurrentReputationPoint()
                }
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(
                            rememberScrollState()
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    repo.gameCore.getStoryPool().sortedBy { it.order }.forEach {
                        if (it.id == currentRepId.value) {
                            val index = it.id - 1
                            val reputationLevel = AwardManager.toReputationLevelData()[index]
                            ReputationItem(index, reputationLevel, showAwardButton = false) {
                                it.desc.toast()
                            }
                        }
                    }
                    Spacer(modifier = Modifier.size(padding22))
                    shopChests.groupBy { it.title }.keys.forEach { title ->
                        val items = shopChests.filter { it.title == title }
                        Box(
                            Modifier
                                .fillMaxWidth()
                                .padding(padding10)
                        ) {
                            FlowRow(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .graphicsLayer {
                                        translationY = padding4.toPx()
                                    },
                                horizontalArrangement = Arrangement.SpaceEvenly,
                                verticalArrangement = Arrangement.spacedBy(padding12),
                                overflow = FlowRowOverflow.Visible,
                                maxItemsInEachRow = 3
                            ) {
                                items
                                    .sortedBy { it.priceType }
                                    .forEach {
                                        OneSellItem(it)
                                    }
                            }
                        }
                    }
                    Spacer(modifier = Modifier.size(padding80))
                }
            }
            if (currentRepId.value > 1) {
                EffectButton(
                    modifier = Modifier.align(Alignment.BottomStart)
                        .padding(bottom = padding10), onClick = {
                        currentRepId.value -= 1
                    }) {
                    Image(
                        modifier = Modifier.size(imageHugeLite),
                        painter = painterResource(Res.drawable.common_arrow_left),
                        contentDescription = stringResource(
                            Res.string.prev_page
                        )
                    )
                }
            }
            if (currentRepId.value < repo.gameCore.getStoryPool().size) {
                EffectButton(
                    modifier = Modifier.align(Alignment.BottomEnd).padding(bottom = padding10),
                    onClick = {
                        currentRepId.value += 1
                    }) {
                    Image(
                        modifier = Modifier.size(imageHugeLite),
                        painter = painterResource(Res.drawable.common_arrow_right),
                        contentDescription = stringResource(
                            Res.string.next_page
                        )
                    )
                }
            }
        }
    }
}