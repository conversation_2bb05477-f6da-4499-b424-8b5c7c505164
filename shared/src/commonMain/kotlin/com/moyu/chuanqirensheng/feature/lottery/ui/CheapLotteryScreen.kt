package com.moyu.chuanqirensheng.feature.lottery.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.sub.datastore.KEY_LOTTERY_INIT_TIME_IN_A_WEEK
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.timeLeft
import com.moyu.chuanqirensheng.util.toDayHourMinuteSecond
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.GameLabel2
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.do_lottery_tips
import shared.generated.resources.lottery_title1
import shared.generated.resources.time_left

@Composable
fun CheapLotteryScreen() {
    GameBackground(title = stringResource(Res.string.lottery_title1)) {
        val leftUpdateTime = remember {
            mutableLongStateOf(0L)
        }
        LaunchedEffect(Unit) {
            LotteryManager.refresh()
        }
        LaunchedEffect(Unit) {
            while (true) {
                leftUpdateTime.longValue = timeLeft(
                    getCurrentTime(), getLongFlowByKey(
                        KEY_LOTTERY_INIT_TIME_IN_A_WEEK
                    ), 5
                )
                if (leftUpdateTime.longValue <= 1000) {
                    delay(1000)
                    goto(LOGIN_SCREEN)
                }
                delay(500)
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize().paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(padding10))
            Row(Modifier.fillMaxWidth().padding(horizontal = padding6), verticalAlignment = Alignment.CenterVertically) {
                if (leftUpdateTime.longValue > 0 && LotteryManager.showCheap()) {
                    GameLabel2(
                        modifier = Modifier
                            .padding(start = padding12).size(padding120, padding30)) {
                        StrokedText(
                            text = stringResource(Res.string.time_left) + leftUpdateTime.longValue.toDayHourMinuteSecond(),                            style = MaterialTheme.typography.h4
                        )
                    }
                }
                Spacer(modifier = Modifier.weight(1f))
                CurrentKeyPoint(showPlus = true)
            }
            Spacer(modifier = Modifier.size(padding4))
            StrokedText(
                modifier = Modifier
                    .padding(horizontal = padding12).clip(RoundedCornerShape(
                        padding2
                    )).background(B50).padding(padding2),
                text = stringResource(Res.string.do_lottery_tips),
                style = MaterialTheme.typography.h3,
            )
            Spacer(modifier = Modifier.size(padding19))
            Box(
                Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                CheapLotteryLayout()
            }
        }
        // 最后3秒钟也不让点击了，准备要自动关闭
        if (LotteryManager.spinning.value || leftUpdateTime.longValue <= 3000) {
            Spacer(modifier = Modifier
                .fillMaxSize()
                .clickable { })
        }
    }
}