package com.moyu.chuanqirensheng.feature.role

import com.moyu.chuanqirensheng.feature.illustration.TcgManager
import com.moyu.chuanqirensheng.feature.skill.ExtraSkillProcessor.doPvpSkillProperty
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.Ally
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.isTalentBattle
import com.moyu.core.model.toAward

fun createPvpRole(race: Ally, talents: Map<Int, Int>, equipIds: List<Int>, tcgIds: List<Int>): Role {
    var resultProperty = Property()
    talents.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool(talents[mainId]?: 0).first { it.mainId == mainId }
        resultProperty += talentSkill.doPvpSkillProperty(race, race.fixedSkills)
    }

    val tcgProperty = tcgIds.map { tcgId ->
        repo.gameCore.getTcgPool().first { it.id == tcgId }.toAward().battleProperty.first()
    }.filter { race.match(it, true) }.map { it.property }.reduceOrNull { acc, propertyAward ->
        acc + propertyAward
    } ?: Property()


    val extraSkills = mutableListOf<Int>()
    if (race.isHero()) {
        equipIds.map { repo.gameCore.getEquipById(it) }.map {
            resultProperty += it.getProperty()
            if (it.skillEffect != 0) {
                extraSkills.add(it.skillEffect)
            }
        }
        talents.keys.forEach { mainId ->
            val talentSkill = repo.gameCore.getSkillPool(talents[mainId]?: 0).first { it.mainId == mainId }
            if (talentSkill.isTalentBattle()) {
                extraSkills.add(talentSkill.id)
            }
        }
    }
    return DefaultAllyCreator.create(
        race,
        resultProperty + tcgProperty,
        extraSkills,
        Identifier.enemy(name = race.name),
        false
    )
}

fun createPvpPlayerRole(race: Ally, talents: Map<Int, Int>, equipIds: List<Int>): Role {
    var resultProperty = Property()
    talents.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool(talents[mainId]?: 0).first { it.mainId == mainId }
        resultProperty += talentSkill.doPvpSkillProperty(race, race.fixedSkills)
    }

    val tcgProperty = TcgManager.doneTcgs.map {
        it.toAward().battleProperty.first()
    }.filter { race.match(it, true) }.map { it.property }.reduceOrNull { acc, propertyAward ->
        acc + propertyAward
    } ?: Property()

    val extraSkills = mutableListOf<Int>()
    if (race.isHero()) {
        equipIds.map { repo.gameCore.getEquipById(it) }.map {
            resultProperty += it.getProperty()
            if (it.skillEffect != 0) {
                extraSkills.add(it.skillEffect)
            }
        }
        talents.keys.forEach { mainId ->
            val talentSkill = repo.gameCore.getSkillPool(talents[mainId]?: 0).first { it.mainId == mainId }
            if (talentSkill.isTalentBattle()) {
                extraSkills.add(talentSkill.id)
            }
        }
    }
    return DefaultAllyCreator.create(
        race,
        resultProperty + tcgProperty,
        extraSkills,
        Identifier.player(name = race.name),
    )
}

fun createTowerPlayerRole(race: Ally, talents: Map<Int, Int>, equipIds: List<Int>): Role {
    var resultProperty = Property()
    talents.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool(talents[mainId]?: 0).first { it.mainId == mainId }
        resultProperty += talentSkill.doPvpSkillProperty(race, race.fixedSkills)
    }
    val tcgProperty = TcgManager.doneTcgs.map {
        it.toAward().battleProperty.first()
    }.filter { race.match(it, true) }.map { it.property }.reduceOrNull { acc, propertyAward ->
        acc + propertyAward
    } ?: Property()

    val extraSkills = mutableListOf<Int>()
    if (race.isHero()) {
        equipIds.map { repo.gameCore.getEquipById(it) }.map {
            resultProperty += it.getProperty()
            if (it.skillEffect != 0) {
                extraSkills.add(it.skillEffect)
            }
        }
        talents.keys.forEach { mainId ->
            val talentSkill = repo.gameCore.getSkillPool(talents[mainId]?: 0).first { it.mainId == mainId }
            if (talentSkill.isTalentBattle()) {
                extraSkills.add(talentSkill.id)
            }
        }
    }
    return DefaultAllyCreator.create(
        race,
        resultProperty + tcgProperty,
        extraSkills,
        Identifier.player(name = race.name)
    )
}
