package com.moyu.chuanqirensheng.feature.equip.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.resource.CurrentResourcesPoint
import com.moyu.chuanqirensheng.feature.resource.ResourcesPoint
import com.moyu.chuanqirensheng.feature.role.ui.MainPropertyLine
import com.moyu.chuanqirensheng.feature.skill.getRealDescColorful
import com.moyu.chuanqirensheng.feature.skill.ui.SingleEquipView
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.text.getEquipQualityFrame
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.bigButtonWidth
import com.moyu.chuanqirensheng.ui.theme.cardNumHeight
import com.moyu.chuanqirensheng.ui.theme.cardNumWidth
import com.moyu.chuanqirensheng.ui.theme.dialogMediumPlusHeight
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.moneyExtraWidth
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.CommonBar
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.Stars
import com.moyu.chuanqirensheng.widget.dialog.NewPanelDialog
import com.moyu.chuanqirensheng.widget.effect.GifView
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.starUpGif
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Equipment
import com.moyu.core.model.MAX_STAR
import com.moyu.core.music.SoundEffect
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.bar_blue
import shared.generated.resources.bar_empty
import shared.generated.resources.current_star
import shared.generated.resources.next_star
import shared.generated.resources.star_max
import shared.generated.resources.star_up
import shared.generated.resources.three_arrows

@Composable
fun EquipStarUpDialog(show: MutableState<Equipment?>) {
    show.value?.let { equip ->
        val nextEquip = equip.getNextStarEquip()
        NewPanelDialog(dialogHeightDp = dialogMediumPlusHeight, resourceContent = {
            CurrentResourcesPoint(
                index = 0, showPlus = true, boxWidth = moneyExtraWidth
            )
        }, onDismissRequest = {
            show.value = null
        }) {
            Spacer(modifier = Modifier.size(padding12))
            SingleEquipView(
                itemSize = ItemSize.LargePlus, equipment = equip, showName = false, showRed = false
            )
            Spacer(modifier = Modifier.size(padding8))
            EquipSkillPanel(equip, nextEquip)
            Spacer(modifier = Modifier.weight(1f))
            CommonBar(
                modifier = Modifier.size(cardNumWidth, cardNumHeight),
                currentValue = equip.num,
                maxValue = equip.starUpNum,
                fullRes = Res.drawable.bar_blue,
                emptyRes = Res.drawable.bar_empty,
                textColor = Color.White,
                style = MaterialTheme.typography.h5
            )
            Spacer(modifier = Modifier.size(padding4))
            DoEquipStarUpView(equip = equip)
            Spacer(modifier = Modifier.size(padding19))
        }
    }
}

@Composable
fun DoEquipStarUpView(equip: Equipment) {
    Box(contentAlignment = Alignment.Center) {
        val maxStar = equip.star >= MAX_STAR
        val buttonText =
            if (maxStar) AppWrapper.getStringKmp(Res.string.star_max) else AppWrapper.getStringKmp(
                Res.string.star_up
            )
        val enabled = repo.equipManager.canLevelUp(equip)

        val gifShowed = remember {
            mutableStateOf(0)
        }
        val oldLevel = remember(gifShowed.value) {
            mutableStateOf(equip.star)
        }
        if (oldLevel.value != equip.star) {
            GifView(
                modifier = Modifier.size(bigButtonWidth, bigButtonHeight).scale(4f),
                enabled = true,
                gifCount = starUpGif.count,
                gifDrawable = starUpGif.gif,
                pace = starUpGif.pace,
            ) {
                gifShowed.value += 1
            }
        }
        GameButton(
            text = "",
            buttonStyle = ButtonStyle.Green,
            buttonSize = ButtonSize.Big,
            mute = true,
            enabled = enabled,
            onClick = {
                repo.equipManager.starUp(equip)?.let {
                    Dialogs.equipDetailDialog.value = it
                    if (it.star == MAX_STAR) {
                        Dialogs.equipStarUpDialog.value = null
                    } else {
                        Dialogs.equipStarUpDialog.value = it
                    }
                }?: run {
                    GameCore.instance.onBattleEffect(SoundEffect.Click)
                }
            })
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceAround
        ) {
            ResourcesPoint(0, equip.starUpResourceNum)
            StrokedText(text = buttonText, style = MaterialTheme.typography.h1)
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun EquipSkillPanel(
    equip: Equipment,
    nextEquip: Equipment,
) {
    Row(Modifier.width(padding260), horizontalArrangement = Arrangement.SpaceBetween) {
        StrokedText(
            text = stringResource(Res.string.current_star),
            style = MaterialTheme.typography.h2,
        )
        StrokedText(
            text = stringResource(Res.string.next_star),
            style = MaterialTheme.typography.h2,
        )
    }
    Row(
        Modifier.width(padding260).padding(horizontal = padding16),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Stars(Modifier.size(imageLarge), equip.star)
        Image(
            modifier = Modifier.size(imageLarge),
            painter = painterResource(Res.drawable.three_arrows),
            contentDescription = null
        )
        Stars(Modifier.size(imageLarge), nextEquip.star)
    }
    Spacer(modifier = Modifier.size(padding10))
    equip.getSkill()?.let {
        Row(verticalAlignment = Alignment.CenterVertically) {
            SingleSkillView(Modifier, frameZIndex = -999f, frame = equip.quality.getEquipQualityFrame(), skill = it, showName = false)
            Spacer(modifier = Modifier.size(padding4))
            Column {
                StrokedText(
                    text = "Lv.${it.level} " + it.name,
                    style = MaterialTheme.typography.h2,
                    color = Color.White
                )
                Spacer(Modifier.size(padding4))
                StrokedText(
                    text = it.getRealDescColorful(),
                    style = MaterialTheme.typography.h4,
                    color = Color.White
                )
            }
        }
        Spacer(Modifier.size(padding10))
    }
    nextEquip.getSkill()?.let {
        Row(verticalAlignment = Alignment.CenterVertically) {
            SingleSkillView(Modifier, frameZIndex = -999f, frame = equip.quality.getEquipQualityFrame(), skill = it, showName = false)
            Spacer(modifier = Modifier.size(padding4))
            Column {
                StrokedText(
                    text = "Lv.${it.level}" + it.name,
                    style = MaterialTheme.typography.h2,
                    color = Color.White
                )
                Spacer(Modifier.size(padding4))
                StrokedText(
                    text = it.getRealDescColorful(),
                    style = MaterialTheme.typography.h4,
                    color = Color.White
                )
            }
        }
        Spacer(Modifier.size(padding10))
    }
    if (nextEquip.getProperty() != equip.getProperty()) {
        FlowRow(
            horizontalArrangement = Arrangement.spacedBy(padding60),
            overflow = FlowRowOverflow.Visible,
            maxItemsInEachRow = 2
        ) {
            nextEquip.getProperty().MainPropertyLine(
                originProperty = equip.getProperty(),
                textStyle = MaterialTheme.typography.h2,
                showBoost = true,
                textColor = Color.White
            )
        }
    }
}
