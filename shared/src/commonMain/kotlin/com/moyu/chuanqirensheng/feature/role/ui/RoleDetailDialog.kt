package com.moyu.chuanqirensheng.feature.role.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.feature.ally.ui.AllyDetailLayout
import com.moyu.chuanqirensheng.feature.ally.ui.AllyPropertyLayout
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.dialogMediumHeight
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.widget.dialog.NewPanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.role.Role

@Composable
fun RoleDetailDialog(show: MutableState<Role?>) {
    show.value?.let { role ->
        val ally = repo.gameCore.getAllyById(role.getRace().id)
        NewPanelDialog(
            onDismissRequest = { show.value = null },
            dialogHeightDp = dialogMediumHeight
        ) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                StrokedText(
                    text = ally.name,
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding10))
                AllyDetailLayout(ally, role)
                Spacer(modifier = Modifier.size(padding12))
                AllyPropertyLayout(role)
            }
        }
    }
}