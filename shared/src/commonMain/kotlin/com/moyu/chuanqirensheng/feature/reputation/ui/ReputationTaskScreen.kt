package com.moyu.chuanqirensheng.feature.reputation.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.ui.SingleQuest
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.chuanqirensheng.util.millisToMidnight
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.GameLabel2
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.refresh
import shared.generated.resources.reputation_task

@Composable
fun ReputationTaskScreen() {
    val tasks = ReputationManager.collectTasks
    val refresh = remember {
        mutableIntStateOf(0)
    }
    val leftUpdateTime = remember {
        mutableStateOf(0L)
    }
    LaunchedEffect(refresh.intValue) {
        QuestManager.init()
        ReputationManager.createCollectTasks()
        // 完成的任务排前面，已领取的排最后
        ReputationManager.collectTasks.map {
            it.copy(done = QuestManager.getTaskDoneFlow(it))
        }.sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }.apply {
            tasks.clear()
            tasks.addAll(this)
        }
    }
    LaunchedEffect(refresh) {
        refreshNetTime()
        if (isNetTimeValid()) {
            while (true) {
                leftUpdateTime.value = millisToMidnight(getCurrentTime())
                if (leftUpdateTime.value <= 1000) {
                    delay(1000)
                    // 修改这个，上面的LauncherEffect会刷新任务
                    refresh.intValue += 1
                }
                delay(500)
            }
        }
    }
    GameBackground(title = stringResource(Res.string.reputation_task)) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(padding10))
            Row(Modifier.fillMaxWidth().padding(end = padding12)) {
                Spacer(modifier = Modifier.weight(1f))
                GameLabel2(Modifier.size(padding120, padding30)) {
                    StrokedText(
                        modifier = Modifier
                            .padding(end = padding12),
                        text = stringResource(Res.string.refresh) + leftUpdateTime.value.millisToHoursMinutesSeconds(),
                        style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding8))
            tasks.forEach {
                SingleQuest(it) {
                    refresh.intValue += 1
                }
                Spacer(modifier = Modifier.size(padding5))
            }
        }
    }
}