package com.moyu.chuanqirensheng.feature.skill.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.text.getQualityFrame
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.Stars
import com.moyu.chuanqirensheng.widget.common.getTextStyle
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.logic.skill.quality
import com.moyu.core.model.skill.Skill
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_lock
import shared.generated.resources.common_question
import shared.generated.resources.locked
import shared.generated.resources.red_icon


@Composable
fun SingleSkillView(
    modifier: Modifier = Modifier,
    skill: Skill,
    itemSize: ItemSize = ItemSize.Large,
    showName: Boolean = true,
    showNum: Boolean = false,
    textColor: Color = Color.White,
    colorFilter: ColorFilter? = null,
    frame: DrawableResource? = null,
    forceQuality: Int? = null,
    locked: Boolean = false,
    hide: Boolean = false,
    showRed: Boolean = false,
    showStars: Boolean = false,
    frameZIndex: Float = 0f,
    imageClip: Shape = RoundedCornerShape(itemSize.frameSize / 10),
    callback: (Skill) -> Unit = { Dialogs.skillDetailDialog.value = skill }
) {
    Column(modifier = modifier.width(itemSize.frameSize), horizontalAlignment = Alignment.CenterHorizontally) {
        EffectButton(onClick = {
            callback(skill)
        }) {
            Image(
                modifier = Modifier
                    .size(itemSize.itemSize)
                    .clip(imageClip),
                contentScale = ContentScale.Crop,
                colorFilter = colorFilter,
                painter = kmpPainterResource(if (hide) Res.drawable.common_question else kmpDrawableResource(skill.icon)),
                contentDescription = null //skill.getTouchInfo(),
            )
            Image(
                modifier = Modifier.size(itemSize.frameSize).zIndex(frameZIndex),
                painter = kmpPainterResource(frame ?: (forceQuality?: skill.quality()).getQualityFrame()),
                contentDescription = null,
            )
            if (skill.extraInfo.isNotEmpty()) {
                Box(
                    modifier = Modifier
                        .background(B50)
                        .padding(padding4)) {
                    StrokedText(text = skill.extraInfo, style = MaterialTheme.typography.body1)
                }
            }
            if (skill.canShowStar() && showStars) {
                Stars(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(itemSize.itemSize / 10).size(itemSize.itemSize / 3),
                    skill.level,
                )
            }
            if (locked) {
                Image(
                    modifier = Modifier.size(itemSize.frameSize / 2),
                    painter = painterResource(Res.drawable.common_lock),
                    contentDescription = stringResource(Res.string.locked),
                )
            }
            if (showRed && skill.new) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(imageSmall).zIndex(1000f),
                    painter = painterResource(Res.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
        if (showName) {
            Spacer(modifier = Modifier.size(padding2))
            val text = if (showNum) skill.name + "x${skill.num}" else skill.name
            StrokedText(
                text = if (hide) "???" else text,
                style = itemSize.getTextStyle(),
                maxLines = 2,
                minLines = 2,
                textAlign = TextAlign.Center,
                color = textColor,
            )
        }
    }
}