package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.router.QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_TASK
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.MainIcon
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.icon_quest
import shared.generated.resources.quest

@Composable
fun QuestIcon(itemSize: ItemSize) {
    MainIcon(
        itemSize = itemSize,
        unlocks = listOf(repo.gameCore.getUnlockById(UNLOCK_MENU_TASK)),
        click = {
            goto(QUEST_SCREEN)
        },
        red = {
            (QuestManager.dailyTasks.take(VipManager.getExtraDailyQuest() + repo.gameCore.getDailyQuestCount())).any {
                QuestManager.getTaskDoneFlow(it) && !it.opened
            }
        },
        title = stringResource(Res.string.quest),
        icon = Res.drawable.icon_quest
    )
}
