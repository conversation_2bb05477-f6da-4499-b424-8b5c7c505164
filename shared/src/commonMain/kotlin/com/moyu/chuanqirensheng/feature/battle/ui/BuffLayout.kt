package com.moyu.chuanqirensheng.feature.battle.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.buffSize
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.core.model.Buff
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.battle_buff_frame
import shared.generated.resources.battle_debuff_frame

@Composable
fun OneBuffGrid(
    buff: Buff,
    callback: (Buff) -> Unit
) {
    val imageSize = buffSize
    EffectButton(onClick = { callback(buff) }) {
        Column {
            Box {
                Image(
                    painter = painterResource(
                         if (buff.isGood()) Res.drawable.battle_buff_frame else Res.drawable.battle_debuff_frame
                    ),
                    modifier = Modifier.size(imageSize),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
                Image(
                    painter = kmpPainterResource(buff.skill?.icon ?: ""),
                    modifier = Modifier
                        .size(imageSize).padding(padding1),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = buff.skill?.name
                )
            }
            Spacer(modifier = Modifier.size(padding2))
        }
    }
}