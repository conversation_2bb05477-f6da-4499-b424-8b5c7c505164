package com.moyu.chuanqirensheng.feature.router

import androidx.compose.runtime.mutableStateOf
import androidx.navigation.NavHostController
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_SELL
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.killSelf
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.login_status_error_tips
import shared.generated.resources.quit_game_toast
import shared.generated.resources.time_error_tips
import shared.generated.resources.userid_error_tips

const val PAGE_PARAM_TAB_INDEX = "initTabIndex"

const val LOGIN_SCREEN = "login"
const val MORE_SCREEN = "more"
const val DRAW_ACTIVITY_SCREEN = "draw_activity"

const val OUT_EQUIP = "out_hero"
const val OUT_ALLY = "out_ally"
const val DETAIL_ALLY = "detail_ally"
const val TALENT_ALL_SCREEN = "talent_all"
const val TALENT1_SCREEN = "talent1"
const val TALENT2_SCREEN = "talent2"
const val TALENT3_SCREEN = "talent3"
const val QUEST_SCREEN = "quest"
const val SIGN_SCREEN = "sign"
const val NEW_TASK_SCREEN = "new_task"
const val SEVEN_DAY_SCREEN1 = "seven_day1"
const val SEVEN_DAY_SCREEN2 = "seven_day2"
const val SEVEN_DAY_SCREEN3 = "seven_day3"
const val SEVEN_DAY_SCREEN4 = "seven_day4"

const val VIP_SCREEN = "vip"
const val RANK_SCREEN = "rank"
const val PVP_SCREEN = "pvp"
const val PVP2_SCREEN = "pvp2"
const val PVP_SELL_SCREEN = "pvp_sell"
const val PVP_RANK_SCREEN = "pvp_rank"
const val PVP2_RANK_SCREEN = "pvp2_rank"
const val PVP_CHOOSE_ENEMY_SCREEN = "pvp_choose_enemy"
const val PVP2_CHOOSE_ENEMY_SCREEN = "pvp2_choose_enemy"
const val PVP_QUEST_SCREEN = "pvp_quest"
const val PVP2_QUEST_SCREEN = "pvp2_quest"
const val PVP_BATTLE_SCREEN = "pvp_battle"
const val PVP2_BATTLE_SCREEN = "pvp2_battle"

const val LOTTERY_SCREEN1 = "lottery_screen1"
const val LOTTERY_SCREEN2 = "lottery_screen2"

const val TOWER_SCREEN = "tower_screen"
const val TOWER_SELL_SCREEN = "tower_sell"
const val TOWER_RANK_SCREEN = "tower_rank"
const val TOWER_BATTLER_SCREEN = "tower_battle_screen"

// 游戏调试
const val DEBUG_SCREEN = "debug_game"
const val DEBUG_BATTLE = "debug_battle"

// 事件
const val EVENT_SELECT_SCREEN = "event_select"

const val SELL_SCREEN_PREFIX = "sell/"
const val SELL_SCREEN_RAW = "sell/$PAGE_PARAM_TAB_INDEX"

const val HOLIDAY_SCREEN = "holiday"
const val SERVER_RANK_PREFIX = "server_rank/"
const val SERVER_RANK_ALL_SCREEN = "server_rank_all"

const val REPUTATION_SCREEN = "reputation"
const val REPUTATION_TASK = "reputation_task"
const val REPUTATION_LEVEL = "reputation_level"
const val REPUTATION_SHOP_PREFIX = "reputation_shop/"

const val BATTLE_PASS_PREFIX = "battle_pass/"
const val BATTLE_PASS_ALL_SCREEN = "battle_pass_all"

const val ACTIVITIES_SCREEN = "activities_screen"
const val DUNGEON_SCREEN = "dungeon"

const val MAILS_SCREEN = "mails"
const val FEEDBACK_SCREEN = "feedback"
const val TCG_SCREEN = "tcg"


val selectedRouter = mutableStateOf(2)


fun NavHostController.toSingleInstance(route: String) {
    if (!isNetTimeValid()) {
        AppWrapper.getStringKmp(Res.string.login_status_error_tips).toast()
    } else {
        AppWrapper.globalScope.launch(Dispatchers.Main) {
            if (isCurrentRoute(route)) {
                return@launch
            }
            if (!popBackStack(route, false)) {
                navigate(route)
                reportManager().onPage(route)
            }
        }
    }
}

fun isCurrentRoute(route: String): Boolean {
    return if (route.startsWith(SELL_SCREEN_PREFIX)) {
        RouterManager.instance.navController?.currentDestination?.route?.startsWith(
            SELL_SCREEN_PREFIX
        ) == true
    } else {
        RouterManager.instance.navController?.currentDestination?.route?.equals(
            route
        ) == true
    }
}

fun popTop() {
    RouterManager.instance.navController?.currentDestination?.route?.let {
        if (it == LOGIN_SCREEN) {
            Dialogs.alertDialog.value = CommonAlert(
                content = AppWrapper.getStringKmp(Res.string.quit_game_toast),
                onConfirm = {
                    gameSdkDefaultProcessor().quitGame {
                        killSelf()
                    }
                })
        } else {
            AppWrapper.globalScope.launch(Dispatchers.Main) {
                if (isCurrentRouteBottomItems()) {
                    goto(LOGIN_SCREEN)
                } else {
                    RouterManager.instance.navController?.popBackStack(it, true)
                    setRouterIndexByRoute(
                        RouterManager.instance.navController?.currentDestination?.route ?: ""
                    )
                }
            }
        }
    }
}

fun isCurrentRouteBottomItems(): Boolean {
    return isCurrentRoute(OUT_ALLY) || isCurrentRoute(OUT_EQUIP) || isCurrentRoute(TALENT_ALL_SCREEN) || isCurrentRoute(SELL_SCREEN_PREFIX)
}

fun goto(route: String) {
    if (!isNetTimeValid()) {
        (AppWrapper.getStringKmp(Res.string.time_error_tips)).toast()
    } else if (gameSdkDefaultProcessor().getObjectId().isNullOrEmpty()) {
        (AppWrapper.getStringKmp(Res.string.userid_error_tips)).toast()
    } else {
        setRouterIndexByRoute(route)
        RouterManager.instance.navController?.toSingleInstance(route)
    }
}

fun setRouterIndexByRoute(route: String) {
    when  {
        route == LOGIN_SCREEN -> selectedRouter.value = 2
        route == OUT_ALLY -> selectedRouter.value = 1
        route == TALENT_ALL_SCREEN -> selectedRouter.value = 4
        route.startsWith(SELL_SCREEN_PREFIX) -> selectedRouter.value = 0
        route == OUT_EQUIP -> selectedRouter.value = 3
        else -> {
            selectedRouter.value = -1
        }
    }
}

fun gotoSellWithTabIndex(tabIndex: Int = 0) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_SELL)
    if (!UnlockManager.getUnlockedFlow(unlock)) {
        unlock.desc.toast()
        return
    }
    setRouterIndexByRoute("$SELL_SCREEN_PREFIX$tabIndex")
    val targetRoute = "$SELL_SCREEN_PREFIX$tabIndex"
    RouterManager.instance.navController?.navigate(targetRoute) {
        // 确保导航栈中只保留一个 sell 页面实例
        popUpTo(SELL_SCREEN_RAW) { inclusive = true }
        launchSingleTop = true // 避免导航到相同的目标页面时重复实例化
        restoreState = false // 禁用状态恢复
    }

    // 这里是跳转商店，购买魔晶，可能打开着几个弹窗，需要关闭
    Dialogs.allyStarUpDialog.value = null
    Dialogs.allyDetailDialog.value = null
}

fun gotoBattlePassScreenByIndex(id: Int = 1) {
    val targetRoute = "$BATTLE_PASS_PREFIX$id"
    RouterManager.instance.navController?.navigate(targetRoute)
}

fun gotoReputationShopById(id: Int = 1) {
    val targetRoute = "$REPUTATION_SHOP_PREFIX$id"
    RouterManager.instance.navController?.navigate(targetRoute)
}

fun gotoServerRankScreenByIndex(id: Int = 1) {
    val targetRoute = "$SERVER_RANK_PREFIX$id"
    RouterManager.instance.navController?.navigate(targetRoute)
}