package com.moyu.chuanqirensheng.feature.event.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE1
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.setting.SettingManager
import com.moyu.chuanqirensheng.feature.skill.getRealDescColorful
import com.moyu.chuanqirensheng.feature.skill.ui.SingleSkillView
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.text.typeToMagicIcon
import com.moyu.chuanqirensheng.ui.theme.dialogMediumHeight
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.toQualityColor
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.IconView
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.dialog.EmptyDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.Texture
import com.moyu.chuanqirensheng.widget.effect.magicFrame
import com.moyu.chuanqirensheng.widget.effect.sweepHighlight
import com.moyu.core.GameCore
import com.moyu.core.logic.info.getElementTypeName
import com.moyu.core.logic.skill.quality
import com.moyu.core.model.Award
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.check_all_skills
import shared.generated.resources.choose_skill_title
import shared.generated.resources.search_icon
import shared.generated.resources.sell_label

@Composable
fun ChooseSkillDialog(show: MutableState<List<Award>>) {
    if (show.value.isNotEmpty()) {
        EmptyDialog(showTips = false) {
            Column(
                Modifier.fillMaxWidth().height(dialogMediumHeight),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier.size(padding260, padding48),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(Res.drawable.sell_label),
                        contentDescription = null
                    )
                    StrokedText(
                        text = stringResource(Res.string.choose_skill_title),
                        style = MaterialTheme.typography.h1,
                        color = Color.White,
                    )
                }
                Spacer(Modifier.size(padding16))
                LaunchedEffect(Unit) {
                    if (SettingManager.autoSelect.value) {
                        delay(1000)
                        EventManager.selectedEvent.value?.let {
                            val handler = EventManager.getOrCreateHandler(it)
                            if (!handler.eventFinished.value) {
                                handler.eventFinished.value = true
                                show.value.map { it.skills.firstOrNull() }
                                    .firstOrNull()?.let { skill ->
                                        EventManager.getOrCreateHandler(it)
                                            .setEventAward(Award(skills = listOf(skill)), 0)
                                    }

                                show.value = emptyList()
                                EventManager.doEventResult(it, true)
                            }
                            if (GuideManager.guideIndex.value == 4) {
                                GuideManager.guideIndex.value = 5
                                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                            }
                        }
                    }
                }
                show.value.map { it.skills.first() }.forEachIndexed { index, skill ->
                    val click = {
                        EventManager.selectedEvent.value?.let {
                            val handler = EventManager.getOrCreateHandler(it)
                            if (!handler.eventFinished.value) {
                                handler.eventFinished.value = true
                                if (GuideManager.guideIndex.value == 4) {
                                    // 引导过程强制选择第一个
                                    EventManager.getOrCreateHandler(it)
                                        .setEventAward(Award(skills = listOf(show.value.map { it.skills.first() }
                                            .first())), 0)
                                } else {
                                    EventManager.getOrCreateHandler(it)
                                        .setEventAward(Award(skills = listOf(skill)), index)
                                }
                                show.value = emptyList()
                                EventManager.doEventResult(it, true)
                            }
                        }
                    }
                    val backgroundColor = skill.quality().toQualityColor()
                    val borderColor = backgroundColor.copy(
                        red = backgroundColor.red * 0.8f,
                        green = backgroundColor.green * 0.8f,
                        blue = backgroundColor.blue * 0.8f
                    )

                    EffectButton(
                        modifier = Modifier
                            .fillMaxWidth().height(padding100)
                            .magicFrame(                      // 只换这一行
                                base = backgroundColor,
                                borderColor = borderColor,
                                texture = Texture.CHECKER   // 或 Texture.HORIZONTAL_NOISE
                            ).sweepHighlight(),
                        onClick = {
                            click()
                            if (GuideManager.guideIndex.value == 4) {
                                GuideManager.guideIndex.value = 5
                                setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE1)
                            }
                        }
                    ) {
                        Row(
                            Modifier.padding(horizontal = padding16),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            SingleSkillView(
                                skill = skill,
                                showName = false,
                                showStars = false,
                            ) { click() }
                            Spacer(Modifier.size(padding10))
                            Column(
                                Modifier.weight(1f).fillMaxHeight(),
                                verticalArrangement = Arrangement.SpaceEvenly
                            ) {
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    StrokedText(
                                        text = skill.name,
                                        style = MaterialTheme.typography.h2,
                                        color = Color.White
                                    )
                                    Spacer(Modifier.size(padding6))
                                    Image(
                                        modifier = Modifier.size(padding22).clickable {
                                            skill.elementType.getElementTypeName().toast()
                                            GameCore.instance.onBattleEffect(SoundEffect.Click)
                                        },
                                        painter = painterResource(skill.elementType.typeToMagicIcon()),
                                        contentDescription = null
                                    )
                                }
                                StrokedText(
                                    text = skill.getRealDescColorful(spanStyle = MaterialTheme.typography.h4.toSpanStyle()),
                                    style = MaterialTheme.typography.h4,
                                    color = Color.White
                                )
                            }
                        }
                        if (GuideManager.guideIndex.value == 4 && index == 0) {
                            GuideHand(
                                modifier = Modifier.align(Alignment.CenterEnd).height(padding60)
                                    .graphicsLayer {
                                        translationX = -padding30.toPx()
                                    },
                                handType = HandType.LEFT_HAND
                            )
                        }
                    }
                    Spacer(Modifier.size(padding10))
                }
                Spacer(Modifier.size(padding10))
                Row(
                    Modifier.fillMaxWidth().padding(end = padding20),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    IconView(
                        res = Res.drawable.search_icon,
                        frame = null,
                        itemSize = ItemSize.Large,
                    ) {
                        Dialogs.gameSkillDialog.value = true
                    }
                    Spacer(Modifier.size(padding4))
                    StrokedText(
                        text = stringResource(Res.string.check_all_skills),
                        style = MaterialTheme.typography.h3,
                        modifier = Modifier.clickable {
                            Dialogs.gameSkillDialog.value = true
                            GameCore.instance.onBattleEffect(SoundEffect.Click)
                        }
                    )
                }
            }
        }
    }
}
