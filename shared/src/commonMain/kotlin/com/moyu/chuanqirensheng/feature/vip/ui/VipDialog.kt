package com.moyu.chuanqirensheng.feature.vip.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.text.getEquipQualityFrame
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.common.IconView
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Vip
import com.moyu.core.model.toAward
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.awards

@Composable
fun VipDialog(show: MutableState<Vip?>) {
    show.value?.let {
        PanelDialog(onDismissRequest = {
            show.value = null
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                StrokedText(
                    text = stringResource(Res.string.awards),
                    style = MaterialTheme.typography.h1,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(padding34))
                VipItem(vip = it)
            }
        }
    }
}

@Composable
fun VipItem(vip: Vip, itemSize: ItemSize = ItemSize.Large, callback: (() -> Unit)? = null) {
    if (vip.effectType == 1) {
        val award = vip.toAward()
        AwardList(
            Modifier,
            award = award,
            param = defaultParam.copy(
                peek = true,
                textColor = Color.White,
                itemSize = itemSize,
                showName = false,
                frameDrawable = 3.getEquipQualityFrame(),
                callback = callback,
            ),
        )
    } else {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            IconView(res = kmpDrawableResource(vip.pic),
                frame = 3.getEquipQualityFrame(),
                resZIndex = 99f,
                itemSize = itemSize,
                callback = { callback?.invoke() })
            Spacer(modifier = Modifier.size(padding10))
            StrokedText(text = vip.desc, style = MaterialTheme.typography.h3, color = Color.White)
        }
    }
}
