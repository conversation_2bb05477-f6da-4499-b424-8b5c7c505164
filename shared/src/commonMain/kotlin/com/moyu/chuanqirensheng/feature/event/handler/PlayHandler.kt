package com.moyu.chuanqirensheng.feature.event.handler

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.feature.award.toAward
import com.moyu.core.model.Award
import com.moyu.core.model.Event


abstract class PlayHandler {
    abstract val playId: Int
    abstract val skipWin: Boolean
    abstract val hasLayout: MutableState<Boolean> // 是否有自己的事件处理UI
    val eventFinished: MutableState<Boolean> = mutableStateOf(false) // 事件是否结束
    val eventResult: MutableState<Boolean> = mutableStateOf(false) // 事件结果
    val eventChoice: MutableState<Int> = mutableStateOf(-1) // 事件结果
    val doEventPostAwards: MutableState<Boolean> = mutableStateOf(false) // 战斗结束奖励选择
    private val eventAward: MutableState<Award> = mutableStateOf(Award()) // 事件奖励

    @Composable
    abstract fun Layout(event: Event)

    @Composable
    abstract fun HandlerButtons(event: Event)

    @Composable
    abstract fun PostAwardButtons(event: Event)

    suspend fun eventSelect(event: Event) {
        eventFinished.value = false
        eventChoice.value = -1
        eventResult.value = skipWin
        eventAward.value = Award()
        eventAward.value = event.toAward(true).copy(showQuestion = false)
        onEventSelect(event)
    }

    abstract suspend fun onEventSelect(event: Event)

    fun setEventAward(award: Award, choice: Int) {
        eventAward.value += award
        eventChoice.value = choice
    }

    fun getEventAward(): Award {
        return eventAward.value
    }

    abstract fun getAwards(): List<Award>
}
