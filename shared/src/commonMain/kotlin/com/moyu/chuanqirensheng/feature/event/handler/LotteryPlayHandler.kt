package com.moyu.chuanqirensheng.feature.event.handler

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.lottery.ui.LotteryInGameView
import com.moyu.chuanqirensheng.feature.lottery.ui.lotteryScale
import com.moyu.chuanqirensheng.feature.setting.SettingManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.core.AppWrapper
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.toAwards
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.do_lottery
import shared.generated.resources.lottery_top

const val LOTTERY_PLAY_EVENT = 6

class LotteryPlayHandler(
    override val skipWin: Boolean = true,
    override val hasLayout: MutableState<Boolean> = mutableStateOf(true),
    override val playId: Int = -1
) : PlayHandler() {

    private val awards = mutableStateListOf<Award>()
    val spinning = mutableStateOf(true)
    val singleItemAngle = 360 / 8
    val targetAngle = mutableFloatStateOf(0f)

    @Composable
    override fun Layout(event: Event) {
        // 用于控制是否可见的状态变量
        var isPlayersVisible by remember { mutableStateOf(false) }

        // 当界面首次进入时，先延迟一点，然后把两个 column 都设为可见，触发进入动画
        LaunchedEffect(Unit) {
            // 也可以写个延迟，看看动画效果更明显
            delay(300L)
            isPlayersVisible = true
        }
        AnimatedVisibility(
            modifier = Modifier.fillMaxSize(),
            visible = isPlayersVisible, // 这里可改成你的状态
            enter = slideInVertically(initialOffsetY = { -it }) + fadeIn(),
            exit = slideOutVertically(targetOffsetY = { -it }) + fadeOut()
        ) {
            Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                val currentAngle by animateFloatAsState(
                    targetValue = if (spinning.value) targetAngle.floatValue else targetAngle.floatValue % 360,
                    animationSpec = TweenSpec(
                        durationMillis = if (spinning.value) 2000 else 0,
                        easing = FastOutSlowInEasing
                    ),
                    finishedListener = {},
                    label = ""
                )
                Box(Modifier.scale(0.75f).graphicsLayer {
                    translationY = padding66.toPx()
                }, contentAlignment = Alignment.Center) {
                    LotteryInGameView(
                        Modifier.size(padding380).graphicsLayer {
                            // 因为要保证最后一个奖品显示在上面中间，所以要转动显示，保持逻辑不变
                            rotationZ = 360 / 8f
                        },
                        awards,
                    )
                    Image(
                        modifier = Modifier.size(padding380).graphicsLayer {
                            // 因为要保证最后一个奖品显示在上面中间，所以要转动显示，保持逻辑不变
                            rotationZ = currentAngle + 360 / 8f
                            scaleY = lotteryScale
                            scaleX = lotteryScale
                        },
                        painter = painterResource(Res.drawable.lottery_top),
                        contentDescription = null
                    )
                }
            }
        }
    }

    override suspend fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
        awards.clear()
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        val initAward = repo.gameCore.getPoolById(event.playPara1.first())
            .toAwards(forceTotalNum = pool.type.size)
        var gainProps: MutableList<Award> = mutableListOf()
        do {
            gainProps.addAll(pool.toAwards(forceTotalNum = pool.type.size))
        } while (gainProps.isEmpty() || (gainProps.size + initAward.size) < 8)

        if (initAward.size >= 8) {
            awards.addAll(initAward.shuffled().take(8).mapIndexed { index, award ->
                if (index in listOf(3, 7)) {
                    if (award.battleProperty.isNotEmpty()) {
                        award.copy(
                            battleProperty = listOf(
                                award.battleProperty.first()
                                    .copy(
                                        property = award.battleProperty.first().property
                                                + award.battleProperty.first().property
                                    )

                            )
                        )
                    } else {
                        award + award
                    }
                } else award
            })
        } else {
            awards.addAll(
                (initAward.shuffled() + gainProps.shuffled()
                    .take(8 - initAward.size)).mapIndexed { index, award ->
                    if (index in listOf(3, 7)) {
                        if (award.battleProperty.isNotEmpty()) {
                            award.copy(
                                battleProperty = listOf(
                                    award.battleProperty.first()
                                        .copy(
                                            property = award.battleProperty.first().property
                                                    + award.battleProperty.first().property
                                        )
                                )
                            )
                        } else {
                            award + award
                        }
                    } else award
                })
        }
        spinning.value = false
    }

    @Composable
    override fun HandlerButtons(event: Event) {
        var enabled = remember(event.id) {
            mutableStateOf(false)
        }
        LaunchedEffect(Unit) {
            // 太快了，会吞掉动画，原因是上面的Layout还没出现
            delay(500)
            enabled.value = true
            if (SettingManager.autoSelect.value) {
                delay(500)
                if (!eventFinished.value && !spinning.value) {
                    spinning.value = true
                    MusicManager.playSound(SoundEffect.LotteryRing)
                    val (award, index) = awards.mapIndexed { index, award ->
                        award to index
                    }.shuffled().first()
                    targetAngle.floatValue = index * singleItemAngle.toFloat() + 360 * 10
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        delay(2500)
                        EventManager.getOrCreateHandler(event).setEventAward(awards[index], 1)
                        EventManager.doEventResult(event, true)
                        eventFinished.value = true
                    }
                }
            }
        }
        GameButton(
            text = stringResource(Res.string.do_lottery),
            buttonSize = ButtonSize.Huge,
            enabled = !spinning.value,
            hapticFeedbackType = HapticFeedbackType.LongPress,
            onClick = {
                if (enabled.value && !eventFinished.value && !spinning.value) {
                    spinning.value = true
                    MusicManager.playSound(SoundEffect.LotteryRing)
                    val (award, index) = awards.mapIndexed { index, award ->
                        award to index
                    }.shuffled().first()
                    targetAngle.floatValue = index * singleItemAngle.toFloat() + 360 * 10
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        delay(2500)
                        if (index in listOf(3, 7)) {
                            if (award.battleProperty.isEmpty()) {
                                MusicManager.playSound(SoundEffect.DrawOrange)
                            } else {
                                award.battleProperty.firstOrNull()?.let {
                                    if (!it.isBad()) {
                                        MusicManager.playSound(SoundEffect.DrawOrange)
                                    }
                                }
                            }
                        }
                        EventManager.getOrCreateHandler(event).setEventAward(awards[index], 1)
                        EventManager.doEventResult(event, true)
                        eventFinished.value = true
                    }
                }
            })
    }


    @Composable
    override fun PostAwardButtons(event: Event) {
        // do nothing
    }

    override fun getAwards(): List<Award> {
        return awards
    }
}
