package com.moyu.chuanqirensheng.feature.equip.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.toMutableStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.feature.ally.ui.UpgradeAnimView
import com.moyu.chuanqirensheng.feature.skill.ui.SingleEquipView
import com.moyu.chuanqirensheng.platform.statusBarHeightInDp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlusFrame
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding51
import com.moyu.chuanqirensheng.ui.theme.padding54
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.filter.CommonFilterView
import com.moyu.chuanqirensheng.widget.filter.CommonOrderView
import com.moyu.chuanqirensheng.widget.filter.FilterLayout
import com.moyu.chuanqirensheng.widget.filter.OrderLayout
import com.moyu.chuanqirensheng.widget.filter.equipFilterList
import com.moyu.chuanqirensheng.widget.filter.equipOrderList
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.common_choose
import shared.generated.resources.filter_frame
import shared.generated.resources.one_click_deselect_battle
import shared.generated.resources.one_click_equip
import shared.generated.resources.one_click_select_battle
import shared.generated.resources.one_click_un_equip


@Composable
fun OutEquipScreen() {
    GameBackground(title = "", showCloseIcon = false) {
        val showFilter = remember {
            mutableStateOf(false)
        }
        val filter = remember {
            repo.equipManager.filterIndexes.map { equipFilterList[it] }
                .toMutableStateList()
        }
        val showOrder = remember {
            mutableStateOf(false)
        }
        val order = remember {
            mutableStateOf(equipOrderList[repo.equipManager.orderIndex.value])
        }
        val list = repo.equipManager.data.filter { equipment ->
            filter.all { it.filter.invoke(equipment) }
        }.sortedByDescending { order.value.order?.invoke(it) }.sortedByDescending { if (it.equipped) 9999 else 0 }
        DisposableEffect(Unit) {
            onDispose {
                repo.equipManager.setAllUnNew()
            }
        }
        Box(
            modifier = Modifier
                .fillMaxSize().padding(top = statusBarHeightInDp(), bottom = padding54),
        ) {
            Image(
                modifier = Modifier.fillMaxSize().padding(bottom = padding34),
                painter = painterResource(Res.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = padding16).padding(horizontal = padding10)
                    .verticalScroll(rememberScrollState())
            ) {
                EquipDetailLayout()
                EquipFilterRow(showFilter, showOrder)
                LazyVerticalGrid(modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth().padding(bottom = padding36),
                    columns = GridCells.Fixed(6),
                    content = {
                        items(list.size) { index ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Spacer(modifier = Modifier.size(padding3))
                                val equipment = list[index]
                                Box {
                                    SingleEquipView(
                                        equipment = equipment,
                                        showName = false,
                                        showRed = true,
                                        itemSize = ItemSize.Large,
                                        textColor = Color.White
                                    )
                                    if (equipment.equipped) {
                                        Image(
                                            painter = painterResource(Res.drawable.common_choose),
                                            modifier = Modifier.size(imageSmallPlusFrame)
                                                .align(Alignment.BottomEnd).zIndex(999f).graphicsLayer {
                                                    translationY = -padding3.toPx()
                                                },
                                            contentDescription = null
                                        )
                                    }
                                    if (equipment.equipped && repo.equipManager.canLevelUp(equipment)) {
                                        UpgradeAnimView(Modifier.align(Alignment.BottomStart)
                                            .height(ItemSize.Large.frameSize / 2))
                                    }
                                }
                                Spacer(modifier = Modifier.size(padding3))
                            }
                        }
                    })
            }
            OrderLayout(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = padding10),
                show = showOrder,
                filter = order,
                filterList = equipOrderList
            ) {
                repo.equipManager.updateOrder(order)
            }
            FilterLayout(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = padding10),
                show = showFilter,
                filter = filter,
                filterList = equipFilterList
            ) {
                repo.equipManager.updateFilter(filter)
            }
        }
//        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.BottomCenter) {
//            GameButton(
//                Modifier.padding(bottom = padding6),
//                buttonSize = ButtonSize.Big,
//                text = stringResource(Res.string.star_up_all)
//            ) {
//                AppWrapper.globalScope.launch(Dispatchers.Main) {
//                    repo.allyManager.starUpAll(true)
//                }
//            }
//        }
    }
}

@Composable
fun EquipFilterRow(showFilter: MutableState<Boolean>, showOrder: MutableState<Boolean>, showSelect: Boolean = true) {
    Row(
        Modifier.fillMaxWidth().height(padding51).paint(
            painterResource(Res.drawable.filter_frame),
            contentScale = ContentScale.FillBounds
        ),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        CommonOrderView(
            Modifier.padding(start = padding10), showOrder
        )
        if (showSelect) {
            GameButton(
                buttonSize = ButtonSize.MediumMinus,
                text = stringResource(Res.string.one_click_equip)
            ) {
                repo.equipManager.oneShotSelect()
            }
            GameButton(
                buttonSize = ButtonSize.MediumMinus,
                buttonStyle = ButtonStyle.Blue,
                text = stringResource(Res.string.one_click_un_equip)
            ) {
                repo.equipManager.oneShotDeselect()
            }
        }
        CommonFilterView(
            Modifier.padding(end = padding10), showFilter
        )
    }
}



@Composable
fun AllyFilterRow(showFilter: MutableState<Boolean>, showOrder: MutableState<Boolean>, showSelect: Boolean = false) {
    Row(
        Modifier.fillMaxWidth().height(padding51).paint(
            painterResource(Res.drawable.filter_frame),
            contentScale = ContentScale.FillBounds
        ),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        CommonOrderView(
            Modifier.padding(start = padding10), showOrder
        )
        if (showSelect) {
            GameButton(
                buttonSize = ButtonSize.MediumMinus,
                text = stringResource(Res.string.one_click_select_battle)
            ) {
                repo.allyManager.oneShotSelect()
            }
            GameButton(
                buttonSize = ButtonSize.MediumMinus,
                buttonStyle = ButtonStyle.Blue,
                text = stringResource(Res.string.one_click_deselect_battle)
            ) {
                repo.allyManager.oneShotDeselect()
            }
        }
        CommonFilterView(
            Modifier.padding(end = padding10), showFilter
        )
    }
}


