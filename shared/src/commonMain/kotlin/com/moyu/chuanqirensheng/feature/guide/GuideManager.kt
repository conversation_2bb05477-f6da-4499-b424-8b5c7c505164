package com.moyu.chuanqirensheng.feature.guide

import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf

// 引导 6, 8, 17, 22, 25, 30, 35, 39
const val GUIDE_STAGE1 = 5
const val GUIDE_STAGE2 = 8
const val GUIDE_STAGE3 = 17
const val GUIDE_STAGE4 = 22
const val GUIDE_STAGE5 = 25
const val GUIDE_STAGE6 = 30
const val GUIDE_STAGE7 = 34
const val GUIDE_STAGE8 = 39
const val GUIDE_STAGE9 = 9999

object GuideManager {
    val guideIndex = mutableIntStateOf(0)
    val showGuide = mutableStateOf(false)

    fun canBack(): Boolean {
        return !showGuide.value
    }

    fun pauseGame(): Boolean {
        return showGuide.value
    }
}