package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE7
import com.moyu.chuanqirensheng.feature.guide.GUIDE_STAGE8
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.login.unlockDialogDismissed
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.router.DUNGEON_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP2
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TOWER
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.dungeon
import shared.generated.resources.dungeon_frame
import shared.generated.resources.dungeon_icon
import shared.generated.resources.red_icon

@Composable
fun DungeonIcon(modifier: Modifier, itemSize: ItemSize) {
    val unlock1 = repo.gameCore.getUnlockById(UNLOCK_TOWER)
    val unlock2 = repo.gameCore.getUnlockById(UNLOCK_PVP)
    val unlock3 = repo.gameCore.getUnlockById(UNLOCK_PVP2)
    if (UnlockManager.getUnlockedFlow(unlock1) || UnlockManager.getUnlockedFlow(unlock2) || UnlockManager.getUnlockedFlow(
            unlock3
        )
    ) {
        // 这里我假设你有 5 个 BottomItem 的信息，可以放在一个 List 里
        LaunchedEffect(Dialogs.unlockStatusDialog.value, GuideManager.guideIndex.value) {
            // 十连抽
            delay(300)
            if (unlockDialogDismissed() && GuideManager.guideIndex.value == 30) {
                if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_PVP))) {
                    GuideManager.guideIndex.value = 31
                    setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE7)
                    GuideManager.showGuide.value = true
                }
            }

            if (unlockDialogDismissed() && GuideManager.guideIndex.value == 34) {
                if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_TOWER))) {
                    GuideManager.guideIndex.value = 35
                    setIntValueByKey(KEY_GUIDE_INDEX, GUIDE_STAGE8)
                    GuideManager.showGuide.value = true
                }
            }
        }
        val pressing = remember {
            mutableStateOf(false)
        }
        val colorFilter = if (pressing.value) {
            ColorFilter.tint(
                B50, BlendMode.SrcAtop
            )
        } else {
            null
        }
        val color = if (pressing.value) W50 else Color.White
        Column(modifier, horizontalAlignment = Alignment.CenterHorizontally) {
            EffectButton(pressing = pressing, onClick = {
                goto(DUNGEON_SCREEN)
            }) {
                Image(
                    modifier = Modifier.height(itemSize.itemSize),
                    painter = painterResource(Res.drawable.dungeon_frame),
                    contentScale = ContentScale.FillHeight,
                    colorFilter = colorFilter,
                    contentDescription = null
                )
                Image(
                    modifier = Modifier.height(itemSize.itemSize / 1.5f).graphicsLayer {
                        translationY = -padding6.toPx()
                        translationX = padding6.toPx()
                    },
                    painter = painterResource(Res.drawable.dungeon_icon),
                    contentScale = ContentScale.FillHeight,
                    colorFilter = colorFilter,
                    contentDescription = null
                )
                if ((PvpManager.unlocked() && PvpManager.hasRedAll())
                    || (Pvp2Manager.unlocked() && Pvp2Manager.hasRedAll())
                    || (TowerManager.unlocked() && TowerManager.hasRed())
                ) {
                    Image(
                        modifier = Modifier.align(Alignment.TopEnd).padding(itemSize.itemSize / 10)
                            .size(imageTinyPlus).graphicsLayer {
                                translationX = padding6.toPx()
                            },
                        colorFilter = colorFilter,
                        painter = painterResource(Res.drawable.red_icon),
                        contentDescription = null
                    )
                }
                StrokedText(
                    modifier = Modifier.align(Alignment.BottomCenter).graphicsLayer {
                        translationY = -padding12.toPx()
                        translationX = padding6.toPx()
                    },
                    text = stringResource(Res.string.dungeon),
                    maxLines = 1,
                    textAlign = TextAlign.Center,
                    color = color,
                    style = MaterialTheme.typography.h3
                )

                if (GuideManager.guideIndex.value == 31) {
                    GuideHand(
                        modifier = Modifier.align(Alignment.TopCenter).height(padding80).graphicsLayer {
                            translationY = -padding60.toPx()
                        },
                        handType = HandType.DOWN_HAND
                    )
                } else if (GuideManager.guideIndex.value == 35) {
                    GuideHand(
                        modifier = Modifier.align(Alignment.TopCenter).height(padding80).graphicsLayer {
                            translationY = -padding60.toPx()
                        },
                        handType = HandType.DOWN_HAND
                    )
                }
            }
        }
    }
}
