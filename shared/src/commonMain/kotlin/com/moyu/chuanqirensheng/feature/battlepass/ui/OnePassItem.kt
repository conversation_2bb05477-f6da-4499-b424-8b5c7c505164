package com.moyu.chuanqirensheng.feature.battlepass.ui


import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.battlepass.getBattlePassManager
import com.moyu.chuanqirensheng.feature.resource.VipLevel
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.cheatFrameHeight
import com.moyu.chuanqirensheng.ui.theme.cheatFrameWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.core.model.BattlePass
import com.moyu.core.model.toAward
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.common_choose
import shared.generated.resources.common_lock
import shared.generated.resources.equipment_frame
import shared.generated.resources.frame_equip_quality_3
import shared.generated.resources.sell_label
import shared.generated.resources.talent1_frame_dark
import shared.generated.resources.talent1_frame_light

@Composable
fun OnePassItem(
    pass: BattlePass,
    passType: Int,
    callback: () -> Unit
) {
    EffectButton(
        modifier = Modifier.size(cheatFrameWidth, cheatFrameHeight),
        onClick = callback
    ) {
        PassAwards(pass = pass, passType = passType, callback = callback)
        val manager = getBattlePassManager(passType)
        val unlocked = (manager.getCurrentWarPass()?.id ?: 0) >= pass.id
        VipLevel(
            modifier = Modifier
                .align(Alignment.TopStart)
                .offset(y = -padding10),
            cheatLevel = pass.level,
            frame = painterResource(if (unlocked) Res.drawable.talent1_frame_light else Res.drawable.talent1_frame_dark)
        )
    }
}

@Composable
fun PassAwards(
    pass: BattlePass,
    passType: Int,
    callback: () -> Unit
) {
    val manager = getBattlePassManager(passType)
    val unlocked = (manager.getCurrentWarPass()?.id ?: 0) >= pass.id
    val bought = manager.isBattlePassBought()

    Box(contentAlignment = Alignment.Center, modifier = Modifier.fillMaxSize()) {
        // 背景框(已解锁 or 未解锁)
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding2),
            contentScale = ContentScale.FillBounds,
            colorFilter = if (unlocked) null else ColorFilter.tint(
                B50, BlendMode.SrcAtop
            ),
            painter = painterResource(Res.drawable.equipment_frame),
            contentDescription = null
        )
        Column {
            Spacer(modifier = Modifier.size(padding10))
            val award = pass.toAward()
            // 如果已解锁，就可以点击领取
            if (unlocked) {
                AwardList(
                    Modifier,
                    param = defaultParam.copy(
                        peek = true,
                        textColor = Color.White,
                        textFrameDrawable = Res.drawable.sell_label,
                        textFrameDrawableYPadding = padding6,
                        textWidthScale = 1.8f,
                        minLine = 1,
                        frameDrawable = Res.drawable.frame_equip_quality_3,
                        callback = callback
                    ),
                    award = award,
                )
            } else {
                // 未解锁
                AwardList(
                    Modifier,
                    param = defaultParam.copy(
                        peek = true,
                        textColor = Color.White,
                        textWidthScale = 1.8f,
                        minLine = 1,
                        textFrameDrawable = Res.drawable.sell_label,
                        textFrameDrawableYPadding = padding6,
                        frameDrawable = Res.drawable.frame_equip_quality_3,
                        callback = if (pass.unlockType == 2) callback else null // 如果是付费item，点击都弹窗
                    ),
                    award = award
                )
            }
        }

        // 如果是付费 unlockType=2，但没购买，就显示锁
        if (pass.unlockType == 2 && !bought) {
            Image(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(imageLarge),
                painter = painterResource(Res.drawable.common_lock),
                contentDescription = null
            )
        }

        // 如果已解锁，并且 manager.isThisLevelGained(pass) = true，表明已领取
        if (unlocked) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(padding6),
                contentAlignment = Alignment.Center
            ) {
                if (manager.isThisLevelGained(pass)) {
                    Image(
                        modifier = Modifier.size(imageLarge),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(Res.drawable.common_choose),
                        contentDescription = null
                    )
                }
            }
        }
    }
}
