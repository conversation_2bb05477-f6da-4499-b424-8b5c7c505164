package com.moyu.chuanqirensheng.feature.login

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import com.moyu.chuanqirensheng.debug.DebugButton
import com.moyu.chuanqirensheng.feature.activities.ui.ActivitiesIcon
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePassIcon
import com.moyu.chuanqirensheng.feature.illustration.ui.TcgIcon
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.ui.QuestIcon
import com.moyu.chuanqirensheng.feature.rank.ui.RankIcon
import com.moyu.chuanqirensheng.feature.reputation.ui.ReputationIcon
import com.moyu.chuanqirensheng.feature.resource.InfoIcon
import com.moyu.chuanqirensheng.feature.router.MORE_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.sign.ui.SignIcon
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.statusBarHeightInDp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.antiaddict.AntiAddictDialog
import com.moyu.chuanqirensheng.sub.privacy.PrivacyManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.padding9
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.ItemSize
import kotlinx.coroutines.delay


@Composable
fun LoginScreen() {
    LaunchedEffect(Unit) {
        repo.inGame.value = false
        // 海外版本没有健康页面，所以这里还是要有一次初始化，里面有去重
        if (!PrivacyManager.privacyNeedShow && !PrivacyManager.permissionNeedShow) {
            gameSdkDefaultProcessor().initSDK()
        }
        // 保证能及时刷新
        QuestManager.createTasks()
        SevenDayManager.init()
        UnlockManager.checkIfShowUnlockDialog()
        delay(3000)
    }
    GameBackground(showCloseIcon = false) {
        Box(modifier = Modifier.padding(top = statusBarHeightInDp())) {
            Column(Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
                Spacer(modifier = Modifier.weight(1f))
                GameMenu(Modifier.fillMaxWidth())
                Spacer(modifier = Modifier.size(padding80))
            }
            TopItemsRight(Modifier.align(Alignment.TopEnd))
            TopItemsLeft(Modifier.align(Alignment.TopStart))
            Column(Modifier.align(Alignment.TopEnd).padding(top = padding48).width(ItemSize.Large.frameSize),
                horizontalAlignment = Alignment.CenterHorizontally) {
                InfoIcon(size = imageLarge) {
                    goto(MORE_SCREEN)
                }
                GiftDetailIcon(ItemSize.MediumPlus)
                Spacer(Modifier.size(padding10))
                ErrorOrderIcon()
            }
            Column(
                Modifier.align(Alignment.TopStart).padding(top = padding72, start = padding4)
                    .clip(RoundedCornerShape(padding10))
                    .background(
                        B50
                    )
                    .padding(horizontal = padding2, vertical = padding2),
                verticalArrangement = Arrangement.spacedBy(padding9)
            ) {
                SignIcon(ItemSize.MediumPlus)
                ReputationIcon(ItemSize.MediumPlus)
                BattlePassIcon(ItemSize.MediumPlus)
                ActivitiesIcon(ItemSize.MediumPlus)
                QuestIcon(ItemSize.MediumPlus)
                RankIcon(ItemSize.MediumPlus)
                TcgIcon(ItemSize.MediumPlus)
            }
            DebugButton(Modifier.align(Alignment.Center))
        }
    }
    AntiAddictDialog { }
}