package com.moyu.chuanqirensheng.feature.lucky.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.LayoutDirection
import com.moyu.chuanqirensheng.feature.lucky.AdManager
import com.moyu.chuanqirensheng.feature.lucky.AdManager.refreshAdMoney
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageMediumMinus
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.imageTiny
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.chuanqirensheng.util.millisToMidnight
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.ad_index
import shared.generated.resources.ad_money_desc
import shared.generated.resources.ad_value_icon
import shared.generated.resources.bar_blue_long
import shared.generated.resources.bar_empty_long
import shared.generated.resources.common_choose
import shared.generated.resources.daily_gift_icon
import shared.generated.resources.red_icon
import shared.generated.resources.task_refresh_left_time

@Composable
fun AdRewardBar(
    modifier: Modifier = Modifier,
    currentValue: Int,
    maxValue: Int,
    showNum: Boolean = true,
    textColor: Color = Color.White,
    style: TextStyle = MaterialTheme.typography.h6,
) {
    val refresh = remember {
        mutableIntStateOf(0)
    }
    LaunchedEffect(refresh) {
        refreshAdMoney()
    }
    val leftUpdateTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(refresh) {
        refreshNetTime()
        if (isNetTimeValid()) {
            while (true) {
                leftUpdateTime.longValue = millisToMidnight(getCurrentTime())
                if (leftUpdateTime.longValue <= 1000) {
                    delay(1000)
                    // 修改这个，上面的LauncherEffect会刷新任务
                    refresh.intValue += 1
                }
                delay(500)
            }
        }
    }

    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageMedium),
            painter = painterResource(Res.drawable.ad_value_icon),
            contentDescription = null
        )
        Column(Modifier.fillMaxSize()) {
            Row(
                Modifier
                    .height(padding36)
                    .padding(start = padding6, end = padding12)
            ) {
                AdManager.luckyList.forEach {
                    Spacer(modifier = Modifier.weight(it.weight()))
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        StrokedText(
                            text = it.price.toString(),
                            style = style,
                            color = textColor
                        )
                        Image(
                            modifier = Modifier.size(imageSmallPlus),
                            painter = painterResource(Res.drawable.ad_index),
                            contentDescription = null
                        )
                    }
                }
            }
            Box(
                Modifier
                    .fillMaxWidth()
                    .weight(1f), contentAlignment = Alignment.Center
            ) {
                AdBar(
                    modifier = Modifier
                        .fillMaxWidth(),
                    currentValue = currentValue,
                    maxValue = maxValue,
                    showNum = showNum,
                    textColor = textColor,
                    style = style
                )
                StrokedText(
                    modifier = Modifier.align(Alignment.CenterStart).padding(start = padding8),
                    text = stringResource(Res.string.ad_money_desc),
                    style = MaterialTheme.typography.h6
                )
                StrokedText(
                    modifier = Modifier.align(Alignment.CenterEnd)
                        .padding(end = padding8),
                    text = stringResource(Res.string.task_refresh_left_time) + leftUpdateTime.longValue.millisToHoursMinutesSeconds(),
                    style = MaterialTheme.typography.h6
                )
            }
            Row(
                Modifier
                    .height(padding36)
                    .padding(start = padding6, end = padding12)
            ) {
                AdManager.luckyList.forEach {
                    Spacer(modifier = Modifier.weight(it.weight()))
                    EffectButton(onClick = {
                        if (AdManager.canGetAdMoney(it) && AdManager.alreadyGot(lucky = it)
                                .not()
                        ) {
                            AdManager.getAdMoney(it)
                        }
                    }) {
                        val available = AdManager.canGetAdMoney(it)
                        val gained = AdManager.alreadyGot(it)
                        Image(
                            modifier = Modifier.size(imageMediumMinus),
                            painter = painterResource(Res.drawable.daily_gift_icon),
                            colorFilter = if (available) null else ColorFilter.tint(
                                B50, BlendMode.SrcAtop
                            ),
                            contentDescription = null
                        )
                        if (gained) {
                            Image(
                                modifier = Modifier.align(Alignment.BottomEnd).size(imageSmall),
                                painter = painterResource(Res.drawable.common_choose),
                                contentDescription = null
                            )
                        }
                        if (available && !gained) {
                            Image(
                                modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .size(imageTiny),
                                painter = painterResource(Res.drawable.red_icon),
                                contentDescription = null
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AdBar(
    modifier: Modifier = Modifier,
    currentValue: Int,
    maxValue: Int,
    emptyRes: DrawableResource = Res.drawable.bar_empty_long,
    fullRes: DrawableResource = Res.drawable.bar_blue_long,
    showNum: Boolean = true,
    textColor: Color = Color.White,
    style: TextStyle = MaterialTheme.typography.h6
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
    ) {
        val expBuilder: Path.(size: Size, layoutDirection: LayoutDirection) -> Unit =
            { size: Size, _: LayoutDirection ->
                this.addRect(
                    Rect(
                        0f,
                        0f,
                        size.width * currentValue.toFloat() / maxValue,
                        size.height
                    )
                )
            }
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding2, vertical = padding2)
                .clip(GenericShape(expBuilder))
                .clip(RoundedCornerShape(padding2))
                .align(Alignment.CenterStart),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(fullRes),
            contentDescription = null
        )
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(emptyRes),
            contentDescription = null
        )
        if (showNum) {
            StrokedText(
                text = "$currentValue/$maxValue",
                style = style,
                color = textColor
            )
        }
    }
}
