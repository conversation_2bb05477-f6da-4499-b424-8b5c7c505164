package com.moyu.chuanqirensheng.feature.vip.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.resource.CurrentElectricPoint
import com.moyu.chuanqirensheng.feature.resource.VipLevel
import com.moyu.chuanqirensheng.feature.router.gotoSellWithTabIndex
import com.moyu.chuanqirensheng.feature.sell.SELL_KEY_INDEX
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.cheatFrameHeight
import com.moyu.chuanqirensheng.ui.theme.cheatFrameWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.labelHeight
import com.moyu.chuanqirensheng.ui.theme.labelWidth
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding130
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.CardSize
import com.moyu.chuanqirensheng.widget.common.IconView
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.common.getTextStyle
import com.moyu.chuanqirensheng.widget.effect.ForeverGif
import com.moyu.chuanqirensheng.widget.effect.MovableImage
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.chuanqirensheng.widget.effect.vipGif
import com.moyu.core.AppWrapper
import com.moyu.core.model.Vip
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.activity_frame
import shared.generated.resources.common_big_frame
import shared.generated.resources.common_choose
import shared.generated.resources.electric
import shared.generated.resources.equipment_frame
import shared.generated.resources.frame_equip_quality_3
import shared.generated.resources.get_electric
import shared.generated.resources.pass_ally_title
import shared.generated.resources.sell_label
import shared.generated.resources.shop_label7
import shared.generated.resources.shop_label8
import shared.generated.resources.stage_label
import shared.generated.resources.talent1_frame_dark
import shared.generated.resources.talent1_frame_light
import shared.generated.resources.vip_ally_title
import shared.generated.resources.vip_shop_label


@Composable
fun VipScreen() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .paint(
                painterResource(Res.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            )
            .padding(vertical = padding10, horizontal = padding10)
    ) {
        Spacer(Modifier.size(padding4))
        Box {
            Image(
                modifier = Modifier.fillMaxWidth().padding(horizontal = padding4).height(padding120)
                    .padding(padding2).clip(
                    RoundedCornerShape(padding10)
                ),
                painter = painterResource(Res.drawable.vip_shop_label),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
            Image(
                modifier = Modifier.fillMaxWidth().padding(horizontal = padding4)
                    .height(padding120),
                painter = painterResource(Res.drawable.activity_frame),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
            Column(
                Modifier.padding(start = padding40, top = padding30),
            ) {
                StrokedText(
                    modifier = Modifier.scale(1.2f),
                    text = stringResource(Res.string.shop_label7),
                    style = MaterialTheme.typography.h1
                )
                Spacer(Modifier.size(padding22))
                StrokedText(
                    modifier = Modifier.padding(start = padding40).scale(1.2f),
                    text = stringResource(Res.string.shop_label8),
                    style = MaterialTheme.typography.h1
                )
            }
        }
        CurrentElectricPoint(Modifier.align(Alignment.CenterHorizontally))
        Spacer(modifier = Modifier.size(padding10))
        Box(modifier = Modifier.fillMaxSize()) {
            val pool = repo.gameCore.getVipPool()
            val firstGainIndex = pool.indexOfFirst {
                it.level <= VipManager.getVipLevel() && !VipManager.isThisLevelGained(it)
            }
            val state = rememberLazyListState((firstGainIndex - 1).coerceIn(0, pool.size - 1))
            LazyColumn(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = padding4),
                state = state,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                items(pool.size) { index ->
                    if (index == 0) {
                        Spacer(modifier = Modifier.size(padding10))
                    }
                    Spacer(modifier = Modifier.size(padding10))
                    OneCheatItem(pool[index]) {
                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                            val unlocked = VipManager.getVipLevel() >= pool[index].level
                            if (unlocked) {
                                VipManager.gain(pool[index])
                            } else {
                                Dialogs.vipDetailDialog.value = pool[index]
                            }
                        }
                    }
                    if (index == pool.size - 1) {
                        Spacer(modifier = Modifier.size(padding130))
                    }
                }
            }
            CurrentCheatItem(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .graphicsLayer {
                        translationY = -padding60.toPx()
                    })
        }
        Spacer(modifier = Modifier.size(padding10))
    }
}

@Composable
fun CurrentCheatItem(modifier: Modifier) {
    Column(
        modifier = modifier
            .padding(end = padding30)
            .scale(0.9f)
            .graphicsLayer {
                translationX = padding26.toPx()
            },
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        val current = VipManager.getVipLevel()
        val poolId = repo.gameCore.getVipPool().filter { it.level > current }
            .firstOrNull { it.toAward().outAllies.isNotEmpty() }?.effectId
            ?: repo.gameCore.getVipPool().last().effectId
        VipAllyView(poolId = poolId)
        Spacer(modifier = Modifier.size(padding10))
        GameButton(
            text = stringResource(Res.string.get_electric),
            buttonSize = ButtonSize.Big,
            buttonStyle = ButtonStyle.Blue,
            textColor = Color.White
        ) {
            gotoSellWithTabIndex(SELL_KEY_INDEX)
        }
        Spacer(modifier = Modifier.size(padding4))
    }
}

@Composable
fun VipAllyView(poolId: Int, isPass: Boolean = false) {
    val award = repo.gameCore.getPoolById(poolId).toAward()
    // 通行证第二种就不是ally奖励，所以这种情况直接显示award
    award.outAllies.firstOrNull()?.let { ally ->
        EffectButton(onClick = {
            Dialogs.allyDetailDialog.value = ally.copy(peek = true)
        }) {
            val cardSize = CardSize.Large
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Box(
                    modifier = Modifier.width(cardSize.width),
                    contentAlignment = Alignment.TopCenter
                ) {
                    Image(
                        modifier = Modifier.size(labelWidth, labelHeight).scale(1.0f, 1.4f),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(Res.drawable.stage_label),
                        contentDescription = null
                    )
                    StrokedText(
                        modifier = Modifier.padding(
                            top = padding8
                        ),
                        text = stringResource(if (isPass) Res.string.pass_ally_title else Res.string.vip_ally_title),
                        style = MaterialTheme.typography.h1,
                        textAlign = TextAlign.Center,
                    )
                }
                Box(Modifier.size(cardSize.width, cardSize.height)) {
                    MovableImage(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(vertical = padding16)
                            .clip(RoundedCornerShape(cardSize.getRadius())),
                        imageResource = kmpDrawableResource(ally.pic),
                    )
                    ForeverGif(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .size(cardSize.width, cardSize.height),
                        gifDrawable = vipGif.gif,
                        gifCount = vipGif.count,
                        contentScale = ContentScale.Fit,
                        needGap = true
                    )
                }
                Box(contentAlignment = Alignment.Center) {
                    Image(
                        modifier = Modifier
                            .width(ItemSize.Huge.frameSize)
                            .scale(2f),
                        painter = painterResource(Res.drawable.sell_label),
                        contentScale = ContentScale.FillWidth,
                        contentDescription = null
                    )
                    StrokedText(
                        text = ally.name,
                        style = MaterialTheme.typography.h1,
                        color = Color.White
                    )
                }
            }
        }
    } ?: AwardList(
        award = award,
        param = defaultParam.copy(itemSize = ItemSize.Huge, peek = true, textColor = Color.White)
    )
}

@Composable
fun OneCheatItem(cheat: Vip, callback: () -> Unit) {
    EffectButton(
        modifier = Modifier.size(cheatFrameWidth, cheatFrameHeight), onClick = callback
    ) {
        VipAwards(cheat, callback)
        val unlocked = VipManager.getVipLevel() >= cheat.level
        VipLevel(
            modifier = Modifier
                .align(Alignment.TopStart)
                .offset(y = -padding10),
            cheatLevel = cheat.level,
            frame = painterResource(if (unlocked) Res.drawable.talent1_frame_light else Res.drawable.talent1_frame_dark)
        )
        if (VipManager.getVipLevel() + 5 >= cheat.level && cheat.level > VipManager.getVipLevel()) {
            StrokedText(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(end = padding16, top = padding12)
                    .clip(RoundedCornerShape(padding1))
                    .background(
                        B50
                    )
                    .padding(horizontal = padding2, vertical = padding1),
                text = "${cheat.num}" + stringResource(Res.string.electric),
                style = MaterialTheme.typography.h6,
                color = Color.White
            )
        }
    }
}

@Composable
fun VipAwards(cheat: Vip, callback: () -> Unit) {
    val unlocked = VipManager.getVipLevel() >= cheat.level
    Box(contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding2),
            contentScale = ContentScale.FillBounds,
            colorFilter = if (unlocked) null else ColorFilter.tint(
                B50, BlendMode.SrcAtop
            ),
            painter = painterResource(Res.drawable.equipment_frame),
            contentDescription = null
        )
        Column {
            Spacer(modifier = Modifier.size(padding10))
            if (cheat.effectType == 1) {
                val award = cheat.toAward()
                AwardList(
                    Modifier,
                    award = award,
                    param = defaultParam.copy(
                        peek = true,
                        textColor = Color.White,
                        textFrameDrawable = Res.drawable.sell_label,
                        textFrameDrawableYPadding = padding6,
                        frameDrawable = Res.drawable.frame_equip_quality_3,
                        textWidthScale = 1.8f,
                        minLine = 1,
                        callback = if (unlocked) callback else null
                    ),
                )
            } else {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    IconView(
                        modifier = Modifier.scale(0.95f),
                        res = kmpDrawableResource(cheat.pic),
                        resZIndex = 99f, // 不透明框，需要res在上面
                        frame = Res.drawable.frame_equip_quality_3,
                        itemSize = ItemSize.Large,
                        callback = { callback.invoke() })
                    Box(modifier = Modifier.graphicsLayer {
                        translationY = padding6.toPx()
                    }, contentAlignment = Alignment.Center) {
                        Image(
                            modifier = Modifier
                                .width(ItemSize.Large.frameSize)
                                .scale(1.8f),
                            painter = painterResource(Res.drawable.sell_label),
                            contentScale = ContentScale.FillWidth,
                            contentDescription = null
                        )
                        StrokedText(
                            text = cheat.name,
                            style = ItemSize.Large.getTextStyle(),
                            maxLines = 1,
                            overflow = TextOverflow.Visible,
                            softWrap = false,
                            textAlign = TextAlign.Center,
                        )
                    }
                }
            }
        }
        if (unlocked) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(padding6),
                contentAlignment = Alignment.Center
            ) {
                if (VipManager.isThisLevelGained(cheat)) {
                    Image(
                        modifier = Modifier.size(imageLarge),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(Res.drawable.common_choose),
                        contentDescription = null
                    )
                }
            }
        } else {
            // null
        }
    }
}