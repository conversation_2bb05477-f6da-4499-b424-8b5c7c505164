package com.moyu.chuanqirensheng.feature.event.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.event.handler.PlayHandler
import com.moyu.chuanqirensheng.feature.event.isBattle
import com.moyu.chuanqirensheng.feature.event.isKeyBattle
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.effect.ShadowImage


@Composable
fun EventPicView(modifier: Modifier, handler: MutableState<PlayHandler?>) {
    // 普通事件是有事件pic，事件完成就动画消失这个pic
    // 如果是普通战斗，则一直没有pic，直接显示阵容
    // 如果是其他战斗，则点击开始战斗之前(hasLayout)，有pic，点击开始战斗之后，没有。
    // 攻城战的pic放大一些
    AnimatedVisibility(
        modifier = modifier.padding(end = padding60, bottom = padding100)
            .size(if (EventManager.selectedEvent.value?.isKeyBattle() == true) padding200 else padding150),
        visible = handler.value?.eventFinished?.value == false,
        enter = fadeIn() + slideInHorizontally(initialOffsetX = { fullWidth -> fullWidth } // 从右边滑入
        ),
        exit = fadeOut() + slideOutHorizontally(targetOffsetX = { fullWidth -> -fullWidth } // 向左边滑出
        )) {
        EventManager.selectedEvent.value?.let {
            if (!it.isBattle() || (it.pic != "0" && handler.value?.hasLayout?.value != true)) {
                ShadowImage(
                    modifier = Modifier.fillMaxSize(),
                    imageResource = kmpDrawableResource(it.pic),
                )
            }
        }
    }
}