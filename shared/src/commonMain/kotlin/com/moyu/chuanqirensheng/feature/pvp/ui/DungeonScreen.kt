package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.feature.activities.ui.ActivityItem
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideHand
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.feature.pvp.MAX_PVP_NUM
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.router.PVP2_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP2
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TOWER
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.dungeon
import shared.generated.resources.dungeon_top_label
import shared.generated.resources.pvp
import shared.generated.resources.pvp2
import shared.generated.resources.tower_mode

val dungeonItems = listOf(
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.tower_mode) }, route = {
            goto(TOWER_SCREEN)
        },
        frame = "tower_frame",
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_TOWER).desc,
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_TOWER)
            UnlockManager.getUnlockedFlow(unlock)
        },
        red = {
            TowerManager.hasRed()
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.pvp) },
        route = { goto(PVP_SCREEN) },
        frame = "pvp_label",
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_PVP).desc,
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_PVP)
            UnlockManager.getUnlockedFlow(unlock)
        },
        red = {
            PvpManager.pkNumToday.value < MAX_PVP_NUM || QuestManager.pvpTasks.any {
                QuestManager.getTaskDoneFlow(it) && !it.opened
            }
        }
    ),
    ActivityItem(name = { AppWrapper.getStringKmp(Res.string.pvp2) },
        route = { goto(PVP2_SCREEN) },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_PVP2).desc,
        frame = "pvp2_frame",
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_PVP2)
            UnlockManager.getUnlockedFlow(unlock)
        },
        red = {
            Pvp2Manager.pkNumToday.value < MAX_PVP_NUM || QuestManager.pvp2Tasks.any {
                QuestManager.getTaskDoneFlow(it) && !it.opened
            }
        }
    ),
)

@Composable
fun DungeonScreen() {
    GameBackground(title = stringResource(Res.string.dungeon), topContent = {
        Image(
            modifier = Modifier
                .fillMaxWidth().scale(1.25f),
            painter = painterResource(Res.drawable.dungeon_top_label),
            contentScale = ContentScale.FillWidth,
            contentDescription = null
        )
    }) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(padding10),
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(Res.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding10, horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            dungeonItems.forEachIndexed { index, it ->
                Box {
                    ActivityItem(
                        moreItem = it,
                    )
                    if (GuideManager.guideIndex.value == 32 && index == 1) {
                        GuideHand(
                            modifier = Modifier.align(Alignment.BottomEnd).padding(end = padding120).height(
                                padding60
                            ),
                            handType = HandType.RIGHT_HAND
                        )
                    } else if (GuideManager.guideIndex.value == 36 && index == 0) {
                        GuideHand(
                            modifier = Modifier.align(Alignment.BottomEnd).padding(end = padding120).height(
                                padding60
                            ),
                            handType = HandType.RIGHT_HAND
                        )
                    }
                }
            }
        }
    }
}