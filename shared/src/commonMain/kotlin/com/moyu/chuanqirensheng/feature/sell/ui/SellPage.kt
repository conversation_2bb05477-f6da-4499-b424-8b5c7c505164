package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.resource.CurrentDiamondPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.feature.resource.CurrentPvpDiamond
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.sell.preCondition
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.labelHeight
import com.moyu.chuanqirensheng.ui.theme.labelWidth
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.widget.button.RefreshButton
import com.moyu.chuanqirensheng.widget.common.GameLabel
import com.moyu.chuanqirensheng.widget.dialog.CommonAlert
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_big_frame
import shared.generated.resources.key_not_enough
import shared.generated.resources.reach_max_refresh
import shared.generated.resources.refresh
import shared.generated.resources.refresh_cost
import shared.generated.resources.refresh_cost2
import shared.generated.resources.refresh_shop


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun SellPage(
    itemTypes: List<Int>,
    showPvpDiamond: Boolean = false,
    showLockedItem: Boolean = true,
    showLabel: Boolean = true,
    topContent: @Composable ColumnScope.() -> Unit = {}
) {
    LaunchedEffect(Unit) {
        SellManager.init()
    }
    val shopChests =
        SellManager.items.filter { it.type in itemTypes && it.preCondition() }
            .filter {
                // storageType是永久限量，storage是剩余数量
                it.storage > 0 || it.storageType != 2
            }.filter {
                // 钥匙商店，如果是谷歌商店，不显示没有谷歌商品id的商品
                if (it.isAifadian()) {
                    it.googleItemId != "0" || !hasGoogleService()
                } else true
            }.filter {
                if (showLockedItem) true
                else UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(it.unlock))
            }
    Column(
        Modifier.paint(
            painterResource(Res.drawable.common_big_frame),
            contentScale = ContentScale.FillBounds
        )
    ) {
        Spacer(modifier = Modifier.size(padding8))
        Row(
            modifier = Modifier
                .padding(start = padding14)
        ) {
            if (showPvpDiamond) {
                CurrentPvpDiamond(
                    showFrame = true
                )
            } else {
                CurrentKeyPoint(
                    showPlus = true,
                    showFrame = true
                )
                CurrentDiamondPoint(
                    showPlus = true,
                    showFrame = true
                )
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(
                    rememberScrollState()
                ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            topContent()
            Spacer(Modifier.size(padding16))
            shopChests.groupBy { it.type }.entries.forEach { typeGroups ->
                typeGroups.value.groupBy { it.title }.keys.forEach { title ->
                    val items = shopChests.filter { it.type == typeGroups.key }
                        .filter { it.title == title }
                    if (title != "0" && showLabel) {
                        GameLabel(Modifier.size(labelWidth, labelHeight)) {
                            if (items.any { it.canRefresh }) {
                                val cost = VipManager.getShopRefreshCost()
                                RefreshButton(
                                    Modifier
                                        .align(Alignment.CenterEnd)
                                        .padding(end = padding19),
                                    stringResource(Res.string.refresh)
                                ) {
                                    val refreshedNum = when (typeGroups.key) {
                                        1 -> SellManager.refresh1Count.intValue
                                        else -> SellManager.refresh2Count.intValue
                                    }
                                    val limit = VipManager.getRealShopRefreshLimit()
                                    if (refreshedNum >= limit) {
                                        AppWrapper.getStringKmp(Res.string.reach_max_refresh).toast()
                                    } else {
                                        Dialogs.alertDialog.value = CommonAlert(
                                            title = AppWrapper.getStringKmp(Res.string.refresh_shop),
                                            content = AppWrapper.getStringKmp(Res.string.refresh_cost) + cost + AppWrapper.getStringKmp(
                                                Res.string.refresh_cost2
                                            ),
                                            confirmText = AppWrapper.getStringKmp(Res.string.refresh),
                                            onConfirm = {
                                                if (AwardManager.key.value >= cost) {
                                                    AwardManager.gainKey(-cost)
                                                    SellManager.refreshSellItemByType(typeGroups.key)
                                                } else {
                                                    GiftManager.onKeyNotEnough()
                                                    AppWrapper.getStringKmp(Res.string.key_not_enough)
                                                        .toast()
                                                }
                                            }
                                        )
                                    }
                                }
                            }
                            StrokedText(
                                text = title,
                                style = MaterialTheme.typography.h2,
                                textAlign = TextAlign.Center,
                                color = Color.White
                            )
                        }
                    }
                    Spacer(Modifier.size(padding8))
                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        overflow = FlowRowOverflow.Visible,
                        maxItemsInEachRow = 3
                    ) {
                        items
                            .sortedBy { it.priority }
                            .forEach {
                                OneSellItem(it)
                            }
                    }
                    Spacer(Modifier.size(padding10))
                }
                Spacer(modifier = Modifier.size(padding50))
            }
            Spacer(modifier = Modifier.size(padding100))
        }
    }
}