package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.rank.LAST_PVP_TYPE
import com.moyu.chuanqirensheng.feature.rank.PVP_TYPE
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.ui.RankPage
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.music.SoundEffect
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.icon_rank_yesterday
import shared.generated.resources.last_pvp_rank_tips
import shared.generated.resources.pvp_bg
import shared.generated.resources.pvp_rank1
import shared.generated.resources.pvp_rank2
import shared.generated.resources.pvp_score_icon
import shared.generated.resources.pvp_score_tips
import shared.generated.resources.rank_today

val pvpRanks = mutableStateOf(emptyList<RankData>())
val lastPvpRanks = mutableStateOf(emptyList<RankData>())

@Composable
fun PvpRankScreen() {
    LaunchedEffect(Unit) {
        pvpRanks.value = emptyList()
        lastPvpRanks.value = emptyList()
    }
    val pagerState = remember {
        mutableStateOf(0)
    }
    val listTabItems = remember {
        mutableStateListOf(
            Res.drawable.rank_today,
            Res.drawable.icon_rank_yesterday,
        )
    }
    GameBackground(
        title = if (pagerState.value == 0) stringResource(Res.string.pvp_rank1) else stringResource(
            Res.string.pvp_rank2
        ),
        bgMask = B35, background = Res.drawable.pvp_bg
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Row(Modifier.fillMaxWidth()) {
                StrokedText(
                    modifier = Modifier
                        .padding(start = padding12),
                    text = stringResource(Res.string.last_pvp_rank_tips),
                    style = MaterialTheme.typography.h4
                )
            }
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
            ) {
                when (pagerState.value) {
                    0 -> RankPage(type = PVP_TYPE, data = pvpRanks) { rankData, rankIndex ->
                        Row(modifier = Modifier.clickable {
                            MusicManager.playSound(SoundEffect.Click)
                            AppWrapper.getStringKmp(Res.string.pvp_score_tips).toast()
                        }, verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                modifier = Modifier.size(imageSmall),
                                painter = painterResource(Res.drawable.pvp_score_icon),
                                contentDescription = null
                            )
                            StrokedText(
                                text = rankData.pvpScore.toString(),
                                style = MaterialTheme.typography.h4
                            )
                        }
                    }

                    else -> {
                        RankPage(type = LAST_PVP_TYPE, data = lastPvpRanks) { rankData, rankIndex ->
                            Row(modifier = Modifier.clickable {
                                MusicManager.playSound(SoundEffect.Click)
                                AppWrapper.getStringKmp(Res.string.pvp_score_tips).toast()
                            }, verticalAlignment = Alignment.CenterVertically) {
                                Image(
                                    modifier = Modifier.size(imageSmall),
                                    painter = painterResource(Res.drawable.pvp_score_icon),
                                    contentDescription = null
                                )
                                StrokedText(
                                    text = rankData.pvpScore.toString(),
                                    style = MaterialTheme.typography.h3
                                )
                            }
                        }
                    }
                }
            }
            NavigationTab(
                modifier = Modifier.padding(bottom = padding6),
                pageState = pagerState,
                titles = listTabItems
            )
        }
    }
}