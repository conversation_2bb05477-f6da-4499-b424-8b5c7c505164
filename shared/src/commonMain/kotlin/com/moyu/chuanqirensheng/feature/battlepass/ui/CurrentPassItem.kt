package com.moyu.chuanqirensheng.feature.battlepass.ui


import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.battlepass.getBattlePassManager
import com.moyu.chuanqirensheng.feature.battlepass.unlockEvidenceKey
import com.moyu.chuanqirensheng.feature.router.QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.vip.ui.VipAllyView
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding50
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.battle_pass1_unlocked
import shared.generated.resources.battle_pass2_unlocked
import shared.generated.resources.battle_pass3_unlocked
import shared.generated.resources.gain_battle_pass1_exp
import shared.generated.resources.gain_battle_pass2_exp
import shared.generated.resources.gain_battle_pass3_exp
import shared.generated.resources.shop_discount
import shared.generated.resources.unlock


@Composable
fun CurrentPassItem(
    passType: Int,
    modifier: Modifier = Modifier
) {
    // 根据 passType，拿到第一个(或更多)BattlePass
    val item = when (passType) {
        1 -> repo.gameCore.getBattlePass1Pool().first()
        2 -> repo.gameCore.getBattlePass2Pool().first()
        3 -> repo.gameCore.getBattlePass3Pool().first()
        else -> return
    }
    val text = when (passType) {
        1 -> stringResource(Res.string.gain_battle_pass1_exp)
        2 -> stringResource(Res.string.gain_battle_pass2_exp)
        3 -> stringResource(Res.string.gain_battle_pass3_exp) // 需要你新增 unlock_battle_pass3
        else -> "Unlock"
    }
    val manager = getBattlePassManager(passType)
    val bought = manager.isBattlePassBought()

    Box(
        modifier
            .padding(end = padding30)
            .scale(0.9f)
            .graphicsLayer {
                translationX = padding22.toPx()
            }) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(padding10))
            VipAllyView(item.themeReward, true)
            Spacer(modifier = Modifier.size(padding10))
            if (item.isFree == 1) {
                // 展示「前往任务」按钮
                GameButton(
                    text = text,
                    buttonSize = ButtonSize.Big,
                    buttonStyle = ButtonStyle.Blue
                ) {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        goto(QUEST_SCREEN)
                    }
                }
            } else {
                // 付费
                if (bought) {
                    // 已购买
                    StrokedText(
                        text = when (passType) {
                            1 -> stringResource(Res.string.battle_pass1_unlocked)
                            2 -> stringResource(Res.string.battle_pass2_unlocked)
                            3 -> stringResource(Res.string.battle_pass3_unlocked) // 需要你新增 battle_pass3_unlocked
                            else -> "Unlocked!"
                        },
                        style = MaterialTheme.typography.h3
                    )
                } else {
                    // 未购买
                    GameButton(
                        text = stringResource(Res.string.unlock),
                        buttonSize = ButtonSize.Huge,
                        buttonStyle = ButtonStyle.Green,
                        textColor = Color.White
                    ) {
                        // 打开对应解锁对话框
                        Dialogs.battlePassUnlockDialog.value = passType
                    }
                }
            }
            Spacer(modifier = Modifier.size(padding4))
        }
        val sellKey = unlockEvidenceKey(passType)
        repo.gameCore.getSellPool().firstOrNull { it.id == sellKey }?.let {
            if (!bought) {
                Box(Modifier.align(Alignment.BottomCenter).graphicsLayer {
                    translationY = -padding50.toPx()
                    translationX = -padding80.toPx()
                }, contentAlignment = Alignment.Center) {
                    Image(
                        painter = painterResource(Res.drawable.shop_discount),
                        contentDescription = null,
                        modifier = Modifier.size(padding100)
                    )
                    StrokedText(
                        text = it.desc2,
                        style = MaterialTheme.typography.h1
                    )
                }
            }
        }
    }
}
