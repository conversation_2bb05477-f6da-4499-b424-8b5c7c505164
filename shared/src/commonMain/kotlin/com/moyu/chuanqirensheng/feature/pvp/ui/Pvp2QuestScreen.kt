package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.ui.SingleQuest
import com.moyu.chuanqirensheng.platform.isLite
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.chuanqirensheng.util.millisToMidnight
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.GameLabel2
import com.moyu.chuanqirensheng.widget.common.SearchView
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.model.Quest
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.last_pvp_rank_tips
import shared.generated.resources.pvp2_bg
import shared.generated.resources.pvp_quest2

@Composable
fun Pvp2QuestScreen() {
    val leftUpdateTime = remember {
        mutableStateOf(0L)
    }
    GameBackground(
        title = stringResource(Res.string.pvp_quest2), bgMask = B35, background = Res.drawable.pvp2_bg
    ) {
        val tasks = remember {
            mutableStateListOf<Quest>()
        }
        val refresh = remember {
            mutableIntStateOf(0)
        }
        val search = remember {
            mutableStateOf("")
        }
        LaunchedEffect(refresh.intValue.toString() + search.value) {
            if (!isNetTimeValid()) {
                refreshNetTime()
            }
            Pvp2Manager.init()
            QuestManager.init()
            // 完成的任务排前面，已领取的排最后
            QuestManager.pvp2Tasks.map {
                it.copy(done = QuestManager.getTaskDoneFlow(it))
            }.filter { !it.opened }.sortedBy { it.order }.sortedBy { if (it.done) 0 else 9999 }.apply {
                    tasks.clear()
                    if (search.value.isNotEmpty()) {
                        tasks.addAll(this.filter { it.name.contains(search.value) })
                    } else {
                        tasks.addAll(this)
                    }
                }
        }
        LaunchedEffect(refresh) {
            refreshNetTime()
            if (isNetTimeValid()) {
                while (true) {
                    leftUpdateTime.value = millisToMidnight(getCurrentTime())
                    if (leftUpdateTime.value <= 1000) {
                        delay(1000)
                        // 修改这个，上面的LauncherEffect会刷新任务
                        refresh.intValue += 1
                    }
                    delay(500)
                }
            }
        }
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Spacer(modifier = Modifier.size(padding4))
            if (isLite()) {
                SearchView(search)
            }
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                StrokedText(
                    modifier = Modifier
                        .padding(start = padding12),
                    text = stringResource(Res.string.last_pvp_rank_tips),
                    style = MaterialTheme.typography.h3
                )
                Spacer(modifier = Modifier.weight(1f))
                GameLabel2(
                    modifier = Modifier
                        .padding(end = padding12).size(padding120, padding30)) {
                    StrokedText(
                        text = leftUpdateTime.value.millisToHoursMinutesSeconds(),
                        style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding16))
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
            ) {
                tasks.forEach {
                    SingleQuest(it) {
                        refresh.intValue += 1
                    }
                    Spacer(modifier = Modifier.size(padding5))
                }
            }
        }
    }
}