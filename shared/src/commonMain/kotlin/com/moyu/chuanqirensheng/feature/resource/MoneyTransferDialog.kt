package com.moyu.chuanqirensheng.feature.resource

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.GuardedMemoryF
import com.moyu.chuanqirensheng.text.indexToResourceIcon
import com.moyu.chuanqirensheng.ui.theme.SliderColor
import com.moyu.chuanqirensheng.ui.theme.SliderTrackColor
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.moneyExtraWidth
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.slideHeight
import com.moyu.chuanqirensheng.ui.theme.slideWidth
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.EMPTY_RESOURCES
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_key
import shared.generated.resources.exchange
import shared.generated.resources.exchanged
import shared.generated.resources.key_not_enough
import shared.generated.resources.key_title
import shared.generated.resources.resource1
import shared.generated.resources.select_exchange_num_tips
import shared.generated.resources.three_arrows


@Composable
fun MoneyTransferDialog(switch: MutableState<Boolean>) {
    switch.value.takeIf { it }?.let {
        val key = remember {
            GuardedMemoryF()
        }
        LaunchedEffect(Unit) {
            key.value = 0f
        }
        val rate = repo.gameCore.getKeyToResource1Rate()
        PanelDialog(onDismissRequest = { switch.value = false }, contentBelow = {
            GameButton(text = stringResource(Res.string.exchange),
                enabled = key.value.toInt() > 0,
                buttonStyle = ButtonStyle.Green,
                onClick = {
                    if (key.value.toInt() <= 0) {
                        AppWrapper.getStringKmp(Res.string.select_exchange_num_tips).toast()
                    } else {
                        if (AwardManager.key.value >= key.value) {
                            AwardManager.gainKey(-key.value.toInt())
                            AwardManager.gainResources(EMPTY_RESOURCES.toMutableList().apply {
                                this[0] = key.value.toInt() * rate
                            })
                            (AppWrapper.getStringKmp(Res.string.exchanged) + (key.value.toInt() * rate) + AppWrapper.getStringKmp(
                                Res.string.resource1
                            )).toast()
                            key.value = 0f
                        } else {
                            GiftManager.onKeyNotEnough()
                            AppWrapper.getStringKmp(Res.string.key_not_enough).toast()
                        }
                    }
                })
        }) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    CurrentKeyPoint(showPlus = true)
                    CurrentResourcesPoint(Modifier, 0, boxWidth = moneyExtraWidth)
                }
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Image(
                            modifier = Modifier.size(imageHugeLite),
                            painter = painterResource(Res.drawable.common_key),
                            contentDescription = stringResource(Res.string.key_title)
                        )
                        StrokedText(
                            text = key.value.toInt()
                                .toString() + stringResource(Res.string.key_title),
                            color = Color.White,
                            style = MaterialTheme.typography.h3
                        )
                    }
                    Spacer(modifier = Modifier.size(padding48))
                    Image(
                        modifier = Modifier.size(imageLarge),
                        painter = painterResource(Res.drawable.three_arrows),
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.size(padding48))
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Image(
                            modifier = Modifier.size(imageHugeLite),
                            painter = painterResource(0.indexToResourceIcon()),
                            contentDescription = stringResource(Res.string.resource1)
                        )
                        StrokedText(
                            text = (key.value.toInt() * rate).toString() + stringResource(Res.string.resource1),
                            color = Color.White,
                            style = MaterialTheme.typography.h3
                        )
                    }
                }
                Row {
                    Slider(
                        modifier = Modifier.size(slideWidth, slideHeight),
                        value = key.value, onValueChange = { key.value = it.toInt().toFloat() },
                        valueRange = 0f..AwardManager.key.value.toFloat(),
                        colors = SliderDefaults.colors(
                            thumbColor = SliderColor,
                            disabledThumbColor = SliderColor,
                            activeTrackColor = SliderTrackColor,
                            inactiveTrackColor = SliderTrackColor,
                        ),
                    )
                }
            }
        }
    }
}