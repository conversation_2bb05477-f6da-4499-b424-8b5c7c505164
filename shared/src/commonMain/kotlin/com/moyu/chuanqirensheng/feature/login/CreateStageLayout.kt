package com.moyu.chuanqirensheng.feature.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.pvp.ui.DungeonIcon
import com.moyu.chuanqirensheng.feature.resource.PowerPoint
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.imageLargeFrame
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding220
import com.moyu.chuanqirensheng.ui.theme.padding240
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding44
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.button.QUICK_GAP
import com.moyu.chuanqirensheng.widget.common.IconView
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.ShadowImage
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.Dungeon
import kotlinx.coroutines.runBlocking
import org.jetbrains.compose.resources.getString
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.common_arrow_left
import shared.generated.resources.common_arrow_right
import shared.generated.resources.common_lock
import shared.generated.resources.enter
import shared.generated.resources.pass_prev_tips
import shared.generated.resources.pass_prev_tips2


@Composable
fun CreateStageLayout(modifier: Modifier) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Box(
            Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                CurrentStageEmpireLayout(
                    Modifier.size(padding220, padding200),
                    StageManager.currentStage.value
                )
                LabelView(
                    modifier = Modifier.size(padding240, padding44),
                    text = "${StageManager.currentStage.value.id} " + StageManager.currentStage.value.name
                )
                Spacer(Modifier.height(padding16))
                Box(Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                    GameButton(
                        enabled = StageManager.isCurrentUnlocked(),
                        buttonSize = ButtonSize.Huge,
                        text = ""
                    ) {
                        if (StageManager.isCurrentUnlocked()) {
                            repo.startStageMode()
                        } else {
                            if (StageManager.currentStage.value.condition > 0) {
                                runBlocking {
                                    getString(
                                        Res.string.pass_prev_tips2,
                                        StageManager.currentStage.value.condition
                                    ).toast()
                                }
                            } else {
                                AppWrapper.getStringKmp(Res.string.pass_prev_tips)
                                    .toast()
                            }
                        }
                    }
                    Column(horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center) {
                        StrokedText(
                            text = stringResource(Res.string.enter),
                            style = MaterialTheme.typography.h1,
                            color = Color.White
                        )
                        Spacer(Modifier.size(padding2))
                        PowerPoint(repo.gameCore.getEachStageConsumePower())
                    }
                    if (!StageManager.isCurrentUnlocked()) {
                        Image(
                            modifier = Modifier.size(ItemSize.LargePlus.itemSize),
                            painter = painterResource(Res.drawable.common_lock),
                            contentDescription = null,
                        )
                    }
                    DungeonIcon(
                        Modifier.align(Alignment.CenterEnd),
                        ItemSize.LargePlus
                    )
                }
                Spacer(Modifier.height(padding36))
            }
            SelectStageLayoutMain(
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = padding22)
                    .align(Alignment.Center)
                    .graphicsLayer {
                        translationY = -padding22.toPx()
                    })
        }
    }
}

@Composable
fun CurrentStageEmpireLayout(modifier: Modifier, stage: Dungeon) {
    Box(modifier) {
        ShadowImage(
            modifier = Modifier.fillMaxSize().padding(horizontal = padding19),
            imageResource = kmpDrawableResource(stage.levelImg),
        )
        Row(
            modifier = Modifier.align(Alignment.BottomEnd),
            horizontalArrangement = Arrangement.spacedBy(padding2),
            verticalAlignment = Alignment.CenterVertically
        ) {
            stage.enemyType.forEach { storyUnlockId ->
                val story = repo.gameCore.getStoryPool().first {
                    it.unlockId == storyUnlockId
                }
                IconView(
                    res = kmpDrawableResource(story.pic),
                    itemSize = ItemSize.MediumPlus,
                    frame = null,
                    resZIndex = 99f
                ) {
                    story.desc.toast()
                }
            }
        }
    }
}

@Composable
fun SelectStageLayoutMain(modifier: Modifier) {
    Row(modifier, horizontalArrangement = Arrangement.SpaceEvenly) {
        if (StageManager.hasPrev()) {
            EffectButton(clickGap = QUICK_GAP, onClick = {
                StageManager.selectPrev()
            }) {
                Image(
                    modifier = Modifier
                        .size(imageLargeFrame),
                    painter = painterResource(Res.drawable.common_arrow_left),
                    contentDescription = null
                )
            }
        } else {
            Spacer(modifier = Modifier.size(imageLargeFrame))
        }
        Spacer(modifier = Modifier.weight(1f))
        if (StageManager.hasNext()) {
            EffectButton(clickGap = QUICK_GAP, onClick = {
                StageManager.selectNext()
            }) {
                Image(
                    modifier = Modifier.size(imageLargeFrame),
                    painter = painterResource(Res.drawable.common_arrow_right),
                    contentDescription = null
                )
            }
        } else {
            Spacer(modifier = Modifier.size(imageLargeFrame))
        }
    }
}
