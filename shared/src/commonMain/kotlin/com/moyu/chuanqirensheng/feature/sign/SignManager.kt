package com.moyu.chuanqirensheng.feature.sign

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateMapOf
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.quest.getLoginDays
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.GuardedB
import com.moyu.chuanqirensheng.sub.datastore.KEY_MISSION_GAINED
import com.moyu.chuanqirensheng.sub.datastore.KEY_SIGN_GAINED
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.core.AppWrapper
import com.moyu.core.model.Sign
import com.moyu.core.model.toAward
import shared.generated.resources.Res
import shared.generated.resources.already_got
import shared.generated.resources.not_enough_day
import kotlin.math.min


object SignManager {
    private val gainedMap = mutableStateMapOf<Int, MutableState<Boolean>>()

    fun init() {
        repo.gameCore.getSignPool().forEach {
            gainedMap[it.id] = GuardedB(KEY_SIGN_GAINED + it.id)
        }
    }

    suspend fun gain(mission: Sign) {
        val gained = gainedMap[mission.id]!!
        if (gained.value) {
            AppWrapper.getStringKmp(Res.string.already_got).toast()
            return
        }
        if (getLoginDays() < mission.day) {
            AppWrapper.getStringKmp(Res.string.not_enough_day).toast()
            return
        }
        gained.value = true
        setBooleanValueByKey(KEY_MISSION_GAINED + mission.id, true)
        val award = mission.toAward()
        Dialogs.awardDialog.value = award
        AwardManager.gainAward(award)
    }

    fun isSignGained(mission: Sign): Boolean {
        return gainedMap[mission.id]?.value ?: false
    }

    fun getShowSigns(): List<Sign> {
        val gainedSize = gainedMap.values.count { it.value }
        val objects = repo.gameCore.getSignPool()

        val startIndex = if (gainedSize == objects.size) {
            objects.size - 30
        } else {
            (gainedSize / 30) * 30
        }
        val endIndex = min(startIndex + 30, objects.size)

        return objects.subList(startIndex, endIndex)
    }
}