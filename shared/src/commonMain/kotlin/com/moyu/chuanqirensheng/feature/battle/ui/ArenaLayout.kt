package com.moyu.chuanqirensheng.feature.battle.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.unit.dp
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.battle.BattleManager
import com.moyu.chuanqirensheng.feature.event.EventManager
import com.moyu.chuanqirensheng.feature.event.isKeyBattle
import com.moyu.chuanqirensheng.feature.setting.SettingColumn
import com.moyu.chuanqirensheng.feature.setting.settingBattleItems
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.chuanqirensheng.ui.theme.animateLarge
import com.moyu.chuanqirensheng.ui.theme.buffSize
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding170
import com.moyu.chuanqirensheng.ui.theme.padding18
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding53
import com.moyu.chuanqirensheng.ui.theme.padding54
import com.moyu.chuanqirensheng.ui.theme.padding58
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.qualityColorYellow
import com.moyu.chuanqirensheng.ui.theme.singleRoleHeight
import com.moyu.chuanqirensheng.ui.theme.singleRoleWidth
import com.moyu.chuanqirensheng.util.dpToPixel
import com.moyu.chuanqirensheng.util.pixelToDp
import com.moyu.chuanqirensheng.widget.common.HpBar
import com.moyu.chuanqirensheng.widget.effect.ForeverGif
import com.moyu.core.AppWrapper
import com.moyu.core.logic.role.battleAllyList
import com.moyu.core.logic.role.battleEnemyList
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.role.Role
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.battle_board
import shared.generated.resources.battle_power_icon

@OptIn(ExperimentalCoroutinesApi::class)
@Composable
fun BattleFieldLayout(roles: Map<Int, Role?>) {
    Box(Modifier.height(dialogWidth).fillMaxWidth()) {
        // 用于控制是否可见的状态变量
        var isPlayersVisible by remember { mutableStateOf(false) }
        var isBoardVisible by remember { mutableStateOf(false) }

        // 当界面首次进入时，先延迟一点，然后把两个 column 都设为可见，触发进入动画
        LaunchedEffect(Unit) {
            // 也可以写个延迟，看看动画效果更明显
            delay(50)
            isBoardVisible = true
            delay(300L)
            isPlayersVisible = true
        }
        AnimatedVisibility(
            modifier = Modifier.fillMaxSize(),
            visible = isBoardVisible, // 这里可改成你的状态
            enter = slideInVertically(initialOffsetY = { -it }),
            exit = slideOutVertically(targetOffsetY = { -it })
        ) {
            Column {
                Spacer(Modifier.weight(1f))
                Image(
                    modifier = Modifier.fillMaxWidth().height(padding170).scale(1.6f),
                    alignment = Alignment.BottomCenter,
                    painter = painterResource(
                        Res.drawable.battle_board,
                    ),
                    contentDescription = null,
                    contentScale = ContentScale.FillHeight
                )
                Spacer(Modifier.size(padding20))
            }
        }
        Row(
            modifier = Modifier.fillMaxSize().padding(vertical = padding19),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // PlayersColumn
            AnimatedVisibility(
                modifier = Modifier.weight(1f).fillMaxHeight().padding(top = padding66),
                visible = isPlayersVisible, // 这里可改成你的状态
                enter = slideInHorizontally(initialOffsetX = { -it }),
                exit = slideOutHorizontally(targetOffsetX = { -it })
            ) {
                Column {
                    if (repo.gameMode.value.isAnyPvpMode() || EventManager.selectedEvent.value?.isKeyBattle() == true) {
                        Row(
                            modifier = Modifier.align(Alignment.Start).padding(start = padding48)
                                .graphicsLayer {
                                    translationY = -padding12.toPx()
                                }, verticalAlignment = Alignment.CenterVertically
                        ) {
                            val allies = repo.battleRoles.filter { it.key in battleAllyList }.values
                            val hp by animateIntAsState(
                                targetValue = allies.filter { it?.isDeath() == false }
                                    .sumOf { it?.getAlly()?.getAllPower() ?: 0 },
                                animationSpec = TweenSpec(
                                    durationMillis = GameSpeedManager.animDuration().toInt()
                                ),
                                label = ""
                            )
                            Image(
                                modifier = Modifier.size(imageSmallPlus),
                                painter = painterResource(Res.drawable.battle_power_icon),
                                contentDescription = null
                            )
                            Spacer(modifier = Modifier.size(padding3))
                            HpBar(
                                modifier = Modifier.size(padding100, padding18),
                                maxHp = allies.sumOf { it?.getAlly()?.getAllPower() ?: 0 },
                                showNum = true,
                                hpColor = qualityColorYellow,
                                borderSize = padding3,
                                cornerRadius = CornerRadius(
                                    padding6.value.dpToPixel(),
                                    padding6.value.dpToPixel()
                                ),
                                currentHp = hp
                            )
                        }
                    } else {
                        Spacer(Modifier.size(padding18))
                    }
                    PlayersColumn(
                        modifier = Modifier.fillMaxSize().padding(start = padding16), roles
                    )
                }
            }

            // EnemiesColumn
            AnimatedVisibility(
                modifier = Modifier.weight(1f).fillMaxHeight()
                    .padding(top = padding66, end = padding53),
                visible = isPlayersVisible,
                enter = slideInHorizontally(initialOffsetX = { it }),
                exit = slideOutHorizontally(targetOffsetX = { it })
            ) {

                Column {
                    if (repo.gameMode.value.isAnyPvpMode() || EventManager.selectedEvent.value?.isKeyBattle() == true) {
                        Row(
                            modifier = Modifier.align(Alignment.End).padding(end = padding3)
                                .graphicsLayer {
                                    translationY = -padding12.toPx()
                                }, verticalAlignment = Alignment.CenterVertically
                        ) {
                            val allies =
                                repo.battleRoles.filter { it.key in battleEnemyList }.values
                            val hp by animateIntAsState(
                                targetValue = allies.filter { it?.isDeath() == false }
                                    .sumOf { it?.getAlly()?.getAllPower() ?: 0 },
                                animationSpec = TweenSpec(
                                    durationMillis = GameSpeedManager.animDuration().toInt()
                                ),
                                label = ""
                            )
                            HpBar(
                                modifier = Modifier.size(padding100, padding18),
                                maxHp = allies.sumOf { it?.getAlly()?.getAllPower() ?: 0 },
                                showNum = true,
                                hpColor = qualityColorYellow,
                                borderSize = padding3,
                                cornerRadius = CornerRadius(
                                    padding6.value.dpToPixel(),
                                    padding6.value.dpToPixel()
                                ),
                                currentHp = hp
                            )
                            Spacer(modifier = Modifier.size(padding3))
                            Image(
                                modifier = Modifier.size(imageSmallPlus),
                                painter = painterResource(Res.drawable.battle_power_icon),
                                contentDescription = null
                            )
                        }
                    } else {
                        Spacer(Modifier.size(padding18))
                    }
                    EnemiesColumn(
                        Modifier.fillMaxSize().padding(end = padding16), roles
                    )
                }
            }
        }
        SettingColumn(
            Modifier.align(Alignment.TopStart).padding(top = padding34, end = padding4),
            settingBattleItems()
        )
        if (DebugManager.singleStep) {
            Text(modifier = Modifier.align(Alignment.BottomCenter).clickable {
                AppWrapper.globalScope.launch(gameDispatcher) {
                    repo.battle.value.nextStep()
                }
            }, text = "下一步", style = MaterialTheme.typography.h3)
        }
    }
}

@Composable
fun EnemiesColumn(modifier: Modifier, roles: Map<Int, Role?>) {
    Box(modifier, contentAlignment = Alignment.TopEnd) {
        repeat(8) { index ->
            val row = index / 2
            val column = index % 2
            roles[battleEnemyList[index]]?.let {
                val alpha by animateFloatAsState(
                    animationSpec = TweenSpec(
                        durationMillis = GameSpeedManager.animDuration().toInt() * 4
                    ), targetValue = if (it.isDeath()) 0f else 1f, label = ""
                )
                OneRoleInBattle(
                    Modifier.size(singleRoleWidth, singleRoleHeight)
                        .offset(padding22 * (row - 3) + padding54 * column, padding58 * row)
                        .scale(1.5f).alpha(alpha), it
                )
            } ?: Box(Modifier.size(singleRoleWidth, singleRoleHeight))
        }
        repeat(8) { index ->
            roles[battleEnemyList[index]]?.let {
                val row = index / 2
                val column = index % 2
                Box(
                    Modifier.size(singleRoleWidth * 1.5f, singleRoleHeight * 1.5f)
                        .offset(padding22 * (row - 3) + padding54 * column, padding58 * row)
                        .graphicsLayer {
                            translationX = padding12.toPx()
                        }) {
                    DamageTextLayout(role = it)
                    Box(modifier = Modifier.padding(bottom = padding6)) {
                        RoleStatusBeingAttackGif(it)
                    }
                }
            }
        }
    }
}


@Composable
fun PlayersColumn(modifier: Modifier, roles: Map<Int, Role?>) {
    Box(modifier, contentAlignment = Alignment.TopStart) {
        repeat(8) { index ->
            roles[battleAllyList[index]]?.let {
                val alpha by animateFloatAsState(
                    animationSpec = TweenSpec(
                        durationMillis = GameSpeedManager.animDuration().toInt() * 4
                    ), targetValue = if (it.isDeath()) 0f else 1f, label = ""
                )
                val row = index / 2
                val column = index % 2
                OneRoleInBattle(
                    Modifier.size(singleRoleWidth, singleRoleHeight)
                        .offset(padding22 * (3 - row) + padding54 * column, padding58 * row)
                        .scale(1.5f).alpha(alpha), it
                )
            } ?: Box(Modifier.size(singleRoleWidth, singleRoleHeight))
        }
        repeat(8) { index ->
            roles[battleAllyList[index]]?.let {
                val row = index / 2
                val column = index % 2
                Box(
                    Modifier.size(singleRoleWidth * 1.5f, singleRoleHeight * 1.5f)
                        .offset(padding22 * (3 - row) + padding54 * column, padding58 * row)
                        .graphicsLayer {
                            translationX = -padding12.toPx()
                        }) {
                    DamageTextLayout(role = it)
                    Box(modifier = Modifier.padding(bottom = padding6)) {
                        RoleStatusBeingAttackGif(it)
                    }
                }
            }
        }
    }
}

@Composable
fun OneRoleInBattle(modifier: Modifier = Modifier, role: Role) {
    Box(
        modifier.onGloballyPositioned {
            BattleManager.battleRolePositions[role.playerId()] = it.positionInRoot().let {
                try {
                    Pair(
                        it.x.toInt().pixelToDp(), it.y.toInt().pixelToDp()
                    )
                } catch (e: Exception) {
                    Pair(padding1, padding1)
                }
            }
        }, contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Row(
                Modifier.height(buffSize).horizontalScroll(rememberScrollState()),
            ) {
                if (repo.inBattle.value) {
                    role.getShowBothBuff().forEach { buff ->
                        OneBuffGrid(buff) {
                            Dialogs.buffDetailDialog.value = Pair(it, role)
                        }
                    }
                }
            }
            Box(
                Modifier.size(singleRoleWidth, singleRoleHeight),
                contentAlignment = Alignment.Center
            ) {
                SingleRoleInBattleLayout(role = role)
            }
        }
    }
}

@Composable
fun BuffEffects(role: Role) {
    val buffList = role.getBuffList()
    buffList.firstOrNull { it.targetEffectNum != 0 }?.let {
        ForeverGif(
            Modifier.fillMaxSize(),
            gifCount = it.targetEffectNum,
            gifDrawable = it.targetEffect,
            needGap = true
        )
    }
}

@Composable
fun DamageTextLayout(role: Role) {
    val damageOffset by animateDpAsState(
        animationSpec = TweenSpec(
            durationMillis = GameSpeedManager.animDuration().toInt()
        ), targetValue = when {
            role.hasState(ActionStateType.BeingAttack) -> -animateLarge
            role.hasState(ActionStateType.BeingHeal) -> -animateLarge
            role.hasState(ActionStateType.DoSkill) -> -animateLarge
            else -> 0.dp
        }, label = ""
    )
    Column(
        modifier = Modifier.fillMaxSize().graphicsLayer {
            if (damageOffset.toPx().isNaN().not()) {
                translationY = damageOffset.toPx()
            }
        }, horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 确保在屏幕中间靠右地方播放
        role.getStateList().forEach {
            RoleDamageOrSkillText(Modifier, it)
        }
    }
}