package com.moyu.chuanqirensheng.feature.illustration.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.ally.ui.SingleAllyView
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.illustration.TcgManager
import com.moyu.chuanqirensheng.feature.skill.ui.SingleEquipView
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.B85
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding140
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.Tcg
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.already_collected
import shared.generated.resources.award_got_toast
import shared.generated.resources.common_frame_long
import shared.generated.resources.gain_award
import shared.generated.resources.quest_not_done_toast


@Composable
fun SingleTcg(quest: Tcg) {
    Column(
        Modifier.padding(horizontal = padding6), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier.fillMaxWidth().height(padding140)
        ) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(Res.drawable.common_frame_long),
                contentDescription = null
            )
            Row(
                modifier = Modifier.fillMaxSize().padding(horizontal = padding12)
                    .padding(top = if (quest.isAlly()) padding0 else padding12)
                    .horizontalScroll(rememberScrollState()),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Spacer(Modifier.size(padding6))
                val award = quest.toAward()
                AwardList(
                    award = award, param = defaultParam.copy(
                        numInFrame = true, itemSize = ItemSize.MediumPlus, propertyMuteTeam = true
                    ), paddingVerticalInDp = padding0, paddingHorizontalInDp = padding0
                )
                Spacer(Modifier.size(padding12))
                Spacer(Modifier.height(padding30).width(padding2).background(W50))
                Spacer(Modifier.size(padding8))
                quest.content.forEach { mainId ->
                    if (quest.isAlly()) {
                        SingleAllyView(
                            itemSize = ItemSize.Large,
                            colorFilter = if (repo.allyManager.data.any {
                                    it.mainId == mainId && it.star >= quest.star
                                }) null else ColorFilter.tint(
                                B50, BlendMode.SrcAtop
                            ),
                            showRed = false,
                            ally = repo.gameCore.getAlly(mainId, level = 1, star = quest.star)
                                .copy(peek = true)
                        )
                    } else {
                        SingleEquipView(
                            itemSize = ItemSize.Large,
                            equipment = repo.gameCore.getEquipPool(mainId)
                                .first { it.star == quest.star }.copy(peek = true),
                            showRed = false,
                            colorFilter = if (repo.equipManager.data.any {
                                    it.mainId == mainId && it.star >= quest.star
                                }) null else ColorFilter.tint(
                                B50, BlendMode.SrcAtop
                            )
                        )
                    }
                }
            }
        }
        val isDone = TcgManager.isDone(quest)
        val isGain = TcgManager.isGain(quest.type, quest.star)
        Row(verticalAlignment = Alignment.CenterVertically) {
            Spacer(Modifier.size(padding8))
            StrokedText(
                modifier = Modifier.clip(RoundedCornerShape(50f)).background(B85)
                    .padding(horizontal = padding12, vertical = padding6),
                text = quest.name + "(${
                    TcgManager.size(quest)
                }/${quest.content.size})", style = MaterialTheme.typography.h2
            )
            Spacer(Modifier.weight(1f))
            GameButton(
                text = if (isGain) stringResource(Res.string.already_collected) else stringResource(
                    Res.string.gain_award
                ),
                enabled = isDone && !isGain,
                buttonStyle = ButtonStyle.Green,
                buttonSize = ButtonSize.Medium,
                onClick = {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        if (!isDone) {
                            AppWrapper.getStringKmp(Res.string.quest_not_done_toast).toast()
                        } else if (isGain) {
                            AppWrapper.getStringKmp(Res.string.award_got_toast).toast()
                        } else {
                            TcgManager.gain(quest)
                        }
                    }
                })
            Spacer(Modifier.size(padding8))
        }
        Spacer(Modifier.size(padding6))
    }
}
