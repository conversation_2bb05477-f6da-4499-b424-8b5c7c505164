package com.moyu.chuanqirensheng.feature.equip

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.toMutableStateList
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.powerToast
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.basic.BasicItemHolder
import com.moyu.chuanqirensheng.logic.basic.ItemHolder
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_EQUIPS
import com.moyu.chuanqirensheng.sub.datastore.KEY_EQUIP_FILTER
import com.moyu.chuanqirensheng.sub.datastore.KEY_EQUIP_ORDER
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.widget.filter.ItemFilter
import com.moyu.chuanqirensheng.widget.filter.ItemOrder
import com.moyu.chuanqirensheng.widget.filter.equipFilterList
import com.moyu.chuanqirensheng.widget.filter.equipOrderList
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.Equipment
import com.moyu.core.model.MAX_STAR
import com.moyu.core.music.SoundEffect
import shared.generated.resources.Res
import shared.generated.resources.already_max_star_tips
import shared.generated.resources.card_not_enough
import shared.generated.resources.resource1_not_enough_tips
import kotlin.math.max

class EquipManager(
    private val holder: ItemHolder<Equipment> = BasicItemHolder(saveKey = KEY_EQUIPS,
        elementSerializer = Equipment.serializer(),
        data = mutableStateListOf(),
        sameGroup = { item1, item2 -> item1.mainId == item2.mainId },
        increase = { current, add ->
            current.copy(
                num = current.num + add.num,
                new = add.new,
                id = if (add.star > current.star) add.id else current.id,
                star = max(current.star, add.star)
            )
        })
) : ItemHolder<Equipment> by holder {

    val orderIndex = mutableStateOf(0)
    val filterIndexes = mutableStateListOf<Int>()

    override suspend fun init() {
        holder.init()
        orderIndex.value = getIntFlowByKey(KEY_EQUIP_ORDER)
//        filterIndexes.addAll(getListObject<Int>(KEY_EQUIP_FILTER))
    }

    override fun gain(value: Equipment) {
        // todo 注意，如果是首次获得，数量要-1，本体扣除1
        data.indexOfFirst { sameGroup(value, it) }.takeIf { it >= 0 }?.let {
            data[it] = increase(data[it], value)
        } ?: run {
            data.add(value.copy(num = value.num - 1))
            value.power.powerToast()
        }
        save()
    }

    // data修改都需要确保主线程
    // 升星逻辑改了，有基底概念，所以要-1
    fun starUp(equipment: Equipment): Equipment? {
        if (AwardManager.resources[0] < equipment.starUpResourceNum) {
            AppWrapper.getStringKmp(Res.string.resource1_not_enough_tips).toast()
            GiftManager.onResource1NotEnough()
            Dialogs.moneyTransferDialog.value = true
        } else if (equipment.starUpNum == 0) {
            AppWrapper.getStringKmp(Res.string.already_max_star_tips).toast()
        } else if (equipment.starUpNum > equipment.num) {
            AppWrapper.getStringKmp(Res.string.card_not_enough).toast()
        } else if (equipment.star >= MAX_STAR) {
            AppWrapper.getStringKmp(Res.string.already_max_star_tips).toast()
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.StarUpItem)
            AwardManager.gainResources(EMPTY_RESOURCES.toMutableList().apply {
                this[0] = -equipment.starUpResourceNum
            })
            data.indexOfFirst { it.mainId == equipment.mainId }.takeIf { it != -1 }?.let {
//                onTaskStarUp(data[it], data[it].star + 1)
                equipment.getStarUpPowerDiff().powerToast()
                data[it] = data[it].copy(num = data[it].num - equipment.starUpNum).starUp()
                save()
                return data[it]
            }
        }
        return null
    }

    fun equip(equip: Equipment): Equipment? {
        if (equip.equipped) {
            // 卸下
            data.indexOfFirst { it.id == equip.id }.takeIf { it != -1 }?.let {
                data[it] = data[it].copy(equipped = false)
                save()
            }
            return equip.copy(equipped = false)
        } else {
            if (data.filter { it.equipped }.any { it.type == equip.type }) {
                // 替换
                data.indexOfFirst { it.equipped && it.type == equip.type }.let {
                    data[it] = data[it].copy(equipped = false)
                    save()
                }
                data.indexOfFirst { it.id == equip.id }.takeIf { it != -1 }?.let {
                    data[it] = data[it].copy(equipped = true)
                    save()
                }
                return equip.copy(equipped = true)
            } else {
                // 装备
                val lock = UnlockManager.getEquipUnlockByIndex(equip.type)
                if (!UnlockManager.getUnlockedFlow(lock)) {
                    lock.desc.toast()
                    return null
                } else {
                    data.indexOfFirst { it.id == equip.id }.takeIf { it != -1 }?.let {
                        data[it] = data[it].copy(equipped = true)
                        save()
                    }
                }
                return equip.copy(equipped = true)
            }
        }
    }

    fun getPower(): Int {
        return data.sumOf { it.power }
    }

    fun updateFilter(filter: List<ItemFilter<Equipment>>) {
        // 养成页的筛选，需要记录到Manager
        repo.equipManager.filterIndexes.clear()
        equipFilterList.forEachIndexed { index, itemFilter ->
            if (filter.contains(itemFilter)) {
                repo.equipManager.filterIndexes.add(index)
            }
            setListObject(KEY_EQUIP_FILTER, repo.equipManager.filterIndexes)
        }
    }

    fun updateOrder(order: MutableState<ItemOrder<Equipment, Int>>) {
        // 养成页的排序，需要记录到Manager
        repo.equipManager.orderIndex.value = equipOrderList.indexOf(order.value)
        setIntValueByKey(KEY_EQUIP_ORDER, repo.equipManager.orderIndex.value)
    }

    fun canLevelUp(equip: Equipment): Boolean {
        val maxStar = equip.star >= MAX_STAR
        return !equip.peek && equip.starUpNum != 0 && equip.num >= equip.starUpNum && !maxStar && AwardManager.resources[0] >= equip.starUpResourceNum
    }

    fun canAnyEquippedLevelUp(): Boolean {
        return data.filter { it.equipped }.any { canLevelUp(it) }
    }

    fun oneShotSelect() {
        oneShotDeselect()
        data.groupBy { it.type }.forEach {
            it.value.maxByOrNull { it.power }?.let {
                equip(it)
            }
        }
    }

    fun oneShotDeselect() {
        data.filter { it.equipped }.forEach {
            equip(it)
        }
    }

    fun getVipRequire(): Int {
        return data.maxOfOrNull { it.vipValue }?: 0
    }

    fun selectPrev() {
        val filter = repo.equipManager.filterIndexes.map { equipFilterList[it] }
            .toMutableStateList()
        val order = equipOrderList[repo.equipManager.orderIndex.value]
        val orderFilteredList = repo.equipManager.data.filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }.sortedByDescending { order.order?.invoke(it) }.sortedByDescending { if (it.equipped) 9999 else 0 }
        val index = orderFilteredList.indexOfFirst { it.mainId == Dialogs.equipDetailDialog.value?.mainId }
        if (index > 0) {
            Dialogs.equipDetailDialog.value = orderFilteredList[index - 1]
        } else {
            Dialogs.equipDetailDialog.value = orderFilteredList.lastOrNull()
        }
    }

    fun selectNext() {
        val filter = repo.equipManager.filterIndexes.map { equipFilterList[it] }
            .toMutableStateList()
        val order = equipOrderList[repo.equipManager.orderIndex.value]
        val orderFilteredList = repo.equipManager.data.filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }.sortedByDescending { order.order?.invoke(it) }.sortedByDescending { if (it.equipped) 9999 else 0 }
        val index = orderFilteredList.indexOfFirst { it.mainId == Dialogs.equipDetailDialog.value?.mainId }
        if (index < orderFilteredList.size - 1) {
            Dialogs.equipDetailDialog.value = orderFilteredList[index + 1]
        } else {
            Dialogs.equipDetailDialog.value = orderFilteredList.firstOrNull()
        }
    }

}
