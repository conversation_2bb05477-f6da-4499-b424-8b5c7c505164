package com.moyu.chuanqirensheng.feature.resource

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.SizeTransform
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.em
import androidx.compose.ui.zIndex
import com.eygraber.uri.Uri
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass1Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass3Manager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.router.gotoSellWithTabIndex
import com.moyu.chuanqirensheng.feature.sell.SELL_KEY_INDEX
import com.moyu.chuanqirensheng.feature.sell.SELL_SECRET_INDEX
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.openGamePage
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.text.indexToResourceIcon
import com.moyu.chuanqirensheng.text.indexToResourceName
import com.moyu.chuanqirensheng.text.indexToResourceTips
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.cardNumHeight
import com.moyu.chuanqirensheng.ui.theme.cardNumWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlusFrame
import com.moyu.chuanqirensheng.ui.theme.moneyExtraWidth
import com.moyu.chuanqirensheng.ui.theme.moneyHeight
import com.moyu.chuanqirensheng.ui.theme.moneyWidth
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding20
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.addAnimationVertical
import com.moyu.chuanqirensheng.util.calculateTimeToNextInc
import com.moyu.chuanqirensheng.util.formatMillisToHMS
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.CommonBar
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.aiFaDian
import com.moyu.core.util.shrinkNumber
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.account_exp_icon
import shared.generated.resources.account_exp_tips
import shared.generated.resources.account_exp_title
import shared.generated.resources.ally_coupon
import shared.generated.resources.ally_coupon_tips
import shared.generated.resources.ally_exp_icon
import shared.generated.resources.ally_exp_tips
import shared.generated.resources.ally_exp_title
import shared.generated.resources.bar_blue
import shared.generated.resources.bar_empty
import shared.generated.resources.battle_menu_info
import shared.generated.resources.battle_pass2_content
import shared.generated.resources.battle_pass3_content
import shared.generated.resources.battle_pass_content
import shared.generated.resources.cant_get_yet
import shared.generated.resources.common_key
import shared.generated.resources.common_medal
import shared.generated.resources.common_plus
import shared.generated.resources.coupon_ally_icon
import shared.generated.resources.diamond_tips
import shared.generated.resources.diamond_title
import shared.generated.resources.electric_content
import shared.generated.resources.explain
import shared.generated.resources.hero_coupon
import shared.generated.resources.hero_coupon_icon
import shared.generated.resources.hero_coupon_tips
import shared.generated.resources.holiday_money
import shared.generated.resources.holiday_money_tips
import shared.generated.resources.key_tips
import shared.generated.resources.key_title
import shared.generated.resources.lottery_money
import shared.generated.resources.lottery_money_tips
import shared.generated.resources.power
import shared.generated.resources.power_icon
import shared.generated.resources.power_recover_left
import shared.generated.resources.power_tips
import shared.generated.resources.pvp2_score
import shared.generated.resources.pvp2_score_icon
import shared.generated.resources.pvp2_score_tips
import shared.generated.resources.pvp_diamond
import shared.generated.resources.pvp_diamond_icon
import shared.generated.resources.pvp_diamond_tips
import shared.generated.resources.pvp_score
import shared.generated.resources.pvp_score_icon
import shared.generated.resources.pvp_score_tips
import shared.generated.resources.real_money_icon
import shared.generated.resources.real_money_tips
import shared.generated.resources.real_money_title
import shared.generated.resources.reputation_money
import shared.generated.resources.reputation_money_tips
import shared.generated.resources.resource_frame
import shared.generated.resources.talent1_frame_light
import shared.generated.resources.talent_point
import shared.generated.resources.talent_point_icon
import shared.generated.resources.talent_point_tips

/**
 * ================================
 * 1. 通用的「显示当前资源数值」组件
 * ================================
 */
@OptIn(ExperimentalAnimationApi::class)
@Composable
fun ResourceCountBox(
    modifier: Modifier = Modifier,
    // 当前资源的数值
    currentValue: Int,
    // 资源的图标 Painter
    iconPainter: Painter,
    // 资源名称(用于无障碍 contentDescription)，可选
    iconContentDescription: String? = null,
    showFrame: Boolean = true,
    // 是否显示「+」按钮
    showPlus: Boolean = false,
    tips: String = "",
    // 「+」按钮点击回调
    onPlusClicked: (() -> Unit)? = null,
    // 整个 Box 的宽高
    boxWidth: Dp = moneyExtraWidth,
    boxHeight: Dp = moneyHeight,
    // 图标大小
    iconSize: Dp = imageSmall,
    maxValue: Int? = null,
    // 显示数值的文字样式
    textStyle: TextStyle = MaterialTheme.typography.h5,
    // 如果需要背景边框时，使用何种 Frame
    framePainter: Painter? = if (showFrame) painterResource(Res.drawable.resource_frame) else null
) {
    EffectButton(
        modifier = modifier.size(boxWidth, boxHeight),
        onClick = {
            if (tips.isNotEmpty()) {
                tips.toast()
            }
        }
    ) {
        // 如果需要背景帧
        if (framePainter != null) {
            Image(
                modifier = Modifier.fillMaxSize(),
                painter = framePainter,
                contentScale = ContentScale.FillBounds,
                contentDescription = iconContentDescription
            )
        }
        // 左侧图标
        Image(
            modifier = Modifier.align(Alignment.CenterStart)
                .size(iconSize)
                // 让图标稍微向左移动
                .graphicsLayer { translationX = -padding2.toPx() },
            painter = iconPainter,
            contentDescription = iconContentDescription
        )
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 可动画数字
            AnimatedContent(
                modifier = Modifier.weight(1f),
                targetState = currentValue,
                transitionSpec = {
                    addAnimationVertical(duration = 600).using(SizeTransform(clip = true))
                },
                label = "resourceCountAnimation"
            ) { targetCount ->
                Text(
                    text = targetCount.toString().shrinkNumber(!hasGoogleService()) + if (maxValue == null) "" else "/${maxValue}",
                    style = textStyle.copy(
                        fontWeight = FontWeight.Thin,
                        letterSpacing = (-0.05).em
                    ),
                    textAlign = TextAlign.End,
                    maxLines = 1
                )
            }
            Spacer(modifier = Modifier.size(padding20))
        }

        // 可选的「+」按钮
        if (showPlus && onPlusClicked != null) {
            EffectButton(
                modifier = Modifier.align(Alignment.CenterEnd),
                onClick = onPlusClicked
            ) {
                Image(
                    modifier = Modifier.size(imageSmall),
                    painter = painterResource(Res.drawable.common_plus),
                    contentDescription = iconContentDescription
                )
            }
        }
    }
}

/**
 * ==========================
 * 2. 通用的「资源消耗」组件
 * ==========================
 */
@Composable
fun ResourceCostRow(
    cost: Int,
    currentValue: Int,
    iconPainter: Painter,
    contentDescription: String,
    modifier: Modifier = Modifier,
    enoughColor: Color = Color.White,
    notEnoughColor: Color = DARK_RED,
    showNegative: Boolean = false,
    textStyle: TextStyle = MaterialTheme.typography.h2,
    iconSize: Dp = imageSmall,
    spacing: Dp = padding6
) {
    val isEnough = currentValue >= cost

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            modifier = Modifier.size(iconSize),
            painter = iconPainter,
            contentDescription = contentDescription
        )
        Spacer(modifier = Modifier.size(spacing))
        StrokedText(
            text = if (showNegative) "-$cost" else cost.toString(),
            style = textStyle,
            color = if (isEnough) enoughColor else notEnoughColor
        )
    }
}

/**
 * =====================================
 * 3. 示例：提取 "等级 + 进度条 + Info按钮"
 * =====================================
 */
@Composable
fun LevelAndBarRow(
    level: Int,
    currentValue: Int,
    nextLevelValue: Int,
    levelFramePainter: Painter = painterResource(Res.drawable.talent1_frame_light),
    infoClick: () -> Unit
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        // 左边显示等级
        EffectButton(
            modifier = Modifier
                .size(imageLarge)
                .graphicsLayer { translationX = padding10.toPx() }
                .zIndex(10f), onClick = {
                infoClick()
            }
        ) {
            VipLevel(cheatLevel = level, frame = levelFramePainter)
        }
        // 中间的进度条
        CommonBar(
            modifier = Modifier.size(cardNumWidth, cardNumHeight),
            fullRes = Res.drawable.bar_blue,
            emptyRes = Res.drawable.bar_empty,
            currentValue = currentValue,
            maxValue = nextLevelValue,
            textColor = Color.White,
            style = MaterialTheme.typography.h5
        )
    }
}

/**
 * ===========================================
 * 4. 具体资源的 "当前数值" 和 "消耗数值" 示例
 * ===========================================
 */

/** ---- 例子1：钻石 (当前值) ---- */
@Composable
fun CurrentDiamondPoint(
    modifier: Modifier = Modifier,
    showPlus: Boolean = false,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.diamond.value,
        iconPainter = painterResource(Res.drawable.common_medal),
        iconContentDescription = stringResource(Res.string.diamond_title),
        tips = stringResource(Res.string.diamond_tips),
        showFrame = showFrame,
        showPlus = showPlus,
        onPlusClicked = {
            gotoSellWithTabIndex(SELL_SECRET_INDEX)
        }
    )
}

/** ---- 天赋点 (当前值) ---- */
@Composable
fun CurrentTalentPoint(
    modifier: Modifier = Modifier,
    showPlus: Boolean = false,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.talentPoint.value,
        iconPainter = painterResource(Res.drawable.talent_point_icon),
        iconContentDescription = stringResource(Res.string.talent_point),
        tips = stringResource(Res.string.talent_point_tips),
        showFrame = showFrame,
        showPlus = showPlus,
        onPlusClicked = {
            gotoSellWithTabIndex(SELL_SECRET_INDEX)
        }
    )
}

/** ---- 例子1：钻石 (消耗值) ---- */
@Composable
fun DiamondPoint(cost: Int, color: Color = Color.White) {
    ResourceCostRow(
        cost = cost,
        currentValue = AwardManager.diamond.value,
        iconPainter = painterResource(Res.drawable.common_medal),
        contentDescription = stringResource(Res.string.diamond_title),
        enoughColor = color
    )
}

/** ---- 例子2：钥匙 (当前值) ---- */
@Composable
fun CurrentKeyPoint(
    modifier: Modifier = Modifier,
    showPlus: Boolean = false,
    showFrame: Boolean = true,
    boxWidth: Dp = moneyExtraWidth,
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.key.value,
        iconPainter = painterResource(Res.drawable.common_key),
        iconContentDescription = stringResource(Res.string.key_title),
        tips = stringResource(Res.string.key_tips),
        showFrame = showFrame,
        showPlus = showPlus,
        boxWidth = boxWidth,
        onPlusClicked = {
            Dialogs.moneyTransferDialog.value = false
            gotoSellWithTabIndex(SELL_KEY_INDEX)
        }
    )
}

/** ---- 例子2：钥匙 (消耗值) ---- */
@Composable
fun KeyPoint(cost: Int, forceEnough: Boolean = false) {
    val money = AwardManager.key.value
    ResourceCostRow(
        cost = cost,
        currentValue = if (forceEnough) cost else money,
        iconPainter = painterResource(Res.drawable.common_key),
        contentDescription = stringResource(Res.string.key_title)
    )
}

/** ---- 例子3：PVP代币 (当前值) ---- */
@Composable
fun CurrentPvpDiamond(
    modifier: Modifier = Modifier,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.pvpDiamond.value,
        boxWidth = moneyWidth,
        iconPainter = painterResource(Res.drawable.pvp_diamond_icon),
        iconContentDescription = stringResource(Res.string.pvp_diamond),
        tips = stringResource(Res.string.pvp_diamond_tips),
        showFrame = showFrame,
        showPlus = false,
        onPlusClicked = {
            // 你的购买逻辑
        }
    )
}

/** ---- 例子3：PVP代币 (消耗值) ---- */
@Composable
fun PvpPoint(cost: Int, color: Color = Color.White) {
    ResourceCostRow(
        cost = cost,
        currentValue = AwardManager.pvpDiamond.value,
        iconPainter = painterResource(Res.drawable.pvp_diamond_icon),
        contentDescription = stringResource(Res.string.pvp_diamond),
        enoughColor = color
    )
}

/** ---- 例子4：PVP积分 (当前值) ---- */
@OptIn(ExperimentalAnimationApi::class)
@Composable
fun CurrentPvpScore(
    modifier: Modifier = Modifier,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        boxWidth = moneyWidth,
        currentValue = PvpManager.pvpScore.value,
        iconPainter = painterResource(Res.drawable.pvp_score_icon),
        iconContentDescription = stringResource(Res.string.pvp_score),
        tips = stringResource(Res.string.pvp_score_tips),
        showFrame = showFrame,
        showPlus = false // 可能没有「+」按钮
    )
}

/** ---- 例子4：PVP2积分 (当前值) ---- */
@OptIn(ExperimentalAnimationApi::class)
@Composable
fun CurrentPvp2Score(
    modifier: Modifier = Modifier,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = Pvp2Manager.pvpScore.value,
        iconPainter = painterResource(Res.drawable.pvp2_score_icon),
        iconContentDescription = stringResource(Res.string.pvp2_score),
        tips = stringResource(Res.string.pvp2_score_tips),
        showFrame = showFrame,
        showPlus = false
    )
}

/** ---- 例子5：盟约券 (当前值) ---- */
@Composable
fun CurrentAllyCouponPoint(
    modifier: Modifier = Modifier,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.couponAlly.value,
        iconPainter = painterResource(Res.drawable.coupon_ally_icon),
        iconContentDescription = stringResource(Res.string.ally_coupon),
        tips = stringResource(Res.string.ally_coupon_tips),
        showFrame = showFrame,
        showPlus = false
    )
}

/** ---- 例子5：盟约券 (消耗值) ---- */
@Composable
fun AllyCouponPoint(cost: Int = 10, color: Color = Color.White) {
    val money = AwardManager.couponAlly.value
    val enough = money >= cost

    // 如果不够，则用钥匙补差
    val lack = (cost - money) * repo.gameCore.getAllyCouponRate()
    val keyEnough = AwardManager.key.value >= lack
    val realCost = if (enough) cost else if (keyEnough) money else cost

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.graphicsLayer {
            translationY = -padding6.toPx()
        }
    ) {
        // 左边 icon
        Image(
            modifier = Modifier.size(imageSmallPlusFrame),
            painter = painterResource(Res.drawable.coupon_ally_icon),
            contentDescription = stringResource(Res.string.ally_coupon)
        )
        Spacer(modifier = Modifier.size(padding2))
        StrokedText(
            text = realCost.toString(),
            style = MaterialTheme.typography.h2,
            color = if (enough || keyEnough) color else DARK_RED
        )
        // 如果钥匙可以补差，就显示钥匙部分
        if (!enough && keyEnough) {
            Spacer(modifier = Modifier.size(padding8))
            Image(
                modifier = Modifier.size(imageSmallPlusFrame),
                painter = painterResource(Res.drawable.common_key),
                contentDescription = stringResource(Res.string.key_title)
            )
            Spacer(modifier = Modifier.size(padding2))
            StrokedText(
                text = lack.toString(),
                style = MaterialTheme.typography.h2,
                color = color,
                maxLines = 1,
                softWrap = false,
                overflow = TextOverflow.Visible
            )
        }
    }
}

/** ---- 例子6：英雄券 (当前值) ---- */
@Composable
fun CurrentHeroCouponPoint(
    modifier: Modifier = Modifier,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.couponHero.value,
        iconPainter = painterResource(Res.drawable.hero_coupon_icon),
        iconContentDescription = stringResource(Res.string.hero_coupon),
        tips = stringResource(Res.string.hero_coupon_tips),
        showFrame = showFrame,
        showPlus = false
    )
}

/** ---- 例子6：英雄券 (消耗值) ---- */
@Composable
fun HeroCouponPoint(cost: Int = 10, color: Color = Color.White) {
    val money = AwardManager.couponHero.value
    val enough = money >= cost

    // 如果不够，则用钥匙补差
    val lack = (cost - money) * repo.gameCore.getHeroCouponRate()
    val keyEnough = AwardManager.key.value >= lack
    val realCost = if (enough) cost else if (keyEnough) money else cost

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.graphicsLayer {
            translationY = -padding6.toPx()
        }
    ) {
        // 左边 icon
        Image(
            modifier = Modifier.size(imageSmallPlusFrame),
            painter = painterResource(Res.drawable.hero_coupon_icon),
            contentDescription = stringResource(Res.string.hero_coupon)
        )
        Spacer(modifier = Modifier.size(padding2))
        StrokedText(
            text = realCost.toString(),
            style = MaterialTheme.typography.h2,
            color = if (enough || keyEnough) color else DARK_RED
        )
        // 钥匙补差
        if (!enough && keyEnough) {
            Spacer(modifier = Modifier.size(padding8))
            Image(
                modifier = Modifier.size(imageSmallPlusFrame),
                painter = painterResource(Res.drawable.common_key),
                contentDescription = stringResource(Res.string.key_title)
            )
            Spacer(modifier = Modifier.size(padding2))
            StrokedText(
                text = lack.toString(),
                style = MaterialTheme.typography.h2,
                color = color,
                maxLines = 1,
                softWrap = false,
                overflow = TextOverflow.Visible
            )
        }
    }
}

/** ---- 例子7：真金(现金货币) (当前值) ---- */
@Composable
fun CurrentRealMoneyPoint(
    modifier: Modifier = Modifier,
    showPlus: Boolean = false,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.realMoney.value,
        iconPainter = painterResource(Res.drawable.real_money_icon),
        iconContentDescription = stringResource(Res.string.real_money_title),
        showFrame = showFrame,
        showPlus = showPlus,
        tips = stringResource(Res.string.real_money_tips),
        onPlusClicked = {
            // AI 法店跳转
            if (LoginManager.instance.canShowAifadian()) {
                val uri: Uri = Uri.parse(aiFaDian)
                openGamePage(uri)
            } else {
                AppWrapper.getStringKmp(Res.string.cant_get_yet).toast()
            }
        }
    )
}

/** ---- 例子7：真金(现金货币) (消耗值) ---- */
@Composable
fun RealMoneyPoint(cost: Int, color: Color = Color.White) {
    ResourceCostRow(
        cost = cost,
        currentValue = AwardManager.realMoney.value,
        iconPainter = painterResource(Res.drawable.real_money_icon),
        contentDescription = stringResource(Res.string.real_money_title),
        enoughColor = color,
        notEnoughColor = DARK_RED
    )
}

/** ---- 例子8：声望 (当前值) ---- */
@OptIn(ExperimentalAnimationApi::class)
@Composable
fun CurrentReputationPoint(
    modifier: Modifier = Modifier,
    showFrame: Boolean = true
) {
    // 这里和上面类似，也可以直接用 ResourceCountBox。
    // 如果想自定义背景画法，也可手写。
    val money = AwardManager.reputationMoney.value
    EffectButton(
        modifier = modifier.size(moneyExtraWidth, moneyHeight),
        onClick = {
            AppWrapper.getStringKmp(Res.string.reputation_money_tips).toast()
        }
    ) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(Res.drawable.resource_frame),
                contentDescription = stringResource(Res.string.reputation_money)
            )
        }
        Row(
            modifier = Modifier,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.size(padding2))
            Image(
                modifier = Modifier.size(padding26),
                painter = painterResource(Res.drawable.reputation_money),
                contentDescription = stringResource(Res.string.reputation_money)
            )
            Spacer(modifier = Modifier.size(padding2))
            AnimatedContent(
                modifier = Modifier.weight(1f),
                targetState = money,
                transitionSpec = {
                    addAnimationVertical(duration = 600).using(SizeTransform(clip = true))
                },
                label = "reputation"
            ) { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h5,
                    textAlign = TextAlign.End
                )
            }
            Spacer(modifier = Modifier.size(padding6))
        }
    }
}

/** ---- 例子8：声望 (消耗值) ---- */
@Composable
fun ReputationPoint(cost: Int, color: Color = Color.White) {
    ResourceCostRow(
        cost = cost,
        currentValue = AwardManager.reputationMoney.value,
        iconPainter = painterResource(Res.drawable.reputation_money),
        contentDescription = stringResource(Res.string.reputation_money),
        enoughColor = color,
        notEnoughColor = Color.Red,
        textStyle = if (cost >= 10_000) MaterialTheme.typography.h3 else MaterialTheme.typography.h2
    )
}

/** ---- 例子9：抽奖货币 Lottery (当前值) ---- */
@Composable
fun CurrentLotteryMoney(
    modifier: Modifier = Modifier,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.lotteryMoney.value,
        iconPainter = painterResource(Res.drawable.lottery_money),
        tips = stringResource(Res.string.lottery_money_tips),
        iconContentDescription = "",
        showFrame = showFrame,
        showPlus = false
    )
}

/** ---- 例子9：Lottery (消耗值) ---- */
@Composable
fun LotteryPoint(cost: Int) {
    ResourceCostRow(
        cost = cost,
        currentValue = AwardManager.lotteryMoney.value,
        iconPainter = painterResource(Res.drawable.lottery_money),
        contentDescription = "lottery",
        textStyle = MaterialTheme.typography.h4,
        notEnoughColor = Color.Red
    )
}

/** ---- 例子10：Holiday 货币 (当前值) ---- */
@Composable
fun CurrentHolidayMoney(
    modifier: Modifier = Modifier,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.holidayMoney.value,
        iconPainter = painterResource(Res.drawable.holiday_money),
        tips = stringResource(Res.string.holiday_money_tips),
        iconContentDescription = "",
        showFrame = showFrame,
        showPlus = false
    )
}

/** ---- 例子10：Holiday (消耗值) ---- */
@Composable
fun HolidayPoint(cost: Int) {
    ResourceCostRow(
        cost = cost,
        currentValue = AwardManager.holidayMoney.value,
        iconPainter = painterResource(Res.drawable.holiday_money),
        contentDescription = "holiday",
        textStyle = MaterialTheme.typography.h4,
        notEnoughColor = Color.Red
    )
}

/** ---- 例子10：Holiday (消耗值) ---- */
@Composable
fun PowerPoint(cost: Int) {
    ResourceCostRow(
        cost = cost,
        currentValue = AwardManager.power.value,
        iconPainter = painterResource(Res.drawable.power_icon),
        contentDescription = "power",
        textStyle = MaterialTheme.typography.h2,
        notEnoughColor = Color.Red,
        showNegative = true
    )
}

@Composable
fun AllyExpPoint(cost: Int, color: Color = Color.White) {
    ResourceCostRow(
        cost = cost,
        currentValue = AwardManager.allyExp.value,
        iconPainter = painterResource(Res.drawable.ally_exp_icon),
        contentDescription = stringResource(Res.string.ally_exp_title),
        enoughColor = color
    )
}

/** ---- 例子11：盟友经验 (当前值) ---- */
@Composable
fun CurrentAllyExpPoint(
    modifier: Modifier = Modifier,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.allyExp.value,
        iconPainter = painterResource(Res.drawable.ally_exp_icon),
        iconContentDescription = stringResource(Res.string.ally_exp_title),
        tips = stringResource(Res.string.ally_exp_tips),
        showFrame = showFrame,
        showPlus = false
    )
}

/** ---- 例子12：账号经验 (当前值) ---- */
@Composable
fun CurrentAccountExpPoint(
    modifier: Modifier = Modifier,
    showFrame: Boolean = true
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.accountExp.value,
        iconPainter = painterResource(Res.drawable.account_exp_icon),
        iconContentDescription = stringResource(Res.string.account_exp_title),
        showFrame = showFrame,
        tips = stringResource(Res.string.account_exp_tips),
        showPlus = false
    )
}

/** ---- 例子13：体力 (当前值) ---- */
@Composable
fun CurrentPowerPoint(
    modifier: Modifier = Modifier,
    showPlus: Boolean = false,
    showFrame: Boolean = true,
    boxWidth: Dp = moneyExtraWidth,
) {
    Box(modifier.size(boxWidth, moneyHeight)) {
        ResourceCountBox(
            modifier = Modifier,
            currentValue = AwardManager.power.value,
            maxValue = repo.gameCore.getMaxPower(),
            iconPainter = painterResource(Res.drawable.power_icon),
            iconContentDescription = stringResource(Res.string.power),
            tips = stringResource(Res.string.power_tips),
            showFrame = showFrame,
            showPlus = showPlus,
            boxWidth = boxWidth,
            onPlusClicked = {
                gotoSellWithTabIndex(SELL_SECRET_INDEX)
                val power = AwardManager.power              // Guarded<Int>
                val lastPowerIncTime = AwardManager.lastPowerIncTime // Guarded<Long>
                val maxPower = repo.gameCore.getMaxPower()          // 最大体力
                // 计算当前体力、倒计时
                val currentPower = power.value
                val timeToNext =
                    calculateTimeToNextInc(
                        currentPower,
                        maxPower,
                        lastPowerIncTime.value,
                        getCurrentTime()
                    )
                val countdownText = formatMillisToHMS(timeToNext)
                if (maxPower > currentPower) {
                    (AppWrapper.getStringKmp(Res.string.power_recover_left) + countdownText).toast()
                }
            })
        PowerCountDownBar()
    }
}

@Composable
fun PowerCountDownBar() {
    val power = AwardManager.power              // Guarded<Int>
    val lastPowerIncTime = AwardManager.lastPowerIncTime // Guarded<Long>
    val maxPower = repo.gameCore.getMaxPower()          // 最大体力
    val hourlyRegen = repo.gameCore.getEachRecoverPower()// 每小时恢复多少体力

    // 用于触发倒计时刷新，每秒更新一次
    val currentTime = remember { mutableStateOf(getCurrentTime()) }

    // 每秒执行一次，检查是否要恢复体力 & 更新 currentTime
    LaunchedEffect(Unit) {
        while (true) {
            AwardManager.recoverPowerIfNeeded(power, lastPowerIncTime, maxPower, hourlyRegen)
            currentTime.value = getCurrentTime()
            delay(1000)
        }
    }

    // 计算当前体力、倒计时
//    val currentPower = power.value
//    val timeToNext =
//        calculateTimeToNextInc(currentPower, maxPower, lastPowerIncTime.value, currentTime.value)
//    val countdownText = formatMillisToHMS(timeToNext)

    // 主要内容：图标 + (当前值 / 最大值) + 倒计时 or "体力已满"
//    if (currentPower < maxPower) {
//        StrokedText(
//            modifier = modifier,
//            text = countdownText,
//            style = MaterialTheme.typography.body1,
//            color = Color.Green
//        )
//    }
}


/**
 * ===============================
 * 5. Pass 经验/等级示例
 * ===============================
 */
@Composable
fun CurrentPassPoint(
    passType: Int,
    modifier: Modifier = Modifier
) {
    val (manager, passPool, contentStringRes) = when (passType) {
        1 -> Triple(
            BattlePass1Manager,
            { repo.gameCore.getBattlePass1Pool() },
            Res.string.battle_pass_content
        )

        2 -> Triple(
            BattlePass2Manager,
            { repo.gameCore.getBattlePass2Pool() },
            Res.string.battle_pass2_content
        )

        3 -> Triple(
            BattlePass3Manager,
            { repo.gameCore.getBattlePass3Pool() },
            Res.string.battle_pass3_content
        )

        else -> return
    }

    val money = manager.getCurrentLevelWarPass()
    val level = manager.getCurrentWarPass()?.level ?: 0
    val nextLevelValue = passPool().firstOrNull { it.level == level + 1 }?.exp ?: 1000

    // 使用我们提取的 LevelAndBarRow
    LevelAndBarRow(
        level = level,
        currentValue = money,
        nextLevelValue = nextLevelValue,
        levelFramePainter = painterResource(Res.drawable.talent1_frame_light)
    ) {
        // Info 点击时
        AppWrapper.getStringKmp(contentStringRes).toast()
    }
}

// 方便使用者：3个直接函数
@Composable
fun CurrentPass1Point(modifier: Modifier = Modifier) {
    CurrentPassPoint(passType = 1, modifier = modifier)
}

@Composable
fun CurrentPass2Point(modifier: Modifier = Modifier) {
    CurrentPassPoint(passType = 2, modifier = modifier)
}

@Composable
fun CurrentPass3Point(modifier: Modifier = Modifier) {
    CurrentPassPoint(passType = 3, modifier = modifier)
}

/**
 * ===========================
 * 6. 其它示例：电量、历史券
 * ===========================
 */

/** 电量(当前值) */
@Composable
fun CurrentElectricPoint(
    modifier: Modifier = Modifier
) {
    val money = AwardManager.electric.value
    val level = VipManager.getVipLevelData()
    val nextLevelObj = if (money == 0) {
        repo.gameCore.getVipPool().first()
    } else {
        repo.gameCore.getVipPool().firstOrNull { it.level == level.level + 1 }
            ?: repo.gameCore.getVipPool().last()
    }
    // nextLevelObj.num - nextLevelObj.currentNum 只是你项目中的数值逻辑示例
    val nextLevelValue = nextLevelObj.currentNum
    val displayValue = money - (nextLevelObj.num - nextLevelObj.currentNum)

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = Modifier
            .fillMaxWidth()
            .then(modifier)
    ) {
        EffectButton(
            Modifier
                .size(imageLarge)
                .graphicsLayer { translationX = padding10.toPx() }
                .zIndex(10f), onClick = {
                AppWrapper.getStringKmp(Res.string.electric_content).toast()
            }
        ) {
            VipLevel(cheatLevel = if (money == 0) 0 else level.level)
        }
        CommonBar(
            modifier = Modifier.size(cardNumWidth, cardNumHeight),
            fullRes = Res.drawable.bar_blue,
            emptyRes = Res.drawable.bar_empty,
            currentValue = displayValue,
            maxValue = nextLevelValue,
            textColor = Color.White,
            style = MaterialTheme.typography.h5
        )
    }
}

/** 直接显示某资源消耗值 */
@Composable
fun ResourcesPoint(index: Int, cost: Int) {
    val money = AwardManager.resources[index]
    ResourceCostRow(
        cost = cost,
        currentValue = money,
        iconPainter = kmpPainterResource(index.indexToResourceIcon()),
        contentDescription = index.indexToResourceName()
    )
}

/** 显示某资源的当前值 (短条大小) */
@Composable
fun CurrentResourcesPoint(
    modifier: Modifier = Modifier,
    index: Int,
    boxWidth: Dp = moneyExtraWidth,
    showPlus: Boolean = false
) {
    ResourceCountBox(
        modifier = modifier,
        currentValue = AwardManager.resources[index],
        iconPainter = painterResource(index.indexToResourceIcon()),
        iconContentDescription = index.indexToResourceName(),
        showFrame = true,
        boxWidth = boxWidth,
        showPlus = showPlus,
        tips = index.indexToResourceTips(),
        onPlusClicked = {
            if (index == 0) {
                Dialogs.moneyTransferDialog.value = true
            } else {
                gotoSellWithTabIndex(SELL_KEY_INDEX)
            }
        }
    )
}

/** 一次性展示所有资源 (8种) */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun CurrentResourcesPointAll(modifier: Modifier = Modifier) {
    FlowRow(modifier = modifier, maxItemsInEachRow = 4) {
        CurrentResourcesPoint(index = 1, boxWidth = moneyWidth)
        CurrentResourcesPoint(index = 2, boxWidth = moneyWidth)
        CurrentResourcesPoint(index = 3, boxWidth = moneyWidth)
        CurrentResourcesPoint(index = 4, boxWidth = moneyWidth)
        CurrentResourcesPoint(index = 5, boxWidth = moneyWidth)
        CurrentResourcesPoint(index = 6, boxWidth = moneyWidth)
        CurrentResourcesPoint(index = 7, boxWidth = moneyWidth)
    }
}

/**
 * ==========================
 * 8. 其它辅助组件
 * ==========================
 */

/** Info图标按钮 */
@Composable
fun InfoIcon(size: Dp = imageMedium, callback: () -> Unit) {
    val pressing = remember {
        mutableStateOf(false)
    }
    val colorFilter = if (pressing.value) {
        ColorFilter.tint(
            B50, BlendMode.SrcAtop
        )
    } else {
        null
    }
    EffectButton(onClick = callback, pressing = pressing) {
        Image(
            modifier = Modifier.size(size),
            colorFilter = colorFilter,
            painter = painterResource(Res.drawable.battle_menu_info),
            contentDescription = stringResource(Res.string.explain)
        )
    }
}

/** VIP等级显示示例 - 这里引用了你已有的 VipLevel */
@Composable
fun VipLevel(
    modifier: Modifier = Modifier,
    cheatLevel: Int,
    frame: Painter = painterResource(Res.drawable.talent1_frame_light)
) {
    Box(
        modifier = modifier.size(imageLarge),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.size(imageLarge),
            painter = frame,
            contentDescription = null
        )
        StrokedText(text = "$cheatLevel", style = MaterialTheme.typography.h3)
    }
}
