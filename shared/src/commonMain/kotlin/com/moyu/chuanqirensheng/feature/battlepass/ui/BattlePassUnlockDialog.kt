package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement.Absolute.spacedBy
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.style.TextAlign
import com.eygraber.uri.Uri
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.award.ui.AwardList
import com.moyu.chuanqirensheng.feature.award.ui.defaultParam
import com.moyu.chuanqirensheng.feature.battlepass.getBattlePassManager
import com.moyu.chuanqirensheng.feature.battlepass.unlockEvidenceKey
import com.moyu.chuanqirensheng.platform.billPrepay
import com.moyu.chuanqirensheng.platform.hasBilling
import com.moyu.chuanqirensheng.platform.openGamePage
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.text.getPriceTextWithUnit
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.cant_get_yet
import shared.generated.resources.shop_discount
import shared.generated.resources.unlock_all_gain
import shared.generated.resources.unlock_battle_pass1
import shared.generated.resources.unlock_battle_pass2
import shared.generated.resources.unlock_battle_pass3
import shared.generated.resources.unlock_gain_now

@Composable
fun BattlePassUnlockDialog(
    value: MutableState<Int?>
) {
    value.value?.let { passType ->
        // 找到对应的 SellItem
        val sellKey = unlockEvidenceKey(passType)
        val sell = repo.gameCore.getSellPool().firstOrNull { it.id == sellKey }
            ?: return // 容错

        PanelDialog(onDismissRequest = {
            value.value = null
        }, contentBelow = {
            GameButton(
                buttonSize = ButtonSize.Big,
                enabled = (sell.price == 0 || sell.storage > 0),
                buttonStyle = ButtonStyle.Blue,
                text = sell.getPriceTextWithUnit()
            ) {
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    if (!hasBilling()) {
                        // 没谷歌服务就用 aiFaDian ...
                        if (LoginManager.instance.canShowAifadian()) {
                            val unlockUrl = repo.gameCore.getUnlockById(sellKey).url
                            openGamePage(Uri.parse(unlockUrl))
                        } else {
                            AppWrapper.getStringKmp(Res.string.cant_get_yet).toast()
                        }
                    } else {
                        // 谷歌内购
                        billPrepay(sell) {
                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                // 付费成功后，发放奖励
                                AwardManager.realGainItem(sell, sell.toAward())
                            }
                        }
                    }
                    value.value = null
                }
            }
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
            ) {
                StrokedText(
                    modifier = Modifier.width(padding200).align(Alignment.CenterHorizontally),
                    text = when(passType) {
                        1 -> stringResource(Res.string.unlock_battle_pass1)
                        2 -> stringResource(Res.string.unlock_battle_pass2)
                        3 -> stringResource(Res.string.unlock_battle_pass3) // 需要你在 strings 里加
                        else -> "Unlock WarPass"
                    },
                    style = MaterialTheme.typography.h1,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.size(padding48))
                // 解锁后能获得哪些奖励(付费 unlockType=2 的BattlePass)
                val warPassLocked = when(passType) {
                    1 -> repo.gameCore.getBattlePass1Pool().filter { it.unlockType == 2 }
                    2 -> repo.gameCore.getBattlePass2Pool().filter { it.unlockType == 2 }
                    3 -> repo.gameCore.getBattlePass3Pool().filter { it.unlockType == 2 }
                    else -> emptyList()
                }
                StrokedText(
                    text = stringResource(Res.string.unlock_all_gain),
                    style = MaterialTheme.typography.h3,
                    color = Color.White
                )
                Spacer(Modifier.size(padding4))
                AwardList(
                    Modifier,
                    award = warPassLocked.map { it.toAward() }.toAward(),
                    mainAxisAlignment = spacedBy(padding10),
                    param = defaultParam.copy(
                        peek = true,
                        textColor = Color.White,
                        itemSize = ItemSize.Large,
                    ),
                )

                // 立即可领取的奖励(如果玩家等级 >= N)
                val currentLevel = getBattlePassManager(passType).getCurrentWarPass()?.level ?: 0
                val canGainNow = warPassLocked.filter { it.level <= currentLevel }
                if (canGainNow.isNotEmpty()) {
                    Spacer(Modifier.size(padding12))
                    StrokedText(
                        text = stringResource(Res.string.unlock_gain_now),
                        style = MaterialTheme.typography.h1,
                        color = Color.White
                    )
                    Spacer(Modifier.size(padding4))
                    AwardList(
                        Modifier,
                        award = canGainNow.map { it.toAward() }.toAward(),
                        mainAxisAlignment = spacedBy(padding10),
                        param = defaultParam.copy(
                            peek = true,
                            textColor = Color.White,
                            itemSize = ItemSize.Large,
                        ),
                    )
                }
            }
            if (sell.desc2 != "0") {
                Box(Modifier.graphicsLayer {
                    translationY = -padding30.toPx()
                    translationX = -padding19.toPx()
                }, contentAlignment = Alignment.Center) {
                    Image(
                        painter = painterResource(Res.drawable.shop_discount),
                        contentDescription = null,
                        modifier = Modifier.size(padding100)
                    )
                    StrokedText(
                        text = sell.desc2,
                        style = MaterialTheme.typography.h1
                    )
                }
            }
        }
    }
}
