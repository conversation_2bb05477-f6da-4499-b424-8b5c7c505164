package com.moyu.chuanqirensheng.feature.equip.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.ally.ui.UpgradeAnimView
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.resource.PowerLabel
import com.moyu.chuanqirensheng.feature.resource.ResourcesPoint
import com.moyu.chuanqirensheng.feature.role.ui.MainPropertyLine
import com.moyu.chuanqirensheng.feature.skill.getRealDescColorful
import com.moyu.chuanqirensheng.feature.skill.ui.SingleEquipView
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.text.getEquipTypeTips
import com.moyu.chuanqirensheng.text.getTypeImage
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.cardNumHeight
import com.moyu.chuanqirensheng.ui.theme.cardNumWidth
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageMediumMinus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.powerHeight
import com.moyu.chuanqirensheng.ui.theme.powerWidth
import com.moyu.chuanqirensheng.widget.button.ButtonSize
import com.moyu.chuanqirensheng.widget.button.ButtonStyle
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.button.GameButton
import com.moyu.chuanqirensheng.widget.button.QUICK_GAP
import com.moyu.chuanqirensheng.widget.common.CommonBar
import com.moyu.chuanqirensheng.widget.common.ItemSize
import com.moyu.chuanqirensheng.widget.dialog.NewPanelDialog
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.Equipment
import com.moyu.core.model.MAX_STAR
import com.moyu.core.model.property.Property
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.already_max_star_tips
import shared.generated.resources.bar_blue
import shared.generated.resources.bar_empty
import shared.generated.resources.card_not_enough
import shared.generated.resources.common_arrow_left
import shared.generated.resources.common_arrow_right
import shared.generated.resources.effects
import shared.generated.resources.equip_off
import shared.generated.resources.equip_on
import shared.generated.resources.equip_replace
import shared.generated.resources.properties
import shared.generated.resources.resource1_not_enough_tips
import shared.generated.resources.star_max
import shared.generated.resources.star_up
import shared.generated.resources.text_frame


@Composable
fun EquipDetailDialog(show: MutableState<Equipment?>) {
    show.value?.let { equip ->
        LaunchedEffect(Unit) {
            repo.equipManager.setUnNew(equip)
        }
        NewPanelDialog(onDismissRequest = {
            show.value = null
        }) {
            Box(Modifier.fillMaxSize()) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    EquipDialogLayout(equip = equip)
                }
                if (!equip.peek) {
                    SelectEquipArrows(Modifier.align(Alignment.Center).fillMaxWidth())
                }
            }
        }
    }
}

@Composable
fun EquipDialogLayout(modifier: Modifier = Modifier, equip: Equipment) {
    val itemSize = ItemSize.LargePlus
    Row(modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
        SingleEquipView(
            equipment = equip,
            showName = false,
            showRed = false,
            itemSize = itemSize
        )
        Spacer(Modifier.size(padding8))
        Column(
            Modifier.height(itemSize.itemSize),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            StrokedText(
                modifier = Modifier.padding(horizontal = padding12).scale(1.2f),
                text = equip.name,
                style = MaterialTheme.typography.h1
            )
            Row(verticalAlignment = Alignment.CenterVertically) {
                EffectButton(onClick = {
                    equip.type.getEquipTypeTips().toast()
                }) {
                    Image(
                        modifier = Modifier.size(imageMediumMinus),
                        painter = painterResource(equip.getTypeImage()),
                        contentDescription = null
                    )
                }
                Spacer(Modifier.size(padding4))
                PowerLabel(Modifier.size(powerWidth, powerHeight), power = equip.power)
            }
        }
    }
    Spacer(Modifier.size(padding10))
    Column(
        Modifier.height(padding300).fillMaxWidth().paint(
            painter = painterResource(Res.drawable.text_frame),
            contentScale = ContentScale.FillBounds
        ).padding(horizontal = padding12, vertical = padding8)
    ) {
        StrokedText(
            modifier = Modifier.padding(start = padding6, top = padding4),
            text = if (equip.skillEffect != 0) stringResource(Res.string.effects) else stringResource(
                Res.string.properties
            ),
            style = MaterialTheme.typography.h1
        )
        Spacer(Modifier.size(padding14))
        if (equip.skillEffect != 0) {
            StrokedText(
                text = repo.gameCore.getSkillById(equip.skillEffect)
                    .getRealDescColorful(spanStyle = MaterialTheme.typography.h3.toSpanStyle()),
                style = MaterialTheme.typography.h3,
            )
        }
        equip.getProperty().MainPropertyLine(
            originProperty = Property(),
            textStyle = MaterialTheme.typography.h3,
            showZero = false,
            textColor = Color.White
        )
    }
    Spacer(Modifier.size(padding10))
    if (!equip.peek) {
        CommonBar(
            modifier = Modifier.size(cardNumWidth, cardNumHeight),
            currentValue = equip.num,
            maxValue = equip.starUpNum,
            fullRes = Res.drawable.bar_blue,
            emptyRes = Res.drawable.bar_empty,
            textColor = Color.White,
            style = MaterialTheme.typography.h5
        )
        Spacer(Modifier.size(padding10))
        Row {
            ArmEquipView(equip)
            Spacer(Modifier.size(padding10))
            EquipStarUpView(equip)
        }
        Spacer(Modifier.size(padding10))
    }
}


@Composable
fun EquipStarUpView(equip: Equipment) {
    Box(contentAlignment = Alignment.Center) {
        val maxStar = equip.star >= MAX_STAR
        val buttonText =
            if (maxStar) AppWrapper.getStringKmp(Res.string.star_max) else AppWrapper.getStringKmp(
                Res.string.star_up
            )
        val allyNumOk = equip.num >= equip.starUpNum
        val resource1Ok = AwardManager.resources[0] >= equip.starUpResourceNum
        val enabled = allyNumOk && !maxStar && resource1Ok
        GameButton(
            text = "",
            buttonStyle = ButtonStyle.Green,
            buttonSize = ButtonSize.Big,
            enabled = enabled,
            onClick = {
                if (enabled) {
                    Dialogs.equipStarUpDialog.value = equip
                } else {
                    if (!allyNumOk) {
                        AppWrapper.getStringKmp(Res.string.card_not_enough).toast()
                    } else if (maxStar) {
                        AppWrapper.getStringKmp(Res.string.already_max_star_tips).toast()
                    } else if (!resource1Ok) {
                        AppWrapper.getStringKmp(Res.string.resource1_not_enough_tips).toast()
                        GiftManager.onResource1NotEnough()
                        Dialogs.moneyTransferDialog.value = true
                    }
                }
            })
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceAround
        ) {
            ResourcesPoint(0, equip.starUpResourceNum)
            StrokedText(text = buttonText, style = MaterialTheme.typography.h1)
        }
        if (enabled) {
            UpgradeAnimView(
                Modifier.align(Alignment.BottomEnd).height(bigButtonHeight / 1.5f)
            )
        }
    }
}


@Composable
fun ArmEquipView(equip: Equipment) {
    val text = if (equip.equipped) {
        stringResource(Res.string.equip_off)
    } else {
        if (repo.equipManager.data.filter { it.equipped }.any { it.type == equip.type }) {
            stringResource(Res.string.equip_replace)
        } else {
            stringResource(Res.string.equip_on)
        }
    }
    GameButton(
        text = text,
        buttonStyle = ButtonStyle.Blue,
        buttonSize = ButtonSize.Big,
        onClick = {
            repo.equipManager.equip(equip)?.let {
                // 装备成功了关闭
                Dialogs.equipDetailDialog.value = null
            }
        })
}

@Composable
fun SelectEquipArrows(modifier: Modifier) {
    Row(modifier.graphicsLayer {
        translationY = padding22.toPx()
    }, horizontalArrangement = Arrangement.SpaceEvenly) {
        EffectButton(Modifier.graphicsLayer {
            translationX = -padding30.toPx()
        }, clickGap = QUICK_GAP, onClick = {
            repo.equipManager.selectPrev()
        }) {
            Image(
                modifier = Modifier.size(imageHugeLite),
                painter = painterResource(Res.drawable.common_arrow_left),
                contentDescription = null
            )
        }
        Spacer(modifier = Modifier.weight(1f))
        EffectButton(Modifier.graphicsLayer {
            translationX = padding30.toPx()
        }, clickGap = QUICK_GAP, onClick = {
            repo.equipManager.selectNext()
        }) {
            Image(
                modifier = Modifier.size(imageHugeLite),
                painter = painterResource(Res.drawable.common_arrow_right),
                contentDescription = null
            )
        }
    }
}