package com.moyu.chuanqirensheng.feature.pvp

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.api.postPvp2RankData
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.gamemode.GameMode
import com.moyu.chuanqirensheng.feature.gamemode.MODE_PVP2
import com.moyu.chuanqirensheng.feature.illustration.TcgManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.onTaskPvpLose
import com.moyu.chuanqirensheng.feature.quest.onTaskPvpWin
import com.moyu.chuanqirensheng.feature.rank.PvpData
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.role.createPvpRole
import com.moyu.chuanqirensheng.feature.router.PVP2_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP2
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_PVP
import com.moyu.chuanqirensheng.sub.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK2_ALLY_IDS
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK2_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK2_TARGET
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK2_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP2_LOSE
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP2_LOSE_TODAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP2_SCORE
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP2_WIN
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP2_WIN_TODAY
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.util.getCurrentDay
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.logic.role.ENEMY_HERO_POSITION
import com.moyu.core.logic.role.battleEnemyNoMasterList
import com.moyu.core.model.Ally
import com.moyu.core.model.Arena
import com.moyu.core.model.Award
import com.moyu.core.model.MASTER_MAIN_ID
import com.moyu.core.model.allyIdToMainId
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextInteger
import com.moyu.core.util.p_getTimeMillis
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import shared.generated.resources.Res
import shared.generated.resources.not_login
import shared.generated.resources.pvp2_lose_tips
import shared.generated.resources.pvp_ally_error
import shared.generated.resources.pvp_error_ally
import shared.generated.resources.pvp_mock
import shared.generated.resources.pvp_num_limit
import shared.generated.resources.pvp_upgrade

const val MAX_PVP2_NUM = 5
const val INIT_PVP2_SCORE = 1000

object Pvp2Manager {
    val pvpScore = Guarded(KEY_PVP2_SCORE, mutableStateOf(INIT_PVP2_SCORE))
    val lastPvpAllyIds = mutableListOf<Int>()
    val pkNumToday = Guarded(KEY_PK2_NUM)
    val pkWin = Guarded(KEY_PVP2_WIN)
    val pkLose = Guarded(KEY_PVP2_LOSE)
    val pkWinToday = Guarded(KEY_PVP2_WIN_TODAY)
    val pkLoseToday = Guarded(KEY_PVP2_LOSE_TODAY)
    val pkTargetList = mutableStateListOf<String>()
    val currentTarget = mutableStateOf(RankData(p_getTimeMillis()))
    val targetsFromServer = mutableStateOf(emptyList<RankData>())
    val currentTargets = mutableStateOf(emptyList<RankData>())

    suspend fun init() {
        initPkNum()
        lastPvpAllyIds.clear()
        lastPvpAllyIds.addAll(getListObject(KEY_PK2_ALLY_IDS))

    }

    fun initPkNum() {
        if (!isNetTimeValid()) {
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_PK2_UPDATE_TIME_IN_MILLIS),
                getCurrentTime()
            )
        ) {
            if (pkTargetList.isEmpty()) {
                pkTargetList.addAll(getListObject(KEY_PK2_TARGET))
            }
        } else {
            pvpScore.value = INIT_PVP2_SCORE
            pkNumToday.value = 0
            pkWinToday.value = 0
            pkLoseToday.value = 0
            pkTargetList.clear()
            setLongValueByKey(KEY_PK2_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_PK2_TARGET, emptyList<String>())
        }
    }

    fun pk(rankData: RankData) {
        if (!DebugManager.unlockAll) {
            if (pkWinToday.value + pkLoseToday.value >= MAX_PVP2_NUM
                || pkNumToday.value >= MAX_PVP2_NUM
            ) {
                AppWrapper.getStringKmp(Res.string.pvp_num_limit).toast()
                return
            }
        }
        if (rankData.pvpData.allyIds.isEmpty()) {
            AppWrapper.getStringKmp(Res.string.pvp_ally_error).toast()
            return
        }
        val allies = rankData.pvpData.allyIds.take(8).toMutableList()
        // 如果有不符合要求的兵种，要改成符合要求的兵种，这里保留玩家的兵种星级
        allies.forEach { ally ->
            if (repo.gameCore.getAllyPool(ally.allyIdToMainId()).isEmpty()) {
                AppWrapper.getStringKmp(Res.string.pvp_upgrade).toast()
                return
            }
        }
        if (currentTarget.value.userId.isNotEmpty()) {
            // 已经选择了对手，正在pk中
            return
        }
        currentTarget.value = rankData
        repo.gameMode.value = GameMode(MODE_PVP2)
        pkNumToday.value += 1
        val roleHashMap = mutableMapOf<Int, Role?>()

        battleEnemyNoMasterList.forEachIndexed { index, position ->
            allies.filter { it.allyIdToMainId() != MASTER_MAIN_ID }.getOrNull(index)?.let {
                roleHashMap[position] =
                    createPvpRole(
                        repo.gameCore.getAllyById(it),
                        rankData.pvpData.talentIds,
                        rankData.pvpData.equipIds,
                        rankData.pvpData.tcgIds
                    )
            }
        }
        roleHashMap[ENEMY_HERO_POSITION] = createPvpRole(
            repo.gameCore.getAllyById(allies.first { it.allyIdToMainId() == MASTER_MAIN_ID }),
            rankData.pvpData.talentIds,
            rankData.pvpData.equipIds,
            rankData.pvpData.tcgIds
        )
        repo.battleRoles.clear()
        repo.battleRoles.putAll(roleHashMap)
        goto(PVP2_BATTLE_SCREEN)
    }

    fun pkFailed(allies: List<Role>, enemies: List<Role>) {
        // 需要根据时间确认是否刷新pk数据
        initPkNum()

        pkLose.value += 1
        pkLoseToday.value += 1
        if (lessThanMax()) {
            AppWrapper.getStringKmp(Res.string.pvp2_lose_tips).toast()
            reportManager().pk(0, pvpScore.value)
            uploadPvpRank()
        }
        repo.inBattle.value = false
        goto(PVP2_CHOOSE_ENEMY_SCREEN)
        currentTarget.value = RankData(p_getTimeMillis())
        GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
        onTaskPvpLose()
        increaseIntValueByKey(KEY_DIED_IN_PVP)
    }

    private fun lessThanMax(): Boolean {
        return (pkLoseToday.value + pkWinToday.value <= MAX_PVP2_NUM) &&
                pkNumToday.value <= MAX_PVP2_NUM
    }

    fun pkWined(allies: List<Role>, enemies: List<Role>) {
        // 需要根据时间确认是否刷新pk数据
        initPkNum()

        pkWin.value += 1
        pkWinToday.value += 1
        if (pkTargetList.contains(currentTarget.value.userId)) {
            AppWrapper.getStringKmp(Res.string.pvp_error_ally).toast()
            repo.inBattle.value = false
        } else {
            pkTargetList.add(currentTarget.value.userId)
            val index = currentTargets.value.sortedByDescending { it.pvpScore }
                .indexOfFirst { it.userId == currentTarget.value.userId }

            val pvpData = if (index == -1 || index >= repo.gameCore.getPvpPool(MODE_PVP2).size) {
                repo.gameCore.getPvpPool(MODE_PVP2).last()
            } else {
                repo.gameCore.getPvpPool(MODE_PVP2)[index]
            }
            if (lessThanMax()) {
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    setListObject(KEY_PK2_TARGET, pkTargetList)
                    Dialogs.awardDialog.value =
                        Award(pvpDiamond = pvpData.winToken, pvp2Score = pvpData.winPoint).apply {
                            AwardManager.gainAward(this)
                        }
                    uploadPvpRank()
                    reportManager().pk(1, pvpScore.value)
                }
            }
            repo.inBattle.value = false
            onTaskPvpWin()
            goto(PVP2_CHOOSE_ENEMY_SCREEN)
            currentTarget.value = RankData(p_getTimeMillis())
            GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
//            restartEffect(dialogEffectState, winBattleEffect)
        }
    }

    fun getMockPvpData(): List<RankData> {
        val allyPool = repo.gameCore.getAllyPool()
        val arena = getCurrentArena()
        val allyHeroes = allyPool.filter { it.isHero() && it.level <= 50 }
            .shuffled(RANDOM)
        val allies = allyPool
                        .filter { !it.isHero() && it.level == 1 }
                        .filter { arena.filter(it) }
        return List(20) { index ->
            val id = UUID.generateUUID().toString().take(5)
            RankData(
                time = getCurrentTime(),
                userId = id,
                userPic = gameSdkDefaultProcessor().getAvatarUrl() ?: "",
                userName = AppWrapper.getStringKmp(Res.string.pvp_mock) + id,
                pvpScore = RANDOM.nextInteger(
                    (pvpScore.value),
                    (pvpScore.value * 1.1f).toInt()
                ),
                pvpData = PvpData(
                    talentIds = TalentManager.getPvpTalents(),
                    allyIds = listOf(allyHeroes[index].id) + allies.shuffled().take(7).map { it.id })
            )
        } + List(30) { index ->
            val id = UUID.generateUUID().toString().take(5)
            RankData(
                time = getCurrentTime(),
                userId = id,
                userPic = gameSdkDefaultProcessor().getAvatarUrl() ?: "",
                userName = AppWrapper.getStringKmp(Res.string.pvp_mock) + id,
                pvpScore = RANDOM.nextInteger(
                    (pvpScore.value * 0.9f).toInt(),
                    (pvpScore.value),
                ),
                pvpData = PvpData(
                    talentIds = TalentManager.getPvpTalents(),
                    allyIds = listOf(allyHeroes[index].id) + allies.shuffled().take(7).map { it.id })
            )
        }
    }

    fun getPvpData(): PvpData {
        return PvpData(
            talentIds = TalentManager.getPvpTalents(),
            allyIds = lastPvpAllyIds.ifEmpty {
                repo.gameCore.getAllyPool().filter { it.star == 0 && it.quality == 3 }
                    .shuffled(RANDOM).take(6).map { it.id }
            },
            equipIds = repo.equipManager.data.filter { it.equipped }.map { it.id },
            win = pkWinToday.value,
            lose = pkLoseToday.value,
            tcgIds = TcgManager.doneTcgs.map { it.id }
        )
    }

    fun refreshTargets() {
        val listAbove = targetsFromServer.value.filter { it.pvpScore > pvpScore.value }
            .filter { it.userId !in pkTargetList }.shuffled()
        val listBelow = targetsFromServer.value.filter { it.pvpScore <= pvpScore.value }
            .filter { it.userId !in pkTargetList }.shuffled()
        if (listAbove.size < 2) {
            currentTargets.value =
                (listAbove + listBelow.take(5 - listAbove.size)).sortedByDescending { it.pvpScore }
        } else if (listBelow.size < 3) {
            currentTargets.value =
                (listAbove.take(5 - listBelow.size) + listBelow).sortedByDescending { it.pvpScore }
        } else {
            currentTargets.value =
                (listAbove.take(2) + listBelow.take(3)).sortedByDescending { it.pvpScore }
        }
    }

    fun uploadPvpRank() {
        if (!DebugManager.debug || DebugManager.uploadRank) {
            AppWrapper.globalScope.launch(Dispatchers.IO) {
                try {
                    postPvp2RankData(
                        RankData(
                            time = getCurrentTime(),
                            versionCode = getVersionCode(),
                            userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else gameSdkDefaultProcessor().getUserName()
                                ?: AppWrapper.getStringKmp(Res.string.not_login),
                            userId = gameSdkDefaultProcessor().getObjectId() ?: "0",
                            userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "role_101" else gameSdkDefaultProcessor().getAvatarUrl() ?: "0",
                            pvpScore = pvpScore.value,
                            pvpLastScore = 0,
                            pvpData = getPvpData(),
                            platformChannel = platformChannel(),
                            serverId = LoginManager.instance.getSavedServerId(),
                        )
                    )
                } catch (e: Exception) {
                    e.toString().toast()
                }
            }
        }
    }

    fun getCurrentArena(): Arena {
        return repo.gameCore.getArenaPool()[getCurrentDay() % 60]
    }

    /**
     *  101-可以上阵1阶兵种
     *  102-可以上阵2阶兵种
     *  103-可以上阵3阶兵种
     *  104-可以上阵4阶兵种
     *  105-可以上阵5阶兵种
     *  106-可以上阵6阶兵种
     *  107-可以上阵7阶兵种
     *  201-可以上阵城堡阵营的英雄和兵种
     *  202-可以上阵壁垒阵营的英雄和兵种
     *  203-可以上阵塔楼阵营的英雄和兵种
     *  204-可以上阵地狱阵营的英雄和兵种
     *  205-可以上阵地下阵营的英雄和兵种
     *  206-可以上阵墓园阵营的英雄和兵种
     *  207-可以上阵堡垒阵营的英雄和兵种
     *  208-可以上阵要塞阵营的英雄和兵种
     *  209-可以上阵元素阵营的英雄和兵种
     *  210-可以上阵港口阵营的英雄和兵种
     *  211-可以上阵工厂阵营的英雄和兵种
     *  301-可以上阵近战兵种
     *  302-可以上阵远程兵种
     *  303-可以上阵飞行兵种
     *  304-可以上阵施法兵种
     */
    fun Arena.filter(ally: Ally): Boolean {
        return this.type.map {
            when (it) {
                in 101..107 -> {
                    val quality = it - 100
                    ally.isHero() || ally.quality == quality
                }
                in 201..211 -> {
                    val raceType2 = it - 200
                    ally.raceType2 == raceType2
                }
                in 301..304 -> {
                    val raceType = it - 300
                    ally.raceType == raceType
                }
                else -> { false}
            }
        }.any { it }
    }

    fun hasRedPk(): Boolean {
        return pkNumToday.value < MAX_PVP_NUM
    }
    fun hasRedTask(): Boolean {
        return QuestManager.pvp2Tasks.any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        }
    }

    fun hasRedAll(): Boolean {
        return hasRedTask() || hasRedPk()
    }

    fun unlocked(): Boolean {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_PVP2)
        return UnlockManager.getUnlockedFlow(unlock)
    }
}

fun Int.getStar(): Int {
    return this % 100
}
