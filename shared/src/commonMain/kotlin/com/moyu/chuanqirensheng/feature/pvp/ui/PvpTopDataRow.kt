package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.resource.CurrentPvpDiamond
import com.moyu.chuanqirensheng.feature.resource.CurrentPvpScore
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.all_pk
import shared.generated.resources.today_pk

@Composable
fun PvpTopDataRow(modifier: Modifier) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Spacer(modifier = Modifier.size(padding22))
        CurrentPvpDiamond(modifier = Modifier.padding(start = padding2))
        Spacer(modifier = Modifier.size(padding2))
        CurrentPvpScore(modifier = Modifier.padding(start = padding2))
        Spacer(modifier = Modifier.size(padding12))
        Column {
            StrokedText(
                text = stringResource(
                    Res.string.today_pk,
                    PvpManager.pkWinToday.value,
                    PvpManager.pkLoseToday.value
                ),
                style = MaterialTheme.typography.h6
            )
            StrokedText(
                text = stringResource(
                    Res.string.all_pk,
                    PvpManager.pkWin.value,
                    PvpManager.pkLose.value
                ),
                style = MaterialTheme.typography.h6
            )
        }
        Spacer(modifier = Modifier.size(padding12))
        val rank = PvpManager.getRankLevel()
        Image(
            modifier = Modifier.size(imageSmallPlus),
            painter = painterResource(kmpDrawableResource(rank.pic)),
            contentDescription = null
        )
        Spacer(Modifier.size(padding2))
        StrokedText(
            text = rank.name,
            style = MaterialTheme.typography.h6
        )
    }
}