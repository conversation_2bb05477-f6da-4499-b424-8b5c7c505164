package com.moyu.chuanqirensheng.feature.rank.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.api.RetrofitModel.uploadLikedData
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.award.AwardManager
import com.moyu.chuanqirensheng.feature.rank.FAMOUS_MAX_ONE_DAY
import com.moyu.chuanqirensheng.feature.rank.FAMOUS_TYPE
import com.moyu.chuanqirensheng.feature.rank.LikedData
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.RankManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import com.moyu.chuanqirensheng.widget.common.NavigationTab
import com.moyu.chuanqirensheng.widget.effect.StrokedText
import com.moyu.core.AppWrapper
import com.moyu.core.model.Award
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.shrinkNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.battle_power_icon
import shared.generated.resources.battle_power_tips
import shared.generated.resources.common_menu_vote
import shared.generated.resources.famous_icon
import shared.generated.resources.famous_rank
import shared.generated.resources.icon_stage
import shared.generated.resources.like
import shared.generated.resources.rank1_title
import shared.generated.resources.rank2_title
import shared.generated.resources.rank3_title
import shared.generated.resources.rank4_title
import shared.generated.resources.rank5_title
import shared.generated.resources.stage_progress_tips
import shared.generated.resources.talent1_icon
import shared.generated.resources.talent2_icon
import shared.generated.resources.talent3_icon
import shared.generated.resources.talent_total_level1
import shared.generated.resources.talent_total_level2
import shared.generated.resources.talent_total_level3
import shared.generated.resources.top_like_count_one_day

val rankTabs = listOf(
    Res.drawable.battle_power_icon,
    Res.drawable.icon_stage,
    Res.drawable.talent1_icon,
    Res.drawable.talent2_icon,
    Res.drawable.talent3_icon,
    Res.drawable.famous_icon,
)

val battlePowerRanks = mutableStateOf(emptyList<RankData>())
val endingsNumRanks = mutableStateOf(emptyList<RankData>())
val talentNumRanks = mutableStateOf(emptyList<RankData>())
val talentNumRanks2 = mutableStateOf(emptyList<RankData>())
val talentNumRanks3 = mutableStateOf(emptyList<RankData>())
val famousRanks = mutableStateOf(emptyList<RankData>())

@Composable
fun RankScreen() {
    LaunchedEffect(Unit) {
        battlePowerRanks.value = emptyList()
        endingsNumRanks.value = emptyList()
        talentNumRanks.value = emptyList()
        talentNumRanks2.value = emptyList()
        talentNumRanks3.value = emptyList()
        famousRanks.value = emptyList()
    }
    val pagerState = remember {
        mutableStateOf(0)
    }
    GameBackground(
        title = when (pagerState.value) {
            0 -> stringResource(Res.string.rank1_title)
            1 -> stringResource(Res.string.rank2_title)
            2 -> stringResource(Res.string.rank3_title)
            3 -> stringResource(Res.string.rank4_title)
            4 -> stringResource(Res.string.rank5_title)
            else -> stringResource(Res.string.famous_rank)
        }
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Box(
                modifier = Modifier.weight(1f).fillMaxWidth()
            ) {
                when (pagerState.value) {
                    0 -> RankPage(1, battlePowerRanks) { rankData, _ ->
                        Row(modifier = Modifier.clickable {
                            MusicManager.playSound(SoundEffect.Click)
                            AppWrapper.getStringKmp(Res.string.battle_power_tips).toast()
                        }, verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                modifier = Modifier.size(imageSmallPlus),
                                painter = painterResource(Res.drawable.battle_power_icon),
                                contentDescription = null
                            )
                            StrokedText(
                                text = rankData.battlePower.toString().shrinkNumber(!hasGoogleService()),
                                style = MaterialTheme.typography.h2
                            )
                        }
                    }

                    1 -> RankPage(2, endingsNumRanks) { rankData, _ ->
                        Row(modifier = Modifier.clickable {
                            MusicManager.playSound(SoundEffect.Click)
                            AppWrapper.getStringKmp(Res.string.stage_progress_tips).toast()
                        }, verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                modifier = Modifier.size(imageSmallPlus),
                                painter = painterResource(Res.drawable.icon_stage),
                                contentDescription = null
                            )
                            StrokedText(
                                text = rankData.endingNum.toString(),
                                style = MaterialTheme.typography.h2
                            )
                        }
                    }

                    2 -> RankPage(3, talentNumRanks) { rankData, _ ->
                        Row(modifier = Modifier.clickable {
                            MusicManager.playSound(SoundEffect.Click)
                            (AppWrapper.getStringKmp(Res.string.talent_total_level1) + rankData.talentLevel).toast()
                        }, verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                modifier = Modifier.size(imageSmallPlus),
                                painter = painterResource(Res.drawable.talent1_icon),
                                contentDescription = null
                            )
                            StrokedText(
                                text = rankData.talentLevel.toString(),
                                style = MaterialTheme.typography.h2
                            )
                        }
                    }

                    3 -> RankPage(4, talentNumRanks2) { rankData, _ ->
                        Row(modifier = Modifier.clickable {
                            MusicManager.playSound(SoundEffect.Click)
                            (AppWrapper.getStringKmp(Res.string.talent_total_level2) + rankData.tcgValue).toast()
                        }, verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                modifier = Modifier.size(imageSmallPlus),
                                painter = painterResource(Res.drawable.talent2_icon),
                                contentDescription = null
                            )
                            StrokedText(
                                text = rankData.tcgValue.toString(),
                                style = MaterialTheme.typography.h2
                            )
                        }
                    }

                    4 -> RankPage(5, talentNumRanks3) { rankData, _ ->
                        Row(modifier = Modifier.clickable {
                            MusicManager.playSound(SoundEffect.Click)
                            (AppWrapper.getStringKmp(Res.string.talent_total_level3) + rankData.level).toast()
                        }, verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                modifier = Modifier.size(imageSmallPlus),
                                painter = painterResource(Res.drawable.talent3_icon),
                                contentDescription = null
                            )
                            StrokedText(
                                text = rankData.level.toString(),
                                style = MaterialTheme.typography.h2
                            )
                        }
                    }

                    else -> {
                        val likedData = remember {
                            mutableStateOf(LikedData(serverId = LoginManager.instance.getSavedServerId()))
                        }
                        LaunchedEffect(Unit) {
                            RankManager.init()
                            famousRanks.value = emptyList()
                        }
                        DisposableEffect(Unit) {
                            onDispose {
                                if (likedData.value.isNotEmpty()) {
                                    AppWrapper.globalScope.launch {
                                        uploadLikedData(likedData.value)
                                    }
                                }
                            }
                        }
                        RankPage(FAMOUS_TYPE, famousRanks) { rankData, rankIndex ->
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                EffectButton(onClick = {
                                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                                        if (RankManager.famousLiked.value >= FAMOUS_MAX_ONE_DAY) {
                                            AppWrapper.getStringKmp(Res.string.top_like_count_one_day).toast()
                                        } else {
                                            RankManager.famousLiked.value += 1
                                            likedData.value = likedData.value.addLiked(rankData.userId)
                                            val award = Award(diamond = repo.gameCore.getFamousDiamond())
                                            Dialogs.awardDialog.value = award
                                            AwardManager.gainAward(award)
                                        }
                                    }
                                }) {
                                    Image(
                                        modifier = Modifier.size(imageMedium),
                                        painter = painterResource(Res.drawable.common_menu_vote),
                                        contentDescription = stringResource(Res.string.like)
                                    )
                                }
                                StrokedText(
                                    modifier = Modifier.width(padding26),
                                    text = (rankData.liked + likedData.value.getExtraNum(rankData.userId)).toString(),
                                    style = MaterialTheme.typography.h3
                                )
                            }
                        }
                    }
                }
            }
            NavigationTab(
                modifier = Modifier.padding(bottom = padding6),
                pageState = pagerState,
                titles = rankTabs
            )
        }
    }
}