package com.moyu.chuanqirensheng.feature.info

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.widget.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10

@Composable
fun InfoDialog(info: MutableState<Boolean>) {
    if (info.value) {
        PanelDialog(
            onDismissRequest = { info.value = false }) {
            Column(
                modifier = Modifier
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                InfoLayout(Modifier, repo.battleInfo)
                Spacer(modifier = Modifier.size(padding10))
            }
        }
    }
}