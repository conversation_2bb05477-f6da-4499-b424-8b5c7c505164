package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.feature.router.TOWER_RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SELL_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.widget.button.EffectButton
import com.moyu.chuanqirensheng.widget.common.GameBackground
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.icon_frame
import shared.generated.resources.rank_icon
import shared.generated.resources.red_icon
import shared.generated.resources.tower_bg
import shared.generated.resources.tower_chest_icon
import shared.generated.resources.tower_mode
import kotlin.math.min

@Composable
fun TowerScreen() {
    GameBackground(
        title = stringResource(Res.string.tower_mode),
        background = Res.drawable.tower_bg
    ) {
        Column(
            modifier = Modifier.fillMaxSize().padding(horizontal = padding12),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val pool = repo.gameCore.getTowerPool()
            // 要展示的数量
            val count = min(pool.size - TowerManager.maxLevel.value, 50)
            // 将需要的那一段数据截取并反转，得到「倒序」列表
            val list = pool
                .subList(
                    TowerManager.maxLevel.value,
                    TowerManager.maxLevel.value + count
                )
            val state = rememberPagerState(initialPage = 0) {
                count
            }
            Spacer(Modifier.size(padding10))
            VerticalPager(
                state = state,
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                reverseLayout = true
            ) { page ->
                // 这里的 page 即为当前页索引
                OneTowerItem(list[page]) {
                    TowerManager.fight(tower = list[page])
                }
            }
            Row(
                Modifier.fillMaxWidth().padding(padding10),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                EffectButton(modifier = Modifier.size(padding200, padding100), onClick = {
                    goto(TOWER_SELL_SCREEN)
                }) {
                    Image(
                        painter = painterResource(Res.drawable.icon_frame),
                        contentDescription = null,
                        modifier = Modifier.fillMaxSize()
                    )
                    Box {
                        Image(
                            modifier = Modifier.size(imageLargePlus),
                            painter = painterResource(Res.drawable.tower_chest_icon),
                            contentDescription = null
                        )
                        if (TowerManager.hasRed()) {
                            Image(
                                modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .size(imageSmall).graphicsLayer {
                                        translationX = padding6.toPx()
                                    },
                                painter = painterResource(Res.drawable.red_icon),
                                contentDescription = null
                            )
                        }
                    }
                }
                EffectButton(modifier = Modifier.size(padding200, padding100), onClick = {
                    goto(TOWER_RANK_SCREEN)
                }) {
                    Image(
                        painter = painterResource(Res.drawable.icon_frame),
                        contentDescription = null,
                        modifier = Modifier.fillMaxSize()
                    )
                    Image(
                        modifier = Modifier.size(imageLargePlus),
                        painter = painterResource(Res.drawable.rank_icon),
                        contentDescription = null
                    )
                }
            }
        }
    }
}