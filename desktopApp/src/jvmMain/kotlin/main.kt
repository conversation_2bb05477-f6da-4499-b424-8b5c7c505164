import androidx.compose.ui.Alignment
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Window
import androidx.compose.ui.window.WindowPosition
import androidx.compose.ui.window.application
import androidx.compose.ui.window.rememberWindowState
import com.moyu.chuanqirensheng.deskMainView
import com.moyu.chuanqirensheng.platform.screenHeightInDp
import com.moyu.chuanqirensheng.platform.screenWidthInDp

fun main() {
    application {
        val windowState = rememberWindowState(
            width = screenWidthInDp().dp,
            height = screenHeightInDp().dp,
            position = WindowPosition.Aligned(alignment = Alignment.Center)
        )

        Window(
            onCloseRequest = ::exitApplication,
            state = windowState,
            title = "Dragon's Treasure",
            resizable = false,
        ) {
            deskMainView()
        }
    }
}